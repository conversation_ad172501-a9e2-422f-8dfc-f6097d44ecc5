/* 网络拓扑图样式 */
.switch-network-topology {
  width: 100%;
  height: 100%;
  position: relative;
}

.switch-network-topology .topology-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  overflow: hidden;
}

/* 节点样式增强 */
.network-node {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.15));
}

.network-node:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 16px rgba(0, 0, 0, 0.25));
}

.network-node.selected {
  transform: scale(1.1);
  filter: drop-shadow(0 6px 20px rgba(24, 144, 255, 0.4));
}

/* 交换机节点特殊样式 */
.network-node.switch {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  border: 3px solid #40a9ff;
}

.network-node.switch:hover {
  border-color: #1890ff;
  box-shadow: 0 0 20px rgba(24, 144, 255, 0.5);
}

/* 物理端口节点样式 */
.network-node.physical-port {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  border: 2px solid #73d13d;
}

.network-node.physical-port:hover {
  border-color: #52c41a;
  box-shadow: 0 0 15px rgba(82, 196, 26, 0.4);
}

/* 聚合端口节点样式 */
.network-node.trunk-port {
  background: linear-gradient(135deg, #fa8c16, #d46b08);
  border: 2px solid #ffa940;
}

.network-node.trunk-port:hover {
  border-color: #fa8c16;
  box-shadow: 0 0 15px rgba(250, 140, 22, 0.4);
}

/* 机房节点样式 */
.network-node.machine-room {
  background: linear-gradient(135deg, #722ed1, #531dab);
  border: 3px solid #9254de;
}

.network-node.machine-room:hover {
  border-color: #722ed1;
  box-shadow: 0 0 20px rgba(114, 46, 209, 0.5);
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
}

.status-indicator.active {
  background: #52c41a;
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.6);
}

.status-indicator.inactive {
  background: #ff4d4f;
  box-shadow: 0 0 8px rgba(255, 77, 79, 0.6);
}

.status-indicator.unknown {
  background: #d9d9d9;
}

/* 边的动画效果 */
.topology-edge {
  transition: all 0.3s ease;
}

.topology-edge.active {
  animation: flow 2s linear infinite;
}

@keyframes flow {
  0% {
    stroke-dasharray: 5 5;
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dasharray: 5 5;
    stroke-dashoffset: 10;
  }
}

/* 工具栏样式 */
.topology-toolbar {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.topology-toolbar .ant-btn {
  border: none;
  box-shadow: none;
}

.topology-toolbar .ant-btn:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

/* 统计信息面板 */
.topology-stats {
  position: absolute;
  top: 16px;
  left: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 120px;
}

.topology-stats .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.topology-stats .stat-item:last-child {
  margin-bottom: 0;
}

.topology-stats .stat-label {
  font-size: 12px;
  color: #666;
}

.topology-stats .stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

/* 图例样式增强 */
.topology-legend {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.topology-legend .legend-item {
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.topology-legend .legend-item:last-child {
  border-bottom: none;
}

.topology-legend .legend-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  margin-right: 8px;
}

.topology-legend .legend-line {
  height: 3px;
  border-radius: 2px;
  margin-right: 8px;
  min-width: 30px;
}

/* 加载状态 */
.topology-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fafafa;
}

.topology-loading .ant-spin {
  margin-bottom: 16px;
}

/* 错误状态 */
.topology-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fafafa;
  padding: 40px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .network-node.switch {
    transform: scale(0.9);
  }
  
  .network-node.machine-room {
    transform: scale(0.9);
  }
  
  .topology-stats {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .network-node {
    transform: scale(0.8);
  }
  
  .topology-toolbar {
    top: 8px;
    right: 8px;
    padding: 4px;
  }
  
  .topology-stats {
    top: 8px;
    left: 8px;
    padding: 8px 12px;
  }
  
  .topology-legend {
    font-size: 11px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .switch-network-topology .topology-container {
    background: linear-gradient(135deg, #1f1f1f 0%, #2d2d2d 100%);
  }
  
  .topology-toolbar,
  .topology-stats,
  .topology-legend {
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
  }
  
  .topology-stats .stat-label {
    color: #ccc;
  }
  
  .topology-legend .legend-item {
    border-bottom-color: #333;
  }
}

/* 打印样式 */
@media print {
  .topology-toolbar,
  .topology-stats {
    display: none;
  }
  
  .switch-network-topology .topology-container {
    background: #fff;
  }
  
  .network-node {
    filter: none;
    box-shadow: none;
  }
}
