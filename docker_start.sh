#!/bin/bash
# 本地启动命令
# python manage.py makemigrations
# python manage.py migrate
# python manage.py init -y
# python manage.py runserver
if [ "$CELERY_WORKER" == "1" ]; then
    # startup celery, linux Commands
    # 启动【异步任务】同时开启【定时任务】
    celery -A  application.celery worker --loglevel=info --beat -c 20
    # windows 默认不支持celery 需要借助 gevent或eventlet 事件管理器，此框架使用的为gevent，故指定为gevent
    # windows 启动 celery 异步任务
    #  celery -A application.celery worker -l info -c 1 -P gevent
    # windows 启动 celery 定时任务
    # celery -A application.celery beat --loglevel=info
else
    # startup webapp
    export PYTHONPATH="/code"
    uvicorn application.asgi:application --port 8000 --host 0.0.0.0 --workers 4
fi
