import React, { useState } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Select, 
  Space, 
  Typography, 
  Drawer,
  Descriptions,
  Tag,
  Button,
  message
} from 'antd';
import { 
  SettingOutlined,
  InfoCircleOutlined,
  ExportOutlined
} from '@ant-design/icons';
import SwitchNetworkTopology from './SwitchNetworkTopology';
import NetworkTopologyLegend from './NetworkTopologyLegend';

const { Title, Text } = Typography;
const { Option } = Select;

const SwitchTopologyPage = () => {
  const [selectedSwitch, setSelectedSwitch] = useState(null);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [viewMode, setViewMode] = useState('switch'); // 'switch' | 'room'
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedNodeData, setSelectedNodeData] = useState(null);
  
  // 模拟数据 - 实际使用时从API获取
  const switches = [
    { id: '1', name: 'Core-Switch-01', ip: '***********' },
    { id: '2', name: 'Access-Switch-02', ip: '***********' },
    { id: '3', name: 'Distribution-Switch-03', ip: '***********' }
  ];
  
  const rooms = [
    { id: '1', name: '机房A' },
    { id: '2', name: '机房B' },
    { id: '3', name: '机房C' }
  ];
  
  const handleNodeClick = (nodeData) => {
    setSelectedNodeData(nodeData);
    setDrawerVisible(true);
  };
  
  const handleExport = () => {
    message.info('导出功能开发中...');
  };
  
  const renderNodeDetails = () => {
    if (!selectedNodeData) return null;
    
    const { data } = selectedNodeData;
    
    return (
      <Descriptions column={1} size="small">
        <Descriptions.Item label="类型">
          <Tag color={getTypeColor(data.type)}>
            {getTypeLabel(data.type)}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="名称">{data.text}</Descriptions.Item>
        <Descriptions.Item label="状态">
          <Tag color={getStatusColor(data.status)}>
            {getStatusLabel(data.status)}
          </Tag>
        </Descriptions.Item>
        
        {data.ip && (
          <Descriptions.Item label="IP地址">{data.ip}</Descriptions.Item>
        )}
        
        {data.vendor && (
          <Descriptions.Item label="厂商">{data.vendor}</Descriptions.Item>
        )}
        
        {data.speed && (
          <Descriptions.Item label="速度">{data.speed}</Descriptions.Item>
        )}
        
        {data.state && (
          <Descriptions.Item label="接口状态">{data.state}</Descriptions.Item>
        )}
        
        {data.member_count && (
          <Descriptions.Item label="成员数量">{data.member_count}</Descriptions.Item>
        )}
        
        {data.interface_count && (
          <Descriptions.Item label="接口总数">{data.interface_count}</Descriptions.Item>
        )}
        
        {data.switch_count && (
          <Descriptions.Item label="交换机数量">{data.switch_count}</Descriptions.Item>
        )}
        
        {data.details && (
          <>
            {data.details.sn && (
              <Descriptions.Item label="序列号">{data.details.sn}</Descriptions.Item>
            )}
            {data.details.mac_address && (
              <Descriptions.Item label="MAC地址">{data.details.mac_address}</Descriptions.Item>
            )}
            {data.details.last_sync_at && (
              <Descriptions.Item label="最后同步">
                {new Date(data.details.last_sync_at).toLocaleString()}
              </Descriptions.Item>
            )}
            {data.details.members && data.details.members.length > 0 && (
              <Descriptions.Item label="成员端口">
                <Space wrap>
                  {data.details.members.map((member, index) => (
                    <Tag key={index} size="small">{member}</Tag>
                  ))}
                </Space>
              </Descriptions.Item>
            )}
          </>
        )}
      </Descriptions>
    );
  };
  
  const getTypeColor = (type) => {
    const colors = {
      'switch': 'blue',
      'physical-port': 'green',
      'trunk-port': 'orange',
      'machine-room': 'purple'
    };
    return colors[type] || 'default';
  };
  
  const getTypeLabel = (type) => {
    const labels = {
      'switch': '交换机',
      'physical-port': '物理端口',
      'trunk-port': '聚合端口',
      'machine-room': '机房'
    };
    return labels[type] || type;
  };
  
  const getStatusColor = (status) => {
    const colors = {
      'active': 'success',
      'inactive': 'error',
      'unknown': 'default'
    };
    return colors[status] || 'default';
  };
  
  const getStatusLabel = (status) => {
    const labels = {
      'active': '活跃',
      'inactive': '非活跃',
      'unknown': '未知'
    };
    return labels[status] || status;
  };
  
  return (
    <div style={{ padding: 24, height: '100vh', background: '#f0f2f5' }}>
      <Row gutter={[16, 16]} style={{ height: '100%' }}>
        {/* 控制面板 */}
        <Col span={24}>
          <Card size="small">
            <Row justify="space-between" align="middle">
              <Col>
                <Space size="large">
                  <div>
                    <Text strong>查看模式：</Text>
                    <Select
                      value={viewMode}
                      onChange={setViewMode}
                      style={{ width: 120, marginLeft: 8 }}
                      size="small"
                    >
                      <Option value="switch">单个交换机</Option>
                      <Option value="room">机房视图</Option>
                    </Select>
                  </div>
                  
                  {viewMode === 'switch' && (
                    <div>
                      <Text strong>选择交换机：</Text>
                      <Select
                        value={selectedSwitch}
                        onChange={setSelectedSwitch}
                        placeholder="请选择交换机"
                        style={{ width: 200, marginLeft: 8 }}
                        size="small"
                        showSearch
                        optionFilterProp="children"
                      >
                        {switches.map(item => (
                          <Option key={item.id} value={item.id}>
                            {item.name} ({item.ip})
                          </Option>
                        ))}
                      </Select>
                    </div>
                  )}
                  
                  {viewMode === 'room' && (
                    <div>
                      <Text strong>选择机房：</Text>
                      <Select
                        value={selectedRoom}
                        onChange={setSelectedRoom}
                        placeholder="请选择机房"
                        style={{ width: 150, marginLeft: 8 }}
                        size="small"
                      >
                        {rooms.map(item => (
                          <Option key={item.id} value={item.id}>
                            {item.name}
                          </Option>
                        ))}
                      </Select>
                    </div>
                  )}
                </Space>
              </Col>
              
              <Col>
                <Space>
                  <Button 
                    icon={<ExportOutlined />} 
                    size="small"
                    onClick={handleExport}
                  >
                    导出
                  </Button>
                  <Button 
                    icon={<InfoCircleOutlined />} 
                    size="small"
                    type="primary"
                    ghost
                  >
                    帮助
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>
        </Col>
        
        {/* 主要内容区域 */}
        <Col span={18}>
          <SwitchNetworkTopology
            switchId={viewMode === 'switch' ? selectedSwitch : null}
            machineRoomId={viewMode === 'room' ? selectedRoom : null}
            onNodeClick={handleNodeClick}
          />
        </Col>
        
        {/* 图例和信息面板 */}
        <Col span={6}>
          <Space direction="vertical" size={16} style={{ width: '100%' }}>
            <NetworkTopologyLegend />
            
            <Card 
              title="使用说明" 
              size="small"
              style={{ fontSize: 12 }}
            >
              <Space direction="vertical" size={8}>
                <Text style={{ fontSize: 11 }}>
                  • 点击节点查看详细信息
                </Text>
                <Text style={{ fontSize: 11 }}>
                  • 拖拽节点调整位置
                </Text>
                <Text style={{ fontSize: 11 }}>
                  • 滚轮缩放视图
                </Text>
                <Text style={{ fontSize: 11 }}>
                  • 右键拖拽平移画布
                </Text>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>
      
      {/* 节点详情抽屉 */}
      <Drawer
        title={
          <Space>
            <InfoCircleOutlined />
            节点详情
          </Space>
        }
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={400}
      >
        {renderNodeDetails()}
      </Drawer>
    </div>
  );
};

export default SwitchTopologyPage;
