"""
<AUTHOR>
@Date    ：2024/11/16
"""
from dvadmin.utils.json_response import SuccessResponse


from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet

from tenant.models import TenantOpenstackSecurityGroup
from tenant.serializers.tenant_openstack_security_group import (
    TenantOpenstackSecurityGroupSerializer,
    # TenantOpenstackSecurityGroupCreateSerializer,
    TenantOpenstackSecurityGroupImportSerializer
)


class TenantOpenstackSecurityGroupViewSet(ChaosTenantModelViewSet):
    """
    客户信息接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantOpenstackSecurityGroup.objects.order_by('-create_datetime')
    serializer_class = TenantOpenstackSecurityGroupSerializer
    # create_serializer_class = TenantOpenstackSecurityGroupCreateSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = TenantOpenstackSecurityGroupImportSerializer
    import_field_dict = {
        "name": "项目名称",
    }
    # permission_classes = []

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        node = request.GET.get('node')
        if node:
            queryset = queryset.filter(node=node)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True, request=request)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True, request=request)
        return SuccessResponse(data=serializer.data, msg="获取成功")
