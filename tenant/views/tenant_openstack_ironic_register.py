"""
<AUTHOR>
@Date    ：2024/11/16
"""
import time

from django.utils import timezone
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from django.core.cache import cache
from dvadmin.utils.json_response import ErrorResponse, SuccessResponse
from application.logger import logger
from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet

from dvadmin.system.models import Dictionary
from tenant.models import TenantOpenstackIronicHypervisorRegister, TenantOpenstackIronicHypervisor
from tenant.serializers.tenant_openstack_ironic_register import (
    TenantOpenstackIronicHypervisorRegisterSerializer,
    TenantOpenstackIronicHypervisorRegisterImportSerializer
)
import tenant.dictionary_enum_keys as ENUM_KEYS
from tenant.utils.sdk.op_baremetal_node.op_baremetal_node_admin import OpenstackBaremetalNodeManager
from tenant.dictionary_enum_keys import HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY


class TenantOpenstackIronicHypervisorRegisterViewSet(ChaosTenantModelViewSet):
    """
    客户信息接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantOpenstackIronicHypervisorRegister.objects.order_by('-create_datetime')
    serializer_class = TenantOpenstackIronicHypervisorRegisterSerializer
    # create_serializer_class = TenantOpenstackIronicHypervisorRegisterCreateSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': False,
        'id_field': 'id',
        'name_field': 'name',
    }
    signal_filed = 'ironic_hyper_id'
    # 导入
    import_serializer_class = TenantOpenstackIronicHypervisorRegisterImportSerializer
    import_field_dict = {
        "name": "裸机节点名称(必填)",
        "description": "描述(非必填)",
        "node_resource_class": "裸机信息-资源类(必填)",
        "node_network_interface": {
            "title": "裸机信息-管理接口(必填)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.CLOUD_RESOURCE_POOL_BAREMETAL_NODE_NETWORK_INTERFACE_KEY
            ), "values_name": "label", "key_name": "value"}
            },
        "node_driver": {
            "title": "裸机信息-驱动(必填)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.CLOUD_RESOURCE_POOL_BAREMETAL_NODE_DRIVER_KEY
            ), "values_name": "label", "key_name": "value"}
            },
        "node_properties": "裸机信息-属性(必填)",
        "node_driver_ipmi_username": "裸机IPMI信息-用户名(必填)",
        "node_driver_ipmi_password": "裸机IPMI信息-密码(必填)",
        "node_driver_ipmi_address": "裸机IPMI信息-BMC_IP地址(必填)",
        "port_group_mode": {
            "title": "端口组-模式(必填)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.CLOUD_RESOURCE_POOL_BAREMETAL_NODE_PORT_GROUP_MODE_KEY
            ), "values_name": "label", "key_name": "value"}
            },
        "port_group_standalone_ports_supported": {
            "title": "端口组-SA-Ports(必填)",
            "choices": {
                "data": { "是": 1, "否": 0}
                }
            },
        "port_group_properties": "端口组-属性(必填)",
        "port_switch_id": "端口-交换机ID(必填)",
        "port_switch_info": "端口-交换机信息(必填)",
        "port_port_id": "端口-VLAN-端口ID(必填)",
        "port_hardware_address": "端口-mac1(必填)",
        "port_network_address": "端口-mac2(必填)",
        "sn": "序列号(必填项)",
        "node": {
            "title": "区域节点(必填)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.CLOUD_RESOURCE_POOL_BAREMETAL_NODE_REGION_KEY
            ), "values_name": "label", "key_name": "value"}
            },
    }
    # permission_classes = []

    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def manage_start_register(self, request):
        """
        开启裸机节点注册任务
        """

        request_data = request.data
        register_ids = request_data.get("register_ids", [])
        if not register_ids:
            return ErrorResponse(msg="请选择要操作的裸机节节点")
        from dvadmin.celery_workers.sync_openstack_resource_worker import (
            sync_multi_register_baremetal_node_worker,
            )
        sync_multi_register_baremetal_node_worker.apply_async(args=(register_ids,))
        # logger.info(f'register_ids: {register_ids}')
        # objs = TenantOpenstackIronicHypervisorRegister.objects.filter(
        #     id__in=register_ids,
        #     is_deleted=False,
        #     ).all()
        # if not objs:
        #     return ErrorResponse(msg="裸机节点注册任务不存在或已被删除")
        # for register_task in objs:
        #     admin_client = OpenstackBaremetalNodeManager(node=register_task.node)
        #     code, msg, data = admin_client.start_register_ironic_hypervisor_worker(
        #         register_id=register_task.id
        #         )
        #
        #     logger.info(f'【注册任务结果】code:{str(code)},data: {str(data)}, msg: {msg}')
        return SuccessResponse(msg="注册任务调度成功")


def multi_register_baremetal_node(register_ids):
    """
    批量注册裸机节点
    :param register_ids:
    :return:
    """
    if not register_ids:
        return ErrorResponse(msg="请选择要操作的裸机节节点")
    logger.info(f'register_ids: {register_ids}')
    objs = TenantOpenstackIronicHypervisorRegister.objects.filter(
        id__in=register_ids,
        is_deleted=False,
        ).all()
    # 校验注册区域节点区域唯一
    area_node = set()
    for register_task in objs:
        area_node.add(register_task.node)
    if len(area_node) > 1:
        logger.warning('为了安全考虑，裸机节点注册任务区域必须为同一区域!!!')
        return ErrorResponse(msg="为了安全考虑，裸机节点注册任务区域必须为同一区域!!!")
    elif not area_node:
        logger.warning("地域不存在，请检查!!!")
        return ErrorResponse(msg="地域不存在，请检查!!!")
    if not objs:
        return ErrorResponse(msg="裸机节点注册任务不存在或已被删除")
    registered_op_ironic_hyper_node_ids = []
    for register_task in objs:
        admin_client = OpenstackBaremetalNodeManager(node=register_task.node)
        code, msg, data = admin_client.start_register_ironic_hypervisor_worker(
            register_id=register_task.id,
            is_validate_provision_state=True
            )
        logger.info(f'【注册任务结果】code:{str(code)},data: {str(data)}, msg: {msg}')

        if isinstance(data, dict):
            register_task.is_success = True
            register_task.created_hyp_ironic_node_id = data['id']
            register_task.register_status = '已成功'
            registered_op_ironic_hyper_node_ids.append(data['id'])
        else:
            register_task.is_success = False
            register_task.register_status = '已失败'
        register_task.created_logger_info = msg
        register_task.save()
        # 注册成功后等待2秒
        time.sleep(2)
    # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
    cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=list(area_node)[0]), True, timeout=20 * 60)
    return None
