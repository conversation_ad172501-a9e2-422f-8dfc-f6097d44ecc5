"""
<AUTHOR>
@Date    ：2024/11/5
"""
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from django.core.cache import cache

from application.logger import logger
from dvadmin.utils.json_response import DetailResponse, ErrorResponse, SuccessResponse
from operatorcmdb.models import Host
from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet
from tenant.models import (
    TenantOpenstackServer,
    TenantOpenstackIronicHypervisor,
    TenantOPServerSoftware,
)
from tenant.serializers.tenant_openstack_server import (
    TenantOpenstackServerSerializer,
    TenantOpenstackServerImportSerializer,
    tenant_openstack_server_to_operator_cmdb_hosts,
    retry_op_server_software_install_task,
)
from tenant.dictionary_enum_keys import HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY
from tenant.views.tenant_hisec_engine_nat_server_policy import recycle_hisec_nat_server_policy
from tenant.utils.sdk.openstack_client import OpenstackAdminClient
from tenant.utils.tenant_to_project import get_tenant_account_projects


class TenantOpenstackServerViewSet(ChaosTenantModelViewSet):
    """
    Openstack Vlan接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantOpenstackServer.objects.order_by('-create_datetime')
    serializer_class = TenantOpenstackServerSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'instance_id',
        'name_field': 'name',
    }
    signal_filed = 'instance_id'
    # 导入
    import_serializer_class = TenantOpenstackServerImportSerializer
    import_field_dict = {
        "name": "vlan名称(非必填项)",
        "description": "描述(非必填项)",
        "vlan_id": "VlanId(必填项)",
        "phy_vlan_type": "物理vlan类型(默认为规定的vlan)(必填项)",
        "subnet": "子网网段(必填项)",
        "available_ip_pool": "可用IP池(必填项)",
        "gateway_ip": "网关IP(必填项)",
        "node": "Openstack节点(默认暂定为金华)(非必填项)"
    }
    permission_classes = []


    # def get_list_by_ids(self, request, *args, **kwargs):
    #     """根据ID列表查询"""
    #     if not self.api_list_by_ids_dict['is_opened']:
    #         return ErrorResponse(data={}, msg='此接口未配置显示信息')
    #     ids = request.query_params.getlist('ids')
    #     base_filters = {
    #         "is_deleted": False
    #         }
    #     if ids:
    #         base_filters.update({f"{self.api_list_by_ids_dict['id_field']}__in": ids})
    #     for key, value in request.query_params.items():
    #         print(f'key: {key}, value: {value}')
    #     print(f'queryset: {self.queryset}')
    #     is_all = request.query_params.getlist('is_all', False)
    #     if is_all:
    #         queryset = self.filter_queryset(
    #             self.get_queryset().values(
    #                 self.api_list_by_ids_dict['id_field'], self.api_list_by_ids_dict['name_field'] or 'description'
    #                 )
    #             )
    #     else:
    #         queryset = self.filter_queryset(
    #             self.get_queryset().filter(id__in=ids).values(
    #                 self.api_list_by_ids_dict['id_field'], self.api_list_by_ids_dict['name_field'] or 'description'
    #                 )
    #             )
    #     return SuccessResponse(data=queryset, msg="获取成功")

    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def soft_delete_openstack_server(self, request):
        request_data = request.data
        server_id = request_data.get('id')
        obj = TenantOpenstackServer.objects.filter(
            id=server_id,
            is_deleted=False,
        ).first()
        if not obj:
            return ErrorResponse(msg="实例不存在或已被删除")
        if obj.is_lock:
            return ErrorResponse(msg="实例已被锁定，请先解锁")
        try:
            admin_client = OpenstackAdminClient(node=obj.node)
            res = admin_client.delete_server(instance_id=obj.instance_id)
            if isinstance(res, dict) and res.get('delete_code', True):
                # 同步主机数据任务
                return ErrorResponse(msg=res['error_msg'])
            obj.is_deleted = True
            obj.save()
        except Exception as e:
            logger.error(f"Error soft deleting Openstack Server Instance,<Detail: {str(e)}>")
            return ErrorResponse(data={}, msg="删除实例错误")
        return DetailResponse(data=[], msg="删除成功")

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        obj = TenantOpenstackServer.objects.filter(
            id=instance.id,
            is_deleted=False,
            ).first()
        if not obj:
            return ErrorResponse(msg="实例不存在或已被删除")

        if obj.is_lock:
            return ErrorResponse(msg="实例已被锁定，请先解锁")
        with transaction.atomic():
            admin_client = OpenstackAdminClient(node=obj.node)
            admin_client.delete_server(instance_id=obj.instance_id, is_destroy=True)
            obj.is_deleted = True
            obj.save()
            # 信息同步到运维主机管理
            tenant_openstack_server_to_operator_cmdb_hosts(obj.id)
            # 删除 hisec_nat_server_policy 回收公网 ip
            recycle_hisec_nat_server_policy(**{ "op_server": obj.instance_id, "node": admin_client.node})
            # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
            cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=obj.node), True, timeout=20 * 60)
        return DetailResponse(data=[], msg="删除成功")

    @staticmethod
    def get_ops_softwares(data):
        _data = []
        for item in data:
            # 获取主机关联的软件信息
            softwares = TenantOPServerSoftware.objects.filter(
                node=item['node'],
                op_server_id=item['instance_id'],
                is_deleted=False,
            ).all()
            ops_softwares = [software.to_dict for software in softwares]
            item['ops_softwares'] = ops_softwares
            _data.append(item)
        return _data

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        if 'account_id' in request.query_params.keys():
            project_ids = get_tenant_account_projects(
                account_id=request.query_params['account_id'], is_project_ids=True)
            queryset = queryset.filter(project_id__in=project_ids)
        # 区域节点信息node 采用 equal 查询
        node = request.GET.get('node')
        if node:
            queryset = queryset.filter(node=node)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True, request=request)
            # 添加 TenantOPSoftwareStatus 数据
            data = self.get_ops_softwares(serializer.data)
            return self.get_paginated_response(data)
        serializer = self.get_serializer(queryset, many=True, request=request)
        # 添加 TenantOPSoftwareStatus 数据
        data = self.get_ops_softwares(serializer.data)
        return SuccessResponse(data=data, msg="获取成功")

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def update_server_name_or_desc(self, request, id):
        """
        更新Openstack中的名称或描述
        """
        server_id = id
        request_data = request.data
        name = request_data.get('name')
        description = request_data.get('description')
        obj = TenantOpenstackServer.objects.filter(
            id=server_id,
            is_deleted=False,
        ).first()
        if not obj:
            return ErrorResponse(msg="实例不存在或已被删除")
        with transaction.atomic():
            admin_client = OpenstackAdminClient(node=obj.node)
            code, msg, data = admin_client.update_server_name_and_desc(
                server_id=obj.instance_id,
                name=name,
                description=description
            )
            if code:
                if name:
                    obj.name = name
                if description:
                    obj.description = description
                obj.save()
                return SuccessResponse(data=data, msg='更新成功')
            else:
                return ErrorResponse(msg=msg)

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def update_server_expire_at(self, request, id):
        """
        更新裸金属的过期时间
        """
        server_id = id
        request_data = request.data
        expire_at = request_data.get('expire_at', None)
        obj = TenantOpenstackServer.objects.filter(
            id=server_id,
            is_deleted=False,
        ).first()
        if not obj:
            return ErrorResponse(msg="实例不存在或已被删除")
        # 更新 expire_at 字段
        obj.expire_at = expire_at
        obj.save()
        # 同步更新 operatorcmdb 模块下的 Host 通过 BMC_IP 关联的主机信息过期时间
        return tenant_openstack_server_to_operator_cmdb_hosts(server_id)

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def update_server_to_op_cmdb_host(self, request, id):
        """
        同步 Openstack 内裸金属服务器信息到 CMDB 主机管理中(通过 BMC_IP 关联)
        """
        return tenant_openstack_server_to_operator_cmdb_hosts(id)

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def stop_server(self, request, id):
        """
        关闭实例会停止其实例的运行，但保留其所有配置和数据。
        """
        server_id = id
        obj = TenantOpenstackServer.objects.filter(
            id=server_id,
            is_deleted=False,
        ).first()
        if not obj:
            return ErrorResponse(msg="实例不存在或已被删除")

        if obj.is_lock:
            return ErrorResponse(msg="实例已被锁定，请先解锁")
        with transaction.atomic():
            admin_client = OpenstackAdminClient(node=obj.node)
            code, msg, data = admin_client.stop_server(
                server_id=obj.instance_id,
            )
            # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
            cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=obj.node), True, timeout=20 * 60)
            if code:
                obj.status = 'RESIZED'
                obj.save()
                return SuccessResponse(data=data, msg='关机调度成功')
            else:
                return ErrorResponse(msg=msg)

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def start_server(self, request, id):
        """
        将实例从停止状态恢复到运行状态，开始消耗计算资源。
        """
        server_id = id
        obj = TenantOpenstackServer.objects.filter(
            id=server_id,
            is_deleted=False,
        ).first()
        if not obj:
            return ErrorResponse(msg="实例不存在或已被删除")
        if obj.is_lock:
            return ErrorResponse(msg="实例已被锁定，请先解锁")
        with transaction.atomic():
            admin_client = OpenstackAdminClient(node=obj.node)
            code, msg, data = admin_client.start_server(
                server_id=obj.instance_id,
            )
            # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
            cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=obj.node), True, timeout=20 * 60)
            if code:
                obj.status = 'RESIZED'
                obj.save()
                return SuccessResponse(data=data, msg='开机调度成功')
            else:
                return ErrorResponse(msg=msg)

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def reboot_server(self, request, id):
        """
        重启实例并执行特定的操作。
        1. [软重启], 操作系统有机会正常关闭服务并保存状态，适用于计划内的维护或更新。
        2. [硬重启], 立即重启实例，不给操作系统机会来优雅地关闭服务。这可能用于恢复因软件故障而无法响应的实例。
        """
        server_id = id
        request_data = request.data
        reboot_type = request_data.get('reboot_type')
        obj = TenantOpenstackServer.objects.filter(
            id=server_id,
            is_deleted=False,
        ).first()
        if not obj:
            return ErrorResponse(msg="实例不存在或已被删除")
        if obj.is_lock:
            return ErrorResponse(msg="实例已被锁定，请先解锁")
        with transaction.atomic():
            admin_client = OpenstackAdminClient(node=obj.node)
            code, msg, data = admin_client.reboot_server(
                server_id=obj.instance_id,
                reboot_type=reboot_type,
            )
            # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
            cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=obj.node), True, timeout=20 * 60)
            if code:
                obj.status = 'RESIZED'
                obj.save()
                return SuccessResponse(data=data, msg='重启调度成功')
            else:
                return ErrorResponse(msg=msg)

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def rebuild_server(self, request, id):
        """
        重建主机[Actions.rebuild_server]
        重建实例会用新的镜像覆盖现有实例的磁盘，同时保持实例的ID、IP地址和其他配置不变。
        重建实例相当于格式化磁盘并安装一个新的操作系统镜像，常用于快速部署新环境或修复严重损坏的操作系统。
        """
        server_id = id
        request_data = request.data
        image_id = request_data.get('image_id')
        name = request_data.get('name')
        description = request_data.get('description')
        obj = TenantOpenstackServer.objects.filter(
            id=server_id,
            is_deleted=False,
        ).first()
        if not obj:
            return ErrorResponse(msg="实例不存在或已被删除")
        if obj.is_lock:
            return ErrorResponse(msg="实例已被锁定，请先解锁")
        with transaction.atomic():
            admin_client = OpenstackAdminClient(node=obj.node)
            code, msg, data = admin_client.rebuild_server(
                server_id=obj.instance_id,
                image_id=image_id,
                name=name,
                description=description
            )
            # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
            cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=obj.node), True, timeout=20 * 60)
            if code:
                if name:
                    obj.name = name
                if description:
                    obj.description = description
                obj.status = 'RESIZED'
                obj.save()
                return SuccessResponse(data=data, msg='重建主机调度成功')
            else:
                return ErrorResponse(msg=msg)

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def retry_op_server_software_install_task(self, request, id):
        """
        重试主机内的软件安装任务。
        """
        op_server_software_install_id = id
        ops_software = TenantOPServerSoftware.objects.filter(
            id=op_server_software_install_id,
            is_deleted=False
            ).first()
        if not ops_software:
            logger.error(
                f'【原任务ID不存在】op_server_software_install_id:{op_server_software_install_id}未找到, 跳过更新'
                )
            return ErrorResponse(msg='软件安装任务调度失败【原任务ID不存在】')

        obj = TenantOpenstackServer.objects.filter(
            instance_id=ops_software.op_server_id,
            node=ops_software.node,
            is_deleted=False,
            ).first()
        if not obj:
            return ErrorResponse(msg="实例不存在或已被删除")
        if obj.is_lock:
            return ErrorResponse(msg="实例已被锁定，请先解锁")
        return retry_op_server_software_install_task(op_server_software_install_id=op_server_software_install_id)
