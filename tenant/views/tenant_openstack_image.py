"""
<AUTHOR>
@Date    ：2024/11/4
"""
from dvadmin.utils.json_response import SuccessResponse

from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet
from tenant.models import TenantOpenstackImage
from tenant.serializers.tenant_openstack_image import (
    TenantOpenstackImageSerializer,
    TenantOpenstackImageImportSerializer
)


class TenantOpenstackImageViewSet(ChaosTenantModelViewSet):
    """
    Openstack Vlan接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantOpenstackImage.objects.order_by('-create_datetime')
    serializer_class = TenantOpenstackImageSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'account_nick_name',
    }
    # 导入
    import_serializer_class = TenantOpenstackImageImportSerializer
    import_field_dict = {
        "name": "vlan名称(非必填项)",
        "description": "描述(非必填项)",
        "vlan_id": "VlanId(必填项)",
        "phy_vlan_type": "物理vlan类型(默认为规定的vlan)(必填项)",
        "subnet": "子网网段(必填项)",
        "available_ip_pool": "可用IP池(必填项)",
        "gateway_ip": "网关IP(必填项)",
        "node": "Openstack节点(默认暂定为金华)(非必填项)"
    }
    permission_classes = []

    def list(self, request, *args, **kwargs):
        query_params = request.query_params.copy()
        queryset = self.filter_queryset(self.get_queryset())
        node = query_params.get('node', None)
        if node:
            queryset = queryset.filter(node=node)
        # 构建公共资源共享查询集的基础条件
        public_filters = {
            'visibility': 'public',
            # TODO 暂不加入到查询条件中, 后续有需要再加入
            # 'visibility__in': ['public', 'shared'],
            'is_deleted': False,
        }
        public_columns = ['limit', 'page']
        equ_columns = ['project_id', 'image_type', 'is_to_portal', 'status',]
        # 动态添加其他查询条件
        for key, value in query_params.items():
            if key in public_columns:
                continue
            elif key in equ_columns:
                public_filters[key] = value
            else:
                public_filters[f'{key}__contains'] = value
        # 获取公共资源共享查询集
        public_queryset = queryset.filter(**public_filters)
        # 使用|操作符合并两个查询集，确保没有重复项
        combined_queryset = queryset | public_queryset
        # 如果需要去重，可以使用distinct()方法
        combined_queryset = combined_queryset.distinct()
        page = self.paginate_queryset(combined_queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True, request=request)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True, request=request)
        return SuccessResponse(data=serializer.data, msg="获取成功")
