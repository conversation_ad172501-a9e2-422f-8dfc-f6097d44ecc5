"""
<AUTHOR>
@Date    ：2024/10/30
"""
from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet
from tenant.models import TenantOpenstackVlan
from tenant.serializers.tenant_openstack_vlan import (
    TenantOpenstackVlanSerializer,
    TenantOpenstackVlanImportSerializer,
    single_create_openstack_physical_vlan_network,
)
from rest_framework.decorators import action
from dvadmin.utils.json_response import DetailResponse, ErrorResponse
from rest_framework.permissions import IsAuthenticated


class TenantOpenstackVlanViewSet(ChaosTenantModelViewSet):
    """
    Openstack Vlan接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantOpenstackVlan.objects.order_by('-create_datetime')
    serializer_class = TenantOpenstackVlanSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'account_nick_name',
    }
    # 导入
    import_serializer_class = TenantOpenstackVlanImportSerializer
    import_field_dict = {
        "name": "vlan名称(非必填项)",
        "description": "描述(非必填项)",
        "vlan_id": "VlanId(必填项)",
        "phy_vlan_type": "物理vlan类型(默认为规定的vlan)(必填项)",
        "subnet": "子网网段(必填项)",
        "available_ip_pool": "可用IP池(必填项)",
        "gateway_ip": "网关IP(必填项)",
        "node": "Openstack节点(默认暂定为金华)(非必填项)"
    }
    permission_classes = []

    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def create_openstack_physical_vlan_network(self, request):
        """
        创建 openstack 中对应的物理网络
        :param request:
        :return:
        """
        request_data = request.data
        project_id = request_data.get('project_id')
        if not project_id:
            return ErrorResponse(msg="project_id 不能为空")
        physical_vlan_network_id = request_data.get('id')
        if not id:
            return ErrorResponse(msg="Chaos系统内的物理网络id不能为空")
        physical_vlan_network_obj = TenantOpenstackVlan.objects.filter(
            id=physical_vlan_network_id,
            is_deleted=False,
            is_used=False
        ).first()
        physical_vlan_network_obj.project_id = project_id
        physical_vlan_network_obj.save()
        if not physical_vlan_network_obj:
            return ErrorResponse(msg="vlan 信息不存在或已被使用")
        physical_vlan_network_data = physical_vlan_network_obj.to_dict
        physical_vlan_network_obj.is_tasking = True
        physical_vlan_network_obj.save()
        from dvadmin.celery_workers.sync_openstack_resource_worker import (
            async_single_create_openstack_physical_vlan_network_worker
        )
        async_single_create_openstack_physical_vlan_network_worker.apply_async(args=(
            None,
            physical_vlan_network_data['id'],
            None,
            physical_vlan_network_data['node'],))
        return DetailResponse(msg="异步任务调度成功", data=physical_vlan_network_data)










