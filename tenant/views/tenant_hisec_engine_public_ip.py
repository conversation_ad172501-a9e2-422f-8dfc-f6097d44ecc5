"""
<AUTHOR>
@Date    ：2025/03/13
"""
from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet
from tenant.models import TenantHisecEnginePublicIP
from tenant.serializers.tenant_hisec_engine_public_ip import (
    TenantHisecEnginePublicIpSerializer,
    TenantHisecEnginePublicIpImportSerializer,
)
from rest_framework.decorators import action
from dvadmin.utils.json_response import DetailResponse, ErrorResponse
from rest_framework.permissions import IsAuthenticated


class TenantHisecEnginePublicIPViewSet(ChaosTenantModelViewSet):
    """
    Openstack Vlan接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantHisecEnginePublicIP.objects.order_by('-create_datetime')
    serializer_class = TenantHisecEnginePublicIpSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'public_ip',
        'name_field': 'public_ip',
    }
    # 导入
    import_serializer_class = TenantHisecEnginePublicIpImportSerializer
    import_field_dict = {
        "public_ip": "公网地址(必填项)",
    }
    permission_classes = []









