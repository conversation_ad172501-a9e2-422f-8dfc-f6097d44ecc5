"""
<AUTHOR>
@Date    ：2024/03/13
"""
from django.db import transaction

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from netaddr import IPAddress, IPNetwork, AddrFormatError
from application.logger import logger
from operatorcmdb.serializers.host import check_operator_cmdb_host_quite_buffer
from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet
from tenant.models import (
    TenantHisecEngineNatServerPolicy,
    TenantHisecEnginePublicIP,
    TenantOpenstackServer,
    TenantOpenstackProject,
)
from tenant.serializers.tenant_hisec_engine_nat_server_policy import (
    TenantHisecEngineNatServerPolicySerializer,
    TenantHisecEngineNatServerPolicyImportSerializer,
)
from tenant.utils.sdk.hisec_engine_client import HisecEngineClient
from tenant.utils.tenant_to_project import get_project_account
from rest_framework.decorators import action
from dvadmin.utils.json_response import DetailResponse, ErrorResponse, SuccessResponse
from rest_framework.permissions import IsAuthenticated


def check_hisec_nat_server_is_all_deleted(project_id, public_ip, node=None):
    """
    若所有的规则均被删除后，回收 public_ip
    """
    nat_server_policies = TenantHisecEngineNatServerPolicy.objects.filter(
        project_id=project_id,
        public_ip=public_ip,
        node=node,
        is_deleted=False,
    ).count()
    if not nat_server_policies:
        logger.warning(f'NAT Server 公网IP映射规则已全部删除，回收公网IP,'
                       f'<公网IP:{public_ip}><原项目ID:{project_id}>')
        TenantHisecEnginePublicIP.objects.filter(
            project_id=project_id,
            public_ip=public_ip,
            node=node,
            is_deleted=False,
        ).update(
            used=False,
            project_id=None,
            tenant_id=None,
            description=None,
        )


def check_ip_validity(ip):
    """
    验证IP地址的合法性
    :param ip: 待验证的IP地址
    :return: 布尔值（True=合法，False=非法）
    """
    try:
        IPAddress(ip)
        return True
    except AddrFormatError:
        return False

def is_ip_in_subnet_range(ip: str) -> bool:
    """
    验证IP是否在***********/24到***********/24的连续子网范围内
    :param ip: 目标IP地址（如'***********'）
    :return: 布尔值（True=在范围内，False=不在）
    """
    try:
        target_ip = IPAddress(ip)
        # 生成所有待匹配的/24子网（200 ≤ 第三段 ≤ 253）
        subnet_third_octet_range = range(200, 250)  # 200~253
        subnets = [
            IPNetwork(f"10.10.{third_octet}.0/24")
            for third_octet in subnet_third_octet_range
        ]
        # 检查IP是否属于任意子网
        return any(target_ip in subnet for subnet in subnets)
    except AddrFormatError:
        raise ValueError(f"无效的IP格式：{ip}")


def recycle_hisec_nat_server_policy(**kwargs):
    """回收NAT Server 公网IP映射规则"""
    op_server = kwargs.get('op_server')
    node = kwargs.get('node')
    policy_id = kwargs.get('id')
    # 是否检测NAT Server 公网IP映射规则是否全部删除
    is_check_nat_server_all_deleted = kwargs.get('is_check_nat_server_all_deleted', True)
    with transaction.atomic():
        admin_client = HisecEngineClient(env='product', node=node)
        if policy_id:
            nat_server_policies = TenantHisecEngineNatServerPolicy.objects.filter(
                id=policy_id,
                node=node,
                is_deleted=False,
            ).all()
        else:
            nat_server_policies = TenantHisecEngineNatServerPolicy.objects.filter(
                op_server_id=op_server,
                is_deleted=False,
                node=node,
            ).all()
        if not nat_server_policies:
            return False, '无映射需要删除', {}
        for nat_server_policy in nat_server_policies:
            node = nat_server_policy.op_server.node
            name = nat_server_policy.name
            if 'chaos' not in name:
                return False, '非chaos 创建的映射,无法删除', {}
            code, hisec_res = admin_client.delete_nat_server(name=name)
            if code == 200:
                nat_server_policy.delete()
                if is_check_nat_server_all_deleted:
                    check_hisec_nat_server_is_all_deleted(
                        project_id=nat_server_policy.project_id, public_ip=nat_server_policy.public_ip.public_ip, node=node
                    )
        return True, '删除成功', {}


class TenantHisecEngineNatServerPolicyViewSet(ChaosTenantModelViewSet):
    """
    Openstack Vlan接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantHisecEngineNatServerPolicy.objects.order_by('-create_datetime')
    serializer_class = TenantHisecEngineNatServerPolicySerializer
    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = TenantHisecEngineNatServerPolicyImportSerializer
    import_field_dict = {
        "name": "vlan名称(必填项)",
        "description": "描述(非必填项)",
        "project_name": "项目名称(必填项)",
        "public_ip": "公网IP(必填项)",
        "public_port": "公网端口(非必填项)",
        "inside_ip": "内网IP(必填项)",
        "inside_port": "内网端口(非必填项)",
    }
    permission_classes = []

    # op_server = openapi.Parameter('op_server', openapi.IN_QUERY, description='主机实例ID', type=openapi.TYPE_STRING)
    # public = openapi.Parameter('key', openapi.IN_QUERY, description='openstack节点配置键', type=openapi.TYPE_STRING)
    #
    # @swagger_auto_schema(method='GET', manual_parameters=[op_server, node, ],
    #                      operation_summary='创建公网主机映射')
    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def create_hisec_nat_server_policy(self, request, *args, **kwargs):
        # virtual_params = {
        #     'op_server': 'xxxxxx',
        #     'public_ip': 'xxxx',
        #     'inside_port': 4000,
        #     'public_port': 4000,
        # }
        return self.static_create_hisec_nat_server_policy(request=request)

    @staticmethod
    def static_create_hisec_nat_server_policy(request=None, body_data=None):
        if request:
            body_data = request.data
        with transaction.atomic():
            # 整理成hisec引擎需要的数据
            op_server = body_data.get('op_server')
            node = body_data.get('node')
            if not node:
                return ErrorResponse(msg="未获取到node参数")
            op_server_instance = TenantOpenstackServer.objects.filter(
                instance_id=op_server,
                node=node,
                is_deleted=False,
            ).only('project_id', 'ipaddr', 'node', 'extr_public_ipv4').first()
            if not op_server_instance:
                return ErrorResponse(msg='OpenstackServer中主机已被删除或不存在')
            server_project = TenantOpenstackProject.objects.filter(
                project_id=op_server_instance.project_id,
                node=op_server_instance.node,
                is_deleted=False,
            ).only('name').first()
            if not server_project:
                return ErrorResponse(msg='OpenstackProject中项目已被删除或不存在')
            public_port = int(body_data.get('public_port', 0))
            inside_port = int(body_data.get('inside_port', 0))
            temp_public_ip = body_data.get('public_ip')
            if temp_public_ip and not check_ip_validity(temp_public_ip):
                return ErrorResponse(msg='公网ip有误')
            public_ip_obj = TenantHisecEnginePublicIP.objects.filter(
                public_ip=temp_public_ip,
                node=op_server_instance.node,
                is_deleted=False,
            ).first()
            inside_ip = op_server_instance.ipaddr
            try:
                is_in = is_ip_in_subnet_range(inside_ip)
                if not is_in:
                    return ErrorResponse(msg='内网ip不在10.10.2**.***(Chaos自动化管理)网段内,不予创建')
            except Exception as e:
                logger.error(e)
                return ErrorResponse(msg='ip地址有誤!!!!')
            if not public_ip_obj:
                return ErrorResponse(msg='公网ip不存在')

            # 判断公网ip是否被使用，并记录该公网ip的使用情况
            if public_ip_obj.used and public_ip_obj.project_id != op_server_instance.project_id:
                return ErrorResponse(msg='公网ip已被使用,不可再次分配')
            else:
                public_ip_obj.used = True
                public_ip_obj.project_id = op_server_instance.project_id
                # 获取项目ID对应的账号信息
                account_info = get_project_account(op_server_instance.project_id)
                if account_info:
                    public_ip_obj.description = account_info.company
                public_ip_obj.save()

            # 公网IP映射规则 指定端口映射特殊处理
            if public_port and inside_port:
                # 需删除旧的全部映射规则,此端口映射才会生效
                had_policy = TenantHisecEngineNatServerPolicy.objects.filter(
                    public_ip=public_ip_obj,
                    inside_ip=inside_ip,
                    public_port=0,
                    inside_port=0,
                    node=op_server_instance.node,
                    protocol="any",
                    is_deleted=False,
                ).order_by('-create_datetime').first()
                if had_policy:
                    recycle_hisec_nat_server_policy(**{
                        "id": had_policy.id,
                        "is_check_nat_server_all_deleted": False,
                        "node": had_policy.node,
                    })
                else:
                    logger.warning('无需删除多余规则！！！')
                policy_data = {
                    "project_name": server_project.name,
                    "public_ip": body_data.get('public_ip'),
                    "public_port": public_port,
                    "inside_ip": inside_ip,
                    "inside_port": inside_port,
                }
            else:
                policy_data = {
                    "project_name": server_project.name,
                    "public_ip": body_data.get('public_ip'),
                    "inside_ip": inside_ip,
                }
            admin_client = HisecEngineClient(env='product', node=op_server_instance.node)
            code, hisec_res = admin_client.create_nat_server(**policy_data)
            if code != 200:
                return ErrorResponse(msg=f'nat server创建失败, hisec引擎返回信息:{hisec_res}')
            create_nat_server_policy_params = {
                'name': hisec_res['server-mapping']['name'],
                'protocol': hisec_res['server-mapping'].get('protocol', 'any'),
                'project_id': op_server_instance.project_id,
                'op_server_id': op_server,
                'inside_ip': inside_ip,
                'inside_port': inside_port,
                'public_port': public_port,
                'node': op_server_instance.node,
                'public_ip_id': body_data.get('public_ip'),
            }
            policy_obj = TenantHisecEngineNatServerPolicy.objects.create(**create_nat_server_policy_params)
            policy_obj.save()
            # 策略保存前同步公网信息到主机信息内
            if op_server_instance.extr_public_ipv4 != temp_public_ip:
                op_server_instance.extr_public_ipv4 = temp_public_ip
                op_server_instance.save()
            return SuccessResponse(data=policy_obj.to_dict, msg="创建成功")

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        code, msg, data = recycle_hisec_nat_server_policy(**{'id': instance.id, "node": instance.node})
        logger.info(f'【recycle_hisec_nat_server_policy】【node: {instance.node}, code:{str(code)},data: {str(data)}, msg: {msg}】')
        return DetailResponse(data=[], msg="删除成功")

    @staticmethod
    def format_children_list(data):
        new_data = []
        pub_ips = set()
        for item in data:
            pub_ips.add(item["public_ip"])

        for pub_ip in pub_ips:
            temp_pub_policies = []
            for item in data:
                if item["public_ip"] == pub_ip:
                    temp_pub_policies.append(item)
            obj = TenantHisecEnginePublicIP.objects.filter(
                public_ip=pub_ip,
                used=True,
                is_deleted=False,
            ).first()
            if not obj:
                continue
            temp_pub = obj.to_dict
            temp_pub['nat_server_policies'] = temp_pub_policies
            new_data.append(temp_pub)
        return new_data

    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def public_ip_children_list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True, request=request)
            # 更新聚合公网IP策略数据
            data = self.format_children_list(serializer.data)
            return self.get_paginated_response(data)
        serializer = self.get_serializer(queryset, many=True, request=request)
        # 更新聚合公网IP策略数据
        data = self.format_children_list(serializer.data)
        return SuccessResponse(data=data, msg="获取成功")








