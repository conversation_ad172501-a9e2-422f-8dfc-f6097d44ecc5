from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet
from django.core.cache import cache

from dvadmin.utils.json_response import ErrorResponse

from tenant.utils.tenant_base.redis_keys.user_key import USER_PROFILE_INFO_KEY
from tenant.models import TenantOpenstackProject
from tenant.serializers.tenant_openstack_project import (
    TenantOpenstackProjectSerializer,
    TenantOpenstackProjectCreateSerializer,
    TenantOpenstackProjectImportSerializer
)


class TenantOpenstackProjectViewSet(ChaosTenantModelViewSet):
    """
    客户信息接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantOpenstackProject.objects.order_by('-create_datetime')
    serializer_class = TenantOpenstackProjectSerializer
    create_serializer_class = TenantOpenstackProjectCreateSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'project_id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = TenantOpenstackProjectImportSerializer
    import_field_dict = {
        "name": "项目名称",
    }

    def update(self, request, *args, **kwargs):
        # 获取之前的 数据
        instance = self.get_object()
        response = super().update(request, *args, **kwargs)
        res_data = response.data
        # 清理租户账号用户登录缓存
        if 'data' in res_data and 'account' in res_data['data']:
            account_id = res_data['data']['account']
            # 清除新用户的缓存
            cache.delete(USER_PROFILE_INFO_KEY.format(account_id=account_id))
            # 清除旧用户的缓存
            cache.delete(USER_PROFILE_INFO_KEY.format(account_id=instance.account_id))
        return response
