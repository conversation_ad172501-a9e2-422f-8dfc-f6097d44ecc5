from datetime import timedelta
from django.utils import timezone
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

from application.logger import logger
from dvadmin.utils.json_response import (
    SuccessResponse,
    ErrorResponse,
)

from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet
from tenant.utils.sdk.openstack_client import OpenstackAdminClient
from tenant.models import (
    TenantAccount,
    TenantOpenstackServer,
    TenantOpenstackProject,
)
from tenant.serializers.tenant_account import (
    TenantAccountSerializer,
    TenantAccountCreateSerializer,
)


class TenantAccountViewSet(ChaosTenantModelViewSet):
    """
    客户信息接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantAccount.objects.order_by('-create_datetime')
    serializer_class = TenantAccountSerializer
    create_serializer_class = TenantAccountCreateSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'company',
    }
    # 导入
    # import_serializer_class = TenantProjectImportSerializer
    # import_field_dict = {
    #     "name": "项目名称",
    # }
    # permission_classes = []

    @staticmethod
    def get_interval_tenant_portal_dashboard(account_id=None, user_type=None):
        """
        获取��户概况数据
        :param request: 请求
        :param account_id: 账号ID
        :param user_type: 账号类型
        :return:
        """
        # 近N天过期
        expire_time_interval = 15
        # 近N天已过期
        had_expired_time_interval = 15
        # 近N天创建
        create_time_interval = 7

        server_dashboard_data = {
            'total': 0,
            'running': 0,
            'coming_expire': 0,
            'had_expired': 0,
            'coming_created': 0,
            'gpu_counts': 0,
        }
        project_quota_set_and_usage = {
            'quota_set': {
                'cores': 0,
                'instances': 0,
                'ram': 0,
            },
            'usage': {
                'cores': 0,
                'instances': 0,
                'ram': 0,
            }
        }
        if account_id is None or user_type is None:
            return {
            'server_statical': server_dashboard_data,
            'project_quota_set_and_usage': project_quota_set_and_usage,  # 租户裸金属配额和使用情况
        }

        try:
            if user_type == 1:
                projects = TenantOpenstackProject.objects.filter(
                    account_id=account_id,
                    is_deleted=False,
                ).all()
                project_ids = [i.project_id for i in projects]
            else:
                project_ids = 'all'

            # 获取裸金属统计
            if project_ids == 'all':
                servers = TenantOpenstackServer.objects.filter(
                    is_deleted=False,
                ).all()
            else:
                servers = TenantOpenstackServer.objects.filter(
                    project_id__in=project_ids,
                    is_deleted=False,
                ).all()
            # 整理成dashboard 数据

            now_time = timezone.now()
            utc_now_time = timezone.datetime.now()
            coming_expire_end_time = now_time + timedelta(days=expire_time_interval)
            had_expire_start_time = now_time - timedelta(days=had_expired_time_interval)
            create_time = utc_now_time - timedelta(days=create_time_interval)
            for server in servers:
                server_dashboard_data['total'] += 1
                if server.status == 'ACTIVE':
                    server_dashboard_data['running'] += 1
                if server.expire_at and now_time < server.expire_at <= coming_expire_end_time:
                    server_dashboard_data['coming_expire'] += 1
                if server.expire_at and had_expire_start_time <= server.expire_at <= now_time:
                    server_dashboard_data['had_expired'] += 1
                elif server.create_datetime and create_time < server.create_datetime:
                    server_dashboard_data['coming_created'] += 1
                # 获取总的 GPU 数量
                try:
                    flavor_info = server.flavor_info
                    if flavor_info:
                        server_dashboard_data['gpu_counts'] += int(
                            flavor_info.get('extra_specs', {}).get('gpu_count', 0))
                except Exception as e:
                    logger.error(f'get_tenant_portal_dashboard_gpu_count_error:<ErrorDetail: {str(e)}>')
                    server_dashboard_data['gpu_counts'] += 0
            if project_ids != 'all':
                # 创建 Openstack 管理 SDK 实例
                admin_client = OpenstackAdminClient(node='金华')
                # 调用 SDK 接口获取��户概况数据
                project_quota_set_and_usage = admin_client.get_project_quota_set_and_usage(project_id=project_ids[0])
        except Exception as e:
            logger.error(f'get_tenant_portal_dashboard:<ErrorDetail: {str(e)}>')

        tenant_dashboard = {
            'server_statical': server_dashboard_data,
            'project_quota_set_and_usage': project_quota_set_and_usage,  # 租户裸金属配额和使用情况
        }
        return tenant_dashboard

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def get_tenant_portal_dashboard(self, request):
        """
        获取��户概况数据
        :param request: 请求
        :return:
        """
        # 获取租户 ID
        account_id = request.user.account_id
        user_type = request.user.user_type
        tenant_dashboard = self.get_interval_tenant_portal_dashboard(account_id=account_id, user_type=user_type)
        return SuccessResponse(tenant_dashboard)
