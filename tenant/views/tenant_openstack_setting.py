"""
<AUTHOR>
@Date    ：2024/10/30
"""
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

from dvadmin.utils.json_response import SuccessResponse, ErrorResponse, DetailResponse
from dvadmin.utils.toolkits import mask_constant_length_string

from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet
from tenant.models import TenantOpenstackSetting
from tenant.serializers.tenant_openstack_setting import (
    TenantOpenstackSettingSerializer,
    TenantOpenstackSettingImportSerializer
)


class TenantOpenstackSettingViewSet(ChaosTenantModelViewSet):
    """
    客户信息接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantOpenstackSetting.objects.order_by('-create_datetime')
    serializer_class = TenantOpenstackSettingSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'account_nick_name',
    }
    # 导入
    import_serializer_class = TenantOpenstackSettingImportSerializer
    import_field_dict = {
        "node": "openstack节点(必填项)",
        "name": "配置键名称",
        "key": "配制键(必填项)",
        "value": "配置值(必填项)",
    }
    permission_classes = []

    def list(self, request, *args, **kwargs):
        """重构 list 查询数据"""
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True, request=request)
            data = []
            for item in serializer.data:
                item['value'] = mask_constant_length_string(item['value'])
                data.append(item)
            return self.get_paginated_response(data)
        serializer = self.get_serializer(queryset, many=True, request=request)
        data = []
        for item in serializer.data:
            item['value'] = mask_constant_length_string(item['value'])
            data.append(item)
        return SuccessResponse(data=data, msg="获取成功")

    node = openapi.Parameter('node', openapi.IN_QUERY, description='openstack节点', type=openapi.TYPE_STRING)
    key = openapi.Parameter('key', openapi.IN_QUERY, description='openstack节点配置键', type=openapi.TYPE_STRING)

    @swagger_auto_schema(method='GET', manual_parameters=[key, node,], operation_summary='获取指定节点Openstack配置信息')
    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def get_op_settings(self, request, *args, **kwargs):
        """暂仅后端使用"""
        request_data = request.query_params
        node = request_data.get('node', '')
        key = request_data.get('key', None)
        if not node:
            return ErrorResponse(msg="未获取到node参数")
        data = TenantOpenstackSetting.get_op_settings(node, key)
        return DetailResponse(data=data)

    is_dict = openapi.Parameter('is_dict', openapi.IN_QUERY, description='是否返回字典', type=openapi.TYPE_BOOLEAN)
    @swagger_auto_schema(
        method='GET', manual_parameters=[is_dict,], operation_summary='获取Openstack所有节点'
        )
    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def get_op_all_nodes(self, request, *args, **kwargs):
        """获取已配置的所有已启用的Openstack节点"""
        is_dict = request.query_params.get('is_dict', True)
        data = TenantOpenstackSetting.get_op_distinct_nodes()
        res_data = []
        if is_dict:
            for item in data:
                res_data.append({"label": item, "value": item})
        else:
            res_data = data
        return DetailResponse(data=res_data)





