"""
<AUTHOR>
@Date    ：2024/11/4
"""
from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet
from tenant.models import TenantOpenstackFlavor
from tenant.serializers.tenant_openstack_flavor import (
    TenantOpenstackFlavorSerializer,
    TenantOpenstackFlavorImportSerializer
)
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from dvadmin.utils.json_response import SuccessResponse, ErrorResponse, DetailResponse
from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet
from tenant.models import TenantOpenstackSetting
from tenant.serializers.tenant_openstack_setting import (
    TenantOpenstackSettingSerializer,
    TenantOpenstackSettingImportSerializer
)


class TenantOpenstackFlavorViewSet(ChaosTenantModelViewSet):
    """
    Openstack Flavor接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantOpenstackFlavor.objects.order_by('-create_datetime')
    serializer_class = TenantOpenstackFlavorSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'account_nick_name',
    }
    # 导入
    import_serializer_class = TenantOpenstackFlavorImportSerializer
    import_field_dict = {
        "name": "vlan名称(非必填项)",
        "description": "描述(非必填项)",
        "vlan_id": "VlanId(必填项)",
        "phy_vlan_type": "物理vlan类型(默认为规定的vlan)(必填项)",
        "subnet": "子网网段(必填项)",
        "available_ip_pool": "可用IP池(必填项)",
        "gateway_ip": "网关IP(必填项)",
        "node": "Openstack节点(默认暂定为金华)(非必填项)"
    }
    permission_classes = []

    def list(self, request, *args, **kwargs):
        query_params = request.query_params.copy()
        queryset = self.filter_queryset(self.get_queryset())
        node = query_params.get('node', None)
        if node:
            queryset = queryset.filter(node=node)
        # 构建公共资源共享查询集的基础条件
        public_filters = {
            'is_public': True,
            'is_deleted': False,
        }
        public_columns = ['limit', 'page']
        equ_columns = ['project_id', 'is_to_portal', 'flavor_type',]
        # 动态添加其他查询条件
        for key, value in query_params.items():
            if key in public_columns:
                continue
            elif key in equ_columns:
                public_filters[key] = value
            else:
                public_filters[f'{key}__contains'] = value
        # 获取公共资源共享查询集
        public_queryset = queryset.filter(**public_filters)
        # 使用|操作符合并两个查询集，确保没有重复项
        combined_queryset = queryset | public_queryset
        # 如果需要去重，可以使用distinct()方法
        combined_queryset = combined_queryset.distinct()
        page = self.paginate_queryset(combined_queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True, request=request)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True, request=request)
        return SuccessResponse(data=serializer.data, msg="获取成功")

    def search_flavor(self, request):
        """
        查询 Openstack Flavor列表
        """
        request_data = request.query_params
        filter_conditions = {}
        for key, value in request_data.items():
            if value is not None:
                filter_conditions[f'{key}__icontains'] = value
        queryset = self.get_queryset().filter(**filter_conditions)
        serializer = TenantOpenstackFlavorSerializer(queryset, many=True)
        return SuccessResponse(data=serializer.data)


# class TenantOpenstackFlavorSearchViewSet(ChaosTenantModelViewSet):
#     """
#     客户信息接口
#     list:查询
#     create:新增
#     update:修改
#     retrieve:单例
#     destroy:删除
#     """
#     queryset = TenantOpenstackFlavor.objects.order_by('-create_datetime')
#     serializer_class = TenantOpenstackFlavorSerializer
#
#     permission_classes = []
#
#     msg = openapi.Parameter('node', openapi.IN_QUERY, description='openstack节点', type=openapi.TYPE_STRING)
#     key = openapi.Parameter('key', openapi.IN_QUERY, description='openstack节点配置键', type=openapi.TYPE_STRING)
#
#     @swagger_auto_schema(method='GET', manual_parameters=[key, node,], operation_summary='获取指定节点Openstack配置信息')
#     @action(methods=['get'], detail=False)
#     def get_op_settings(self, request, *args, **kwargs):
#         """暂仅后端使用"""
#         request_data = request.query_params
#         node = request_data.get('node', '')
#         key = request_data.get('key', None)
#         if not node:
#             return ErrorResponse(msg="未获取到node参数")
#         data = TenantOpenstackSetting.get_op_settings(node, key)
#         return DetailResponse(data=data)
