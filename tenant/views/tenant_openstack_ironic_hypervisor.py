"""
<AUTHOR>
@Date    ：2024/11/16
"""
from django.utils import timezone
from django.core.cache import cache
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from dvadmin.utils.json_response import ErrorResponse, SuccessResponse
from tenant.utils.tenant_base.chaos_tenant_viewset import ChaosTenantModelViewSet

from tenant.models import TenantOpenstackIronicHypervisor
from tenant.serializers.tenant_openstack_ironic_hypervisor import (
    TenantOpenstackIronicHypervisorSerializer,
    # TenantOpenstackIronicHypervisorCreateSerializer,
    TenantOpenstackIronicHypervisorImportSerializer
)
from tenant.utils.sdk.op_baremetal_node.op_baremetal_node_admin import  OpenstackBaremetalNodeManager
from tenant.dictionary_enum_keys import HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY


class TenantOpenstackIronicHypervisorViewSet(ChaosTenantModelViewSet):
    """
    客户信息接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TenantOpenstackIronicHypervisor.objects.order_by('-create_datetime')
    serializer_class = TenantOpenstackIronicHypervisorSerializer
    # create_serializer_class = TenantOpenstackIronicHypervisorCreateSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'ironic_hyper_id',
        'name_field': 'name',
    }
    signal_filed = 'ironic_hyper_id'
    # 导入
    import_serializer_class = TenantOpenstackIronicHypervisorImportSerializer
    import_field_dict = {
        "name": "名称"
    }
    # permission_classes = []

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def set_node_maintenance(self, request, id):
        """
        设置节点为维护模式
        """
        ironic_hyper_id = id
        request_data = request.data
        is_maintenance = request_data.get("is_maintenance", False)
        obj = TenantOpenstackIronicHypervisor.objects.filter(
            ironic_hyper_id=ironic_hyper_id,
            is_deleted=False,
            ).first()
        if not obj:
            return ErrorResponse(msg="裸机节点不存在或已被删除")
        with transaction.atomic():
            admin_client = OpenstackBaremetalNodeManager(node=obj.node)
            code = admin_client.set_node_maintenance(
                node_uuid=obj.ironic_hyper_id,
                reason="维护模式",
                is_maintenance=is_maintenance,
                )
            # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
            cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=obj.node), True, timeout=20*60)
            return SuccessResponse(data=code, msg='操作成功')

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def manage_node_state_transition(self, request, id):
        """
        设置节点为维护模式
        """
        ironic_hyper_id = id
        request_data = request.data
        target_state = request_data.get("target_state",)
        if not target_state:
            return ErrorResponse(msg="请选择目标状态")
        obj = TenantOpenstackIronicHypervisor.objects.filter(
            ironic_hyper_id=ironic_hyper_id,
            is_deleted=False,
            ).first()
        if not obj:
            return ErrorResponse(msg="裸机节点不存在或已被删除")
        with transaction.atomic():
            admin_client = OpenstackBaremetalNodeManager(node=obj.node)
            code = admin_client.manage_node_state_transition(
                node_uuid=obj.ironic_hyper_id,
                target_state=target_state,
                )
            # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
            cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=obj.node), True, timeout=20 * 60)
            return SuccessResponse(data=code, msg='操作成功')

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def manage_node_state_power(self, request, id):
        """
        设置节点为维护模式
        """
        ironic_hyper_id = id
        request_data = request.data
        target_state = request_data.get("target_state", )
        if not target_state:
            return ErrorResponse(msg="请选择目标电源状态")
        obj = TenantOpenstackIronicHypervisor.objects.filter(
            ironic_hyper_id=ironic_hyper_id,
            is_deleted=False,
            ).first()
        if not obj:
            return ErrorResponse(msg="裸机节点不存在或已被删除")
        with transaction.atomic():
            admin_client = OpenstackBaremetalNodeManager(node=obj.node)
            code = admin_client.manage_node_state_power(
                node_uuid=obj.ironic_hyper_id,
                target_state=target_state,
                )
            # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
            cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=obj.node), True, timeout=20 * 60)
            return SuccessResponse(data=code, msg='操作成功')

    @action(methods=['delete'], detail=True, permission_classes=[IsAuthenticated])
    def delete_node(self, request, id):
        """
        设置节点为维护模式
        """
        ironic_hyper_id = id
        obj = TenantOpenstackIronicHypervisor.objects.filter(
            ironic_hyper_id=ironic_hyper_id,
            is_deleted=False,
            ).first()
        if not obj:
            return ErrorResponse(msg="裸机节点不存在或已被删除")
        with transaction.atomic():
            admin_client = OpenstackBaremetalNodeManager(node=obj.node)
            data = admin_client.delete_baremetal_node(
                node_uuid=obj.ironic_hyper_id,
                )
            if isinstance(data, dict):
                msg = '删除节点成功'
            else:
                msg = '删除节点失败'
            obj.is_deleted = True
            obj.save()
            # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
            cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=obj.node), True, timeout=20 * 60)
            return SuccessResponse(data={}, msg=msg)
