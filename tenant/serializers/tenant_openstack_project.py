from rest_framework import serializers

from django.db import transaction
from django.utils import timezone

from application.logger import logger

from dvadmin.utils.validator import CustomUniqueValidator
from dvadmin.utils.exception import ErrorResponse

from tenant.utils.sdk.openstack_client import OpenstackAdminClient

from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import TenantOpenstackProject, TenantAccount
from tenant.serializers.tenant_account import TenantAccountCreateSerializer


def project_fixed_quotas(project_data):
    """
    修改项目默认配额
    """
    # 计算优化后的配额配置
    fixed_compute_quotas_config = {
        'instances': 100,  # 实例数量 default 10
        'cores': 12800,  # VCPU数量 default 128
        'ram': ********,  # 内存(MB) default 512000
        'metadata_items': 12800,  # 元数据条目 default 128
        'key_pairs': 10000,  # 秘钥对数量 default 100
    }
    # 磁盘优化后的配额配置
    fixed_volume_quotas_config = {
        'volumes': 1000,  # 卷 defect 10
        'snapshots': 1000,  # 卷快照 default 10
        'gigabytes': 100000,  # 卷及快照总大小 (GiB) default 1000

    }
    admin_client = OpenstackAdminClient(node=project_data['node'])
    admin_client.update_project_compute_quotas_set(
        project_id=project_data['project_id'],
        compute_quotas=fixed_compute_quotas_config
    )
    admin_client.update_project_volume_quotas_set(
        project_id=project_data['project_id'],
        volume_quotas=fixed_volume_quotas_config
    )


def project_assign_to_admin(project_data):
    """
    将项目默认添加admin管理状态
    """
    admin_client = OpenstackAdminClient(node=project_data['node'])
    admin_client.assign_project_to_admin(project_id=project_data['project_id'])


class TenantOpenstackProjectImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantOpenstackProject
        exclude = ()


class TenantOpenstackProjectSerializer(ChaosTenantModelSerializer):
    """
    日志-序列化器
    """

    class Meta:
        model = TenantOpenstackProject
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


class TenantOpenstackProjectCreateSerializer(ChaosTenantModelSerializer):
    """
    租户openstack中项目新增-序列化器
    """

    name = serializers.CharField(
        max_length=63,
        required=True,
        validators=[
            CustomUniqueValidator(queryset=TenantOpenstackProject.objects.all(), message="账号必须唯一")
        ],
    )
    account = serializers.CharField(max_length=63, required=True)

    def save(self, **kwargs):
        # 同步配置 Openstack 项目信息
        if not self.validated_data['node']:
            return ErrorResponse(data={}, msg='The node does not exist.', code=400)
        admin_openstack_client = OpenstackAdminClient(debug=True, node=self.validated_data['node'])
        new_project_id = None
        tenant_obj = TenantAccount.objects.filter(
            id=self.validated_data.get('account'),
        ).first()

        if not tenant_obj:
            return ErrorResponse(data={}, msg='The account does not exist.', code=404)
        try:
            with transaction.atomic(using='tenant'):
                # 若为非tenant-admin 用户创建，则生成新tenant_id
                tenant_id = getattr(self.request.user, 'tenant_id')
                if tenant_id:
                    # TODO 暂时不允许非admin管理员用户创建
                    return ErrorResponse(
                        data={}, msg='Permission denied, Only allowed system admin to set this.', code=403)
                # 在 openstack 中创建对应的项目
                res = admin_openstack_client.create_project(
                    name=self.validated_data.get('name'),
                    description=self.validated_data.get('description'),
                )
                if res:
                    new_project_id = res.id
                    self.validated_data['account'] = tenant_obj
                    self.validated_data['tenant_id'] = tenant_obj.tenant_id
                    self.validated_data['project_id'] = new_project_id
                    self.validated_data['enabled'] = res.is_enabled
                    self.validated_data['domain_id'] = res.domain_id
                    self.validated_data['parent_id'] = res.parent_id
                project = super().save(**kwargs)
                project_fixed_quotas({
                    'node': project.node,
                    'project_id': new_project_id
                })
                project_assign_to_admin({
                    'node': project.node,
                    'project_id': new_project_id,
                })
            return project
        except Exception as e:
            if new_project_id:
                res = admin_openstack_client.delete_project(project_id=new_project_id)
                print('创建事务错误，自动删除已创建的项目!!!', res)
            raise ValueError(f"<CreateOpenstackProjectTransactionError>Transaction failed: {e}")

    class Meta:
        model = TenantOpenstackProject
        fields = "__all__"
        read_only_fields = ["id"]
        extra_kwargs = {}


def sync_project_fixed_config():
    """
    同步 Openstack 项目默认配额
    """
    pass


def sync_tenant_openstack_project(nodes: any=None):
    if not nodes:
        nodes = []
    if isinstance(nodes, str):
        nodes = [nodes]
    if not nodes:
        logger.warning("自动同步Openstack项目信息失败，未指定同步的Openstack节点！")
        return
    for node in nodes:
        admin_client = OpenstackAdminClient(node=node)
        projects = admin_client.get_all_projects()
        # 逻辑删除chaos系统内的资源信息
        openstack_all_resource_ids = set()
        tenant_all_resource_ids = set()
        for project in projects:
            openstack_all_resource_ids.add(project.id)
            project_obj = TenantOpenstackProject.objects.filter(
                project_id=project.id,
                node=node,
            ).first()

            account_obj = TenantAccount.objects.filter(
                account_name=project.name,
                is_deleted=False,
            ).first()
            if not account_obj:
                create_account_params = {
                    'account_name': project.name,
                    'email': '<EMAIL>',
                    'mobile': '***********',
                    'company': project.description,
                    'account_nick_name': project.name,
                }
                serializer = TenantAccountCreateSerializer(data=create_account_params)
                if serializer.is_valid():
                    account_obj = serializer.save()

            updated_data = {
                "name": project.name,
                "project_id": project.id,
                "description": project.description,
                "enabled": project.is_enabled,
                "domain_id": project.domain_id,
                "parent_id": project.parent_id,
                "sync_time": timezone.now(),
                "node": admin_client.node,
                "account_id": account_obj.id,
            }
            if project_obj:
                for key, value in updated_data.items():
                    setattr(project_obj, key, value)
            else:
                project_obj = TenantOpenstackProject.objects.create(**updated_data)
            project_obj.save()
        # 逻辑删除chaos系统内的多余资源信息
        tenant_all_objs = TenantOpenstackProject.objects.filter(
            node=admin_client.node,
            is_deleted=False
        ).all()
        [tenant_all_resource_ids.add(tenant_obj.project_id) for tenant_obj in tenant_all_objs]
        is_needed_to_logical_delete = tenant_all_resource_ids - openstack_all_resource_ids
        if is_needed_to_logical_delete:
            TenantOpenstackProject.objects.filter(
                project_id__in=list(is_needed_to_logical_delete),
                node=admin_client.node,
                is_deleted=False,
            ).update(is_deleted=True)
