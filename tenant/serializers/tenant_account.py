from rest_framework import serializers

from django.core.validators import EmailValidator
from django.db import transaction

from dvadmin.system.models import Users, Dept
from dvadmin.utils.validator import CustomUniqueValidator

from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import TenantAccount


class TenantAccountImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantAccount
        exclude = ()


class TenantAccountCreateSerializer(ChaosTenantModelSerializer):
    """
    租户账号新增-序列化器
    """

    account_name = serializers.CharField(
        max_length=50,
        required=True,
        validators=[
            CustomUniqueValidator(queryset=TenantAccount.objects.all(), message="账号必须唯一")
        ],
    )
    company = serializers.CharField(
        max_length=50,
        required=True,
    )
    password = serializers.SerializerMethodField()
    email = serializers.EmailField(
        max_length=255,
        required=True,
        validators=[
            EmailValidator,
        ]
    )
    account_nick_name = serializers.CharField(
        max_length=50,
        required=False,
    )

    def get_password(self, obj):
        # 无密码时，默认为`账号名`+ `@123`
        return str(obj.account_name) + '@123456'

    def save(self, **kwargs):
        # data = super().save(**kwargs)
        # 同时配置用户登录密码
        try:
            with transaction.atomic(using='tenant'):
                # 若为非tenant-admin 用户创建，则生成新tenant_id
                try:
                    tenant_id = getattr(self.request.user, 'tenant_id')
                except Exception:
                    tenant_id = None
                # 若无则生成 tenant_id
                if not tenant_id:
                    tenant_id = Users.generate_tenant_id()
                    kwargs['is_master'] = True  # 主账号默认为tenant-admin
                else:
                    # 为tenant-admin时，仅允许创建属于本tenant账号下的子账号
                    kwargs['is_master'] = False
                kwargs['tenant_id'] = tenant_id
                account = super().save(**kwargs)
                with transaction.atomic(using='default'):
                    dept_info = Dept.objects.filter(
                        name=account.account_name,
                        # key=f'customer-{"".join(lazy_pinyin(account.company))}',
                        key=f'customer-{account.account_name}',
                    ).first()
                    if account.is_master:
                        if dept_info:
                            raise Exception('Dept Had Exists: %s' % dept_info)
                        dept_info = Dept.objects.create(
                            name=account.company,
                            # key=f'customer-{"".join(lazy_pinyin(account.company))}',
                            key=f'customer-{account.account_name}',
                        )
                        dept_info.save()
                    else:
                        if not dept_info:
                            raise Exception('Dept Had Not Exists: %s' % dept_info)
                    user_params = {
                        'username': account.account_name,
                        'email': account.email,
                        'name': account.account_nick_name,
                        'user_type': 1,
                        'tenant_id': account.tenant_id,
                        'is_tenant_master': account.is_master,
                        'account_id': account.id,
                        'creator_id': account.creator_id,
                        'modifier': account.modifier,
                        'dept_belong_id': dept_info.id,
                        'dept_id': dept_info.id,
                    }
                    system_user = Users.objects.create(
                        **user_params)
                    system_user.save()
                    # TODO 密码问题需确认
                    system_user.set_password(self.get_password(account))
            return account
        except Exception as e:
            raise ValueError(f"<CreateSystemUsersTransactionError>Transaction failed: {e}")

    class Meta:
        model = TenantAccount
        fields = "__all__"
        read_only_fields = ["id"]
        extra_kwargs = {}


class TenantAccountSerializer(ChaosTenantModelSerializer):
    """
    日志-序列化器
    """

    class Meta:
        model = TenantAccount
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]
