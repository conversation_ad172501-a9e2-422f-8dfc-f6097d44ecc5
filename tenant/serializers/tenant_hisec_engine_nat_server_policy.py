"""
<AUTHOR>
@Date    ：2024/03/13
"""
import time
from random import randint

from dvadmin.utils.json_response import ErrorResponse, SuccessResponse

from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import TenantHisecEngineNatServerPolicy
from application.logger import logger


class TenantHisecEngineNatServerPolicyImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantHisecEngineNatServerPolicy
        exclude = ()


class TenantHisecEngineNatServerPolicySerializer(ChaosTenantModelSerializer):
    """
    日志-序列化器
    """

    class Meta:
        model = TenantHisecEngineNatServerPolicy
        fields = "__all__"
        read_only_fields = ["is_deleted", "seq"]

