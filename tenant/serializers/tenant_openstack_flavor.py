"""
<AUTHOR>
@Date    ：2024/10/30
"""
# import time

from django.utils import timezone

from application.logger import logger
from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import (
    TenantOpenstackFlavor,
    TenantOpenstackMetadata
)
from tenant.utils.sdk.openstack_client import OpenstackAdminClient


class TenantOpenstackFlavorImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantOpenstackFlavor
        exclude = ()


class TenantOpenstackFlavorSerializer(ChaosTenantModelSerializer):
    """
    日志-序列化器
    """

    class Meta:
        model = TenantOpenstackFlavor
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


def sync_tenant_openstack_flavor(nodes: any=None):
    """
    同步租户Flavor
    """
    # 固定参数
    # description = 'SyncWorker自动同步创建'
    # 获取租户Flavor数据
    if nodes is None:
        nodes = []
    if isinstance(nodes, str):
        nodes = [nodes]
    if not nodes:
        logger.warning("自动同步Openstack <Flavor>信息失败，未指定同步的Openstack节点！")
        return
    for node in nodes:
        admin_client = OpenstackAdminClient(node=node)
        flavor_list = admin_client.get_all_flavors()

        # 逻辑删除chaos系统内的资源信息
        openstack_all_resource_ids = set()
        tenant_all_resource_ids = set()
        for flavor in flavor_list:
            # 添加到 openstack_all_flavor_ids
            openstack_all_resource_ids.add(flavor.id)

            # 查询租户Flavor是否存在
            tenant_flavor_obj = TenantOpenstackFlavor.objects.filter(
                flavor_id=flavor.id,
                node=admin_client.node,
            ).first()
            temp_specs = flavor.extra_specs
            project_id = None

            # 格式化规格数据
            is_public = flavor.is_public
            flavor_type = temp_specs.get('flavor_type', '未配置')
            is_to_portal = temp_specs.get('is_to_portal', False)

            # 规格元数据 同步到 独立的元数据表
            for key, value in temp_specs.items():
                # 获取 Flavor 元数据
                metadata_obj = TenantOpenstackMetadata.objects.filter(
                    resource_id=flavor.id,
                    key=key.lower(),
                    node=admin_client.node,
                    is_deleted=False,
                ).first()
                updated_metadata_data = {
                    'resource_id': flavor.id,
                    'type': 0,
                    'key': key.lower(),
                    'value': value,
                    'is_public': is_public,
                    'node': admin_client.node,
                }
                if metadata_obj:
                    for temp_key, temp_value in updated_metadata_data.items():
                        setattr(metadata_obj, temp_key, temp_value)
                else:
                    metadata_obj = TenantOpenstackMetadata.objects.create(**updated_metadata_data)
                metadata_obj.save()

            # 获取租户Flavor所属的租户
            # 租户Flavor可能没有租户
            try:
                project_id = flavor.location.project.id
            except:
                pass
            updated_data = {
                'name': flavor.name,
                'ram': flavor.ram,
                'vcpus': flavor.vcpus,
                'disk': flavor.disk,
                'ephemeral': flavor.ephemeral,
                'swap': flavor.swap,
                'rxtx_factor': flavor.rxtx_factor,
                'is_public': flavor.is_public,
                'extra_specs': flavor.extra_specs,
                'project_id': project_id,
                'creator_id': 1,
                'flavor_id': flavor.id,
                'sync_time': timezone.now(),
                'flavor_type': flavor_type,
                'is_to_portal': is_to_portal,
                'node': admin_client.node,
                # 'tenant_id': '',
            }
            if tenant_flavor_obj:
                for key, value in updated_data.items():
                    setattr(tenant_flavor_obj, key, value)
            else:
                tenant_flavor_obj = TenantOpenstackFlavor.objects.create(**updated_data)
            tenant_flavor_obj.save()

        # 逻辑删除chaos系统内的多余资源信息
        tenant_all_objs = TenantOpenstackFlavor.objects.filter(
            is_deleted=False,
            node=admin_client.node,
        ).all()
        [tenant_all_resource_ids.add(tenant_obj.flavor_id) for tenant_obj in tenant_all_objs]
        is_needed_to_logical_delete = tenant_all_resource_ids - openstack_all_resource_ids
        if is_needed_to_logical_delete:
            TenantOpenstackFlavor.objects.filter(
                flavor_id__in=list(is_needed_to_logical_delete),
                node=admin_client.node,
                is_deleted=False,
            ).update(is_deleted=True)
