"""
<AUTHOR>
@Date    ：2024/11/16
"""
from django.utils import timezone

from application.logger import logger

from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import TenantOpenstackNetwork, TenantOpenstackMetadata
from tenant.utils.sdk.test_openstack_client import OpenstackAdminClient


class TenantOpenstackNetworkImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantOpenstackNetwork
        exclude = ()


class TenantOpenstackNetworkSerializer(ChaosTenantModelSerializer):
    """
    Openstack镜像-序列化器
    """

    class Meta:
        model = TenantOpenstackNetwork
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


def sync_tenant_openstack_network(nodes: any=None):
    if not nodes:
        nodes = []
    if isinstance(nodes, str):
        nodes = [nodes]
    if not nodes:
        logger.warning("自动同步Openstack网络<Network>信息失败，未指定同步的Openstack节点！")
        return
    for node in nodes:
        admin_client = OpenstackAdminClient(node=node)
        networks = admin_client.get_project_networks(is_all=True)
        # 逻辑删除chaos系统内的资源信息
        openstack_all_resource_ids = set()
        tenant_all_resource_ids = set()
        for network in networks:
            openstack_all_resource_ids.add(network.id)
            network_obj = TenantOpenstackNetwork.objects.filter(
                network_id=network.id,
                node=admin_client.node,
            ).first()

            updated_data = {
                "name": network.name,
                "network_id": network.id,
                "subnet_ids": network.subnet_ids,
                "mtu": network.mtu,
                "status": network.status,
                "availability_zones": network.availability_zones,
                "is_admin_state_up": network.is_admin_state_up,
                "is_router_external": network.is_router_external,
                "is_shared": network.is_shared,
                "is_default": network.is_default,
                "provider_network_type": network.provider_network_type,
                "provider_physical_network": network.provider_physical_network,
                "provider_segmentation_id": network.provider_segmentation_id,
                "created_at": network.created_at,
                "updated_at": network.updated_at,
                "sync_time": timezone.now(),
                "project_id": network.project_id,
                "node": admin_client.node,
                "is_deleted": False,
            }
            if network_obj:
                for key, value in updated_data.items():
                    setattr(network_obj, key, value)
            else:
                network_obj = TenantOpenstackNetwork.objects.create(**updated_data)
            network_obj.save()
        # 逻辑删除chaos系统内的多余资源信息
        tenant_all_objs = TenantOpenstackNetwork.objects.filter(
            node=admin_client.node,
            is_deleted=False
        ).all()
        [tenant_all_resource_ids.add(tenant_obj.network_id) for tenant_obj in tenant_all_objs]
        is_needed_to_logical_delete = tenant_all_resource_ids - openstack_all_resource_ids
        if is_needed_to_logical_delete:
            TenantOpenstackNetwork.objects.filter(
                network_id__in=list(is_needed_to_logical_delete),
                node=admin_client.node,
                is_deleted=False,
            ).update(is_deleted=True)
