"""
<AUTHOR>
@Date    ：2024/11/16
"""
from django.utils import timezone

from application.logger import logger

from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import TenantOpenstackSecurityGroup, TenantOpenstackMetadata
from tenant.utils.sdk.test_openstack_client import OpenstackAdminClient


class TenantOpenstackSecurityGroupImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantOpenstackSecurityGroup
        exclude = ()


class TenantOpenstackSecurityGroupSerializer(ChaosTenantModelSerializer):
    """
    Openstack镜像-序列化器
    """

    class Meta:
        model = TenantOpenstackSecurityGroup
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


def sync_tenant_openstack_security_group(nodes: any= None):
    if not nodes:
        nodes = []
    if isinstance(nodes, str):
        nodes = [nodes]
    if not nodes:
        logger.warning("自动同步Openstack安全组<SecurityGroup>信息失败，未指定同步的Openstack节点！")
        return
    for node in nodes:
        admin_client = OpenstackAdminClient(node=node)
        security_groups = admin_client.get_project_security_groups(is_all=True)
        # 逻辑删除chaos系统内的资源信息
        openstack_all_resource_ids = set()
        tenant_all_resource_ids = set()
        for security_group in security_groups:
            openstack_all_resource_ids.add(security_group.id)
            security_group_obj = TenantOpenstackSecurityGroup.objects.filter(
                sg_id=security_group.id,
                node=admin_client.node,
            ).first()

            updated_data = {
                "name": security_group.name,
                "sg_id": security_group.id,
                "description": security_group.description,
                "security_group_rules": security_group.security_group_rules,
                "project_id": security_group.project_id,
                "is_shared": security_group.shared,
                "created_at": security_group.created_at,
                "updated_at": security_group.updated_at,
                "sync_time": timezone.now(),
                "node": admin_client.node,
                "is_deleted": False,
            }
            if security_group_obj:
                for key, value in updated_data.items():
                    setattr(security_group_obj, key, value)
            else:
                security_group_obj = TenantOpenstackSecurityGroup.objects.create(**updated_data)
            security_group_obj.save()
        # 逻辑删除chaos系统内的多余Flavor
        tenant_all_objs = TenantOpenstackSecurityGroup.objects.filter(
            node=admin_client.node,
            is_deleted=False
        ).all()
        [tenant_all_resource_ids.add(tenant_obj.sg_id) for tenant_obj in tenant_all_objs]
        is_needed_to_logical_delete = tenant_all_resource_ids - openstack_all_resource_ids
        if is_needed_to_logical_delete:
            TenantOpenstackSecurityGroup.objects.filter(
                sg_id__in=list(is_needed_to_logical_delete),
                node=admin_client.node,
                is_deleted=False,
            ).update(is_deleted=True)
