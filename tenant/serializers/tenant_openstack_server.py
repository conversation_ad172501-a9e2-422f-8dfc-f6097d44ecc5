"""
<AUTHOR>
@Date    ：2024/11/5
"""
import json
import time

import ping3
# from random import randint
from datetime import timedelta, datetime

from django.utils import timezone
from django.db.models import Q
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.views import APIView
from django.core.cache import cache

from application.logger import logger
from dvadmin.celery_workers.record_change_worker import record_changes
from dvadmin.utils.toolkits import get_nested_value, str_utc_to_shanghai_date
from dvadmin.utils.json_response import ErrorResponse, SuccessResponse
from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import (
    TenantOpenstackServer,
    TenantOpenstackIronicHypervisor,
    TenantOPServerSoftware,
    )
from tenant.utils.sdk.openstack_client import OpenstackAdminClient
from tenant.utils.tenant_base.chaos_tenant_toolkits import get_tenant_id_from_project
from notice.utils.host_expire_notice import ManagerHostStatusNotice
from operatorcmdb.models import Host
from tenant.utils.shared_system_manager.account_project_customer import project_get_customer_info
from tenant.utils.tenant_resource_relation import get_operatorcmdb_host_ids
from scheduletask.views.work import WorkModelViewSet
from scheduletask.models import Work
from ticket.models import Ticket
from tenant.dictionary_enum_keys import HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY


class TenantOpenstackServerImportSerializer(ChaosTenantModelSerializer):
    def save(self, **kwargs):
        instance = super().save(**kwargs)
        return instance

    class Meta:
        model = TenantOpenstackServer
        exclude = ()


class TenantOpenstackServerSerializer(ChaosTenantModelSerializer):
    """
    日志-序列化器
    """

    class Meta:
        model = TenantOpenstackServer
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


class CreateBaremetalServerView(APIView):
    @swagger_auto_schema(
        operation_summary="创建裸金属服务器",
        operation_description="通过 API 创建裸金属服务器并保存相关信息到数据库",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['name', 'flavor_id', 'image_id', 'network_id', 'project_id', ],
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description="服务器名称"),
                'flavor_id': openapi.Schema(type=openapi.TYPE_STRING, description="规格 ID"),
                'image_id': openapi.Schema(type=openapi.TYPE_STRING, description="镜像 ID"),
                'network_id': openapi.Schema(type=openapi.TYPE_STRING, description="网络 ID"),
                'security_group_names': openapi.Schema(type=openapi.TYPE_STRING, description="服务器名称"),
                # TODO 待完善安全组名称传递
                # 'security_group_names': openapi.Schema(
                #     type=openapi.TYPE_ARRAY,
                #     items=openapi.Items(type=openapi.TYPE_STRING),
                #     description="安全组 ID 列表"),
                'expire_at': openapi.Schema(type=openapi.TYPE_STRING, description="过期时间"),
                'project_id': openapi.Schema(type=openapi.TYPE_STRING, description="项目 ID"),
                'belong_ticket_id': openapi.Schema(type=openapi.TYPE_STRING, description="所属工单ID"),
                'description': openapi.Schema(type=openapi.TYPE_STRING, description="描述"),
                }
            ),
        responses={
            200: TenantOpenstackServerSerializer,
            400: "Bad Request",
            500: "Internal Server Error"
            }
        )
    def post(self, request, *args, **kwargs):
        return self.create_baremetal_server(request.data)

    @staticmethod
    def create_baremetal_server(data: dict):
        name = data.get('name')
        description = data.get('description', )
        flavor_id = data.get('flavor_id', )
        image_id = data.get('image_id', )
        network_id = data.get('network_id', )
        security_group_names = data.get('security_group_names', )
        project_id = data.get('project_id')
        node = data.get('node', None)
        count = int(data.get('count', 1))
        expire_at = data.get('expire_at', )
        belong_ticket_id = data.get('belong_ticket_id', )
        # 最大为10
        if count > 10:
            return ErrorResponse(data={}, msg='count 超出 10!!!')
        instance_type = data['instance_type']
        create_baremetal_server_params = {}
        # 安全组默认配置
        if not security_group_names:
            security_group_names = ['default']
        elif isinstance(security_group_names, str):
            security_group_names = [security_group_names]
        else:
            return ErrorResponse(data={}, msg='security_group_names 设置有误!!!')

        temp_kwargs = {}

        # # 裸金属节点配置
        # ironic_hypervisor_id = request.data.get('ironic_hypervisor_id')
        # if ironic_hypervisor_id:
        #     properties = {'capabilities:node': ironic_hypervisor_id}
        #     temp_kwargs['properties'] = properties

        # 服务器元数据更新
        server_metadata = {
            'instance_type': instance_type,
            'image_id': image_id,
            'flavor_id': flavor_id,
            }
        temp_kwargs['metadata'] = server_metadata
        # 整合创建参数
        create_baremetal_server_params.update(
            {
                'project_id': project_id,
                'name': name,
                'description': description,
                'flavor_id': flavor_id,
                'image_id': image_id,
                'network_id': network_id,
                'security_group_names': security_group_names,
                }
            )
        create_baremetal_server_params.update(temp_kwargs)

        from dvadmin.celery_workers.sync_openstack_resource_worker import (
    single_sync_openstack_server_worker, wait_for_tenant_openstack_server_finished_worker,
    )
        admin_client = OpenstackAdminClient(node=node)  # 初始化 OpenStack 客户端
        # if count > 1:
        create_baremetal_server_params.update(
            {
                'instance_count': count
                }
            )
        # 批量创建服务器任务
        results = admin_client.create_multi_baremetal_server(**create_baremetal_server_params)
        if results:
            for i in results:
                if i.get('error_code'):
                    logger.info('【调度失败：%s】' % i.get('error_info'))
                elif i.get('id'):
                    # 同步主机数据任务
                    single_sync_openstack_server_worker.apply_async(
                        args=(i['id'], expire_at, belong_ticket_id, node)
                        )
                    # 更新状态
                    wait_for_tenant_openstack_server_finished_worker.apply_async(
                        args=(i['id'], node)
                        )
                else:
                    logger.ERROR('【创建任务失败<未知原因：%s>】' % str(i))
                    # if len(results) == 1:
                    #     return SuccessResponse(data=results[0], msg='创建任务已调度')
                    # 配置有新变动更新的任务，每分钟同步更新裸机节点信息变更
                    cache.set(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=admin_client.node), True, timeout=20 * 60)
            return SuccessResponse(data=results, msg='创建任务已调度')
        else:
            return ErrorResponse(data=results, msg='调度任务失败')
        # else:
        #     # 单台创建服务器任务
        #     res = admin_client.create_baremetal_server(**create_baremetal_server_params)
        #     if res.get('error_code'):
        #         logger.info('【调度失败：%s】' % res.get('error_info'))
        #         return ErrorResponse(data={}, msg='创建调度任务失败')
        #     elif res.get('id'):
        #         # 同步主机数据任务
        #         single_sync_openstack_server_worker.apply_async(
        #             args=(res['id'], expire_at, belong_ticket_id, node,)
        #         )
        #         # 更新状态
        #         wait_for_tenant_openstack_server_finished_worker.apply_async(
        #             args=(res['id'],node,)
        #         )
        #         return SuccessResponse(data=create_baremetal_server_params, msg='创建任务已调度')
        #     else:
        #         logger.ERROR('【创建任务失败<未知原因：%s>】' % str(res))
        #         return ErrorResponse(data={}, msg=res.error)


def get_ipaddr(data, key):
    """
    获取 json 值
    """
    try:
        if isinstance(data, dict):
            return data.get(key, '')
        elif isinstance(data, list):
            for item in data:
                result = get_ipaddr(item, key)
                if result:
                    return result
        else:
            return None
    except Exception as e:
        logger.error(f'get_ipaddr Error<Detail: {str(e)}>')
    return None


def async_multi_create_baremetal_servers(data):
    CreateBaremetalServerView.create_baremetal_server(data=data)


def get_image_id(server_data):
    """
    从 Openstack API 响应结果中，获取镜像ID
    """
    try:
        image_id = getattr(server_data, 'image_id', None)
        if image_id and image_id != 'none_image_id':
            return image_id
        # 若 image_id 为空，从 metadata 中 获取 镜像ID
        image_id = server_data.metadata.get('image_id', 'none_image_id')
        if image_id and image_id != 'none_image_id':
            return image_id
        # TODO 暂时不需要这些
        # # 若依然为空，则从 image.properties
        # image_href = server_data.image.properties['links'][0]['href']
        # image_id = image_href.split('/')[-1]
        # if image_id and image_id != 'none_image_id':
        #     return image_id
    except Exception as e:
        logger.error(f'get_image_id Error<Detail: {str(e)}>')
    return None


def get_flavor_id(server_data):
    """
    从 Openstack API 响应结果中，获取规格ID
    """
    try:
        flavor_id = getattr(server_data, 'flavor_id', None)
        if flavor_id and flavor_id != 'none_flavor_id':
            return flavor_id
        # 若 flavor_id 为空，从 metadata 中 获取 镜像ID
        flavor_id = server_data.metadata.get('flavor_id', 'none_flavor_id')
        if flavor_id and flavor_id != 'none_flavor_id':
            return flavor_id

    except Exception as e:
        logger.error(f'get_flavor_id Error<Detail: {str(e)}>')
    return None


def tenant_openstack_server_to_operator_cmdb_hosts(server_id):
    """
    同步 Openstack 内裸金属服务器信息到 CMDB 主机管理中(通过 BMC_IP 关联)
    """
    server_obj = TenantOpenstackServer.objects.filter(
        id=server_id,
        is_deleted=False,
        ).first()
    if not server_obj:
        return ErrorResponse(data={}, msg='未发现主机ID或已删除')
    if server_obj.instance_type != '裸金属':
        return SuccessResponse(data={}, msg='主机类型不匹配,无需更新')
    modifier = server_obj.modifier or 1
    if server_obj.is_deleted:
        host_need_updated_data = {
            'host_status': '空闲',
            'is_buffer': True,
            'ip_private': '',
            'ip_public': '',
            'area': server_obj.node,
            'modifier': modifier,
            'expire_time': None,
            'os': None,
            }
    else:
        try:
            # 获取主机信息
            start_time = server_obj.created_at
            start_time = datetime.strptime(
                start_time, '%Y-%m-%dT%H:%M:%SZ'
                ) + timedelta(days=1)
            host_need_updated_data = {
                'host_status': '启用',
                'is_buffer': False,
                'start_time': start_time.strftime('%Y-%m-%d 00:00:00'),
                'ip_private': server_obj.ipaddr,
                'ip_public': server_obj.extr_public_ipv4,
                'area': server_obj.node,
                'expire_time': server_obj.expire_at,
                'modifier': modifier,
                'os': server_obj.image_name or 'ubuntu<Unknown>',
                }
            # 获取客户信息
            customer_info = project_get_customer_info(project_id=server_obj.project_id, is_detail=True)
            if customer_info:
                customer_id = customer_info['customer_id']
                if customer_id:
                    host_need_updated_data.update(
                        {
                            'customer_id': customer_id,
                            }
                        )
        except Exception as e:
            logger.warning(f'<sync_tenant_openstack_server_to_operator_cmdb_hosts>ParamsError: {str(e)}')
            return ErrorResponse(data={}, msg=f'同步裸金属主机基础信息有误，详情: {str(e)}')
    baremetal_node_id = server_obj.baremetal_node_id
    # 1. 获取关联的物理机信息
    if baremetal_node_id:
        bmc_ip = TenantOpenstackIronicHypervisor.objects.filter(
            node=server_obj.node,
            ironic_hyper_id=baremetal_node_id,
            is_deleted=False,
            ).first()
        if bmc_ip:
            # 查询对应的运维主机信息
            need_update_host_filter = Host.objects.filter(
                area=server_obj.node,
                ip_bmc=bmc_ip.name,
                host_type='裸金属',
                is_deleted=False,
                )
            need_update_host_filter_count = need_update_host_filter.count()
            if need_update_host_filter_count == 0:
                return SuccessResponse(
                    data={
                        'is_warning': True,
                        }, msg='更新成功，未发现【运维管理中的主机】，请【人工确认】!'
                    )
            elif need_update_host_filter_count > 1:
                return SuccessResponse(
                    data={
                        'is_warning': True,
                        }, msg='更新成功，发现【运维管理中的主机】超过1个，请【人工确认】!'
                    )
            else:
                host_obj = need_update_host_filter.first()
                for key, value in host_need_updated_data.items():
                    setattr(host_obj, key, value)
                host_obj.save()
    else:
        return SuccessResponse(
            data={
                'is_warning': True,
                }, msg='更新成功，未发现【关联的物理机】，请【人工确认】!'
            )
    return SuccessResponse(data={'is_warning': False}, msg='自动更新【运维管理中的主机】成功')


def get_port_ipv4(admin_client, project_id, server_id):
    """
    获取 Openstack 端口的 IPv4 地址
    """
    try:
        ports = admin_client.get_ports(project_id=project_id, server_id=server_id)
        if len(ports):
            port = ports[0]
            return port['fixed_ips'][0]['ip_address']
    except Exception as e:
        logger.error(f'get_port_ipv4 Error<Detail: {str(e)}>')
    return ''


def single_sync_openstack_server(
        admin_client=None,
        server_data=None,
        instance_id=None,
        timeout_seconds_range=None,
        node=None,
        belong_ticket_id=None,
        expire_at=None,
        ):
    if not admin_client:
        admin_client = OpenstackAdminClient(node=node)  # 初始化 OpenStack 客户端
        node = admin_client.node
    # 暂取消等待时间
    # 生成 delay 请求时间
    # if timeout_seconds_range is None:
    #     timeout_seconds_range = (1, 10,)
    # random_timeout = randint(timeout_seconds_range[0], timeout_seconds_range[1])
    # if random_timeout:
    #     time.sleep(random_timeout)
    if server_data is None and instance_id is None:
        raise Exception('server_data is required')
    if instance_id:
        server_data = admin_client.get_server_detail(instance_id)
    server_obj = TenantOpenstackServer.objects.filter(
        instance_id=server_data.id,
        node=admin_client.node,
        ).first()

    # 获取镜像详情
    temp_server_image_id = get_image_id(server_data)
    if temp_server_image_id and temp_server_image_id != 'none_image_id':
        image_detail = admin_client.get_image_detail(temp_server_image_id)
    else:
        image_detail = {}

    image_name = None
    if image_detail:
        image_name = image_detail.get('properties', {}).get('desc')
    # 获取镜像详情
    temp_server_flavor_id = get_flavor_id(server_data)
    if temp_server_flavor_id and temp_server_flavor_id != 'none_flavor_id':
        flavor_detail = admin_client.get_flavor_detail(temp_server_flavor_id)
    else:
        flavor_detail = {}
    flavor_name = None
    if flavor_detail:
        flavor_name = flavor_detail.get('extra_specs', {}).get('desc')
    # 获取net_name
    temp_addresses = server_data.addresses
    if not temp_addresses:
        net_name = '失败'
    else:
        if not isinstance(temp_addresses, dict):
            temp_addresses = {'失败': '未知'}
        net_name = list(temp_addresses.keys())[0]
    if server_obj and server_obj.expire_at:
        expire_at = server_obj.expire_at
    if server_obj and server_obj.belong_ticket_id:
        belong_ticket_id = server_obj.belong_ticket_id

    # 获取主机地址
    ipaddr = get_nested_value(server_data.addresses, ['*', 'addr']) or ''
    if not ipaddr:
        # 获取指定项目及主机的端口信息
        ipaddr = get_port_ipv4(
            admin_client,
            project_id=server_data.project_id,
            server_id=server_data.id,
            )
    updated_data = {
        "instance_id": server_data.id,
        "name": server_data.name,
        "description": server_data.description,
        "instance_type": server_data.metadata.get('instance_type', '云主机'),  # TODO 待自定义源数据
        "compute_host": server_data.compute_host,
        "ipaddr": ipaddr,
        "image_id": temp_server_image_id if temp_server_image_id and temp_server_image_id != 'none_image_id' else None,
        "image_name": image_name or getattr(server_data.image, 'name'),
        "image_info": dict(image_detail),
        "key_name": server_data.key_name,
        "flavor_id": temp_server_flavor_id if temp_server_flavor_id and temp_server_flavor_id != 'none_flavor_id'
        else None,
        "flavor_name": flavor_name or getattr(server_data.flavor, 'name'),
        "flavor_info": dict(flavor_detail),
        "vcpus": getattr(server_data.flavor, 'vcpus'),
        "disk": getattr(server_data.flavor, 'disk'),
        "ram": getattr(server_data.flavor, 'ram'),
        "security_groups": server_data.security_groups,
        "status": server_data.status,
        "vm_state": server_data.vm_state,
        "metadata": server_data.metadata,
        "net_name": net_name,
        "hostname": server_data.hostname,
        "launched_at": str_utc_to_shanghai_date(server_data.launched_at),
        "private_v4": server_data.private_v4,
        "public_v4": server_data.public_v4,
        "host_id": server_data.host_id,
        "baremetal_node_id": server_data.hypervisor_hostname,
        "created_at": server_data.created_at,
        "updated_at": server_data.updated_at,
        "sync_time": timezone.now(),
        "project_id": server_data.project_id,
        "tenant_id": get_tenant_id_from_project(project_id=server_data.project_id),
        "node": admin_client.node,
        "expire_at": expire_at,
        "belong_ticket_id": belong_ticket_id,
        "is_deleted": False,
        }
    if server_obj:
        for key, value in updated_data.items():
            setattr(server_obj, key, value)
    else:
        server_obj = TenantOpenstackServer.objects.create(**updated_data)
    server_obj.save()
    if server_obj.status != server_data.status:
        from dvadmin.celery_workers.sync_openstack_resource_worker import (
            async_openstack_server_to_operator_cmdb_host_worker
            )
        async_openstack_server_to_operator_cmdb_host_worker.apply_async(args=(server_obj.id,))
    return server_obj


def wait_for_server_finished(instance_id, node=None):
    """
    等待服务器状态为 active
    """
    # from ticket.models import TenantOPServerStatusMachine
    admin_client = OpenstackAdminClient(node=node)
    interval_time = 30  # s/秒
    occurrences = 120
    status = 'ERROR'
    record_log_msg = ''
    create_msg = ''
    # 共等待啥时间 total = interval_time * occurrences = 30 * 60 秒
    for sleep_time in range(0, occurrences):
        # 每隔几秒查询一次状态
        time.sleep(interval_time)
        try:
            server = admin_client.conn.compute.get_server(instance_id)
            status = server.status
            create_msg = f"当前状态: {status}"
            logger.warning(create_msg)
            record_log_msg += f"<当前时间:{timezone.now()}><当前状态:{status}><调度物理节点ID:{server.hypervisor_hostname}>\n"
            if status == 'ACTIVE':
                create_msg = "裸金属节点已成功激活"
                logger.warning(create_msg)
                break
            elif status == 'ERROR':
                create_msg = "裸金属节点创建失败"
                logger.warning(create_msg)
                # 获取详细的错误信息
                fault = getattr(server, 'fault', None)
                if fault:
                    create_msg = f"【实例创建失败】<instance_id:{instance_id}>故障详情: {fault}"
                    logger.error(create_msg)
                break
        except Exception as e:
            create_msg = f"查询状态失败: {e}"
            logger.warning(create_msg)
            break
    # 跳出后依然需要更新状态
    server_info = TenantOpenstackServer.objects.filter(
        instance_id=instance_id,
        node=node,
        is_deleted=False,
        ).first()
    if not server_info:
        logger.error(f'【更新主机状态及调度同步到运维主机表】instance_id:{instance_id}未找到, 跳过更新')
        return
    server_info.status = status
    create_msg = f'<当前时间:{timezone.now()}><{create_msg}>\n'
    server_info.create_msg = record_log_msg + create_msg
    server_info.save()
    server_info.to_operatorcmdb_host()


def sync_tenant_openstack_server(nodes: any = None):
    """
    同步 openstack 中所有服务器数据
    """
    if not nodes:
        nodes = []
    if isinstance(nodes, str):
        nodes = [nodes]
    if not nodes:
        logger.warning("自动同步Openstack服务器<OpenStackInstance>信息失败，未指定同步的Openstack节点！")
        return
    for node in nodes:
        admin_client = OpenstackAdminClient(node=node)
        response = admin_client.get_project_servers(all_projects=True)
        # 逻辑删除chaos系统内的资源信息
        openstack_all_resource_ids = set()
        tenant_all_resource_ids = set()
        for server in response:
            openstack_all_resource_ids.add(server.id)
            single_sync_openstack_server(admin_client, server_data=server)
        # 逻辑删除chaos系统内的多余资源显示
        tenant_all_objs = TenantOpenstackServer.objects.filter(
            node=admin_client.node,
            is_deleted=False
            ).all()
        [tenant_all_resource_ids.add(tenant_obj.instance_id) for tenant_obj in tenant_all_objs]
        is_needed_to_logical_delete = tenant_all_resource_ids - openstack_all_resource_ids
        if is_needed_to_logical_delete:
            from dvadmin.celery_workers.sync_openstack_resource_worker import (
                async_openstack_server_to_operator_cmdb_host_worker
                )
            need_to_update_objs = TenantOpenstackServer.objects.filter(
                instance_id__in=list(is_needed_to_logical_delete),
                node=admin_client.node,
                is_deleted=False,
                ).all()
            for obj in need_to_update_objs:
                obj.is_deleted = True
                obj.save()
                # 同时自动更新运维主机页面
                async_openstack_server_to_operator_cmdb_host_worker.apply_async(args=(obj.id,))


def check_tenant_openstack_server_expired():
    # 定义过期时间点
    now_time = timezone.now()
    start_expire_time = now_time - timedelta(days=1)
    end_expire_time = timezone.now() + timedelta(days=1)

    # 获取所有即将到期的主机
    expire_near_hosts = TenantOpenstackServer.objects.filter(
        # TODO 暂时仅检测正常运行的过期时间
        status="ACTIVE",
        expire_at__gte=start_expire_time,
        expire_at__lte=end_expire_time,
        is_deleted=False,
        ).all()
    expired_hosts = []
    expiring_hosts = []
    for host in expire_near_hosts:
        host = host.to_dict
        host['baremetal_node_bmc_ip'] = '未知BMC_IP'
        if host['baremetal_node_id']:
            bmc_ip = TenantOpenstackIronicHypervisor.objects.filter(
                ironic_hyper_id=host['baremetal_node_id'],
                is_deleted=False,
                ).first()
            if bmc_ip:
                host['baremetal_node_bmc_ip'] = bmc_ip.name
        if host['expire_at'] < now_time.strftime("%Y-%m-%d %H:%M:%S"):
            expired_hosts.append(host)
        else:
            expiring_hosts.append(host)
    ManagerHostStatusNotice().send_openstack_server_expire_notice(
        '【裸金属过期提醒】',
        expiring_hosts=expiring_hosts,
        expired_hosts=expired_hosts,
        )


def check_tenant_openstack_server_reachable(default_timeout=3):
    """
    检测主机内网是可达状态
    @param default_timeout: ping 默认超时时间 3s
    """
    # 检测时间
    now_time = timezone.now()
    # 默认仅获取前6小时内创建的主机
    # start_check_time = now_time - timedelta(hours=6)

    # 获取所有具有内网IP的主机信息
    active_servers = TenantOpenstackServer.objects.filter(
        is_deleted=False,
        ).exclude(Q(ipaddr=None) | Q(ipaddr=''))

    # 待更新主机
    servers_to_update = []

    for active_server in active_servers:
        # 检测主机ping的状态
        is_reachable = False
        if active_server.status != 'ACTIVE':
            is_reachable = False
        else:
            try:
                # 假设active_server有一个属性ip_address存储了服务器的IP地址
                response_time = ping3.ping(active_server.ipaddr, timeout=default_timeout)
                if response_time is not None:
                    # 如果返回不是None，则认为服务器可达
                    is_reachable = True
            except Exception as e:
                # 记录异常情况，例如网络错误等
                logger.warning(f"Error pinging server {active_server.ip_address}: {e}")
                is_reachable = False
        active_server.is_reachable = is_reachable
        active_server.last_check_reachable_datetime = now_time
        servers_to_update.append(active_server)
    # 批量更新服务器状态
    TenantOpenstackServer.objects.bulk_update(
        servers_to_update,
        ['is_reachable', 'last_check_reachable_datetime']
        )


def retry_op_server_software_install_task(op_server_software_install_id):
    ops_software = TenantOPServerSoftware.objects.filter(
        id=op_server_software_install_id,
        is_deleted=False
        ).first()
    if not ops_software:
        logger.error(f'【原任务ID不存在】op_server_software_install_id:{op_server_software_install_id}未找到, 跳过更新')
        return ErrorResponse(msg='软件安装任务调度失败【原任务ID不存在】')
    # 执行软件安装任务
    software_options = ops_software.software_options or {}
    template_id = software_options.get("ansible_template")
    operatorcmdb_host_ids = get_operatorcmdb_host_ids(
        [
            {
                "op_instance_id": ops_software.op_server_id,
                "node": ops_software.node
                },
            ]
        )
    software_options_option = json.loads(software_options.get("options", '{}'))
    task_params = {
        software_options_option.get('key', 'none'): software_options["current_version"],
        }

    work_description = ops_software.ticket_id or ops_software.op_server_id
    code, data, msg = WorkModelViewSet.create_exec_work(
        project_vars=task_params,
        template_id=template_id,
        host_ids=operatorcmdb_host_ids,
        description=work_description,
        )

    if code:
        ops_software.scheduletask_work_id = data['id']
        ops_software.save()
        logger.info(f' 【软件安装任务调度结果】【code:{str(code)},data: {str(data)}, msg: {msg}】')
        return SuccessResponse(msg='软件安装任务调度成功')
    else:
        logger.error(f' 【软件安装任务调度失败】【code:{str(code)},msg: {msg}】')
        ops_software.status = '软件调度失败'
        ops_software.failed_reason = f'软件调度失败，错误详情:{msg}'
        ops_software.save()
        return ErrorResponse(msg='软件安装任务调度失败')


def wait_create_openstack_server_software_install():
    # 工单创建任务软件安装任务
    RETRY_LIMIT = 3
    all_ops_softwares = TenantOPServerSoftware.objects.filter(
        is_finished=False,
        is_success=False,
        is_deleted=False,
        retry_count__lte=RETRY_LIMIT,
        ticket_id__isnull=False,
        ).all()
    # 查询已创建完成的工单

    for ops_software in all_ops_softwares:
        if ops_software.retry_count >= RETRY_LIMIT:
            ops_software.is_finished = True
            if ops_software.failed_reason:
                ops_software.failed_reason += '软件安装任务重试次数已达上限'
            else:
                ops_software.failed_reason = '软件安装任务重试次数已达上限'
            ops_software.status = '软件安装失败'
            ops_software.save()
            continue
        # 若已有任务则查询当前任务的状态，若处于结束状态则跳过
        if ops_software.scheduletask_work_id:
            worker = Work.objects.filter(
                id=ops_software.scheduletask_work_id,
                is_deleted=False,
                ).first()
            if worker:
                if worker.status == 2:
                    ops_software.is_success = True
                    ops_software.is_finished = True
                    ops_software.status = '软件安装成功'
                    ops_software.save()
                    continue
                elif worker.status in [0, 1]:
                    logger.info(f'<实例ID:{ops_software.op_server_id}>任务正在执行中......')
                    continue
            else:
                logger.error(f'Worker 不存在<id:{ops_software.scheduletask_work_id}>')
                continue
        # 若主机状态处于 ERROR 状态 无需后续操作
        if ops_software.op_server.status == 'ERROR':
            logger.error(
                f'OpenstackServer中主机状态非运行状态'
                f'<op_instance_id:{ops_software.op_server_id}>'
                f'<software_name:{ops_software.software_options.get("name")}>'
                )
            ops_software.is_finished = True
            ops_software.failed_reason = 'OpenstackServer中主机状态非运行状态'
            ops_software.status = '软件安装失败'
            ops_software.save()
            continue
        if ops_software.op_server.is_reachable:
            # 执行软件安装任务
            software_options = ops_software.software_options or {}
            template_id = software_options.get("ansible_template")
            operatorcmdb_host_ids = get_operatorcmdb_host_ids(
                [
                    {
                        "op_instance_id": ops_software.op_server_id,
                        "node": ops_software.node
                        },
                    ]
                )
            software_options_option = json.loads(software_options.get("options", '{}'))
            task_params = {
                software_options_option.get('key', 'none'): software_options["current_version"],
                }

            work_description = ops_software.ticket_id or ops_software.op_server_id
            code, data, msg = WorkModelViewSet.create_exec_work(
                project_vars=task_params,
                template_id=template_id,
                host_ids=operatorcmdb_host_ids,
                description=work_description,
                )

            if code:
                ops_software.scheduletask_work_id = data['id']
                ops_software.retry_count += 1
                ops_software.save()
                logger.info(f' 【软件安装任务调度结果】【code:{str(code)},data: {str(data)}, msg: {msg}】')
            else:
                logger.error(f' 【软件安装任务调度失败】【code:{str(code)},msg: {msg}】')
                ops_software.retry_count += 1
                ops_software.status = '软件调度失败'
                ops_software.failed_reason = f'软件调度失败，错误详情:{msg}'
                ops_software.save()
