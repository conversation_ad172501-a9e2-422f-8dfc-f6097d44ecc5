"""
<AUTHOR>
@Date    ：2024/11/4
"""
from django.utils import timezone

from application.logger import logger

from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import TenantOpenstackImage, TenantOpenstackMetadata
from tenant.utils.sdk.test_openstack_client import OpenstackAdminClient


class TenantOpenstackImageImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantOpenstackImage
        exclude = ()


class TenantOpenstackImageSerializer(ChaosTenantModelSerializer):
    """
    Openstack镜像-序列化器
    """

    class Meta:
        model = TenantOpenstackImage
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


def sync_tenant_openstack_image(nodes: any=None):
    if not nodes:
        nodes = []
    if isinstance(nodes, str):
        nodes = [nodes]
    if not nodes:
        logger.warning("自动同步Openstack镜像<Image>信息失败，未指定同步的Openstack节点！")
        return
    for node in nodes:
        admin_client = OpenstackAdminClient(node=node)
        images = admin_client.get_all_images()

        # 逻辑删除chaos系统内的资源信息
        openstack_all_resource_ids = set()
        tenant_all_resource_ids = set()
        for image in images:
            openstack_all_resource_ids.add(image.id)
            image_obj = TenantOpenstackImage.objects.filter(
                image_id=image.id,
                node=admin_client.node,
            ).first()
            # 获取自定义元数据参数
            image_properties = getattr(image, 'properties', {})
            custom_image_params = {
                "os_type": image_properties.get("os_type", ''),
                'image_type': image_properties.get('image_type', 'Unknown'),
                "system_type": image_properties.get("system_type", ''),
                "major_version":image_properties.get("major_version", ''),
                "minor_version": image_properties.get("minor_version", ''),
                "build_version": image_properties.get("build_version", ''),
                "provider": image_properties.get("provider", ''),
                "is_to_portal": int(image_properties.get("is_to_portal", False)),
                "desc": image_properties.get("desc", ''),
            }

            # TODO 暂不同步 OpenStackImage 元数据
            # 可见性为 公共 时，is_public = True
            # is_public = True if image.visibility == 'public' else False
            # 规格元数据 同步到 独立的元数据表
            # for key, value in custom_image_params.items():
            #     # 获取 Flavor 元数据
            #     metadata_obj = TenantOpenstackMetadata.objects.filter(
            #         resource_id=image.id,
            #         key=key.lower(),
            #         node=admin_client.node,
            #         is_deleted=False,
            #     ).first()
            #     updated_metadata_data = {
            #         'resource_id': image.id,
            #         'type': 1,
            #         'key': key.lower(),
            #         'value': value,
            #         'is_public': is_public,
            #         'node': admin_client.node,
            #     }
            #     if metadata_obj:
            #         for temp_key, temp_value in updated_metadata_data.items():
            #             setattr(metadata_obj, temp_key, temp_value)
            #     else:
            #         metadata_obj = TenantOpenstackMetadata.objects.create(**updated_metadata_data)
            #     metadata_obj.save()

            updated_data = {
                "name": image.name,
                "description": custom_image_params.get("desc", ''),
                "system_type": custom_image_params.get("system_type", ''),
                "os_type": custom_image_params.get("os_type", ''),
                "is_to_portal": custom_image_params.get("is_to_portal", False),
                "image_type": custom_image_params.get("image_type", ''),
                "image_id": image.id,
                "visibility": image.visibility,
                "size": image.size,
                "status": image.status,
                "protected": image.is_protected,
                "min_ram": image.min_ram,
                "min_disk": image.min_disk,
                "owner_id": image.owner,
                # "container_format": getattr(image, "container_format", ""),
                "disk_format": image.disk_format,
                "created_at": image.created_at,
                "updated_at": image.updated_at,
                "node": admin_client.node,
                "sync_time": timezone.now(),
            }
            if image_obj:
                for key, value in updated_data.items():
                    setattr(image_obj, key, value)
            else:
                image_obj = TenantOpenstackImage.objects.create(**updated_data)
            image_obj.save()

        # 逻辑删除chaos系统内的多余资源信息
        tenant_all_objs = TenantOpenstackImage.objects.filter(
            is_deleted=False,
            node=admin_client.node,
        ).all()
        [tenant_all_resource_ids.add(tenant_obj.image_id) for tenant_obj in tenant_all_objs]
        is_needed_to_logical_delete = tenant_all_resource_ids - openstack_all_resource_ids
        if is_needed_to_logical_delete:
            TenantOpenstackImage.objects.filter(
                image_id__in=list(is_needed_to_logical_delete),
                node=admin_client.node,
                is_deleted=False,
            ).update(is_deleted=True)
