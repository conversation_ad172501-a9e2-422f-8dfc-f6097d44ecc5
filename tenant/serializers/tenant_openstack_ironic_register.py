"""
<AUTHOR>
@Date    ：2024/11/16
"""
from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import TenantOpenstackIronicHypervisorRegister


class TenantOpenstackIronicHypervisorRegisterImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantOpenstackIronicHypervisorRegister
        exclude = ()


class TenantOpenstackIronicHypervisorRegisterSerializer(ChaosTenantModelSerializer):
    """
    Openstack镜像-序列化器
    """

    class Meta:
        model = TenantOpenstackIronicHypervisorRegister
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]
