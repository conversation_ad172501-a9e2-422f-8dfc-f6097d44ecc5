"""
<AUTHOR>
@Date    ：2024/10/30
"""
from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import TenantOpenstackSetting


class TenantOpenstackSettingImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantOpenstackSetting
        exclude = ()


class TenantOpenstackSettingSerializer(ChaosTenantModelSerializer):
    """
    日志-序列化器
    """

    class Meta:
        model = TenantOpenstackSetting
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]
