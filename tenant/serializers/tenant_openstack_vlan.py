"""
<AUTHOR>
@Date    ：2024/10/30
"""
import time
from random import randint

from dvadmin.utils.json_response import ErrorResponse, SuccessResponse

from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import TenantOpenstackVlan
from tenant.utils.sdk.openstack_client import OpenstackAdminClient
from application.logger import logger


class TenantOpenstackVlanImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantOpenstackVlan
        exclude = ()


class TenantOpenstackVlanSerializer(ChaosTenantModelSerializer):
    """
    日志-序列化器
    """

    class Meta:
        model = TenantOpenstackVlan
        fields = "__all__"
        read_only_fields = ["is_deleted", "seq"]


def wait_physical_vlan_network_create_finished(physical_network_search_data, node=None):
    """
    等待物理网络创建完成
    """
    admin_client = OpenstackAdminClient(node=node)
    interval_time = 30  # s/秒
    occurrences = 5
    is_success = False
    vlan_data = None
    # 共等待啥时间 total = interval_time * occurrences = 30 * 60 秒
    for sleep_time in range(0, occurrences):
        # 每隔几秒查询一次状态
        time.sleep(interval_time)
        try:
            search_physical_network_params = {
                'project_id': physical_network_search_data['project_id'],
                'name': physical_network_search_data['name'],
                'provider:network_type': physical_network_search_data['network_type'],
                'provider:segmentation_id': physical_network_search_data['vlan_id'],
                'provider:physical_network': physical_network_search_data['phy_vlan_type']
            }
            vlan_data = admin_client.search_physical_vlan_network_only_one(**search_physical_network_params)
            if vlan_data:
                is_success = True
                break
        except Exception as e:
            msg = f"<search_params: {physical_network_search_data}> 查询失败: {e}"
            logger.warning(msg)
            continue

    # 跳出后依然需要更新状态
    if not is_success:
        raise Exception('物理网络创建未完成')
    if not vlan_data:
        raise Exception('物理网络数据为空')
    TenantOpenstackVlan.objects.filter(
        name=physical_network_search_data['name'],
        vlan_id=physical_network_search_data['vlan_id'],
        phy_vlan_type=physical_network_search_data['phy_vlan_type'],
        node=node,
        subnet=physical_network_search_data['subnet'],
        available_ip_pool=physical_network_search_data['available_ip_pool'],
        gateway_ip=physical_network_search_data['gateway_ip'],
        is_deleted=False,
    ).update(
        project_id=vlan_data['project_id'],
        network_id=vlan_data['id'],
        is_used=True,
    )
    with_subnet = physical_network_search_data['with_subnet']

    if with_subnet == "on":
        project_id = physical_network_search_data['project_id']
        cidr = physical_network_search_data['subnet']
        gateway_ip = physical_network_search_data['gateway_ip']
        allocation_pools = physical_network_search_data['available_ip_pool']
        enable_dhcp = physical_network_search_data['enable_dhcp']

        if not project_id:
            raise ValueError("Project ID is required.")
        # 格式化IP池为 SDK 可读取的数据格式
        allocation_pools = admin_client.split_ip_pools(allocation_pools)
        res = admin_client.create_subnet(
            network_id=vlan_data['id'],
            subnetwork_name=physical_network_search_data['name'] + '_subnet',
            cidr=cidr,
            gateway_ip=gateway_ip,
            allocation_pools=allocation_pools,
            enable_dhcp=enable_dhcp,
        )
        from dvadmin.celery_workers.sync_openstack_resource_worker import (
            sync_tenant_openstack_network_worker,
            sync_tenant_openstack_security_group_worker,
        )
        # 执行完成后立即同步网络信息
        sync_tenant_openstack_network_worker.apply_async(args=(admin_client.node,))
        # 执行完成后立即同步安全组信息
        sync_tenant_openstack_security_group_worker.apply_async(args=(admin_client.node,))
        logger.info(dict(res))


def single_create_openstack_physical_vlan_network(
        admin_client=None,
        system_physical_vlan_network_id=None,
        timeout_seconds_range=None,
        node=None
):
    if not admin_client:
        admin_client = OpenstackAdminClient(node=node)  # 初始化 OpenStack 客户端
        node = admin_client.node
    # 生成 delay 请求时间
    if timeout_seconds_range is None:
        timeout_seconds_range = (1, 10,)
    random_timeout = randint(timeout_seconds_range[0], timeout_seconds_range[1])
    if random_timeout:
        time.sleep(random_timeout)
    if system_physical_vlan_network_id is None:
        raise Exception('system_physical_vlan_network_id is required')
    had_vlan_obj = TenantOpenstackVlan.objects.filter(
        id=system_physical_vlan_network_id,
        is_deleted=False,
        is_used=False,
        node=node,
    ).first()
    if not had_vlan_obj:
        return ErrorResponse(data={}, msg='Vlan not found')

    if not had_vlan_obj.project_id:
        return ErrorResponse(data={}, msg='该物理Vlan网络未配置归属项目')

    from dvadmin.celery_workers.sync_openstack_resource_worker import (
        sync_openstack_physical_vlan_network_create_worker,
        single_wait_openstack_physical_vlan_network_create_finished_worker
    )
    created_params = {
        'project_id': had_vlan_obj.project_id,
        'network_name': had_vlan_obj.name,
        'vlan_id': had_vlan_obj.vlan_id,
        'admin_state': 'on',
        'network_type': had_vlan_obj.network_type,
        'physical_network': had_vlan_obj.phy_vlan_type,
    }
    # 仅创建物理网络（不创建子网）
    sync_openstack_physical_vlan_network_create_worker.apply_async(
        args=(created_params, had_vlan_obj.node))
    dict_vlan_data = had_vlan_obj.to_dict
    dict_vlan_data['with_subnet'] = 'on'
    # 等待物理网络创建成功后，创建对应的子网
    single_wait_openstack_physical_vlan_network_create_finished_worker.apply_async(
        args=(dict_vlan_data, dict_vlan_data['node']))
    return SuccessResponse(data=dict_vlan_data, msg='创建物理网络任务已调度')

