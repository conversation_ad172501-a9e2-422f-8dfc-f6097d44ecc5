"""
<AUTHOR>
@Date    ：2024/11/16
"""
import time
from application.logger import logger
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.cache import cache
from dvadmin.utils.json_response import ErrorResponse, SuccessResponse
from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer
from tenant.models import TenantOpenstackIronicHypervisor, TenantOpenstackMetadata, TenantOpenstackIronicHypervisorRegister
from tenant.utils.sdk.test_openstack_client import OpenstackAdminClient
from tenant.dictionary_enum_keys import AUTO_UPDATE_BAREMETAL_NODE_LAST_AT_TIME_KEY, HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY
from operatorcmdb.models import Host
from xresource.models import PhysicalServerMachine


class TenantOpenstackIronicHypervisorImportSerializer(ChaosTenantModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = TenantOpenstackIronicHypervisor
        exclude = ()


class TenantOpenstackIronicHypervisorSerializer(ChaosTenantModelSerializer):
    """
    Openstack镜像-序列化器
    """

    class Meta:
        model = TenantOpenstackIronicHypervisor
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


def sync_tenant_openstack_ironic_hypervisor(nodes: any =None):
    DEFAULT_INTERVAL_MINUTES_TIME = 20

    if not nodes:
        nodes = []
    if isinstance(nodes, str):
        nodes = [nodes]
    if not nodes:
        logger.warning("自动同步Openstack裸机节点<IronicHypervisor>信息失败，未指定同步的Openstack节点！")
        return
    for node in nodes:
        logger.info(f'区域节点: {node}, 开始同步裸机节点信息......')
        update_last_at_time = cache.get(AUTO_UPDATE_BAREMETAL_NODE_LAST_AT_TIME_KEY.format(node=node))
        handle_event = cache.get(HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY.format(node=node))
        if not update_last_at_time:
            update_last_at_time = '1970-01-01 00:00:00'
        update_last_at_time = datetime.strptime(update_last_at_time, "%Y-%m-%d %H:%M:%S")
        need_update_time = update_last_at_time + timedelta(minutes=DEFAULT_INTERVAL_MINUTES_TIME)
        if not handle_event and need_update_time >  timezone.now():
            return

        admin_client = OpenstackAdminClient(node=node)
        ironic_hypervisors = admin_client.get_all_baremetal_nodes(is_detail_list=True)
        # 逻辑删除chaos系统内的资源信息
        openstack_all_resource_ids = set()
        tenant_all_resource_ids = set()
        for ironic_hypervisor in ironic_hypervisors:
            openstack_all_resource_ids.add(ironic_hypervisor.id)
            ironic_hypervisor_obj = TenantOpenstackIronicHypervisor.objects.filter(
                ironic_hyper_id=ironic_hypervisor.id,
                node=admin_client.node,
            ).first()

            updated_data = {
                "name": ironic_hypervisor.name,
                "ironic_hyper_id": ironic_hypervisor.id,
                "description": ironic_hypervisor.description,
                "power_state": ironic_hypervisor.power_state,
                "provision_state": ironic_hypervisor.provision_state,
                "instance_id": ironic_hypervisor.instance_id,
                "conductor": ironic_hypervisor.conductor,
                "driver": ironic_hypervisor.driver,
                "driver_info": ironic_hypervisor.driver_info,
                "maintenance_reason": ironic_hypervisor.maintenance_reason,
                "is_protected": ironic_hypervisor.is_protected,
                "protected_reason": ironic_hypervisor.protected_reason,
                "is_maintenance": ironic_hypervisor.is_maintenance,
                "is_automated_clean_enabled": ironic_hypervisor.is_automated_clean_enabled,
                "is_console_enabled": ironic_hypervisor.is_console_enabled,
                "last_error": ironic_hypervisor.last_error,
                "properties": ironic_hypervisor.properties,
                "resource_class": ironic_hypervisor.resource_class,
                "network_interface": ironic_hypervisor.network_interface,
                "management_interface": ironic_hypervisor.management_interface,
                # "project_id": ironic_hypervisor.project_id,  # 无项目ID
                "created_at": ironic_hypervisor.created_at,
                "updated_at": ironic_hypervisor.updated_at,
                "sync_time": timezone.now(),
                "node": admin_client.node,
                "is_deleted": False,
            }
            if ironic_hypervisor_obj:
                for key, value in updated_data.items():
                    setattr(ironic_hypervisor_obj, key, value)
                ironic_hypervisor_obj.save()
            else:
                ironic_hypervisor_obj = TenantOpenstackIronicHypervisor.objects.create(**updated_data)
                ironic_hypervisor_obj.save()
                # 同步到运维主机管理
                ironic_hypervisor_data = ironic_hypervisor_obj.to_dict
                from dvadmin.celery_workers.sync_openstack_resource_worker import (
                    async_tenant_openstack_ironic_hypervisor_to_operator_cmdb_hosts_worker
                    )
                # 触发等待任务的添加
                async_tenant_openstack_ironic_hypervisor_to_operator_cmdb_hosts_worker.apply_async(
                    args=(admin_client.node, ironic_hypervisor_obj.ironic_hyper_id, None, True)
                    )

        # 逻辑删除chaos系统内的多余资源信息
        tenant_all_objs = TenantOpenstackIronicHypervisor.objects.filter(
            node=admin_client.node,
            is_deleted=False
        ).all()
        [tenant_all_resource_ids.add(tenant_obj.ironic_hyper_id) for tenant_obj in tenant_all_objs]
        is_needed_to_logical_delete = tenant_all_resource_ids - openstack_all_resource_ids
        if is_needed_to_logical_delete:
            TenantOpenstackIronicHypervisor.objects.filter(
                ironic_hyper_id__in=list(is_needed_to_logical_delete),
                node=admin_client.node,
                is_deleted=False,
            ).update(is_deleted=True)
        logger.info(f'区域节点: {node}, 结束同步裸机节点信息......')
        cache.set(AUTO_UPDATE_BAREMETAL_NODE_LAST_AT_TIME_KEY.format(node=node), timezone.now().strftime("%Y-%m-%d %H:%M:%S"))


def sync_tenant_openstack_ironic_hypervisor_to_operator_cmdb_hosts(
        node,
        op_ironic_hypervisor_id=None,
        op_ironic_hypervisor_data=None,
        is_wait=False,
        ):
    """
    同步已注册的裸金属信息到运维管理->主机管理数据
    """
    logger.info("=======================开始准备同步已注册的裸机节点到运维主机管理===============================")
    if is_wait:
        # 等待61秒
        time.sleep(61)
    if not op_ironic_hypervisor_id and not op_ironic_hypervisor_data:
        raise Exception('参数错误')
    if not op_ironic_hypervisor_data:
        op_ironic_hypervisor_obj = TenantOpenstackIronicHypervisor.objects.filter(
            node=node,
            is_deleted=False,
            ironic_hyper_id=op_ironic_hypervisor_id,
            ).first()
        if not op_ironic_hypervisor_obj:
            raise Exception('参数错误')
        op_ironic_hypervisor_data = op_ironic_hypervisor_obj.to_dict
    # 获取ironic_hypervisor属性数据
    properties = op_ironic_hypervisor_data["properties"]
    logger.info(f"properties: {properties}")
    sn = properties.get('sn')
    if not sn:
        logger.warning(f'sn 序列号不存在，不进行自动更新')
        return
    # 获取 资产管理 -> 硬件资产 -> 物理服务器管理 数据
    phy_machine_obj = PhysicalServerMachine.objects.get(
        physical_machine_sn=sn,
        is_deleted=False,
        )
    if not phy_machine_obj:
        logger.warning(f'sn:{sn} 找不到对应的物理资产数据')
        return
    # 获取 ironic_hypervisor_data 关联的注册的任务ID
    phy_register_obj = TenantOpenstackIronicHypervisorRegister.objects.filter(
        created_hyp_ironic_node_id=op_ironic_hypervisor_id,
        node=node,
        is_deleted=False,
        is_success=True,
        ).first()
    if phy_register_obj:
        phy_register_data = phy_register_obj.to_dict
        logger.info(f"phy_register_data: {phy_register_data}")
    else:
        driver_info = op_ironic_hypervisor_data['driver_info']
        phy_register_data = {
            'node_driver_ipmi_address': driver_info.get('ipmi_address'),
            'node_driver_ipmi_username': driver_info.get('ipmi_username'),
            }
    host_data = {
            'instance_id': op_ironic_hypervisor_data.get('name'),
            'area': node,
            'name': op_ironic_hypervisor_data.get('name'),
            'resource_category': '裸金属',
            'host_type': '裸金属',
            'sn': sn,
            'ip_bmc': phy_register_data.get("node_driver_ipmi_address"),
            'bmc_user': phy_register_data.get("node_driver_ipmi_username"),
            'bmc_passwd': phy_register_data.get("node_driver_ipmi_password"),
            'machine_room_id': phy_machine_obj.machine_room_id,
            'private_room_id': phy_machine_obj.private_room_id,
            'idc_rack_machine_id': phy_machine_obj.idc_rack_machine_id,
            'physical_server_machine_id': phy_machine_obj.id,
            'host_status': '空闲',
            'cpu': properties.get('cpus'),
            'mem': f"{properties.get('memory_mb', 0)} MB",
            'sys_disk': f"{properties.get('local_gb', 0)} GB",
            'gpu_model': properties.get('gpu_model'),
            'gpu_count': properties.get('gpu_count', 8),
            'is_buffer': True,
            'is_ipmi_mon': True,
            'u_position': phy_machine_obj.rack_unit,
            'is_deleted': False,
            }
    host_obj = Host.objects.filter(
        sn=sn,
        area=node,
        host_type='裸金属',
        is_deleted=False,
        ).first()
    if host_obj:
        for key, value in host_data.items():
            setattr(host_obj, key, value)
    else:
        host_obj = Host.objects.create(**host_data)
    host_obj.save()
    logger.info(f'SN: {sn}, {op_ironic_hypervisor_data.get("name")}自动更新【运维管理中的主机】成功')
