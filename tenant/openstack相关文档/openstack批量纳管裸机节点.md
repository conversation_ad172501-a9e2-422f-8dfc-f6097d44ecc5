#### 配置文件组成
需要用到三个文件config.yaml，nodeinfo.tmpl，openstacknode

config.yaml：填写需要纳管的node信息，25G跟10G机器不要放在一起

nodeinfo.tmpl：生成nodeinfo的模版，默认不做改动

openstacknode：二进制文件，执行后进行模版替换生成文件，加入集群

#### 命令参数
 nic: 网卡类型，默认是10G。25G网卡是需要加上需要两台交换机

 portcreate：默认是否，是否重建端口组

#### 用法
25G机器相关操作，**config.yaml中的switch_id2和switch_name2必须填写**

端口重建，先在界面删除原先端口组，暂时先不走命令形式

```bash
#普通10G机器进行纳管，会生成nodeinfo-<bmc_ip>文件
root@jh-openstack-01:~/workspace/node# ./openstacknode 

#普通10G机器重建端口组，会生成nodeinfo-<bmc_ip>-portcreate文件
root@jh-openstack-01:~/workspace/node# ./openstacknode -portcreate true

#普通25G机器进行纳管，会生成nodeinfo-<bmc_ip>-25G文件
root@jh-openstack-01:~/workspace/node# ./openstacknode -nic 25G

#普通25G机器重建端口组，会生成nodeinfo-<bmc_ip>-portcreat-25G文件
root@jh-openstack-01:~/workspace/node# ./openstacknode -nic 25G -portcreate true


```



#### 具体模版
```plain
node_name={{.ipmi_address}}
node_mac="{{.mac1}}"    # MAC address of PXE interface (em1 as example)
resource={{.resource}}            # Ironic resource class (matches flavor as CUSTOM_IRONIC_4909)
phys_arch={{.phys_arch}}
phys_cpus={{.phys_cpus}}
phys_ram={{.phys_ram}}
phys_disk={{.phys_disk}}
ipmi_username={{.ipmi_username}}
ipmi_password={{.ipmi_password}}
ipmi_address={{.ipmi_address}}
boot_mode=uefi
network_interface=neutron
ipmi_version=2.0

{{if eq (print .port_recreate) "true"}}
openstack baremetal port group create \
--node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` --name $node_name --address {{.mac1}} --mode 802.3ad \
--property miimon=100 --property xmit_hash_policy="layer2+3" \
--support-standalone-ports

portgroup=`openstack baremetal port group list|grep $node_name|awk '{print $2}'`

{{if eq (print .nic_type) "25G"}}
openstack baremetal port create {{.mac1}} --node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` \
--local-link-connection switch_id={{.switch_id1}} --local-link-connection switch_info={{.switch_name1}} \
--local-link-connection port_id={{.port_id}}  --pxe-enabled true --port-group ${portgroup}

openstack baremetal port create {{.mac2}} --node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` \
--local-link-connection switch_id={{.switch_id2}} --local-link-connection switch_info={{.switch_name2}} \
--local-link-connection port_id={{.port_id}} --pxe-enabled true --port-group ${portgroup}

{{else}}

openstack baremetal port create {{.mac1}} --node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` \
--local-link-connection switch_id={{.switch_id1}} --local-link-connection switch_info={{.switch_name1}} \
--local-link-connection port_id={{.port_id}}  --pxe-enabled true --port-group ${portgroup}

openstack baremetal port create {{.mac2}} --node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` \
--local-link-connection switch_id={{.switch_id1}} --local-link-connection switch_info={{.switch_name1}} \
--local-link-connection port_id={{.port_id}} --pxe-enabled true --port-group ${portgroup}
{{end}}

{{else}}

openstack baremetal node create \
  --driver ipmi \
  --deploy-interface direct \
  --driver-info ipmi_username=$ipmi_username \
  --driver-info ipmi_password=$ipmi_password \
  --driver-info ipmi_address=$ipmi_address \
  --driver-info ipmi_protocol_version=$ipmi_version \
  --property cpus=$phys_cpus \
  --property memory_mb=$phys_ram \
  --property local_gb=$phys_disk \
  --property cpu_arch=$phys_arch \
  --property capabilities='boot_option:local,disk_label:gpt' \
  --resource-class $resource \
  --network-interface $network_interface \
  --name $node_name


openstack baremetal port group create \
--node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` --name $node_name --address {{.mac1}} --mode 802.3ad \
--property miimon=100 --property xmit_hash_policy="layer2+3" \
--support-standalone-ports

portgroup=`openstack baremetal port group list|grep $node_name|awk '{print $2}'`

{{if eq (print .nic_type) "25G"}}
openstack baremetal port create {{.mac1}} --node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` \
--local-link-connection switch_id={{.switch_id1}} --local-link-connection switch_info={{.switch_name1}} \
--local-link-connection port_id={{.port_id}}  --pxe-enabled true --port-group ${portgroup}

openstack baremetal port create {{.mac2}} --node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` \
--local-link-connection switch_id={{.switch_id2}} --local-link-connection switch_info={{.switch_name2}} \
--local-link-connection port_id={{.port_id}} --pxe-enabled true --port-group ${portgroup}

{{else}}

openstack baremetal port create {{.mac1}} --node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` \
--local-link-connection switch_id={{.switch_id1}} --local-link-connection switch_info={{.switch_name1}} \
--local-link-connection port_id={{.port_id}}  --pxe-enabled true --port-group ${portgroup}

openstack baremetal port create {{.mac2}} --node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` \
--local-link-connection switch_id={{.switch_id1}} --local-link-connection switch_info={{.switch_name1}} \
--local-link-connection port_id={{.port_id}} --pxe-enabled true --port-group ${portgroup}
{{end}}
openstack baremetal node manage $node_name
openstack baremetal node provide $node_name
{{end}}


```

#### 填写node信息
这是示例，信息不准确，需要根据实际情况修改

⚠️普通10G只需填写switch_id1跟switch_name1，switch_id2和switch_name2 25g机器需要填写

其他选项一致

```yaml
nodename:
  **********:
    ipmi_address: **********
    mac1: a0:36:9f:54:93:84
    mac2: a0:36:9f:54:93:86
    switch_id1: c4:69:f0:46:7a:90
    switch_id2: ad:cd
    switch_name1: ASW-D4-3.EM101-B01C01-40U-CE6857F-VM-G2-AB
    switch_name2: ASW-qy-01
    port_id: Eth-Trunk11
    resource: ironic-4090
    phys_arch: x86_64
    phys_cpus: 144
    phys_ram: 515072
    phys_disk: 400
    ipmi_username: ADMIN
    ipmi_password: ADMIN

```

#### 用法
登陆到openstack生产环境部署机上，然后ssh到***********这个控制节点。config.yaml、nodeinfo.tmpl、openstacknode文件已经上传到对应目录下了

```bash
root@jh-openstack-deploy:~# ssh ***********
Welcome to Ubuntu 22.04.3 LTS (GNU/Linux 5.15.0-119-generic x86_64)
You have new mail.
Last login: Thu Nov 28 17:54:23 2024 from ***********
root@jh-openstack-01:~# cd workspace/node/
root@jh-openstack-01:~/workspace/node# 
root@jh-openstack-01:~/workspace/node# ./openstacknode --help
Usage of ./openstacknode:
Example: ./openstacknode [options]

Options:
  -nic string
        网卡类型，默认是10G
  -portcreate
        是否重建端口组
root@jh-openstack-01:~/workspace/node# ./openstacknode 
Output written to nodeinfo-**********
add node **********
/root/workspace/node/nodeinfo-**********
2024/11/28 17:54:43 Executing command: /usr/bin/bash -c source /root/openrc && bash /root/workspace/node/nodeinfo-**********
2024/11/28 17:54:43 Environment variables: []
Output: +------------------------+-------------------------------------------------------------------------------------------------------------------------------+
| Field                  | Value                                                                                                                         |
+------------------------+-------------------------------------------------------------------------------------------------------------------------------+
| allocation_uuid        | None                                                                                                                          |
| automated_clean        | None                                                                                                                          |
```

错误日志在/root/workspace/node/logs/add_node.log；只记录失败err的日志


