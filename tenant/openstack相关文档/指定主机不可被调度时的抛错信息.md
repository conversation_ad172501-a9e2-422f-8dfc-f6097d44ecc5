# 主机不能被调度错误信息
> 当主机设置为 `manageable` 时，主机不可被调度，而又指定裸金属节点时，出现的错误。
```
故障详情: {'code': 500, 'created': '2024-11-08T02:02:50Z', 'message': 'No valid host was found. ', 'details': 'Traceback (most recent call last):\n  File "/openstack/venvs/nova-25.5.0/lib/python3.10/site-packages/nova/conductor/manager.py", line 1580, in schedule_and_build_instances\n    host_lists = self._schedule_instances(context, request_specs[0],\n  File "/openstack/venvs/nova-25.5.0/lib/python3.10/site-packages/nova/conductor/manager.py", line 940, in _schedule_instances\n    host_lists = self.query_client.select_destinations(\n  File "/openstack/venvs/nova-25.5.0/lib/python3.10/site-packages/nova/scheduler/client/query.py", line 41, in select_destinations\n    return self.scheduler_rpcapi.select_destinations(context, spec_obj,\n  File "/openstack/venvs/nova-25.5.0/lib/python3.10/site-packages/nova/scheduler/rpcapi.py", line 160, in select_destinations\n    return cctxt.call(ctxt, \'select_destinations\', **msg_args)\n  File "/openstack/venvs/nova-25.5.0/lib/python3.10/site-packages/oslo_messaging/rpc/client.py", line 189, in call\n    result = self.transport._send(\n  File "/openstack/venvs/nova-25.5.0/lib/python3.10/site-packages/oslo_messaging/transport.py", line 123, in _send\n    return self._driver.send(target, ctxt, message,\n  File "/openstack/venvs/nova-25.5.0/lib/python3.10/site-packages/oslo_messaging/_drivers/amqpdriver.py", line 689, in send\n    return self._send(target, ctxt, message, wait_for_reply, timeout,\n  File "/openstack/venvs/nova-25.5.0/lib/python3.10/site-packages/oslo_messaging/_drivers/amqpdriver.py", line 681, in _send\n    raise result\nnova.exception_Remote.NoValidHost_Remote: No valid host was found. \nTraceback (most recent call last):\n\n  File "/openstack/venvs/nova-25.5.0/lib/python3.10/site-packages/oslo_messaging/rpc/server.py", line 241, in inner\n    return func(*args, **kwargs)\n\n  File "/openstack/venvs/nova-25.5.0/lib/python3.10/site-packages/nova/scheduler/manager.py", line 209, in select_destinations\n    raise exception.NoValidHost(reason="")\n\nnova.exception.NoValidHost: No valid host was found. \n\n'}

```