# openstack 主机状态枚举

## v1 版本主机状态描述
- ACTIVE：活动
- BUILDING：构建中
- DELETED：已删除
- ERROR：错误
- PAUSED：暂停
- RESCUED：救援中
- RESIZED：调整大小中
- SHELVED：搁置
- SHELVED_OFFLOADED：离线搁置
- SOFT_DELETED：软删除
- STOPPED：停止
- SUSPENDED：挂起
- 详细解释
- ACTIVE：活动状态，表示服务器正在运行。
- BUILDING：构建中，表示服务器正在创建过程中。
- DELETED：已删除，表示服务器已经被删除。
- ERROR：错误，表示服务器遇到了某种错误。
- PAUSED：暂停，表示服务器已被暂停，但仍保留内存状态。
- RESCUED：救援中，表示服务器处于救援模式，通常用于修复问题。
- RESIZED：调整大小中，表示服务器正在调整其资源配置。
- SHELVED：搁置，表示服务器已关闭并存储在后端存储中。
- SHELVED_OFFLOADED：离线搁置，表示服务器已从计算节点移除并存储在后端存储中。
- SOFT_DELETED：软删除，表示服务器已被标记为删除，但在一定时间内仍可恢复。
- STOPPED：停止，表示服务器已关闭。
- SUSPENDED：挂起，表示服务器已挂起，类似于暂停，但可能涉及更多的资源释放。


## V2 版本修正
服务器状态枚举
ACTIVE:
实例正在运行，并且可以正常使用。
BUILD:
实例正在创建过程中。这是一个临时状态，表示资源正在被分配和配置。
REBUILD:
实例正在进行重建操作。这也是一个临时状态，表示实例正在用新的镜像进行重装。
SHUTOFF:
实例已经停止，但仍然保留其所有配置和数据。它不消耗计算资源（取决于提供商的计费政策）。
DELETED:
实例已经被删除，不再存在于云环境中。这通常是一个短暂的状态，因为一旦实例被删除，它的记录也会很快从系统中清除。
SOFT_DELETED:
实例已被软删除，这意味着它暂时被标记为已删除，但在一段时间内仍可恢复。此功能取决于云提供商的支持。
ERROR:
创建、启动、重启或其他操作过程中发生了错误，导致实例无法进入预期的状态。需要检查日志以了解具体原因。
HARD_REBOOT:
实例正在经历硬重启。这是个临时状态，表示实例正在被强制重启。
PASSWORD:
此状态很少见，表示实例正在处理与密码相关的操作，如更改管理员密码。
RESCUE:
实例处于救援模式。这是一种特殊状态，允许用户访问实例的文件系统以修复问题。
RESIZE:
实例正在调整大小（改变资源配置）。这是一个临时状态，表示实例正在迁移到新的硬件或调整资源。
VERIFY_RESIZE:
实例调整大小后，等待用户确认新配置是否满意。用户可以选择确认或回滚到原始配置。
PAUSED:
实例已被暂停。暂停后的实例会冻结其当前状态，类似于保存游戏进度。
SUSPENDED:
实例已被挂起。挂起的实例将释放大部分计算资源，但仍保持内存快照，以便快速恢复。
RESCUED:
实例已在救援模式下完成操作，并处于可用状态。
STOPPED:
类似于SHUTOFF，表示实例已停止。这个状态在某些OpenStack版本中使用。
UNKNOWN:
无法确定实例的状态。这可能是由于API或底层基础设施的问题造成的。
MIGRATING:
实例正在迁移至另一个主机。这是个临时状态，表示实例正在跨物理节点移动。


## 简化后前端展示
### 简化后的状态分类

1. **运行中 (Running)**
   - 包括：`ACTIVE`
   - **描述**：实例正在正常运行并可以正常使用。

2. **创建中 (Creating)**
   - 包括：`BUILD`
   - **描述**：实例正在创建过程中，资源正在分配和配置。

3. **重建中 (Rebuilding)**
   - 包括：`REBUILD`
   - **描述**：实例正在进行重建操作，使用新的镜像重装系统。

4. **已停止 (Stopped)**
   - 包括：`SHUTOFF`, `STOPPED`
   - **描述**：实例已经停止，但保留其所有配置和数据。不消耗计算资源（取决于提供商的计费政策）。

5. **已删除 (Deleted)**
   - 包括：`DELETED`, `SOFT_DELETED`
   - **描述**：实例已被删除或标记为软删除，可能在一段时间内可恢复。

6. **错误 (Error)**
   - 包括：`ERROR`
   - **描述**：实例在创建、启动、重启或其他操作过程中发生了错误，无法进入预期状态。需要检查日志以了解具体原因。

7. **重启中 (Rebooting)**
   - 包括：`HARD_REBOOT`
   - **描述**：实例正在经历硬重启，即强制重启。

8. **救援模式 (Rescue Mode)**
   - 包括：`RESCUE`, `RESCUED`
   - **描述**：实例处于救援模式，允许用户访问文件系统以修复问题。

9. **调整大小 (Resizing)**
   - 包括：`RESIZE`, `VERIFY_RESIZE`
   - **描述**：实例正在调整大小或等待用户确认新配置。

10. **暂停/挂起 (Paused/Suspended)**
    - 包括：`PAUSED`, `SUSPENDED`
    - **描述**：实例已被暂停或挂起，当前状态被冻结或大部分计算资源被释放。

11. **未知 (Unknown)**
    - 包括：`UNKNOWN`
    - **描述**：无法确定实例的状态，可能是由于API或底层基础设施的问题造成的。

12. **迁移中 (Migrating)**
    - 包括：`MIGRATING`
    - **描述**：实例正在迁移至另一个主机，跨物理节点移动。

### 映射表

为了方便在代码中进行状态映射，您可以使用一个字典来将原始状态映射到简化后的状态：

chaos_system_dictionary
---
| label | value | 
| --- | --- | 
| 调整大小 | VERIFY_RESIZE | 
| 未知 | UNKNOWN | 
| 挂起中 | SUSPENDED | 
| 已停止 | STOPPED | 
| 软删除 | SOFT_DELETED | 
| 已关机 | SHUTOFF | 
| 离线搁置 | SHELVED_OFFLOADED | 
| 搁置 | SHELVED | 
| 调整中 | RESIZED | 
| 调整大小 | RESIZE | 
| 救援中 | RESCUED | 
| 救援中 | RESCUE | 
| 重建中 | REBUILD | 
| 暂停 | PAUSED | 
| 其他 | PASSWORD | 
| 迁移中 | MIGRATING | 
| 重启中 | HARD_REBOOT | 
| 错误 | ERROR | 
| 已删除 | DELETED | 
| 创建中 | BUILDING | 
| 创建中 | BUILD | 
| 运行中 | ACTIVE | 
