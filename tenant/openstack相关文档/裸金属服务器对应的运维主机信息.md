# 获取所有裸金属服务器及其对应的运维主机信息

```SQL
--- 获取所有openstack已纳管裸金属节点是否在运维主机信息表内 正常为Match found 未记录到运维主机信息表内，则为 No matching host in CMDB

WITH openstack_hypervisors AS (
    SELECT 
        id AS hypervisor_id,
        ironic_hyper_id,
        NAME AS bmc_ip,
        is_deleted
    FROM 
        product_chaos_tenants.tenant_op_ironic_phys
    WHERE 
        is_deleted = FALSE
)

SELECT 
    oh.hypervisor_id,
    oh.ironic_hyper_id,
    oh.bmc_ip,
    h.id AS host_id,
    h.ip_bmc,
    h.expire_time,
    CASE 
        WHEN h.ip_bmc IS NULL THEN 'No matching host in CMDB'
        ELSE 'Match found'
    END AS match_status
FROM 
    openstack_hypervisors oh
LEFT JOIN 
    product_chaos.operator_cmdb_hosts h ON oh.bmc_ip = h.ip_bmc AND h.is_deleted = FALSE;
```
