[{"options": [{"key": "cuda_version", "label": "CUDA版本", "type": "select", "options": [{"label": "CUDA 11.7", "value": "11.7"}, {"label": "CUDA 11.8", "value": "11.8"}, {"label": "CUDA 12.0", "value": "12.0"}]}, {"key": "ken_version", "label": "ken <PERSON>", "type": "select", "options": [{"label": "ken 11.7", "value": "11.7"}, {"label": "ken 11.8", "value": "11.8"}, {"label": "ken 12.0", "value": "12.0"}]}], "id": "1223131231313131312", "name": "CUDA", "description": "CUDA", "key": "cuda", "ansible_template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"options": {"key": "docker_version", "label": "版本", "type": "select", "options": [{"label": "CUDA 11.7", "value": "11.7"}, {"label": "CUDA 11.8", "value": "11.8"}, {"label": "CUDA 12.0", "value": "12.0"}]}, "id": "1223131231313131312", "name": "<PERSON>er", "description": "<PERSON>er", "key": "docker", "ansible_template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]