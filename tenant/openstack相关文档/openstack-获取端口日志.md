# Openstack-获取端口日志
## 日志
```text
[openstack.network.v2.port.Port(id=54ad3117-0d5d-4ead-87fb-37a6800dacb2, name=, network_id=72ec0e8b-20ae-4ecf-ac72-992dea709ab7, tenant_id=8f788892a6b444e69c618031b66a1240, mac_address=a0:36:9f:53:cf:94, admin_state_up=True, status=ACTIVE, device_id=c7eb7e76-1009-4785-9e14-a2377c5b9b5b, device_owner=compute:nova, fixed_ips=[{'subnet_id': 'c839d6ed-73e6-4cbf-82e1-f0a0cac0cb05', 'ip_address': '************'}], allowed_address_pairs=[], extra_dhcp_opts=[{'opt_name': '150', 'opt_value': '***********', 'ip_version': 4}, {'opt_name': '66', 'opt_value': '***********', 'ip_version': 4}, {'opt_name': 'server-ip-address', 'opt_value': '***********', 'ip_version': 4}, {'opt_name': 'tag:!ipxe,67', 'opt_value': 'ipxe.efi', 'ip_version': 4}, {'opt_name': 'tag:ipxe,67', 'opt_value': 'http://***********:8051/boot.ipxe', 'ip_version': 4}], security_groups=['3ac79052-82a7-44a7-8a36-f1992f7f86f0'], description=, binding:vnic_type=baremetal, binding:profile={'local_link_information': [{'switch_id': '38:90:52:15:d5:50', 'switch_info': 'ASW-D4-3.EM101-C01B01-38U-CE6857F-VM-G3-AB', 'port_id': 'Eth-Trunk13'}, {'switch_id': '38:90:52:15:d5:50', 'switch_info': 'ASW-D4-3.EM101-C01B01-38U-CE6857F-VM-G3-AB', 'port_id': 'Eth-Trunk13'}], 'local_group_information': {'id': 'd0eeed83-cd02-490c-ad30-5889fe8f9af8', 'name': '**********', 'bond_mode': '802.3ad', 'bond_properties': {'bond_miimon': 100, 'bond_xmit_hash_policy': 'layer2+3'}}}, binding:host_id=8134af37-2050-4dcf-9fc8-5ff886cd33e2, binding:vif_type=other, binding:vif_details={'bound_drivers': {'0': 'genericswitch'}}, port_security_enabled=True, tags=[], created_at=2025-01-02T07:35:56Z, updated_at=2025-01-02T07:45:33Z, revision_number=9, project_id=8f788892a6b444e69c618031b66a1240, location=Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': None, 'project': Munch({'id': '8f788892a6b444e69c618031b66a1240', 'name': None, 'domain_id': None, 'domain_name': None})}))]
```

## 错误分析
由于主机尚未创建成功，OpenStack 网络中端口状态尚处于`Down`状态, 主机列表数据无法关联网络信息（需要等待主机创建成功后才会显示关联数据）。

## 解决方案
`不在运行状态的主机` 从 `网络端口接口`获取数据。
