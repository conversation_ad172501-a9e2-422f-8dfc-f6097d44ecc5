![openstack调度后主机截图](./img/openstack调度后主机截图.png)

[2025-01-02 14:52:36,843: INFO/ForkPoolWorker-3] <OpenstackData:{'links': [{'rel': 'self', 'href': 'https://*************:8774/v2.1/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}, {'rel': 'bookmark', 'href': 'https://*************:8774/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}], 'access_ipv4': '', 'access_ipv6': '', 'addresses': {}, 'admin_password': None, 'attached_volumes': [], 'volumes': [], 'availability_zone': 'nova', 'block_device_mapping': None, 'config_drive': 'True', 'compute_host': None, 'created_at': '2025-01-02T06:52:35Z', 'description': '同时查看创建状态ip数据', 'disk_config': 'AUTO', 'flavor_id': None, 'flavor': {'name': 'baremetal-4090', 'original_name': 'baremetal-4090', 'description': None, 'disk': 300, 'is_public': True, 'ram': 515072, 'vcpus': 144, 'swap': 0, 'ephemeral': 0, 'is_disabled': None, 'rxtx_factor': None, 'extra_specs': {'resources:CUSTOM_IRONIC_4090': '1', 'resources:VCPU': '0', 'resources:MEMORY_MB': '0', 'resources:DISK_GB': '0', 'capabilities:boot_option': 'local', 'flavor_type': '裸金属', 'gpu_type': '4090', 'gpu_count': '1', 'is_to_portal': '1', 'desc': '裸金属-4090'}, 'id': 'baremetal-4090', 'location': None}, 'has_config_drive': 'True', 'host_id': '', 'fault': None, 'host': None, 'host_status': '', 'hostname': '1', 'hypervisor_hostname': None, 'image_id': None, 'image': {'checksum': None, 'container_format': None, 'created_at': None, 'disk_format': None, 'is_hidden': None, 'is_protected': None, 'hash_algo': None, 'hash_value': None, 'min_disk': None, 'min_ram': None, 'name': None, 'owner': None, 'owner_id': None, 'properties': {'links': [{'rel': 'bookmark', 'href': 'https://*************:8774/images/a2483e38-3f70-47ec-82cd-53c7afe9091c'}]}, 'size': None, 'store': None, 'status': None, 'updated_at': None, 'virtual_size': None, 'visibility': None, 'file': None, 'locations': None, 'direct_url': None, 'url': None, 'metadata': None, 'architecture': None, 'hypervisor_type': None, 'instance_type_rxtx_factor': None, 'instance_uuid': None, 'needs_config_drive': None, 'kernel_id': None, 'os_distro': None, 'os_version': None, 'needs_secure_boot': None, 'os_shutdown_timeout': None, 'ramdisk_id': None, 'vm_mode': None, 'hw_cpu_sockets': None, 'hw_cpu_cores': None, 'hw_cpu_threads': None, 'hw_disk_bus': None, 'hw_cpu_policy': None, 'hw_cpu_thread_policy': None, 'hw_rng_model': None, 'hw_machine_type': None, 'hw_scsi_model': None, 'hw_serial_port_count': None, 'hw_video_model': None, 'hw_video_ram': None, 'hw_watchdog_action': None, 'os_command_line': None, 'hw_vif_model': None, 'is_hw_vif_multiqueue_enabled': None, 'is_hw_boot_menu_enabled': None, 'vmware_adaptertype': None, 'vmware_ostype': None, 'has_auto_disk_config': None, 'os_type': None, 'os_admin_user': None, 'hw_qemu_guest_agent': None, 'os_require_quiesce': None, 'schema': None, 'id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'location': None, 'tags': []}, 'instance_name': 'instance-000003c2', 'interface_ip': '', 'is_locked': False, 'kernel_id': '8264426d-290a-412c-aa08-8efcd2253d0b', 'key_name': 'xingzai', 'launch_index': 0, 'launched_at': None, 'locked_reason': None, 'max_count': None, 'min_count': None, 'networks': None, 'personality': None, 'pinned_availability_zone': None, 'power_state': 0, 'progress': 0, 'project_id': '8f788892a6b444e69c618031b66a1240', 'private_v4': '', 'private_v6': '', 'public_v4': '', 'public_v6': '', 'ramdisk_id': '4dc58528-af26-4fc4-b192-9394b9fb1906', 'reservation_id': 'r-0cejx6y4', 'root_device_name': None, 'scheduler_hints': None, 'security_groups': None, 'server_groups': [], 'status': 'BUILD', 'task_state': 'scheduling', 'terminated_at': None, 'trusted_image_certificates': None, 'updated_at': '2025-01-02T06:52:36Z', 'user_data': '', 'user_id': 'bcb70a7299c441d0b3417cf68f723ed7', 'vm_state': 'building', 'id': '347b58d4-8284-4f34-9e18-3f935558ebaf', 'name': '测试完成订单的通知内容_1', 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': 'nova', 'project': Munch({'id': '8f788892a6b444e69c618031b66a1240', 'name': None, 'domain_id': None, 'domain_name': None})}), 'metadata': {'instance_type': '裸金属', 'image_id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'flavor_id': 'c6d9b737-6f78-41d7-8786-ee0949da40c1'}, 'tags': []}>




[2025-01-02 14:53:01,657: INFO/ForkPoolWorker-3] <OpenstackData:{'links': [{'rel': 'self', 'href': 'https://*************:8774/v2.1/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}, {'rel': 'bookmark', 'href': 'https://*************:8774/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}], 'access_ipv4': '', 'access_ipv6': '', 'addresses': {}, 'admin_password': None, 'attached_volumes': [], 'volumes': [], 'availability_zone': 'nova', 'block_device_mapping': None, 'config_drive': 'True', 'compute_host': 'jh-openstack-01-ironic-compute-container-711de48d', 'created_at': '2025-01-02T06:52:35Z', 'description': '同时查看创建状态ip数据', 'disk_config': 'AUTO', 'flavor_id': None, 'flavor': {'name': 'baremetal-4090', 'original_name': 'baremetal-4090', 'description': None, 'disk': 300, 'is_public': True, 'ram': 515072, 'vcpus': 144, 'swap': 0, 'ephemeral': 0, 'is_disabled': None, 'rxtx_factor': None, 'extra_specs': {'resources:CUSTOM_IRONIC_4090': '1', 'resources:VCPU': '0', 'resources:MEMORY_MB': '0', 'resources:DISK_GB': '0', 'capabilities:boot_option': 'local', 'flavor_type': '裸金属', 'gpu_type': '4090', 'gpu_count': '1', 'is_to_portal': '1', 'desc': '裸金属-4090'}, 'id': 'baremetal-4090', 'location': None}, 'has_config_drive': 'True', 'host_id': '493a97ef5976efa193cb49a25b55b33cf211f6d8308c192a74201af3', 'fault': None, 'host': None, 'host_status': 'UP', 'hostname': '1', 'hypervisor_hostname': '8134af37-2050-4dcf-9fc8-5ff886cd33e2', 'image_id': None, 'image': {'checksum': None, 'container_format': None, 'created_at': None, 'disk_format': None, 'is_hidden': None, 'is_protected': None, 'hash_algo': None, 'hash_value': None, 'min_disk': None, 'min_ram': None, 'name': None, 'owner': None, 'owner_id': None, 'properties': {'links': [{'rel': 'bookmark', 'href': 'https://*************:8774/images/a2483e38-3f70-47ec-82cd-53c7afe9091c'}]}, 'size': None, 'store': None, 'status': None, 'updated_at': None, 'virtual_size': None, 'visibility': None, 'file': None, 'locations': None, 'direct_url': None, 'url': None, 'metadata': None, 'architecture': None, 'hypervisor_type': None, 'instance_type_rxtx_factor': None, 'instance_uuid': None, 'needs_config_drive': None, 'kernel_id': None, 'os_distro': None, 'os_version': None, 'needs_secure_boot': None, 'os_shutdown_timeout': None, 'ramdisk_id': None, 'vm_mode': None, 'hw_cpu_sockets': None, 'hw_cpu_cores': None, 'hw_cpu_threads': None, 'hw_disk_bus': None, 'hw_cpu_policy': None, 'hw_cpu_thread_policy': None, 'hw_rng_model': None, 'hw_machine_type': None, 'hw_scsi_model': None, 'hw_serial_port_count': None, 'hw_video_model': None, 'hw_video_ram': None, 'hw_watchdog_action': None, 'os_command_line': None, 'hw_vif_model': None, 'is_hw_vif_multiqueue_enabled': None, 'is_hw_boot_menu_enabled': None, 'vmware_adaptertype': None, 'vmware_ostype': None, 'has_auto_disk_config': None, 'os_type': None, 'os_admin_user': None, 'hw_qemu_guest_agent': None, 'os_require_quiesce': None, 'schema': None, 'id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'location': None, 'tags': []}, 'instance_name': 'instance-000003c2', 'interface_ip': '', 'is_locked': False, 'kernel_id': '8264426d-290a-412c-aa08-8efcd2253d0b', 'key_name': 'xingzai', 'launch_index': 0, 'launched_at': None, 'locked_reason': None, 'max_count': None, 'min_count': None, 'networks': None, 'personality': None, 'pinned_availability_zone': None, 'power_state': 0, 'progress': 0, 'project_id': '8f788892a6b444e69c618031b66a1240', 'private_v4': '', 'private_v6': '', 'public_v4': '', 'public_v6': '', 'ramdisk_id': '4dc58528-af26-4fc4-b192-9394b9fb1906', 'reservation_id': 'r-0cejx6y4', 'root_device_name': '/dev/sda', 'scheduler_hints': None, 'security_groups': [{'name': 'default'}], 'server_groups': None, 'status': 'BUILD', 'task_state': 'spawning', 'terminated_at': None, 'trusted_image_certificates': None, 'updated_at': '2025-01-02T06:52:41Z', 'user_data': '', 'user_id': 'bcb70a7299c441d0b3417cf68f723ed7', 'vm_state': 'building', 'id': '347b58d4-8284-4f34-9e18-3f935558ebaf', 'name': '测试完成订单的通知内容_1', 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': 'nova', 'project': Munch({'id': '8f788892a6b444e69c618031b66a1240', 'name': None, 'domain_id': None, 'domain_name': None})}), 'metadata': {'instance_type': '裸金属', 'image_id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'flavor_id': 'c6d9b737-6f78-41d7-8786-ee0949da40c1'}, 'tags': []}>



[2025-01-02 14:54:03,273: INFO/ForkPoolWorker-3] <OpenstackData:{'links': [{'rel': 'self', 'href': 'https://*************:8774/v2.1/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}, {'rel': 'bookmark', 'href': 'https://*************:8774/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}], 'access_ipv4': '', 'access_ipv6': '', 'addresses': {}, 'admin_password': None, 'attached_volumes': [], 'volumes': [], 'availability_zone': 'nova', 'block_device_mapping': None, 'config_drive': 'True', 'compute_host': 'jh-openstack-01-ironic-compute-container-711de48d', 'created_at': '2025-01-02T06:52:35Z', 'description': '同时查看创建状态ip数据', 'disk_config': 'AUTO', 'flavor_id': None, 'flavor': {'name': 'baremetal-4090', 'original_name': 'baremetal-4090', 'description': None, 'disk': 300, 'is_public': True, 'ram': 515072, 'vcpus': 144, 'swap': 0, 'ephemeral': 0, 'is_disabled': None, 'rxtx_factor': None, 'extra_specs': {'resources:CUSTOM_IRONIC_4090': '1', 'resources:VCPU': '0', 'resources:MEMORY_MB': '0', 'resources:DISK_GB': '0', 'capabilities:boot_option': 'local', 'flavor_type': '裸金属', 'gpu_type': '4090', 'gpu_count': '1', 'is_to_portal': '1', 'desc': '裸金属-4090'}, 'id': 'baremetal-4090', 'location': None}, 'has_config_drive': 'True', 'host_id': '493a97ef5976efa193cb49a25b55b33cf211f6d8308c192a74201af3', 'fault': None, 'host': None, 'host_status': 'UP', 'hostname': '1', 'hypervisor_hostname': '8134af37-2050-4dcf-9fc8-5ff886cd33e2', 'image_id': None, 'image': {'checksum': None, 'container_format': None, 'created_at': None, 'disk_format': None, 'is_hidden': None, 'is_protected': None, 'hash_algo': None, 'hash_value': None, 'min_disk': None, 'min_ram': None, 'name': None, 'owner': None, 'owner_id': None, 'properties': {'links': [{'rel': 'bookmark', 'href': 'https://*************:8774/images/a2483e38-3f70-47ec-82cd-53c7afe9091c'}]}, 'size': None, 'store': None, 'status': None, 'updated_at': None, 'virtual_size': None, 'visibility': None, 'file': None, 'locations': None, 'direct_url': None, 'url': None, 'metadata': None, 'architecture': None, 'hypervisor_type': None, 'instance_type_rxtx_factor': None, 'instance_uuid': None, 'needs_config_drive': None, 'kernel_id': None, 'os_distro': None, 'os_version': None, 'needs_secure_boot': None, 'os_shutdown_timeout': None, 'ramdisk_id': None, 'vm_mode': None, 'hw_cpu_sockets': None, 'hw_cpu_cores': None, 'hw_cpu_threads': None, 'hw_disk_bus': None, 'hw_cpu_policy': None, 'hw_cpu_thread_policy': None, 'hw_rng_model': None, 'hw_machine_type': None, 'hw_scsi_model': None, 'hw_serial_port_count': None, 'hw_video_model': None, 'hw_video_ram': None, 'hw_watchdog_action': None, 'os_command_line': None, 'hw_vif_model': None, 'is_hw_vif_multiqueue_enabled': None, 'is_hw_boot_menu_enabled': None, 'vmware_adaptertype': None, 'vmware_ostype': None, 'has_auto_disk_config': None, 'os_type': None, 'os_admin_user': None, 'hw_qemu_guest_agent': None, 'os_require_quiesce': None, 'schema': None, 'id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'location': None, 'tags': []}, 'instance_name': 'instance-000003c2', 'interface_ip': '', 'is_locked': False, 'kernel_id': '8264426d-290a-412c-aa08-8efcd2253d0b', 'key_name': 'xingzai', 'launch_index': 0, 'launched_at': None, 'locked_reason': None, 'max_count': None, 'min_count': None, 'networks': None, 'personality': None, 'pinned_availability_zone': None, 'power_state': 0, 'progress': 0, 'project_id': '8f788892a6b444e69c618031b66a1240', 'private_v4': '', 'private_v6': '', 'public_v4': '', 'public_v6': '', 'ramdisk_id': '4dc58528-af26-4fc4-b192-9394b9fb1906', 'reservation_id': 'r-0cejx6y4', 'root_device_name': '/dev/sda', 'scheduler_hints': None, 'security_groups': [{'name': 'default'}, {'name': 'default'}, {'name': 'default'}], 'server_groups': None, 'status': 'BUILD', 'task_state': 'spawning', 'terminated_at': None, 'trusted_image_certificates': None, 'updated_at': '2025-01-02T06:52:41Z', 'user_data': '', 'user_id': 'bcb70a7299c441d0b3417cf68f723ed7', 'vm_state': 'building', 'id': '347b58d4-8284-4f34-9e18-3f935558ebaf', 'name': '测试完成订单的通知内容_1', 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': 'nova', 'project': Munch({'id': '8f788892a6b444e69c618031b66a1240', 'name': None, 'domain_id': None, 'domain_name': None})}), 'metadata': {'instance_type': '裸金属', 'image_id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'flavor_id': 'c6d9b737-6f78-41d7-8786-ee0949da40c1'}, 'tags': []}>



[2025-01-02 14:55:01,486: INFO/ForkPoolWorker-3] <OpenstackData:{'links': [{'rel': 'self', 'href': 'https://*************:8774/v2.1/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}, {'rel': 'bookmark', 'href': 'https://*************:8774/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}], 'access_ipv4': '', 'access_ipv6': '', 'addresses': {}, 'admin_password': None, 'attached_volumes': [], 'volumes': [], 'availability_zone': 'nova', 'block_device_mapping': None, 'config_drive': 'True', 'compute_host': 'jh-openstack-01-ironic-compute-container-711de48d', 'created_at': '2025-01-02T06:52:35Z', 'description': '同时查看创建状态ip数据', 'disk_config': 'AUTO', 'flavor_id': None, 'flavor': {'name': 'baremetal-4090', 'original_name': 'baremetal-4090', 'description': None, 'disk': 300, 'is_public': True, 'ram': 515072, 'vcpus': 144, 'swap': 0, 'ephemeral': 0, 'is_disabled': None, 'rxtx_factor': None, 'extra_specs': {'resources:CUSTOM_IRONIC_4090': '1', 'resources:VCPU': '0', 'resources:MEMORY_MB': '0', 'resources:DISK_GB': '0', 'capabilities:boot_option': 'local', 'flavor_type': '裸金属', 'gpu_type': '4090', 'gpu_count': '1', 'is_to_portal': '1', 'desc': '裸金属-4090'}, 'id': 'baremetal-4090', 'location': None}, 'has_config_drive': 'True', 'host_id': '493a97ef5976efa193cb49a25b55b33cf211f6d8308c192a74201af3', 'fault': None, 'host': None, 'host_status': 'UP', 'hostname': '1', 'hypervisor_hostname': '8134af37-2050-4dcf-9fc8-5ff886cd33e2', 'image_id': None, 'image': {'checksum': None, 'container_format': None, 'created_at': None, 'disk_format': None, 'is_hidden': None, 'is_protected': None, 'hash_algo': None, 'hash_value': None, 'min_disk': None, 'min_ram': None, 'name': None, 'owner': None, 'owner_id': None, 'properties': {'links': [{'rel': 'bookmark', 'href': 'https://*************:8774/images/a2483e38-3f70-47ec-82cd-53c7afe9091c'}]}, 'size': None, 'store': None, 'status': None, 'updated_at': None, 'virtual_size': None, 'visibility': None, 'file': None, 'locations': None, 'direct_url': None, 'url': None, 'metadata': None, 'architecture': None, 'hypervisor_type': None, 'instance_type_rxtx_factor': None, 'instance_uuid': None, 'needs_config_drive': None, 'kernel_id': None, 'os_distro': None, 'os_version': None, 'needs_secure_boot': None, 'os_shutdown_timeout': None, 'ramdisk_id': None, 'vm_mode': None, 'hw_cpu_sockets': None, 'hw_cpu_cores': None, 'hw_cpu_threads': None, 'hw_disk_bus': None, 'hw_cpu_policy': None, 'hw_cpu_thread_policy': None, 'hw_rng_model': None, 'hw_machine_type': None, 'hw_scsi_model': None, 'hw_serial_port_count': None, 'hw_video_model': None, 'hw_video_ram': None, 'hw_watchdog_action': None, 'os_command_line': None, 'hw_vif_model': None, 'is_hw_vif_multiqueue_enabled': None, 'is_hw_boot_menu_enabled': None, 'vmware_adaptertype': None, 'vmware_ostype': None, 'has_auto_disk_config': None, 'os_type': None, 'os_admin_user': None, 'hw_qemu_guest_agent': None, 'os_require_quiesce': None, 'schema': None, 'id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'location': None, 'tags': []}, 'instance_name': 'instance-000003c2', 'interface_ip': '', 'is_locked': False, 'kernel_id': '8264426d-290a-412c-aa08-8efcd2253d0b', 'key_name': 'xingzai', 'launch_index': 0, 'launched_at': None, 'locked_reason': None, 'max_count': None, 'min_count': None, 'networks': None, 'personality': None, 'pinned_availability_zone': None, 'power_state': 0, 'progress': 0, 'project_id': '8f788892a6b444e69c618031b66a1240', 'private_v4': '', 'private_v6': '', 'public_v4': '', 'public_v6': '', 'ramdisk_id': '4dc58528-af26-4fc4-b192-9394b9fb1906', 'reservation_id': 'r-0cejx6y4', 'root_device_name': '/dev/sda', 'scheduler_hints': None, 'security_groups': [{'name': 'default'}, {'name': 'default'}, {'name': 'default'}], 'server_groups': None, 'status': 'BUILD', 'task_state': 'spawning', 'terminated_at': None, 'trusted_image_certificates': None, 'updated_at': '2025-01-02T06:52:41Z', 'user_data': '', 'user_id': 'bcb70a7299c441d0b3417cf68f723ed7', 'vm_state': 'building', 'id': '347b58d4-8284-4f34-9e18-3f935558ebaf', 'name': '测试完成订单的通知内容_1', 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': 'nova', 'project': Munch({'id': '8f788892a6b444e69c618031b66a1240', 'name': None, 'domain_id': None, 'domain_name': None})}), 'metadata': {'instance_type': '裸金属', 'image_id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'flavor_id': 'c6d9b737-6f78-41d7-8786-ee0949da40c1'}, 'tags': []}>


[2025-01-02 14:56:02,212: INFO/ForkPoolWorker-3] <OpenstackData:{'links': [{'rel': 'self', 'href': 'https://*************:8774/v2.1/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}, {'rel': 'bookmark', 'href': 'https://*************:8774/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}], 'access_ipv4': '', 'access_ipv6': '', 'addresses': {}, 'admin_password': None, 'attached_volumes': [], 'volumes': [], 'availability_zone': 'nova', 'block_device_mapping': None, 'config_drive': 'True', 'compute_host': 'jh-openstack-01-ironic-compute-container-711de48d', 'created_at': '2025-01-02T06:52:35Z', 'description': '同时查看创建状态ip数据', 'disk_config': 'AUTO', 'flavor_id': None, 'flavor': {'name': 'baremetal-4090', 'original_name': 'baremetal-4090', 'description': None, 'disk': 300, 'is_public': True, 'ram': 515072, 'vcpus': 144, 'swap': 0, 'ephemeral': 0, 'is_disabled': None, 'rxtx_factor': None, 'extra_specs': {'resources:CUSTOM_IRONIC_4090': '1', 'resources:VCPU': '0', 'resources:MEMORY_MB': '0', 'resources:DISK_GB': '0', 'capabilities:boot_option': 'local', 'flavor_type': '裸金属', 'gpu_type': '4090', 'gpu_count': '1', 'is_to_portal': '1', 'desc': '裸金属-4090'}, 'id': 'baremetal-4090', 'location': None}, 'has_config_drive': 'True', 'host_id': '493a97ef5976efa193cb49a25b55b33cf211f6d8308c192a74201af3', 'fault': None, 'host': None, 'host_status': 'UP', 'hostname': '1', 'hypervisor_hostname': '8134af37-2050-4dcf-9fc8-5ff886cd33e2', 'image_id': None, 'image': {'checksum': None, 'container_format': None, 'created_at': None, 'disk_format': None, 'is_hidden': None, 'is_protected': None, 'hash_algo': None, 'hash_value': None, 'min_disk': None, 'min_ram': None, 'name': None, 'owner': None, 'owner_id': None, 'properties': {'links': [{'rel': 'bookmark', 'href': 'https://*************:8774/images/a2483e38-3f70-47ec-82cd-53c7afe9091c'}]}, 'size': None, 'store': None, 'status': None, 'updated_at': None, 'virtual_size': None, 'visibility': None, 'file': None, 'locations': None, 'direct_url': None, 'url': None, 'metadata': None, 'architecture': None, 'hypervisor_type': None, 'instance_type_rxtx_factor': None, 'instance_uuid': None, 'needs_config_drive': None, 'kernel_id': None, 'os_distro': None, 'os_version': None, 'needs_secure_boot': None, 'os_shutdown_timeout': None, 'ramdisk_id': None, 'vm_mode': None, 'hw_cpu_sockets': None, 'hw_cpu_cores': None, 'hw_cpu_threads': None, 'hw_disk_bus': None, 'hw_cpu_policy': None, 'hw_cpu_thread_policy': None, 'hw_rng_model': None, 'hw_machine_type': None, 'hw_scsi_model': None, 'hw_serial_port_count': None, 'hw_video_model': None, 'hw_video_ram': None, 'hw_watchdog_action': None, 'os_command_line': None, 'hw_vif_model': None, 'is_hw_vif_multiqueue_enabled': None, 'is_hw_boot_menu_enabled': None, 'vmware_adaptertype': None, 'vmware_ostype': None, 'has_auto_disk_config': None, 'os_type': None, 'os_admin_user': None, 'hw_qemu_guest_agent': None, 'os_require_quiesce': None, 'schema': None, 'id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'location': None, 'tags': []}, 'instance_name': 'instance-000003c2', 'interface_ip': '', 'is_locked': False, 'kernel_id': '8264426d-290a-412c-aa08-8efcd2253d0b', 'key_name': 'xingzai', 'launch_index': 0, 'launched_at': None, 'locked_reason': None, 'max_count': None, 'min_count': None, 'networks': None, 'personality': None, 'pinned_availability_zone': None, 'power_state': 0, 'progress': 0, 'project_id': '8f788892a6b444e69c618031b66a1240', 'private_v4': '', 'private_v6': '', 'public_v4': '', 'public_v6': '', 'ramdisk_id': '4dc58528-af26-4fc4-b192-9394b9fb1906', 'reservation_id': 'r-0cejx6y4', 'root_device_name': '/dev/sda', 'scheduler_hints': None, 'security_groups': [{'name': 'default'}, {'name': 'default'}, {'name': 'default'}], 'server_groups': None, 'status': 'BUILD', 'task_state': 'spawning', 'terminated_at': None, 'trusted_image_certificates': None, 'updated_at': '2025-01-02T06:52:41Z', 'user_data': '', 'user_id': 'bcb70a7299c441d0b3417cf68f723ed7', 'vm_state': 'building', 'id': '347b58d4-8284-4f34-9e18-3f935558ebaf', 'name': '测试完成订单的通知内容_1', 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': 'nova', 'project': Munch({'id': '8f788892a6b444e69c618031b66a1240', 'name': None, 'domain_id': None, 'domain_name': None})}), 'metadata': {'instance_type': '裸金属', 'image_id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'flavor_id': 'c6d9b737-6f78-41d7-8786-ee0949da40c1'}, 'tags': []}>


[2025-01-02 14:57:01,629: INFO/ForkPoolWorker-3] <OpenstackData:{'links': [{'rel': 'self', 'href': 'https://*************:8774/v2.1/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}, {'rel': 'bookmark', 'href': 'https://*************:8774/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}], 'access_ipv4': '', 'access_ipv6': '', 'addresses': {}, 'admin_password': None, 'attached_volumes': [], 'volumes': [], 'availability_zone': 'nova', 'block_device_mapping': None, 'config_drive': 'True', 'compute_host': 'jh-openstack-01-ironic-compute-container-711de48d', 'created_at': '2025-01-02T06:52:35Z', 'description': '同时查看创建状态ip数据', 'disk_config': 'AUTO', 'flavor_id': None, 'flavor': {'name': 'baremetal-4090', 'original_name': 'baremetal-4090', 'description': None, 'disk': 300, 'is_public': True, 'ram': 515072, 'vcpus': 144, 'swap': 0, 'ephemeral': 0, 'is_disabled': None, 'rxtx_factor': None, 'extra_specs': {'resources:CUSTOM_IRONIC_4090': '1', 'resources:VCPU': '0', 'resources:MEMORY_MB': '0', 'resources:DISK_GB': '0', 'capabilities:boot_option': 'local', 'flavor_type': '裸金属', 'gpu_type': '4090', 'gpu_count': '1', 'is_to_portal': '1', 'desc': '裸金属-4090'}, 'id': 'baremetal-4090', 'location': None}, 'has_config_drive': 'True', 'host_id': '493a97ef5976efa193cb49a25b55b33cf211f6d8308c192a74201af3', 'fault': None, 'host': None, 'host_status': 'UP', 'hostname': '1', 'hypervisor_hostname': '8134af37-2050-4dcf-9fc8-5ff886cd33e2', 'image_id': None, 'image': {'checksum': None, 'container_format': None, 'created_at': None, 'disk_format': None, 'is_hidden': None, 'is_protected': None, 'hash_algo': None, 'hash_value': None, 'min_disk': None, 'min_ram': None, 'name': None, 'owner': None, 'owner_id': None, 'properties': {'links': [{'rel': 'bookmark', 'href': 'https://*************:8774/images/a2483e38-3f70-47ec-82cd-53c7afe9091c'}]}, 'size': None, 'store': None, 'status': None, 'updated_at': None, 'virtual_size': None, 'visibility': None, 'file': None, 'locations': None, 'direct_url': None, 'url': None, 'metadata': None, 'architecture': None, 'hypervisor_type': None, 'instance_type_rxtx_factor': None, 'instance_uuid': None, 'needs_config_drive': None, 'kernel_id': None, 'os_distro': None, 'os_version': None, 'needs_secure_boot': None, 'os_shutdown_timeout': None, 'ramdisk_id': None, 'vm_mode': None, 'hw_cpu_sockets': None, 'hw_cpu_cores': None, 'hw_cpu_threads': None, 'hw_disk_bus': None, 'hw_cpu_policy': None, 'hw_cpu_thread_policy': None, 'hw_rng_model': None, 'hw_machine_type': None, 'hw_scsi_model': None, 'hw_serial_port_count': None, 'hw_video_model': None, 'hw_video_ram': None, 'hw_watchdog_action': None, 'os_command_line': None, 'hw_vif_model': None, 'is_hw_vif_multiqueue_enabled': None, 'is_hw_boot_menu_enabled': None, 'vmware_adaptertype': None, 'vmware_ostype': None, 'has_auto_disk_config': None, 'os_type': None, 'os_admin_user': None, 'hw_qemu_guest_agent': None, 'os_require_quiesce': None, 'schema': None, 'id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'location': None, 'tags': []}, 'instance_name': 'instance-000003c2', 'interface_ip': '', 'is_locked': False, 'kernel_id': '8264426d-290a-412c-aa08-8efcd2253d0b', 'key_name': 'xingzai', 'launch_index': 0, 'launched_at': None, 'locked_reason': None, 'max_count': None, 'min_count': None, 'networks': None, 'personality': None, 'pinned_availability_zone': None, 'power_state': 0, 'progress': 0, 'project_id': '8f788892a6b444e69c618031b66a1240', 'private_v4': '', 'private_v6': '', 'public_v4': '', 'public_v6': '', 'ramdisk_id': '4dc58528-af26-4fc4-b192-9394b9fb1906', 'reservation_id': 'r-0cejx6y4', 'root_device_name': '/dev/sda', 'scheduler_hints': None, 'security_groups': [{'name': 'default'}, {'name': 'default'}, {'name': 'default'}], 'server_groups': None, 'status': 'BUILD', 'task_state': 'spawning', 'terminated_at': None, 'trusted_image_certificates': None, 'updated_at': '2025-01-02T06:52:41Z', 'user_data': '', 'user_id': 'bcb70a7299c441d0b3417cf68f723ed7', 'vm_state': 'building', 'id': '347b58d4-8284-4f34-9e18-3f935558ebaf', 'name': '测试完成订单的通知内容_1', 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': 'nova', 'project': Munch({'id': '8f788892a6b444e69c618031b66a1240', 'name': None, 'domain_id': None, 'domain_name': None})}), 'metadata': {'instance_type': '裸金属', 'image_id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'flavor_id': 'c6d9b737-6f78-41d7-8786-ee0949da40c1'}, 'tags': []}>


[2025-01-02 15:02:01,731: INFO/ForkPoolWorker-3] <OpenstackData:{'links': [{'rel': 'self', 'href': 'https://*************:8774/v2.1/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}, {'rel': 'bookmark', 'href': 'https://*************:8774/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}], 'access_ipv4': '', 'access_ipv6': '', 'addresses': {}, 'admin_password': None, 'attached_volumes': [], 'volumes': [], 'availability_zone': 'nova', 'block_device_mapping': None, 'config_drive': 'True', 'compute_host': 'jh-openstack-01-ironic-compute-container-711de48d', 'created_at': '2025-01-02T06:52:35Z', 'description': '同时查看创建状态ip数据', 'disk_config': 'AUTO', 'flavor_id': None, 'flavor': {'name': 'baremetal-4090', 'original_name': 'baremetal-4090', 'description': None, 'disk': 300, 'is_public': True, 'ram': 515072, 'vcpus': 144, 'swap': 0, 'ephemeral': 0, 'is_disabled': None, 'rxtx_factor': None, 'extra_specs': {'resources:CUSTOM_IRONIC_4090': '1', 'resources:VCPU': '0', 'resources:MEMORY_MB': '0', 'resources:DISK_GB': '0', 'capabilities:boot_option': 'local', 'flavor_type': '裸金属', 'gpu_type': '4090', 'gpu_count': '1', 'is_to_portal': '1', 'desc': '裸金属-4090'}, 'id': 'baremetal-4090', 'location': None}, 'has_config_drive': 'True', 'host_id': '493a97ef5976efa193cb49a25b55b33cf211f6d8308c192a74201af3', 'fault': None, 'host': None, 'host_status': 'UP', 'hostname': '1', 'hypervisor_hostname': '8134af37-2050-4dcf-9fc8-5ff886cd33e2', 'image_id': None, 'image': {'checksum': None, 'container_format': None, 'created_at': None, 'disk_format': None, 'is_hidden': None, 'is_protected': None, 'hash_algo': None, 'hash_value': None, 'min_disk': None, 'min_ram': None, 'name': None, 'owner': None, 'owner_id': None, 'properties': {'links': [{'rel': 'bookmark', 'href': 'https://*************:8774/images/a2483e38-3f70-47ec-82cd-53c7afe9091c'}]}, 'size': None, 'store': None, 'status': None, 'updated_at': None, 'virtual_size': None, 'visibility': None, 'file': None, 'locations': None, 'direct_url': None, 'url': None, 'metadata': None, 'architecture': None, 'hypervisor_type': None, 'instance_type_rxtx_factor': None, 'instance_uuid': None, 'needs_config_drive': None, 'kernel_id': None, 'os_distro': None, 'os_version': None, 'needs_secure_boot': None, 'os_shutdown_timeout': None, 'ramdisk_id': None, 'vm_mode': None, 'hw_cpu_sockets': None, 'hw_cpu_cores': None, 'hw_cpu_threads': None, 'hw_disk_bus': None, 'hw_cpu_policy': None, 'hw_cpu_thread_policy': None, 'hw_rng_model': None, 'hw_machine_type': None, 'hw_scsi_model': None, 'hw_serial_port_count': None, 'hw_video_model': None, 'hw_video_ram': None, 'hw_watchdog_action': None, 'os_command_line': None, 'hw_vif_model': None, 'is_hw_vif_multiqueue_enabled': None, 'is_hw_boot_menu_enabled': None, 'vmware_adaptertype': None, 'vmware_ostype': None, 'has_auto_disk_config': None, 'os_type': None, 'os_admin_user': None, 'hw_qemu_guest_agent': None, 'os_require_quiesce': None, 'schema': None, 'id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'location': None, 'tags': []}, 'instance_name': 'instance-000003c2', 'interface_ip': '', 'is_locked': False, 'kernel_id': '8264426d-290a-412c-aa08-8efcd2253d0b', 'key_name': 'xingzai', 'launch_index': 0, 'launched_at': None, 'locked_reason': None, 'max_count': None, 'min_count': None, 'networks': None, 'personality': None, 'pinned_availability_zone': None, 'power_state': 0, 'progress': 0, 'project_id': '8f788892a6b444e69c618031b66a1240', 'private_v4': '', 'private_v6': '', 'public_v4': '', 'public_v6': '', 'ramdisk_id': '4dc58528-af26-4fc4-b192-9394b9fb1906', 'reservation_id': 'r-0cejx6y4', 'root_device_name': '/dev/sda', 'scheduler_hints': None, 'security_groups': [{'name': 'default'}], 'server_groups': None, 'status': 'BUILD', 'task_state': 'spawning', 'terminated_at': None, 'trusted_image_certificates': None, 'updated_at': '2025-01-02T06:52:41Z', 'user_data': '', 'user_id': 'bcb70a7299c441d0b3417cf68f723ed7', 'vm_state': 'building', 'id': '347b58d4-8284-4f34-9e18-3f935558ebaf', 'name': '测试完成订单的通知内容_1', 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': 'nova', 'project': Munch({'id': '8f788892a6b444e69c618031b66a1240', 'name': None, 'domain_id': None, 'domain_name': None})}), 'metadata': {'instance_type': '裸金属', 'image_id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'flavor_id': 'c6d9b737-6f78-41d7-8786-ee0949da40c1'}, 'tags': []}>


[2025-01-02 15:03:01,889: INFO/ForkPoolWorker-3] <OpenstackData:{'links': [{'rel': 'self', 'href': 'https://*************:8774/v2.1/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}, {'rel': 'bookmark', 'href': 'https://*************:8774/servers/347b58d4-8284-4f34-9e18-3f935558ebaf'}], 'access_ipv4': '', 'access_ipv6': '', 'addresses': {'Vlanif2002': [{'version': 4, 'addr': '*************', 'OS-EXT-IPS:type': 'fixed', 'OS-EXT-IPS-MAC:mac_addr': 'fa:16:3e:dc:af:7d'}]}, 'admin_password': None, 'attached_volumes': [], 'volumes': [], 'availability_zone': 'nova', 'block_device_mapping': None, 'config_drive': 'True', 'compute_host': 'jh-openstack-01-ironic-compute-container-711de48d', 'created_at': '2025-01-02T06:52:35Z', 'description': '同时查看创建状态ip数据', 'disk_config': 'AUTO', 'flavor_id': None, 'flavor': {'name': 'baremetal-4090', 'original_name': 'baremetal-4090', 'description': None, 'disk': 300, 'is_public': True, 'ram': 515072, 'vcpus': 144, 'swap': 0, 'ephemeral': 0, 'is_disabled': None, 'rxtx_factor': None, 'extra_specs': {'resources:CUSTOM_IRONIC_4090': '1', 'resources:VCPU': '0', 'resources:MEMORY_MB': '0', 'resources:DISK_GB': '0', 'capabilities:boot_option': 'local', 'flavor_type': '裸金属', 'gpu_type': '4090', 'gpu_count': '1', 'is_to_portal': '1', 'desc': '裸金属-4090'}, 'id': 'baremetal-4090', 'location': None}, 'has_config_drive': 'True', 'host_id': '493a97ef5976efa193cb49a25b55b33cf211f6d8308c192a74201af3', 'fault': None, 'host': None, 'host_status': 'UP', 'hostname': '1', 'hypervisor_hostname': '8134af37-2050-4dcf-9fc8-5ff886cd33e2', 'image_id': None, 'image': {'checksum': None, 'container_format': None, 'created_at': None, 'disk_format': None, 'is_hidden': None, 'is_protected': None, 'hash_algo': None, 'hash_value': None, 'min_disk': None, 'min_ram': None, 'name': None, 'owner': None, 'owner_id': None, 'properties': {'links': [{'rel': 'bookmark', 'href': 'https://*************:8774/images/a2483e38-3f70-47ec-82cd-53c7afe9091c'}]}, 'size': None, 'store': None, 'status': None, 'updated_at': None, 'virtual_size': None, 'visibility': None, 'file': None, 'locations': None, 'direct_url': None, 'url': None, 'metadata': None, 'architecture': None, 'hypervisor_type': None, 'instance_type_rxtx_factor': None, 'instance_uuid': None, 'needs_config_drive': None, 'kernel_id': None, 'os_distro': None, 'os_version': None, 'needs_secure_boot': None, 'os_shutdown_timeout': None, 'ramdisk_id': None, 'vm_mode': None, 'hw_cpu_sockets': None, 'hw_cpu_cores': None, 'hw_cpu_threads': None, 'hw_disk_bus': None, 'hw_cpu_policy': None, 'hw_cpu_thread_policy': None, 'hw_rng_model': None, 'hw_machine_type': None, 'hw_scsi_model': None, 'hw_serial_port_count': None, 'hw_video_model': None, 'hw_video_ram': None, 'hw_watchdog_action': None, 'os_command_line': None, 'hw_vif_model': None, 'is_hw_vif_multiqueue_enabled': None, 'is_hw_boot_menu_enabled': None, 'vmware_adaptertype': None, 'vmware_ostype': None, 'has_auto_disk_config': None, 'os_type': None, 'os_admin_user': None, 'hw_qemu_guest_agent': None, 'os_require_quiesce': None, 'schema': None, 'id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'location': None, 'tags': []}, 'instance_name': 'instance-000003c2', 'interface_ip': '', 'is_locked': False, 'kernel_id': '8264426d-290a-412c-aa08-8efcd2253d0b', 'key_name': 'xingzai', 'launch_index': 0, 'launched_at': '2025-01-02T07:02:36.000000', 'locked_reason': None, 'max_count': None, 'min_count': None, 'networks': None, 'personality': None, 'pinned_availability_zone': None, 'power_state': 1, 'progress': 0, 'project_id': '8f788892a6b444e69c618031b66a1240', 'private_v4': '', 'private_v6': '', 'public_v4': '', 'public_v6': '', 'ramdisk_id': '4dc58528-af26-4fc4-b192-9394b9fb1906', 'reservation_id': 'r-0cejx6y4', 'root_device_name': '/dev/sda', 'scheduler_hints': None, 'security_groups': [{'name': 'default'}], 'server_groups': None, 'status': 'ACTIVE', 'task_state': None, 'terminated_at': None, 'trusted_image_certificates': None, 'updated_at': '2025-01-02T07:02:36Z', 'user_data': '', 'user_id': 'bcb70a7299c441d0b3417cf68f723ed7', 'vm_state': 'active', 'id': '347b58d4-8284-4f34-9e18-3f935558ebaf', 'name': '测试完成订单的通知内容_1', 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': 'nova', 'project': Munch({'id': '8f788892a6b444e69c618031b66a1240', 'name': None, 'domain_id': None, 'domain_name': None})}), 'metadata': {'instance_type': '裸金属', 'image_id': 'a2483e38-3f70-47ec-82cd-53c7afe9091c', 'flavor_id': 'c6d9b737-6f78-41d7-8786-ee0949da40c1'}, 'tags': []}>

