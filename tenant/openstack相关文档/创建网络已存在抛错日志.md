# Openstack 创建网络已存在抛错日志
```shell

D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\venv\Scripts\python.exe D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\tenant\utils\sdk\production_create_baremetal_server.py 
VmkF9Wou5k5CpsIe47g7nk7_MwFSEkxmHbb9du4y9V4=
provider_params: {'provider:network_type': 'vlan', 'provider:physical_network': 'vlan', 'provider:segmentation_id': 2002}
Traceback (most recent call last):
  File "D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\tenant\utils\sdk\production_create_baremetal_server.py", line 770, in create_vlan_network
    network = self.conn.network.create_network(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\venv\Lib\site-packages\openstack\network\v2\_proxy.py", line 2357, in create_network
    return self._create(_network.Network, **attrs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\venv\Lib\site-packages\openstack\proxy.py", line 643, in _create
    return res.create(self, base_path=base_path)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\venv\Lib\site-packages\openstack\resource.py", line 1533, in create
    self._translate_response(response, **response_kwargs)
  File "D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\venv\Lib\site-packages\openstack\resource.py", line 1285, in _translate_response
    exceptions.raise_from_response(response, error_message=error_message)
  File "D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\venv\Lib\site-packages\openstack\exceptions.py", line 244, in raise_from_response
    raise cls(
openstack.exceptions.ConflictException: ConflictException: 409: Client Error for url: https://10.10.202.201:9696/v2.0/networks, Unable to create the network. The VLAN 2002 on physical network vlan is in use.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\tenant\utils\sdk\production_create_baremetal_server.py", line 801, in create_vlan_network
    self.conn.network.delete_network(network.id)
                                     ^^^^^^^
UnboundLocalError: cannot access local variable 'network' where it is not associated with a value

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\tenant\utils\sdk\production_create_baremetal_server.py", line 851, in <module>
    network, subnet, msg = admin_client.create_vlan_network(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\星临办公\code-projects\projects\nova-django-admin\nova-admin\tenant\utils\sdk\production_create_baremetal_server.py", line 806, in create_vlan_network
    logger.error(f"Failed to delete network {network.id}: {delete_error}")
                                             ^^^^^^^
UnboundLocalError: cannot access local variable 'network' where it is not associated with a value

进程已结束，退出代码为 1

```