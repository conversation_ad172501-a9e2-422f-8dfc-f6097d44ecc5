# 各类资源标签说明

## 规格配置说明

| 标签键          | 标签描述    | 标签值类型 | 标签值                                  | 示例       | 是否属于自定义 | 是否必填    | 作用                   |
|--------------|---------|-------|--------------------------------------|----------|---------|---------|----------------------|
| flavor_type  | 规格类型    | 枚举    | `云主机`、`裸金属`                          | 裸金属      | 是       | 是       | 过滤规格                 |
| gpu_type     | GPU类型   | 枚举    | `4090`、`3090`、`H100`、`H100`、`no-gpu` | 4090     | 是       | 否（推荐添加） | 过滤规格                 |
| gpu_count    | gpu数量   | 整型    | `1`、`2`、`3`、`4`、`5`、`6`、`...`        | 1        | 是       | 否（推荐添加） | 过滤规格                 |  
| is_to_portal | 是否在前端展示 | 布尔值   | `1`、`0`                              | 1        | 是       | 是       | 过滤规格（后端控制是否在创建裸机时展示） |
| desc         | 规格描述    | 字符串   | `任意字符串(最大63)`                        | 裸金属-3090 | 是       | 是       | 前端展示主机规格、规格描述        |

## 镜像配置说明

| 标签键           | 标签描述               | 标签值类型 | 标签值                                                                                                   | 示例       | 是否属于自定义 | 是否必填    | 作用                   |
|---------------|--------------------|-------|-------------------------------------------------------------------------------------------------------|----------|---------|---------|----------------------|
| os_type       | 镜像内系统类型            | 枚举    | `linux`、`windows`                                                                                     | linux    | 否       | 否（推荐添加） | 过滤镜像                 |
| system_type   | 镜像内linux系统类型       | 枚举    | `CentOS`、`Ubuntu`、`Fedora`、`Windows` 、`Debian`、`CoreOS`、`Arch`、`Freebsd`、`OpenEulerOS`、`OpenAnolisOS` | 4090     | 是       | 否（推荐添加） | 过滤镜像                 |
| major_version | 镜像内linux系统大版本      | 字符串   | 任意字符串(最大31)                                                                                           | 1        | 是       | 否（推荐添加） | 过滤镜像                 |  
| minor_version | 镜像内linux系统小版本      | 字符串   | 任意字符串(最大31)                                                                                           | 1        | 是       | 否（推荐添加） | 过滤镜像                 |
| provider      | 镜像内linux系统是否为国产化系统 | 整型    | `国产化`、`非国产化`                                                                                          | 1        | 是       | 否（推荐添加） | 过滤镜像                 |
| image_type    | 镜像类型               | 枚举    | `云主机`、`裸金属`                                                                                           | 1        | 是       | 是       | 过滤镜像                 |
| is_to_portal  | 是否在前端展示            | 布尔值   | `1`、`0`                                                                                               | 1        | 是       | 是       | 过滤镜像（后端控制是否在创建裸机时展示） |
| desc          | 镜像描述               | 字符串   | `任意字符串(最大63)`                                                                                         | 裸金属-3090 | 是       | 是       | 前端展示主机镜像、镜像描述        |

## 主机配置说明

为什么要在主机元数据中设置 `flavor_id`、`image_id`标签？
解答：

- 在Openstack api中，虚拟云主机数据可正常获取对应的镜像及规格信息;<span style="color: red">
  裸金属机器无法正常获取对应的数据</span>。
- <span style="color: red">通过Chaos平台创建的服务器无需另外配置对应的元数据</span>。

| 标签键           | 标签描述         | 标签值类型 | 标签值                                    | 示例                                   | 是否属于自定义 | 是否必填 | 作用        |
|---------------|--------------|-------|----------------------------------------|--------------------------------------|---------|------|-----------|
| instance_type | 实例类型         | 枚举    | `云主机`、`裸金属`、`GPU云主机`                   | 裸金属                                  | 是       | 是    | 前端展示、过滤主机 |
| image_id      | 创建主机时对应的镜像ID | 字符串   | `d9c24a94-f7c8-4fd5-a07b-aa7aa3cae421` | d9c24a94-f7c8-4fd5-a07b-aa7aa3cae421 | 是       | 是    | 前端展示      |
| flavor_id     | 创建主机时对应的规格ID | 字符串   | `d9c24a94-f7c8-4fd5-a07b-aa7aa3cae421` | d9c24a94-f7c8-4fd5-a07b-aa7aa3cae421 | 是       | 是    | 前端展示      |
