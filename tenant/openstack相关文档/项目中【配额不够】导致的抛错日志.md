# 工单执行调度日志
```shell
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/code/ticket/views/ticket.py", line 171, in approval_ticket_task
    self.create_ticket_and_task(request_data)
  File "/code/ticket/views/ticket.py", line 156, in create_ticket_and_task
    sync_server_to_openstack(form_data=form_data)
  File "/code/ticket/serializers/task.py", line 41, in sync_server_to_openstack
    response = CreateBaremetalServerView.create_baremetal_server(form_data)
  File "/code/tenant/serializers/tenant_openstack_server.py", line 141, in create_baremetal_server
    results = admin_client.create_multi_baremetal_server(**create_baremetal_server_params)
  File "/code/tenant/utils/sdk/openstack_client.py", line 448, in create_multi_baremetal_server
    vm = self.create_baremetal_server(
  File "/code/tenant/utils/sdk/openstack_client.py", line 410, in create_baremetal_server
    vm = new_conn.compute.create_server(**create_params)
  File "/usr/local/lib/python3.10/site-packages/openstack/compute/v2/_proxy.py", line 727, in create_server
    return self._create(_server.Server, **attrs)
openstack.exceptions.ForbiddenException: ForbiddenException: 403: Client Error for url: https://10.10.202.201:8774/v2.1/servers, Quota exceeded for ram: Requested 515072, but already used 4635648 of 5119998 ram
```