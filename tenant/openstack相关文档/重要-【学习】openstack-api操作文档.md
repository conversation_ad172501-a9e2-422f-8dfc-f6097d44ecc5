# OpenstackSDK Client 请求操作文档
## 重要说明
- <span style="color: red">请谨慎使用`创建``更新`及`删除`操作</span>
- <span style="color: red">请谨慎使用`创建``更新`及`删除`操作</span>
- <span style="color: red">请谨慎使用`创建``更新`及`删除`操作</span>

----------------------------------------------------------------

- <span style="color: red">在【生产环境】中，请【更】谨慎使用`创建``更新`及`删除`操作</span>
- <span style="color: red">在【生产环境】中，请【更】谨慎使用`创建``更新`及`删除`操作</span>
- <span style="color: red">在【生产环境】中，请【更】谨慎使用`创建``更新`及`删除`操作</span>

## 配置文件
> 可先查看官方文档，不懂可以再问我。
- [Openstack官方文档](https://docs.openstack.org/zh_CN/api-quick-start/)

## Python ChaosAdminOpenstackClient 接口测试
> 此为部分功能的测试，非全部功能的测试。
```python
import unittest
from tenant.utils.sdk.openstack_client import OpenstackAdminClient

import os
from django.conf import settings
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()


class TestOpenStackAdminClient(unittest.TestCase):

    def setUp(self):
        self.admin_client = OpenstackAdminClient(debug=True)
        self.test_project_name = 'test-project'
        self.test_project_description = 'Test Project Description'

    def test_get_all_projects(self):
        projects = self.admin_client.get_all_projects()
        self.assertIsInstance(projects, list)
        for project in projects:
            self.assertIsNotNone(project.id)
            self.assertIsNotNone(project.name)

    def test_create_and_delete_project(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        self.assertIsNotNone(project.id)
        self.assertEqual(project.name, self.test_project_name)
        self.assertEqual(project.description, self.test_project_description)

        deleted_project = self.admin_client.delete_project(project.id)
        self.assertIsNone(deleted_project)

    def test_update_project(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        new_name = 'updated-test-project'
        new_description = 'Updated Test Project Description'
        updated_project = self.admin_client.update_project(project.id, name=new_name, description=new_description)
        self.assertEqual(updated_project.name, new_name)
        self.assertEqual(updated_project.description, new_description)

        self.admin_client.delete_project(project.id)

    def test_assign_project_to_admin(self):
        # 创建测试项目
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        self.assertIsNotNone(project.id)

        # 添加 admin 用户到此项目
        result = self.admin_client.assign_project_to_admin(project.id,)
        self.assertIsNone(result)

    def test_update_project_compute_quotas(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        compute_quotas = {
            'cores': 20,
            'instances': 10,
            'ram': 20480  # 单位为 MB
        }
        updated_quotas = self.admin_client.update_project_compute_quotas_set(project.id, **compute_quotas)
        self.assertIsNotNone(updated_quotas)

        self.admin_client.delete_project(project.id)

    def test_update_project_network_quotas(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        network_quotas = {
            'network': 10,
            'subnet': 20,
            'port': 50,
            'router': 5,
            'floatingip': 20,
            'security_group': 20,
            'security_group_rule': 100
        }
        updated_quotas = self.admin_client.update_project_network_quotas_set(project.id, **network_quotas)
        self.assertIsNotNone(updated_quotas)

        self.admin_client.delete_project(project.id)

    def test_update_project_volume_quotas(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        volume_quotas = {
            'volumes': 10,
            'gigabytes': 1000  # 单位为 GB
        }
        updated_quotas = self.admin_client.update_project_volume_quotas_set(project.id, **volume_quotas)
        self.assertIsNotNone(updated_quotas)

        self.admin_client.delete_project(project.id)

    def test_update_project_block_storage_quotas(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        block_storage_quotas = {
            'volumes': 10,
            'gigabytes': 1000  # 单位为 GB
        }
        updated_quotas = self.admin_client.update_project_block_storage_quotas_set(project.id, **block_storage_quotas)
        self.assertIsNotNone(updated_quotas)

        self.admin_client.delete_project(project.id)

    def tearDown(self):
        # 清理测试项目
        projects = self.admin_client.get_all_projects()
        for project in projects:
            if project.name == self.test_project_name:
                self.admin_client.delete_project(project.id)


if __name__ == "__main__":
    unittest.main()


```

## Python ChaosAdminOpenstackClient
```python
import json

from openstack import connect
from openstack import enable_logging
from openstack.exceptions import ResourceFailure
from django.conf import settings
from application.logger import logger


class OpenstackAdminClient(object):
    """
    此OpenStack客户端仅适配于以admin用户来管理所有项目、用户、资源的情况,禁用域的概念
    """

    def __init__(self, debug=False, auth_type='v3applicationcredential', node='金华'):
        conf_node = getattr(settings, 'OPENSTACK_NODE', '金华')
        if conf_node != node:
            self.node = conf_node
        else:
            self.node = node
        node_settings = self.get_openstack_settings()
        if node_settings.get('AUTH_TYPE'):
            auth_type = node_settings['AUTH_TYPE'].rstrip()
        self.auth_url = node_settings['AUTH_URL']
        self.region_name = node_settings['REGION_NAME']
        self.project_name = node_settings['PROJECT_NAME']
        self.username = node_settings['USERNAME']
        self.password = node_settings['PASSWORD']
        self.user_domain_name = node_settings['USER_DOMAIN_NAME']
        self.project_domain_name = node_settings['PROJECT_DOMAIN_NAME']
        self.auth_type = auth_type
        self.openstack_application_credential_id = node_settings['APPLICATION_CREDENTIAL_ID']
        self.openstack_application_credential_secret = node_settings['APPLICATION_CREDENTIAL_SECRET']
        self.admin_user_id = node_settings['ADMIN_USER_ID']
        self.admin_role_id = node_settings['ADMIN_ROLE_ID']

        if not debug:
            enable_logging(debug=False)
        self.conn = self.get_conn()

    def get_openstack_settings(self):
        """Return the openstack settings"""
        from tenant.models import TenantOpenstackSetting
        node_settings = TenantOpenstackSetting.get_op_settings(node=self.node)
        return node_settings

    def get_conn(self):
        if self.auth_type == 'v3applicationcredential':
            conn = connect(
                region_name=self.region_name,
                auth_type=self.auth_type,
                auth_url=self.auth_url,
                application_credential_id=self.openstack_application_credential_id,
                application_credential_secret=self.openstack_application_credential_secret,

                verify=False  # 关闭SSL验证
            )
            return conn
        # password 认证
        auth = {
            'auth_url': self.auth_url,
            'project_name': self.project_name,
            'username': self.username,
            'password': self.password,
            'user_domain_name': self.user_domain_name,
            'project_domain_name': self.project_domain_name
        }
        conn = connect(
            region_name=self.region_name,
            auth=auth,
            verify=False  # 关闭SSL验证
        )
        return conn

    def get_project_scope_conn(self, project_id):
        auth = {
            'auth_url': self.auth_url,
            'project_id': project_id,
            'username': self.username,
            'password': self.password,
            'user_domain_name': self.user_domain_name,
            'project_domain_name': self.project_domain_name
        }
        conn = connect(
            region_name=self.region_name,
            auth=auth,
            verify=False  # 关闭SSL验证
        )
        return conn

    def get_all_projects(self):
        """
        获取此账号下所有项目列表
        :return: server list
        """
        projects = self.conn.identity.projects()
        logger.info('get_all_projects Response: %s' % str(projects))
        return list(projects)

    def create_project(self, name, description):
        """
        创建项目
        :param name: 项目名称
        :param description: 项目描述
        :return:
        """
        project = self.conn.identity.create_project(
            name=name,
            description=description
        )
        logger.info('create_project: Response %s' % str(project))
        return project

    def delete_project(self, project_id):
        """
        删除项目
        :param project_id: 项目ID
        :return: project
        """
        project = self.conn.identity.delete_project(project_id)
        logger.info('delete_project Response: %s' % str(project))
        return project

    def update_project(self, project_id, name=None, description=None):
        """
        更新项目
        :param project_id: 项目ID
        :param name: 新名称
        :param description: 新描述
        :return: project
        """
        project = self.conn.identity.update_project(project_id, name=name, description=description)
        logger.info('update_project Response: %s' % str(project))
        return project

    def get_admin_user_id(self):
        admin_user_id = self.admin_user_id
        if not admin_user_id:
            admin_user = self.conn.identity.find_user('admin', domain_id=self.user_domain_name)
            admin_user_id = admin_user.id
        return admin_user_id

    def get_admin_role_id(self):
        admin_role_id = self.admin_role_id
        if not admin_role_id:
            admin_role = self.conn.identity.find_role('admin')
            admin_role_id = admin_role.id
        return admin_role_id

    def assign_project_to_admin(self, project_id, ):
        admin_user_id = self.get_admin_user_id()
        admin_role_id = self.get_admin_role_id()
        res = self.conn.identity.assign_project_role_to_user(
            project=project_id,
            user=admin_user_id,
            role=admin_role_id
        )
        logger.info('assign_project_to_admin Response: %s', str(res))
        return res

    def update_project_compute_quotas_set(self, project_id, **compute_quotas):
        """
        更新项目计算配额
        :param project_id: 项目ID
        :param compute_quotas: 计算配额
        :return:
        """
        res = self.conn.compute.update_quota_set(project_id, **compute_quotas)
        logger.info('update_project_compute_quotas_set Response: %s' % str(res))
        return res

    def update_project_network_quotas_set(self, project_id, **network_quotas):
        """
        更新项目网络配额(暂不使用)
        :param project_id: 项目ID
        :param network_quotas: 网络配额
        :return:
        """
        res = self.conn.network.update_quota(project_id, **network_quotas)
        logger.info('update_project_network_quotas_set Response: %s' % str(res))
        return res

    def update_project_volume_quotas_set(self, project_id, **volume_quotas):
        """
        更新项目存储配额(暂不使用)
        :param project_id: 项目ID
        :param volume_quotas: 存储配额
        :return:
        """
        res = self.conn.volume.update_quota_set(project_id, **volume_quotas)
        return res

    def update_project_block_storage_quotas_set(self, project_id, **block_storage_quotas):
        """
        更新项目存储配额(暂不使用)
        :param project_id: 项目ID
        :param block_storage_quotas: 块存储配额
        :return:
        """
        res = self.conn.block_storage.update_quota_set(project_id, **block_storage_quotas)
        return res

    def get_all_flavors(self):
        """
        获取所有可用Flavor
        :return: flavor list
        """
        flavors = self.conn.compute.flavors()
        return list(flavors)

    def get_flavor_detail(self, flavor_id):
        """
        获取Flavor详情
        :param flavor_id: Flavor ID
        :return: flavor detail
        :raise ValueError: If flavor ID is not provided.
        """
        if not flavor_id:
            raise ValueError("Flavor ID is required.")
        flavor = self.conn.compute.get_flavor(flavor_id)
        return dict(flavor)

    def get_all_images(self):
        """
        获取所有可用Image
        :return: image list
        """
        images = self.conn.image.images()
        return list(images)

    def get_image_detail(self, image_id):
        """
        获取Image详情
        :param image_id: Image ID
        :return: image detail
        :raise ValueError: If image ID is not provided.
        """
        if not image_id:
            raise ValueError("Image ID is required.")
        image = self.conn.image.get_image(image_id)
        return dict(image)

    def get_all_servers(self):
        """
        获取所有可用Server
        :return: server list
        """
        servers = self.conn.compute.servers()
        return list(servers)

    def get_project_servers(self, all_projects=False, project_id=''):
        """
        根据项目id获取该项目下的所有主机
        :param all_projects: 是否为所有项目,为真时，项目ID失效
        :param project_id: 项目ID
        :return: server list
        """
        if all_projects:
            servers = self.conn.compute.servers(all_projects=all_projects)
        else:
            servers = self.conn.compute.servers(project_id=project_id)
        return list(servers)

    def get_server_detail(self, instance_id):
        """

        """
        if not instance_id:
            raise ValueError("Server ID is required.")
        server = self.conn.compute.get_server(instance_id)
        return server

    def set_server_metadata(self, instance_id, metadata: dict):
        """
        设置主机元数据
        :param instance_id: 主机ID
        :param metadata: 元数据
        :return:
        """
        if not instance_id:
            raise ValueError("Server ID is required.")
        response = self.conn.compute.set_server_metadata(instance_id, **metadata)
        return response

    def sync_image_flavor_to_metadata(self):
        """
        同步所有镜像、规格信息到云主机实例中
        """
        import json
        # 获取所有主机
        servers = self.get_project_servers(all_projects=True)
        # 获取所有规格信息
        flavor_dict = {}
        flavors = self.get_all_flavors()
        for flavor in flavors:
            flavor_dict[flavor.name] = {
                'id': flavor.id,
            }
        for server in servers:
            # 获取镜像详情
            temp_server_image_id = getattr(server.image, 'id')
            if not temp_server_image_id:
                temp_server_image_id = getattr(server.metadata, 'image_id', None)
                if not temp_server_image_id:
                    image_detail = {'id': 'none_image_id'}
                else:
                    image_detail = admin_client.get_image_detail(temp_server_image_id)
            else:
                image_detail = admin_client.get_image_detail(temp_server_image_id)
            # 获取镜像详情
            temp_server_flavor_id = getattr(server.flavor, 'id')
            if not temp_server_flavor_id:
                temp_server_flavor_id = getattr(server.metadata, 'flavor_id', None)
                if not temp_server_flavor_id:
                    flavor_detail = {'id': 'none_flavor_id'}
                else:
                    flavor_detail = admin_client.get_flavor_detail(flavor_dict.get(temp_server_flavor_id))
            else:
                flavor_detail = admin_client.get_flavor_detail(flavor_dict.get(temp_server_flavor_id))
            # 保存metadata
            metadata = {
                'image_id': image_detail['id'],
                'flavor_id': flavor_detail['id'],
            }
            self.set_server_metadata(server.id, metadata)

    def delete_server(self, instance_id, is_destroy=False):
        """
        删除云主机
        :param instance_id: 实例ID
        :param is_destroy: 是否直接删除，为假则为软删除
        :return:
        """
        if not instance_id:
            raise ValueError("Server ID is required.")
        if is_destroy:
            server = self.conn.compute.delete_server(instance_id)
        else:
            server = self.conn.compute.soft_delete_server(instance_id)
        return server

    def create_baremetal_server(
        self,
        project_id,
        name,
        flavor_id,
        image_id,
        network_id,
        security_group_names=None,
        description='',
        zone="nova",
        instance_count=1,
        **kwargs
    ):
        """
        创建裸金属服务器
        :param project_id: 项目ID
        :param name: 名称
        :param description: 描述
        :param flavor_id: 规格ID
        :param image_id: 镜像ID
        :param network_id: 网络ID
        :param security_group_names: 安全组名称列表 # ['default', 'testing-001']
        :param zone: 逻辑域
        :param instance_count: 实例数量
        :param kwargs: 其他参数
        :return: 虚拟机
        :raises: OpenStackAPIError, NovaException, NoValidHost, InvalidParameterValue, InvalidInput, Invalid, or
            NoValidHost, InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue, or Invalid, or NoValidHost,
            InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue,
        """
        if security_group_names is None:
            security_group_names = ["default"]
        if not project_id:
            raise ValueError("Project ID is required.")
        new_conn = self.get_project_scope_conn(project_id=project_id)
        create_params = kwargs
        security_groups = [{"name": sg_name} for sg_name in security_group_names]
        create_params.update({
            "name": name,
            "availability_zone": zone,
            "config_drive": True,
            "description": description,
            "user_data": "",
            "instance_count": instance_count,
            "disk_config": "AUTO",
            "security_groups": security_groups,
            "scheduler_hints": {},
            "image_id": image_id,
            "flavor_id": flavor_id,
            "metadata": kwargs.get("metadata", {}),
            "create_volume_default": True,
            "hide_create_volume": False,
            "networks": [{"uuid": network_id}],
        })
        # keypair validation
        if kwargs.get("key_name"):
            create_params.update({"key_name": kwargs["key_name"]})
        vm = new_conn.compute.create_server(**create_params)
        logger.info('create_baremetal_server Response:%s' % str(vm))
        return vm

    def create_multi_baremetal_server(
            self,
            project_id,
            name,
            flavor_id,
            image_id,
            network_id,
            security_group_names=None,
            description='',
            zone="nova",
            scheduler_hints=None,
            instance_count=1,
            **kwargs
    ):
        """
        创建裸金属服务器
        :param project_id: 项目ID
        :param name: 名称
        :param description: 描述
        :param flavor_id: 规格ID
        :param image_id: 镜像ID
        :param network_id: 网络ID
        :param security_group_names: 安全组名称列表 # ['default', 'testing-001']
        :param zone: 逻辑域
        :param scheduler_hints: 定制化脚本
        :param instance_count: 实例数量
        :param kwargs: 其他参数
        :return: 虚拟机
        :raises: OpenStackAPIError, NovaException, NoValidHost, InvalidParameterValue, InvalidInput, Invalid, or
            NoValidHost, InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue, or Invalid, or NoValidHost,
            InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue,
        """
        result = []
        for i in range(int(instance_count)):
            vm = self.create_baremetal_server(
                project_id=project_id,
                name=f"{name}_{i+1}",
                flavor_id=flavor_id,
                image_id=image_id,
                network_id=network_id,
                security_group_names=security_group_names,
                description=description,
                zone=zone,
                scheduler_hints=scheduler_hints,
                **kwargs
            )
            result.append(vm)
        return result

    def watch_server_create_status(self, instance_id):
        """
        持续查询创建任务状态
        :param instance_id: 实例ID
        :return: status
        """
        import time
        while True:
            try:
                server = self.conn.compute.get_server(instance_id)
                status = server.status
                logger.warning(f"当前状态: {status}")

                if status == 'ACTIVE':
                    logger.warning("裸金属节点已成功激活")
                    break
                elif status == 'ERROR':
                    logger.warning("裸金属节点创建失败")
                    # 获取详细的错误信息
                    fault = getattr(server, 'fault', None)
                    if fault:
                        logger.warning(f"故障详情: {fault}")
                    break
            except Exception as e:
                logger.warning(f"查询状态失败: {e}")
                break

            # 每隔几秒查询一次状态
            time.sleep(10)

    def get_all_baremetal_nodes(self, is_detail_list=False, fields=None):
        """
        获取所有裸金属节点
        :param is_detail_list: 是否返回detail列表
        :param fields: 查询返回的字段
        :return: node list
        """
        nodes = self.conn.baremetal.nodes()
        if is_detail_list:
            nodes = [self.get_baremetal_node_detail(node_uuid=node.id, fields=fields) for node in nodes]
        return list(nodes)

    def get_baremetal_node_detail(self, node_uuid, fields=None):
        """
        获取裸金属节点详情
        :param node_uuid: 节点UUID
        :param fields: 查询返回的字段
        :return: node detail
        """
        if fields is None:
            node_detail = self.conn.baremetal.get_node(node_uuid)
        else:
            node_detail = self.conn.baremetal.get_node(node_uuid, fields=fields)
        return node_detail

    def get_project_networks(self, project_id='', is_all=False):
        """
        获取所有网络
        :param project_id: 项目ID, 当is_all为假时，project_id 必填
        :param is_all: 是否获取所有项目的网络
        :return: network list
        """
        if not project_id and not is_all:
            raise ValueError("Project ID is required.")
        if is_all:
            networks = self.conn.network.networks()
        else:
            networks = self.conn.network.list_networks(project_id=project_id)
        return list(networks)

    @staticmethod
    def split_ip_pools(ip_pools):
        """
        分割获取ip池
        :param ip_pools: 多段IP池字符串，用'\r\n'或者'\n'分隔,如下所示
            "***********,*************\r\n192.168.10.5,**************"
            "************,**************\n192.168.10.5,**************"
            "************,**************"
        :return: 单段ip pool列表
        """
        allocation_ip_pools = []
        if '\r\n' in ip_pools:
            temp_ip_pools = ip_pools.split('\r\n')
        elif '\n' in ip_pools:
            temp_ip_pools = ip_pools.split('\n')
        else:
            temp_ip_pools = [ip_pools]
        for ip_pool in temp_ip_pools:
            if ',' not in ip_pool:
                raise ValueError(f"Invalid IP pool format: {ip_pool}")
            ip_pool_info = ip_pool.split(',')
            start_ip, end_ip = ip_pool_info
            allocation_ip_pools.append({
                'start': start_ip.strip(),
                'end': end_ip.strip(),
            })
        return allocation_ip_pools

    def create_network(
        self,
        project_id,
        network_name,
        cidr,
        gateway_ip,
        allocation_pools,
        sub_name='',
        ip_version=4,
        enable_dhcp="on",
        dns_nameservers="***************",
        admin_state="on",
        with_subnet="on",
        mtu=1450
    ):
        """
        普通用户-创建网络
        :param project_id: 项目 ID
        :param network_name: 网络名称
        :param cidr: CIDR 网段
        :param gateway_ip: 网关 IP
        :param allocation_pools: 分配地址池
        :param sub_name: 子网名称
        :param ip_version: IP 版本
        :param enable_dhcp: DHCP 开关
        :param dns_nameservers: DNS 服务器
        :param admin_state: 状态
        :param with_subnet: 创建子网
        :param mtu: MTU 默认 1450
        :return: network
        """
        msg = ''
        if not project_id:
            raise ValueError("Project ID is required.")
        new_conn = self.get_project_scope_conn(project_id=project_id)
        if not sub_name:
            sub_name = cidr.split('/')[0].replace('.', '-')

        try:
            # 创建网络
            network = new_conn.network.create_network(
                name=network_name,
                project_id=project_id,
                admin_state_up=(admin_state == "on"),
                mtu=mtu
            )

            # 格式化IP池为 SDK 可读取的数据格式
            allocation_pools = self.split_ip_pools(allocation_pools)

            if with_subnet == "on":
                # 创建子网
                subnet = new_conn.network.create_subnet(
                    name=sub_name or network_name + '_subnet',
                    network_id=network.id,
                    ip_version=ip_version,
                    cidr=cidr,
                    gateway_ip=gateway_ip,
                    enable_dhcp=(enable_dhcp == "on"),
                    allocation_pools=allocation_pools,
                    dns_nameservers=[dns_nameservers]
                )
                return dict(network), dict(subnet), msg
            else:
                return dict(network), {}, msg

        except (ResourceFailure, Exception) as e:
            # 发生错误时删除已创建的网络
            try:
                new_conn.network.delete_network(network.id)
                msg = f'【创建失败】原因：{str(e)}'
                logger.warning(f"Network [<NAME:{network_name}><ID:{network.id}>] deleted due to error: {e}")
                return {}, {}, msg
            except Exception as delete_error:
                logger.error(f"Failed to delete network {network.id}: {delete_error}")
                raise e  # 重新抛出原始异常

    def create_vlan_network(
        self,
        project_id,
        vlan_id,
        network_name,
        cidr,
        gateway_ip,
        allocation_pools,
        physical_network='vlan',
        sub_name='',
        ip_version=4,
        enable_dhcp="on",
        dns_nameservers="***************",
        admin_state="on",
        with_subnet="on",
        mtu=1450
    ):
        """
        管理员-创建网络
        :param project_id: 项目 ID
        :param vlan_id: VLAN_ID 即 段ID
        :param network_name: 网络名称
        :param cidr: CIDR 网段
        :param gateway_ip: 网关 IP
        :param allocation_pools: 分配地址池
        :param physical_network: 物理网络此系统内定义为 vlan
        :param sub_name: 子网名称
        :param ip_version: IP 版本
        :param enable_dhcp: DHCP 开关
        :param dns_nameservers: DNS 服务器
        :param admin_state: 状态
        :param with_subnet: 创建子网
        :param mtu: MTU 默认 1450
        :return: network
        """
        msg = ''
        if not project_id:
            raise ValueError("Project ID is required.")
        if not sub_name:
            sub_name = cidr.split('/')[0].replace('.', '-')

        try:
            # 创建网络
            provider_params = {
                'provider:network_type': 'vlan',
                'provider:physical_network': physical_network,
                'provider:segmentation_id': vlan_id,
            }
            print('provider_params:', provider_params)
            network = self.conn.network.create_network(
                name=network_name,
                project_id=project_id,
                admin_state_up=(admin_state == "on"),
                mtu=mtu,
                **provider_params
            )
            print('****************')
            print(network)

            # 格式化IP池为 SDK 可读取的数据格式
            allocation_pools = self.split_ip_pools(allocation_pools)

            if with_subnet == "on":
                # 创建子网
                subnet = self.conn.network.create_subnet(
                    name=sub_name or network_name + '_subnet',
                    network_id=network.id,
                    ip_version=ip_version,
                    cidr=cidr,
                    gateway_ip=gateway_ip,
                    enable_dhcp=(enable_dhcp == "on"),
                    allocation_pools=allocation_pools,
                    dns_nameservers=[dns_nameservers]
                )
                return dict(network), dict(subnet), msg
            else:
                return dict(network), {}, msg

        except (ResourceFailure, Exception) as e:
            # 发生错误时删除已创建的网络
            try:
                self.conn.network.delete_network(network.id)
                msg = f'【创建失败】原因：{str(e)}'
                logger.warning(f"Network [<NAME:{network_name}><ID:{network.id}>] deleted due to error: {e}")
                return {}, {}, msg
            except Exception as delete_error:
                logger.error(f"Failed to delete network {network.id}: {delete_error}")
                raise e  # 重新抛出原始异常

    def get_project_security_groups(self, project_id='', is_all=False):
        """
        获取项目下的安全组
        :param project_id: 项目ID
        :param is_all: 是否获取所有安全组
        :return: security_groups
        """
        if not project_id and not is_all:
            raise ValueError("Project ID is required.")
        if is_all:
            # 获取所有安全组
            security_groups = self.conn.network.security_groups()
        else:
            # 获取指定项目的安全组
            security_groups = self.conn.network.security_groups(project_id=project_id)
        return list(security_groups)

    def get_ironic_hypervisor_nodes(self):
        """
        获取Ironic hypervisor节点
        :return: hypervisor nodes
        """
        hypervisor_nodes = self.conn.compute.hypervisors()
        return list(hypervisor_nodes)


if __name__ == '__main__':
    import os
    import django

    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
    django.setup()
    admin_client = OpenstackAdminClient(debug=True)
    temp_project_id = 'b6242db497774ac2a847accfcc463549'
    # # projects = admin_client.get_all_projects()
    # # logger.warning(projects)
    # # for project in projects:
    # #     logger.warning(project)
    #
    # # servers = admin_client.get_project_servers(all_projects=True, project_id=temp_project_id)
    # # logger.warning(servers)
    # # project = admin_client.create_project('cao-testing001', '曹相鹏接口测试创建')
    # # logger.warning(project)
    # # res = admin_client.assign_project_to_admin(temp_project_id)
    # # logger.warning(res)
    #

    # # 设置计算配额
    # compute_quotas = {
    #     'cores': 12800,   # 已乘10
    #     'instances': 200,  # 已乘10
    #     'ram': 20480000  # 单位为 MB
    # }
    #
    # # 设置块存储配额
    # block_storage_quotas = {
    #     'volumes': 100,    # 已乘10
    #     'gigabytes': 1000  # 单位为 GB
    # }
    # admin_client.update_project_compute_quotas_set(temp_project_id,**compute_quotas)
    # admin_client.update_project_block_storage_quotas_set(temp_project_id, **block_storage_quotas)

    # res = admin_client.get_all_flavors()
    # logger.warning(res)
    # for i in res:
    #     logger.warning(dict(i))
    #
    #
    # from tenant.serializers.tenant_openstack_flavor import sync_tenant_flavor
    #
    # sync_tenant_flavor()

    # from tenant.serializers.tenant_openstack_image import sync_tenant_openstack_image
    # sync_tenant_openstack_image()

    # from tenant.serializers.tenant_openstack_server import sync_tenant_openstack_server
    # sync_tenant_openstack_server()

    # # 创建裸金属服务器
    # test_name = 'openstack_api_created_testing'
    # test_description = 'API测试创建裸金属'
    # test_flavor_id = "b9e3702a-33d2-4e60-a71a-8cbb48a6113b"
    # test_image_id = "cfeed70d-57a5-42db-bd30-f9671fe6ca14"
    # test_network_id = '32c75593-9fbc-41e6-8d90-d7214cbcd1ff'
    # test_security_group_ids = [
    #     '95b7a4a3-de36-4231-aa27-c515b6e1db9b'
    # ]
    # res = admin_client.create_baremetal_server(
    #     project_id=temp_project_id,
    #     name=test_name,
    #     description=test_description,
    #     flavor_id=test_flavor_id,
    #     image_id=test_image_id,
    #     network_id=test_network_id,
    #     security_group_ids=test_security_group_ids,
    # )
    # logger.warning(res)

    # # 获取裸金属所有节点
    # res = admin_client.get_baremetal_nodes()
    # logger.warning(res)
    # # 获取裸金属节点详情
    # node_uuid = 'fd77a713-0f02-4df2-ac53-655da85420a2'
    # res = admin_client.get_baremetal_node_detail(node_uuid, fields=['driver_info'])
    # logger.warning(res)
    # logger.warning(res.driver_info.get('impi_password'))
    # logger.warning(dict(res))

    # # 获取云主机实例详情
    # server_id = 'c7f8d79e-5e48-4999-816e-b4f2bb232fc4'
    # res = admin_client.get_server_detail(server_id)
    # logger.warning(res)
    # logger.warning(dict(res))

    # # 删除 云主机实例
    # server_id = 'f5132f4b-ee61-460b-94de-fab7c7a3bdf0'
    # res = admin_client.delete_server(server_id)
    # logger.warning(res)

    # # 创建裸金属服务器时【指定裸金属节点】
    # test_name = 'openstack_api_created_testing'
    # test_description = 'API测试创建裸金属'
    # test_flavor_id = "b9e3702a-33d2-4e60-a71a-8cbb48a6113b"
    # test_image_id = "cfeed70d-57a5-42db-bd30-f9671fe6ca14"
    # test_network_id = '32c75593-9fbc-41e6-8d90-d7214cbcd1ff'
    # test_security_group_names = [
    #     'default'
    # ]
    # # 指定的node节点
    # baremetal_node_uuid = 'fd77a713-0f02-4df2-ac53-655da85420a2'
    # properties = {'capabilities:node': baremetal_node_uuid}
    # res = admin_client.create_baremetal_server(
    #     project_id=temp_project_id,
    #     name=test_name,
    #     description=test_description,
    #     flavor_id=test_flavor_id,
    #     image_id=test_image_id,
    #     network_id=test_network_id,
    #     security_group_names=test_security_group_names,
    #     properties=properties,
    # )
    # logger.warning(res)
    # # 监听主机状态
    # admin_client.watch_server_create_status(instance_id=res.id)

    # # 同步所有镜像、规格信息到云主机实例元数据中
    # admin_client.sync_image_flavor_to_metadata()

    # 获取网络信息
    # admin_client.get_all_networks()

    # 创建网络
    # temp_cidr = '************/24'
    # temp_gateway = '************'
    # temp_allocation_ip = '************,**************'
    #
    # admin_client.create_network(
    #     temp_project_id,
    #     'vlan105',
    #     temp_cidr,
    #     temp_gateway,
    #     allocation_pools=temp_allocation_ip,
    # )

    # # 创建带有物理网络的网络
    # temp_cidr = '************/24'
    # temp_gateway = '************'
    # temp_allocation_ip = '************,**************'
    #
    # admin_client.create_vlan_network(
    #     project_id=temp_project_id,
    #     network_name='vlan108',
    #     vlan_id=108,
    #     cidr=temp_cidr,
    #     gateway_ip=temp_gateway,
    #     allocation_pools=temp_allocation_ip,
    # )

    # from tenant.serializers.tenant_openstack_network import sync_tenant_openstack_network
    # sync_tenant_openstack_network()

    # 获取安全组信息 过率项目ID
    # res = admin_client.get_project_security_groups(temp_project_id)
    # print(res)

    # 获取安全组信息 所有
    # res = admin_client.get_project_security_groups(is_all=True)
    # print(res)

    # from tenant.serializers.tenant_openstack_security_group import sync_tenant_openstack_security_group
    # sync_tenant_openstack_security_group()

    # 获取所有裸金属节点信息
    # res = admin_client.get_all_baremetal_nodes()
    # for i in res:
    #     detail_info = admin_client.get_baremetal_node_detail(i.id)
    #     print(json.dumps(dict(detail_info)))

    # from tenant.serializers.tenant_openstack_ironic_hypervisor import sync_tenant_openstack_ironic_hypervisor
    # sync_tenant_openstack_ironic_hypervisor()

    # from tenant.serializers.tenant_openstack_project import sync_tenant_openstack_project
    #
    # sync_tenant_openstack_project()





```


