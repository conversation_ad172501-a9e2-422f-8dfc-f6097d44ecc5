# 裸机节点注册调研
## 背景
在使用OpenStack Ironic进行裸机节点注册时，需要了解Ironic的节点验证过程，以确定是否会操作裸机节点。
## 核心配置内容
### 配置分类
```text
# 名称
node_name=**********

# 基础配置
## 资源类
resource=ironic-4090
## 基础信息
boot_mode=uefi
network_interface=neutron
# 属性
capabilities=boot_option:local,disk_label:gpt
cpu_arch=x86_64
cpus=144
memory_mb=515072
local_gb=400
vendor=unknown

# IPMI 信息
ipmi_username=ADMIN
ipmi_password=ADMIN
ipmi_address=**********
ipmi_version=2.0
# 驱动信息
--driver-info ipmi_username=$ipmi_username \
--driver-info ipmi_password=$ipmi_password \
--driver-info ipmi_address=$ipmi_address \
--driver-info ipmi_protocol_version=$ipmi_version \

网络信息
1. 创建端口组
2. 创建端口[2]个端口，且端口要关联到此端口组
node_mac="e8:eb:d3:db:71:88"   
switch_id=54:f6:e2:19:fd:80
switch_info=ASW-D4-3.EM101-D13-22U-6885-SQA-G1-A
port_id=Eth-Trunk30
mac1=e8:eb:d3:db:71:88
mac2=e8:eb:d3:db:71:89
```
## 配置流程图
![裸机节点注册流程图.png](img/%E8%A3%B8%E6%9C%BA%E8%8A%82%E7%82%B9%E6%B3%A8%E5%86%8C%E6%B5%81%E7%A8%8B%E5%9B%BE.png)

## Openstack API 操作流程
### 核心API
- create_node(创建节点)
- create_port_group(创建端口组)
- create_port(创建端口)
- validate_node(验证节点)

使用Python OpenStackSDK实现裸金属节点和端口组创建 
 
下面是将您提供的OpenStack CLI命令转换为Python OpenStackSDK的实现：

1. 创建裸金属节点 
 
```python 
from openstack import connection 
 
创建OpenStack连接 
conn = connection.Connection(
    auth_url='http://controller:5000/v3',
    project_name='your_project',
    username='your_username',
    password='your_password',
    user_domain_id='default',
    project_domain_id='default'
)
 
定义节点参数 
node_name = "your-node-name"
ipmi_username = "admin"
ipmi_password = "password"
ipmi_address = "*************"
ipmi_version = "1.5"
phys_cpus = "16"
phys_ram = "32768"  # MB 
phys_disk = "500"   # GB 
phys_arch = "x86_64"
resource = "baremetal"
network_interface = "flat"
 
创建裸金属节点 
node = conn.baremetal.create_node(
    name=node_name,
    driver="ipmi",
    deploy_interface="direct",
    driver_info={
        'ipmi_username': ipmi_username,
        'ipmi_password': ipmi_password,
        'ipmi_address': ipmi_address,
        'ipmi_protocol_version': ipmi_version 
    },
    properties={
        'cpus': phys_cpus,
        'memory_mb': phys_ram,
        'local_gb': phys_disk,
        'cpu_arch': phys_arch,
        'capabilities': 'boot_option:local,disk_label:gpt'
    },
    resource_class=resource,
    network_interface=network_interface 
)
 
print(f"Node {node_name} created with UUID: {node.id}")
```
 
2. 创建端口组 
 
```python 
定义端口组参数 
mac1 = "52:54:00:12:34:56"  # 主MAC地址 
switch_id = "00:11:22:33:44:55"  # 交换机ID 
switch_info = "switch1"  # 交换机信息 
port_id = "Ethernet1/1"  # 交换机端口 
 
创建端口组 
port_group = conn.baremetal.create_port_group(
    node_uuid=node.id,
    name=node_name,
    address=mac1,
    mode="802.3ad",
    properties={
        'miimon': '100',
        'xmit_hash_policy': 'layer2+3'
    },
    support_standalone_ports=True 
)
 
print(f"Port group created with UUID: {port_group.id}")
```
 
3. 创建端口 
 
```python 
创建第一个端口 
port1 = conn.baremetal.create_port(
    address=mac1,
    node_uuid=node.id,
    local_link_connection={
        'switch_id': switch_id,
        'switch_info': switch_info,
        'port_id': port_id 
    },
    pxe_enabled=True,
    port_group_uuid=port_group.id 
)
 
创建第二个端口 
mac2 = "52:54:00:12:34:57"  # 第二个MAC地址 
port2 = conn.baremetal.create_port(
    address=mac2,
    node_uuid=node.id,
    local_link_connection={
        'switch_id': switch_id,
        'switch_info': switch_info,
        'port_id': port_id  # 假设使用相同的交换机端口 
    },
    pxe_enabled=True,
    port_group_uuid=port_group.id 
)
 
print(f"Ports created with UUIDs: {port1.id} and {port2.id}")
```
 
完整流程整合 
 
```python 
from openstack import connection 
 
def create_baremetal_node():
    # 1. 创建连接 
    conn = connection.Connection(
        auth_url='http://controller:5000/v3',
        project_name='your_project',
        username='your_username',
        password='your_password',
        user_domain_id='default',
        project_domain_id='default'
    )
    
    # 2. 定义参数 
    node_name = "your-node-name"
    ipmi_username = "admin"
    ipmi_password = "password"
    ipmi_address = "*************"
    ipmi_version = "1.5"
    phys_cpus = "16"
    phys_ram = "32768"
    phys_disk = "500"
    phys_arch = "x86_64"
    resource = "baremetal"
    network_interface = "flat"
    mac1 = "52:54:00:12:34:56"
    mac2 = "52:54:00:12:34:57"
    switch_id = "00:11:22:33:44:55"
    switch_info = "switch1"
    port_id = "Ethernet1/1"
    
    try:
        # 3. 创建节点 
        node = conn.baremetal.create_node(
            name=node_name,
            driver="ipmi",
            deploy_interface="direct",
            driver_info={
                'ipmi_username': ipmi_username,
                'ipmi_password': ipmi_password,
                'ipmi_address': ipmi_address,
                'ipmi_protocol_version': ipmi_version 
            },
            properties={
                'cpus': phys_cpus,
                'memory_mb': phys_ram,
                'local_gb': phys_disk,
                'cpu_arch': phys_arch,
                'capabilities': 'boot_option:local,disk_label:gpt'
            },
            resource_class=resource,
            network_interface=network_interface 
        )
        print(f"Node {node_name} created with UUID: {node.id}")
        
        # 4. 创建端口组 
        port_group = conn.baremetal.create_port_group(
            node_uuid=node.id,
            name=node_name,
            address=mac1,
            mode="802.3ad",
            properties={
                'miimon': '100',
                'xmit_hash_policy': 'layer2+3'
            },
            support_standalone_ports=True 
        )
        print(f"Port group created with UUID: {port_group.id}")
        
        # 5. 创建端口 
        port1 = conn.baremetal.create_port(
            address=mac1,
            node_uuid=node.id,
            local_link_connection={
                'switch_id': switch_id,
                'switch_info': switch_info,
                'port_id': port_id 
            },
            pxe_enabled=True,
            port_group_uuid=port_group.id 
        )
        
        port2 = conn.baremetal.create_port(
            address=mac2,
            node_uuid=node.id,
            local_link_connection={
                'switch_id': switch_id,
                'switch_info': switch_info,
                'port_id': port_id 
            },
            pxe_enabled=True,
            port_group_uuid=port_group.id 
        )
        
        print(f"Ports created with UUIDs: {port1.id} and {port2.id}")
        
        # 6. 验证节点 
        validation = conn.baremetal.validate_node(node.id)
        print("Node validation result:", validation)
        
        return True 
    
    except Exception as e:
        print(f"Error creating baremetal node: {str(e)}")
        return False 
 
if __name__ == "__main__":
    create_baremetal_node() 
```

## OpenStack Ironic 节点验证阶段是否会操作裸机节点 
 
在 OpenStack Ironic 的验证过程中，会根据验证的项目对裸机节点进行不同程度的操作，具体取决于验证的接口类型和驱动程序实现。
 
验证过程中的操作级别 
 
| 验证类型 | 是否操作节点 | 操作程度 | 典型操作示例 |
|---------|------------|---------|------------|
| 基础配置验证 | ❌ 否 | 仅检查本地配置 | 检查参数完整性、格式验证 |
| 带外管理验证 | ✅ 是 | 轻度操作 | 发送IPMI/REDFISH命令检查电源状态 |
| 硬件信息验证 | ✅ 是 | 中度操作 | 通过带外接口获取CPU/内存信息 |
| 部署接口验证 | ✅ 是 | 重度操作 | 尝试传输测试文件到节点 | 
| 网络验证 | ✅ 是 | 中度操作 | 检查PXE/TFTP服务连通性 |
 
详细说明 
 
1. 会操作节点的验证项
 
a) 电源管理验证
- 通过IPMI/Redfish/iDRAC等接口发送命令 
- 典型操作：`chassis power status` 命令 
- 目的：验证凭证有效且能控制电源 
 
b) 硬件信息收集
- 通过带外接口获取实际硬件数据 
- 典型操作：`get-sensor-data` (IPMI) 或 Redfish资源查询 
- 目的：验证上报的硬件规格是否准确 
 
c) 部署能力验证
- 部分驱动会尝试传输小测试文件 
- 典型操作：通过SCP/REST传输临时文件 
- 目的：验证镜像传输通道是否畅通 
 
d) 网络验证
- 检查网络服务的可达性 
- 典型操作：TFTP/PXE引导测试请求 
- 目的：验证网络引导配置 
 
2. 不会操作节点的验证项
 
a) 配置完整性检查
- 仅验证本地数据库中的参数 
- 例如：检查是否定义了所有必需的驱动参数 
 
b) 逻辑关系验证
- 检查参数间的逻辑一致性 
- 例如：检查RAID配置是否合理 
 
特殊注意事项 
 
1. 安全操作原则：
   - 验证过程不会改变节点电源状态（不会主动开关机）
   - 硬件信息收集是只读操作 
   - 文件传输使用临时测试文件且会自动清理 
 
2. 驱动程序差异：
   - IPMI驱动验证：会发送`GET_DEVICE_ID`等基础命令 
   - Redfish驱动验证：会查询`/redfish/v1/Systems`端点 
   - 物理机代理(PXE)驱动：会检查DHCP/TFTP服务 
 
3. 验证失败影响：
   - 操作失败会导致验证状态为`false`
   - 但不会使节点进入错误状态（仍保持原状态）
 
最佳实践建议 
 
1. 生产环境建议：
   ```bash 
   # 先进行轻度验证（不触发部署测试）
   openstack baremetal node validate --fields driver_info 
   
   # 全量验证建议在维护窗口进行 
   openstack baremetal node validate --full 
   ```
 
2. 监控验证操作：
   ```python 
   # 通过Ironic API获取验证结果详情 
   validation = conn.baremetal.get_node_validate(node.id)
   print(validation.power.result)  # 查看电源验证结果 
   ```
 
3. 网络隔离场景：
   ```bash 
   # 在隔离环境可使用--skip选项跳过特定验证 
   openstack baremetal node validate --skip deploy 
   ```
 
验证过程的设计遵循"最小必要操作"原则，既确保验证有效性，又避免对生产环境造成不必要干扰。