# openstack 核心知识点
## 可用域`availability_zone`中，`localdisk`和 `nova` 的区别和概念

在OpenStack中，availability_zone 是一个逻辑区域，用于将计算节点、存储节点等物理资源分组，以提高高可用性和容错能力。当你创建虚拟机实例时，可以选择一个特定的 availability_zone，以便将实例部署到该区域内的某个计算节点上。

关于 localdisk 和 nova
在OpenStack中，localdisk 和 nova 主要涉及到存储类型和计算服务的配置。以下是它们的具体含义和用法：

- Availability Zone:
定义: availability_zone 是一个逻辑分区，用于将计算节点、存储节点等物理资源分组。
用途: 提高系统的高可用性和容错能力。例如，你可以将不同的计算节点分配到不同的 availability_zone 中，以防止单点故障。
- Local Disk (localdisk):
定义: localdisk 是指虚拟机实例使用的本地存储。这意味着虚拟机的数据将存储在运行该虚拟机的计算节点的本地磁盘上。
用途: 使用本地存储可以减少网络延迟，提高性能，但同时也意味着数据的持久性和可靠性较低，因为数据仅存储在一个节点上。
- Nova:
定义: Nova 是 OpenStack 的计算服务组件，负责管理虚拟机实例的生命周期，包括创建、删除、启动、停止等操作。
用途: Nova 与 availability_zone 和 localdisk 配合使用，可以在指定的区域内创建使用本地存储的虚拟机实例。


## Flavor（规格）
在OpenStack中，flavor 是一种预定义的虚拟机规格，它定义了虚拟机的资源分配，如CPU核心数、内存大小、磁盘空间等。Flavor 使得用户可以轻松地选择适合自己应用需求的虚拟机配置。

Flavor 的主要属性
- vCPUs (虚拟CPU核心数):
虚拟机实例将拥有的CPU核心数量。
- RAM (内存大小):
虚拟机实例将分配的内存大小，通常以MB为单位。
- Disk (磁盘空间):
虚拟机实例将分配的磁盘空间大小，通常以GB为单位。
- Ephemeral Disk (临时磁盘空间):
临时磁盘空间，通常用于存储临时数据，不会持久化到虚拟机的根磁盘中。
- Swap Disk (交换空间):
用于虚拟机的交换分区，类似于物理机器上的交换分区。
- RXTX Factor (网络带宽因子):
控制虚拟机的网络带宽限制，默认值为1.0。
- Extra Specs (额外规格):
自定义的额外属性，可以用于指定特定的硬件要求或其他高级配置。例如，指定使用本地存储的属性。


