```shell
### 常规：交换机堆叠的情况(不同mac对应同一组switch_id、switch_info)；若不加堆叠则会多出 switch_id、switch_info 与 mac一一对应 
node_name=**********
node_mac="e8:eb:d3:db:71:88"   
switch_id=54:f6:e2:19:fd:80
switch_info=ASW-D4-3.EM101-D13-22U-6885-SQA-G1-A
port_id=Eth-Trunk30
mac1=e8:eb:d3:db:71:88
mac2=e8:eb:d3:db:71:89
resource=ironic-4090            
phys_arch=x86_64
phys_cpus=144
phys_ram=515072
phys_disk=400
ipmi_username=ADMIN
ipmi_password=ADMIN
ipmi_address=**********
boot_mode=uefi
network_interface=neutron
ipmi_version=2.0

### 创建裸金属
openstack baremetal node create \
  --driver ipmi \
  --deploy-interface direct \
  --driver-info ipmi_username=$ipmi_username \
  --driver-info ipmi_password=$ipmi_password \
  --driver-info ipmi_address=$ipmi_address \
  --driver-info ipmi_protocol_version=$ipmi_version \
  --property cpus=$phys_cpus \
  --property memory_mb=$phys_ram \
  --property local_gb=$phys_disk \
  --property cpu_arch=$phys_arch \
  --property capabilities='boot_option:local,disk_label:gpt' \
  --resource-class $resource \
  --network-interface $network_interface \
  --name $node_name

### 创建端口组
openstack baremetal port group create \
--node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` --name $node_name --address $mac1 --mode 802.3ad \
--property miimon=100 --property xmit_hash_policy="layer2+3" \
--support-standalone-ports

### 创建端口
portgroup=`openstack baremetal port group list|grep $node_name|awk '{print $2}'`

openstack baremetal port create $mac1 --node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` \
--local-link-connection switch_id=$switch_id --local-link-connection switch_info=switch_info \
--local-link-connection port_id=$port_id  --pxe-enabled true --port-group ${portgroup}

openstack baremetal port create $mac2 --node `openstack baremetal node show $node_name -c uuid |awk -F "|" '/ uuid  / {print $3}'` \
--local-link-connection switch_id=$switch_id --local-link-connection switch_info=$switch_info \
--local-link-connection port_id=$port_id --pxe-enabled true --port-group ${portgroup}
```


https://gitlab.hzxingzai.cn/ops/yunwei/-/blob/main/%E4%BA%91%E5%B9%B3%E5%8F%B0/openstack/openstack%E6%89%B9%E9%87%8F%E7%BA%B3%E7%AE%A1node.md?ref_type=heads

package main

import (
	"bytes"
	"flag"
	"fmt"
	"gopkg.in/yaml.v2"
	"io"
	"log"
	"os"
	"os/exec"
	"text/template"
	"time"
)

// ConfigMap 是一个通用的配置映射,interface{} 是一种空接口类型，它可以表示任何类型的值类型来存储任意类型的值
type ConfigMap map[string]interface{}

// ServiceConfig 用于解析 YAML 文件中的顶层结构
type ServiceConfig struct {
	Nodename map[string]ConfigMap `yaml:"nodename"`
}

func logger(loginfo ...interface{}) {
	abExecutePath, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	logPath := string(abExecutePath) + "/logs/add_node.log"
	//设置日志输出
	logFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_RDWR|os.O_APPEND, 0755)
	if err != nil {
		panic(err)
	}
	log.SetOutput(logFile)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	log.Println(loginfo)
}

func replace(Nodevalue map[string]interface{}, NodeKey string, tmpl *template.Template) {
	// 执行模板并输出结果，之前提取出到node信息，存储在Nodevlaue里面，并进行替换
	var result bytes.Buffer
	if err := tmpl.Execute(&result, Nodevalue); err != nil {
		logger("Error executing template:", err)
		return
	}

	// 将结果写入文件
	outputFileName := fmt.Sprintf("nodeinfo-%s", NodeKey)
	file, err := os.Create(outputFileName)
	if err != nil {
		logger("Error creating output file:", err)
		return
	}
	defer file.Close()

	if _, err := io.WriteString(file, result.String()); err != nil {
		logger("Error writing to output file:", err)
		return
	}

	fmt.Printf("Output written to %s\n", outputFileName)
}

func activate_node(NodeKey string) {
	fmt.Printf("add node %s\n", NodeKey)
	//获取路径
	abExecutePath, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	filename := fmt.Sprintf("nodeinfo-%s", NodeKey)
	filePath := string(abExecutePath) + "/" + filename
	fmt.Println(filePath)
	// 验证文件是否存在,存在的话，赋予权限
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		logger("Error: file does not exist:", filePath)
		return
	} else {
		// 新的权限模式
		mode := 0755
		// 更改文件权限
		err := os.Chmod(filePath, os.FileMode(mode))
		if err != nil {
			logger("os.Chmod() failed with", err)
		}
	}

	//使用 fmt.Sprintf 来构建命令字符串。fmt.Sprintf 允许插入变量并确保其被正确格式化。
	cmd := exec.Command("bash", "-c", fmt.Sprintf(`source /root/openrc && bash %s`, filePath))
	// 打印命令和环境变量
	//log.Printf("Executing command: %s", cmd.String())
	//log.Printf("Environment variables: %v", cmd.Env)
	logger("Executing command: ", cmd.String())
	logger("Environment variables: ", cmd.Env)

	// 捕获输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		logger(fmt.Sprintf("cmd.CombinedOutput() failed with: %v. Output: %s", err, output))
	}

	// 打印输出
	fmt.Printf("Output: %s\n", output)

}

func main() {
	var (
		portcreate = flag.Bool("portcreate", false, "是否重建端口组")
		nicType    = flag.String("nic", "", "网卡类型，默认是10G")
	)
	// 自定义 Usage 函数以显示详细的帮助信息
	flag.Usage = func() {
		fmt.Fprintf(flag.CommandLine.Output(), "Usage of %s:\n", os.Args[0])
		fmt.Fprintf(flag.CommandLine.Output(), "Example: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(flag.CommandLine.Output(), "Options:\n")
		flag.PrintDefaults()
	}
	// 读取 YAML 文件
	yamlData, err := os.ReadFile("config.yaml")
	if err != nil {
		logger("Error reading YAML file:", err)
		return
	}

	// 解析YAML数据，并存储在config中
	var config ServiceConfig
	if err := yaml.Unmarshal(yamlData, &config); err != nil {
		logger("Error parsing YAML file:", err)
		return
	}

	//读取模版
	templateData, err := os.ReadFile("nodeinfo.tmpl")
	if err != nil {
		logger("Error reading template file:", err)
		return
	}

	// 解析模板，返回同一个 template.Template 指针；tmpl即 *template.Template 类型
	tmpl, err := template.New("template").Parse(string(templateData))
	if err != nil {
		logger("Error parsing template:", err)
		return
	}
	flag.Parse()

	// 获取 nodename 下的第一个节点信息并确保其是一个ConfigMap
	for NodeKey, Nodevalue := range config.Nodename {
		nodeFilename := NodeKey
		if *portcreate {
			Nodevalue["port_recreate"] = true
			nodeFilename += "-portcreate"
		}
		if *nicType != "" {
			Nodevalue["nic_type"] = "25G"
			nodeFilename += "-" + *nicType
		}

		fmt.Printf("Processing node: %s\n", NodeKey)
		replace(Nodevalue, nodeFilename, tmpl)
		activate_node(nodeFilename)
		time.Sleep(2 * time.Second) // 暂停2秒
	}

}



openstack baremetal node create \
  --driver ipmi \
  --deploy-interface direct \
  --driver-info ipmi_username=$ipmi_username \
  --driver-info ipmi_password=$ipmi_password \
  --driver-info ipmi_address=$ipmi_address \
  --driver-info ipmi_protocol_version=$ipmi_version \
  --property cpus=$phys_cpus \
  --property memory_mb=$phys_ram \
  --property local_gb=$phys_disk \
  --property cpu_arch=$phys_arch \
  --property capabilities='boot_option:local,disk_label:gpt' \
  --resource-class $resource \
  --network-interface $network_interface \
  --name $node_name
# 名称
node_name=**********
# 资源类
resource=ironic-4090 
# 属性
capabilities=boot_option:local,disk_label:gpt
cpu_arch=x86_64
cpus=144
memory_mb=515072
local_gb=400
# IPMI 信息
ipmi_username=ADMIN
ipmi_password=ADMIN
ipmi_address=**********
boot_mode=uefi
network_interface=neutron
ipmi_version=2.0
# 驱动信息
--driver-info ipmi_username=$ipmi_username \
--driver-info ipmi_password=$ipmi_password \
--driver-info ipmi_address=$ipmi_address \
--driver-info ipmi_protocol_version=$ipmi_version \

网络信息
node_mac="e8:eb:d3:db:71:88"   
switch_id=54:f6:e2:19:fd:80
switch_info=ASW-D4-3.EM101-D13-22U-6885-SQA-G1-A
port_id=Eth-Trunk30
mac1=e8:eb:d3:db:71:88
mac2=e8:eb:d3:db:71:89