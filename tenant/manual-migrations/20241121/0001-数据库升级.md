# 升级步骤备忘录
1. 修改环境变量中数据库的配置
> dev_django_values.yaml

## 测试环境
```shell
    TENANT_DATABASE_NAME: chaos_tenants
    TENANT_DATABASE_USER: admin
    TENANT_DATABASE_PASSWORD: 9UNrEmBMaA
    TENANT_DATABASE_HOST: mysql-primary.dev.svc.cluster.local
    TENANT_DATABASE_PORT: 3306
```

# 生产环境
```SQL
# 先备份
CREATE USER 'product_chaos_tenants'@'%' IDENTIFIED BY 'pGjbb68dnSNn';

GRANT ALL PRIVILEGES ON product_chaos_tenants.* TO 'product_chaos_tenants'@'%';
FLUSH PRIVILEGES;



  CHAOS_CELERY_BROKER_DB: '7'
  TENANT_DATABASE_HOST: mysql8-primary.platform.svc.cluster.local
  TENANT_DATABASE_NAME: product_chaos_tenants
  TENANT_DATABASE_PASSWORD: pGjbb68dnSNn
  TENANT_DATABASE_PORT: '3306'
  TENANT_DATABASE_USER: product_chaos_tenants
  ENCRYPTED_FIELDS_KEY: PQYn2FlA9nxzJy7C7z2Zm-Usjy-uunxKYZ72eovbIUE=
```
# openstack配置
```SQL
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (1, 'op_config-5c686c3cd7c4453ebd238f3c70ef76b7', NULL, '1', '2024-10-30 15:43:20.916224', '2024-10-30 15:43:20.916224', 0, '1', NULL, '金华', 'Openstack REST接口地址', 'AUTH_URL', 'gAAAAABnIeOY6_6_dY6-iD3eZ3VNd-UYeBSrmK9IqlVrnM_1rK5-uEsRIBwayTYBRxgF2dnsb0m88sxHUpV4uJpNfiKtZ23dxmYwfFHA1P5qEnmUsEosC3s=', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (2, 'op_config-0b06deaeb67e46d99272d88648b49488', NULL, '1', '2024-10-30 15:45:59.324762', '2024-10-30 15:45:59.324762', 0, '1', NULL, '金华', 'Openstack 域名称', 'REGION_NAME', 'gAAAAABnIeQ3TDyfflMrqcNan89FNP9h32XzlycK91zdEAUNN2Lp1ThJ-F2y4C_S8nTcFOMh5qaGTVp3pFPv3z6l-KOepb1V3w==', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (3, 'op_setting-1b5b572e366a44b381c0880eeeb17142', NULL, '1', '2024-10-30 16:56:07.318198', '2024-10-30 16:56:07.310217', 0, '1', NULL, '金华', '认证类型', 'AUTH_TYPE', 'gAAAAABnIfSn624SSqHHLMntzOFj998UCnZM2EWNBlNL_HDzScu6WTJjUv7Qbf6xbjKLugy5a2fj432Sr647B4mz7P7OAwqg5XzHu0J96UJUAU6DZrSs8Xs=', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (4, 'op_setting-a39c665377f74331ab853e693b8049ee', NULL, '1', '2024-10-30 16:56:07.331227', '2024-10-30 16:56:07.327197', 0, '1', NULL, '金华', 'SecretId', 'APPLICATION_CREDENTIAL_ID', 'gAAAAABnIfSnvOfujYollRCR86PsRMFe-7BGbTgvBcEUssSXLkN-JYBIH_f4qb6mRQ4M7CISQucJcQbkWVjSwH81yvDalwVtZXBNirNAI-2glZmICf3AP--aF_s2x1Cogkp_foaaFV9T', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (5, 'op_setting-79a7d0654e774459b83d856e1fcdbba1', NULL, '1', '2024-10-30 16:56:07.342538', '2024-10-30 16:56:07.338285', 0, '1', NULL, '金华', 'SecretKey', 'APPLICATION_CREDENTIAL_SECRET', 'gAAAAABnIfSnFlT1LuPEQI46s1xH6ifwEUbgj8lIw62BcwvmuDMub95nTIGS78swXJKlPb1haVHQWEFCx3fT1j_Cegk5x8qS7qtDzGnazsyFTSAr5huKGCvITfqrztlWnpdJ9bPtP-npwYjdRTQitz6-ImLwtmKswxsMLMOrbcP7Y3ZzCmkcsi9BQqRHjTpnxyJ4qgjz4Ra5', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (6, 'op_setting-30ca9d70cb374b17a34f9a11df381107', NULL, '1', '2024-10-30 16:56:07.354630', '2024-10-30 16:56:07.349616', 0, '1', NULL, '金华', '管理员用户ID', 'ADMIN_USER_ID', 'gAAAAABnIfSnxqtZU3WAc1F-YxO4459HKrIawyrFDU1JJz3XPKeav02-JISwBjsPChRH6EsBHm1AAFsLoH12Ys4gwghqBEFhwwE-SUb0JVIg5XVavAsNVCq-hNGNs-tXQgVJp9Jl-5FN', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (7, 'op_setting-1d53752603854b1ea1ae666826ec8e6d', NULL, '1', '2024-10-30 16:56:07.366629', '2024-10-30 16:56:07.362629', 0, '1', NULL, '金华', '管理员角色ID', 'ADMIN_ROLE_ID', 'gAAAAABnIfSn5zzy4Nlv6YWu1lXZ_anTsNMRjv6fn7Zis6HEZAJ-7cRc7zjNgS2JVejlneXlE8LCb6ZCMTcJLEScdmJOBpWPo-mLL-pUryoNOW9nEPS5E_YE9G1rKkrUprAHQNsCmOFY', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (8, 'op_setting-83e2380416444942a7c1178a37cac29e', NULL, '1', '2024-11-21 10:47:52.927739', '2024-11-21 10:42:47.729267', 0, '1', NULL, '金华', '项目 Domain 名称', 'PROJECT_DOMAIN_NAME', 'gAAAAABnPp9YkdKROztAxslfRIZbEuqO7mM_raDjh-zyh-YiTHiVcPICP1veY-0dmD7JkM_-0hVRwY1t-boilJijFNGqQ3ceSw==', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (9, 'op_setting-630d85a156064eacb49dd4d5bb20804f', NULL, '1', '2024-11-21 10:50:20.467260', '2024-11-21 10:46:45.359453', 0, '1', NULL, '金华', '用户 Domian 名称', 'USER_DOMAIN_NAME', 'gAAAAABnPp_syG06iLUOE1yoJnkqbbo0T-PfssV-CSnB0V9v6piVlQp-tZcj-_yUautvvrc4ATfQptyuNIUeUcbjulp02Sh_zw==', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (10, 'op_setting-530ff13759394f8aac8a09d3fa56de10', NULL, '1', '2024-11-21 10:48:55.034971', '2024-11-21 10:48:55.034971', 0, '1', NULL, '金华', '默认项目名称', 'PROJECT_NAME', 'gAAAAABnPp-XyAr6JL8bYA36-iTBQjyEPFj4FHk2mco9dGKzRk_I8x1F9LpDhBM3H0HYVZSXEFNnSutz_4ULJ613itrlDmpRfQ==', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (11, 'op_setting-2565d8059974465eb31dd9fc1477a75c', NULL, '1', '2024-11-21 11:00:18.211710', '2024-11-21 11:00:18.211710', 0, '1', NULL, '金华', '用户名', 'USERNAME', 'gAAAAABnPqJCy7fga7DuHuU9iK_1k0_KGcPiz5MEvx7fdynxH3h4-TP2fpq9HJvPAOxxqItR94CU1iLt3fBvA6KMI6wN6au28A==', 1, 1);
INSERT INTO `tenant_op_settings` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `tenant_id`, `node`, `name`, `key`, `value`, `enabled`, `creator_id`) VALUES (12, 'op_setting-65938a8a67464f8db48158f5b9a0e2a1', NULL, '1', '2024-11-21 11:01:22.903061', '2024-11-21 11:01:22.903061', 0, '1', NULL, '金华', 'MM', 'PASSWORD', 'gAAAAABnPqKCA8mCgwZb-FS8roellMG1bOXms8XA8YNMsoJ2ISTndVo8TcSToclJab-NisBsTrz0qbiOcZDVFCi2cx0b4f0uf44udkQSRZJu1o7jKtsPjWhUScTxd3JAZaxWaw_aTM0g', 1, 1);

```
```SQL
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (164, NULL, '1', 1, '2024-11-20 14:23:03.828775', '2024-11-20 14:22:43.990688', '构建中', 'BUILD', 0, 'success', 1, 1, 13, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (163, NULL, '1', 1, '2024-11-13 10:29:16.539659', '2024-11-13 10:29:16.539659', '挂起中', 'SUSPENDED', 0, 'warning', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (162, NULL, '1', 1, '2024-11-13 10:28:53.393289', '2024-11-13 10:28:53.393289', '已停止', 'STOPPED', 0, 'danger', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (161, NULL, '1', 1, '2024-11-13 10:28:31.070960', '2024-11-13 10:28:31.070960', '软删除', 'SOFT_DELETED', 0, 'danger', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (160, NULL, '1', 1, '2024-11-13 10:28:13.949652', '2024-11-13 10:28:13.949652', '离线搁置', 'SHELVED_OFFLOADED', 0, 'info', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (159, NULL, '1', 1, '2024-11-13 10:27:51.585146', '2024-11-13 10:27:51.585146', '搁置', 'SHELVED', 0, 'info', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (158, NULL, '1', 1, '2024-11-13 10:27:32.475250', '2024-11-13 10:27:32.475250', '调整中', 'RESIZED', 0, 'warning', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (157, NULL, '1', 1, '2024-11-13 10:27:07.455121', '2024-11-13 10:27:07.455121', '救援中', 'RESCUED', 0, 'warning', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (156, NULL, '1', 1, '2024-11-13 10:26:44.402147', '2024-11-13 10:26:44.402147', '暂停', 'PAUSED', 0, 'warning', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (155, NULL, '1', 1, '2024-11-13 10:26:23.881391', '2024-11-13 10:26:23.881391', '错误', 'ERROR', 0, 'danger', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (154, NULL, '1', 1, '2024-11-13 10:25:52.580966', '2024-11-13 10:25:52.580966', '已删除', 'DELETED', 0, 'danger', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (153, NULL, '1', 1, '2024-11-13 10:25:27.823918', '2024-11-13 10:25:27.823918', '构建中', 'BUILDING', 0, 'primary', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (152, NULL, '1', 1, '2024-11-13 10:25:02.910020', '2024-11-13 10:25:02.910020', '运行中', 'ACTIVE', 0, 'success', 1, 1, 1, NULL, 1, 151);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (151, NULL, '1', 1, '2024-11-13 10:24:06.366516', '2024-11-13 10:23:36.577034', '前台模块-主机-状态', 'tenantPortalServerStatus', 0, NULL, 0, 1, 32, NULL, 1, NULL);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (150, NULL, '1', 1, '2024-10-29 14:45:17.049962', '2024-10-29 14:45:17.049962', '内部账号', '3', 1, 'primary', 1, 1, 4, NULL, 1, 146);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (149, NULL, '1', 1, '2024-10-29 14:44:50.170765', '2024-10-29 14:44:50.170765', '测试账号', '2', 1, 'info', 1, 1, 3, NULL, 1, 146);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (148, NULL, '1', 1, '2024-10-29 14:44:25.241396', '2024-10-29 14:44:13.446609', '公司账号', '1', 1, 'danger', 1, 1, 2, NULL, 1, 146);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (147, NULL, '1', 1, '2024-10-29 14:44:31.657066', '2024-10-29 14:43:41.734105', '个人账号', '0', 1, 'success', 1, 1, 1, NULL, 1, 146);
INSERT INTO `chaos_system_dictionary` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `label`, `value`, `type`, `color`, `is_value`, `status`, `sort`, `remark`, `creator_id`, `parent_id`) VALUES (146, NULL, '1', 1, '2024-10-29 14:42:33.128395', '2024-10-29 14:41:50.997807', '租户-账号-账号类型', 'tenant:account:account_type', 0, NULL, 0, 1, 31, NULL, 1, NULL);

```

apiVersion: v1
kind: ConfigMap
metadata:
  name: chaos-django-config
  namespace: dev
  uid: 523e03c5-d892-4040-a544-f7218f936df4
  resourceVersion: '*********'
  creationTimestamp: '2024-07-11T01:26:50Z'
  labels:
    app.kubernetes.io/managed-by: Helm
    k8slens-edit-resource-version: v1
  annotations:
    meta.helm.sh/release-name: chaos-django
    meta.helm.sh/release-namespace: dev
  managedFields:
    - manager: node-fetch
      operation: Update
      apiVersion: v1
      time: '2024-11-21T09:46:56Z'
      fieldsType: FieldsV1
      fieldsV1:
        f:data:
          f:CHAOS_CELERY_BROKER_DB: {}
          f:TENANT_DATABASE_HOST: {}
          f:TENANT_DATABASE_NAME: {}
          f:TENANT_DATABASE_PASSWORD: {}
          f:TENANT_DATABASE_PORT: {}
          f:TENANT_DATABASE_USER: {}
        f:metadata:
          f:labels:
            f:k8slens-edit-resource-version: {}
    - manager: helm
      operation: Update
      apiVersion: v1
      time: '2024-11-22T02:08:44Z'
      fieldsType: FieldsV1
      fieldsV1:
        f:data:
          .: {}
          f:CELERY_BROKER_DB: {}
          f:DATABASE_HOST: {}
          f:DATABASE_NAME: {}
          f:DATABASE_PASSWORD: {}
          f:DATABASE_PORT: {}
          f:DATABASE_USER: {}
          f:DEBUG: {}
          f:ENCRYPTED_FIELDS_KEY: {}
          f:EXPIRE_REMIND_USERS: {}
          f:OP_AUTH_URL: {}
          f:OP_PASSWD: {}
          f:OP_PROJECT_DOMAIN: {}
          f:OP_PROJECT_NAME: {}
          f:OP_USER: {}
          f:OP_USER_DOMAIN: {}
          f:REDIS_DB: {}
          f:REDIS_HOST: {}
          f:REDIS_PASSWORD: {}
          f:REDIS_PORT: {}
          f:SECRET_KEY: {}
          f:WEBHOOK: {}
          f:WORKER_ENV: {}
        f:metadata:
          f:annotations:
            .: {}
            f:meta.helm.sh/release-name: {}
            f:meta.helm.sh/release-namespace: {}
          f:labels:
            .: {}
            f:app.kubernetes.io/managed-by: {}
  selfLink: /api/v1/namespaces/dev/configmaps/chaos-django-config
data:
  CELERY_BROKER_DB: '6'
  CHAOS_CELERY_BROKER_DB: '7'
  DATABASE_HOST: mysql-primary.dev.svc.cluster.local
  DATABASE_NAME: chaos
  DATABASE_PASSWORD: 9UNrEmBMaA
  DATABASE_PORT: '3306'
  DATABASE_USER: admin
  DEBUG: '0'
  ENCRYPTED_FIELDS_KEY: PQYn2FlA9nxzJy7C7z2Zm-Usjy-uunxKYZ72eovbIUE=
  EXPIRE_REMIND_USERS: 15968153838|18637535839
  OP_AUTH_URL: https://************:5000/v3
  OP_PASSWD: f5170ef8b7b4a93d2a1e8ae4b84bd0c6c8aaa235d9de
  OP_PROJECT_DOMAIN: default
  OP_PROJECT_NAME: xingzai
  OP_USER: admin
  OP_USER_DOMAIN: Default
  REDIS_DB: '5'
  REDIS_HOST: redis7-master.platform.svc.cluster.local
  REDIS_PASSWORD: KhztUrhxplcFiP1
  REDIS_PORT: '6379'
  SECRET_KEY: django-insecure--zH1Yv7v$51QL#0yhkj!f@o$qjb&aUW7K1(Yc%qZl2)O7x&WDi2
  TENANT_DATABASE_HOST: mysql-primary.dev.svc.cluster.local
  TENANT_DATABASE_NAME: chaos_tenants
  TENANT_DATABASE_PASSWORD: 9UNrEmBMaA
  TENANT_DATABASE_PORT: '3306'
  TENANT_DATABASE_USER: admin
  WEBHOOK: >-
    https://oapi.dingtalk.com/robot/send?access_token=8d55ce2e14032dc35480970458f9b8d12f785acb5aee445ebe3cdffb7f8b3484
  WORKER_ENV: testing


