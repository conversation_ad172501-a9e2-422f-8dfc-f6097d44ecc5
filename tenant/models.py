from django.db import models
from django.core.cache import cache

from dvadmin.utils.custom_encrypt_field import Encrypted<PERSON><PERSON><PERSON>ield
from tenant.utils.tenant_base.chaos_tenant_models import ChaosCoreTenantModel


TABLE_PREFIX = 'tenant_'


class TenantMenu(ChaosCoreTenantModel):
    parent = models.ForeignKey(
        to="TenantMenu",
        on_delete=models.CASCADE,
        verbose_name="上级菜单",
        null=True,
        blank=True,
        db_constraint=False,
        help_text="上级菜单",
        to_field='id',
    )
    icon = models.CharField(max_length=64, verbose_name="菜单图标", null=True, blank=True, help_text="菜单图标")
    name = models.CharField(max_length=64, verbose_name="菜单名称", help_text="菜单名称")
    sort = models.IntegerField(default=1, verbose_name="显示排序", null=True, blank=True, help_text="显示排序")
    ISLINK_CHOICES = (
        (0, "否"),
        (1, "是"),
    )
    is_link = models.BooleanField(default=False, verbose_name="是否外链", help_text="是否外链")
    link_url = models.CharField(max_length=255, verbose_name="链接地址", null=True, blank=True, help_text="链接地址")
    is_catalog = models.BooleanField(default=False, verbose_name="是否目录", help_text="是否目录")
    web_path = models.CharField(max_length=128, verbose_name="路由地址", null=True, blank=True, help_text="路由地址")
    component = models.CharField(max_length=128, verbose_name="组件地址", null=True, blank=True, help_text="组件地址")
    component_name = models.CharField(max_length=50, verbose_name="组件名称", null=True, blank=True,
                                      help_text="组件名称")
    status = models.BooleanField(default=True, blank=True, verbose_name="菜单状态", help_text="菜单状态")
    cache = models.BooleanField(default=False, blank=True, verbose_name="是否页面缓存", help_text="是否页面缓存")
    visible = models.BooleanField(default=True, blank=True, verbose_name="侧边栏中是否显示",
                                  help_text="侧边栏中是否显示")
    is_iframe = models.BooleanField(default=False, blank=True, verbose_name="框架外显示", help_text="框架外显示")
    is_affix = models.BooleanField(default=False, blank=True, verbose_name="是否固定", help_text="是否固定")
    is_public = models.BooleanField(
        default=False, blank=True,
        verbose_name="是否tenant公共菜单", help_text="是否tenant公共菜单", db_comment="是否tenant公共菜单")

    @classmethod
    def get_all_parent(cls, id: int, all_list=None, nodes=None):
        """
        递归获取给定ID的所有层级
        :param id: 参数ID
        :param all_list: 所有列表
        :param nodes: 递归列表
        :return: nodes
        """
        if not all_list:
            all_list = TenantMenu.objects.values("id", "name", "parent")
        if nodes is None:
            nodes = []
        for ele in all_list:
            if ele.get("id") == id:
                parent_id = ele.get("parent")
                if parent_id is not None:
                    cls.get_all_parent(parent_id, all_list, nodes)
                nodes.append(ele)
        return nodes

    class Meta:
        db_table = TABLE_PREFIX + "menus"
        verbose_name = "tenant菜单表"
        verbose_name_plural = verbose_name
        ordering = ("sort",)


class TenantMenuButton(ChaosCoreTenantModel):
    menu = models.ForeignKey(
        to="TenantMenu",
        db_constraint=False,
        related_name="tenantMenuPermission",
        on_delete=models.CASCADE,
        verbose_name="关联菜单",
        help_text="关联菜单",
        to_field='id',
    )
    name = models.CharField(max_length=64, verbose_name="名称", help_text="名称")
    value = models.CharField(unique=True, max_length=64, verbose_name="权限值", help_text="权限值")
    api = models.CharField(max_length=200, verbose_name="接口地址", help_text="接口地址")
    METHOD_CHOICES = (
        (0, "GET"),
        (1, "POST"),
        (2, "PUT"),
        (3, "DELETE"),
    )
    is_public = models.BooleanField(default=False, blank=True, verbose_name="是否公共", help_text="是否公共", db_comment="是否公共")
    method = models.IntegerField(default=0, verbose_name="接口请求方法", null=True, blank=True,
                                 help_text="接口请求方法")

    class Meta:
        db_table = TABLE_PREFIX + "menu_buttons"
        verbose_name = "tenant菜单权限表"
        verbose_name_plural = verbose_name
        ordering = ("-name",)


class TenantAccountGroup(ChaosCoreTenantModel):
    name = models.CharField(max_length=64, verbose_name="名称", help_text="名称")
    description = models.TextField(verbose_name="描述", help_text="描述")

    class Meta:
        db_table = TABLE_PREFIX + "account_groups"
        verbose_name = "tenant用户组表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class TenantAccountGroupPermission(ChaosCoreTenantModel):
    account_group = models.ForeignKey(
        to="TenantAccountGroup",
        db_constraint=False,
        on_delete=models.CASCADE,
        verbose_name="关联tenant用户组",
        help_text="关联tenant用户组",
        to_field='id',
    )
    menu = models.ForeignKey(
        to="TenantMenu",
        db_constraint=False,
        on_delete=models.CASCADE,
        verbose_name="关联tenant菜单",
        help_text="关联tenant菜单",
        to_field='id',
    )
    menu_button = models.ForeignKey(
        to="TenantMenuButton",
        db_constraint=False,
        on_delete=models.CASCADE,
        verbose_name="关联tenant按钮",
        help_text="关联tenant按钮",
        to_field='id',
    )
    PERMISSION_TYPE_CHOICES = (
        ("menu_button", "按钮权限"),
        ("menu", "菜单权限"),
    )
    permission_type = models.CharField(
        max_length=63, default='menu_button', null=False, blank=False,
        db_index=True, choices=PERMISSION_TYPE_CHOICES,
        verbose_name="权限类型", help_text="权限类型", db_comment='权限类型')

    class Meta:
        db_table = TABLE_PREFIX + "account_group_permissions"
        verbose_name = "tenant用户组菜单权限表"
        verbose_name_plural = verbose_name
        # ordering = ("-create_datetime",)


class TenantAccount(ChaosCoreTenantModel):
    account_name = models.CharField(
        max_length=255, unique=True, db_index=True, verbose_name="用户账号",
        help_text="用户账号", db_comment='用户账号')
    email = models.CharField(
        # max_length实际为 255,因为需要加密故存储时需要更大的内存空间
        max_length=1023, null=False, default='<EMAIL>', blank=True,
        verbose_name="邮箱", help_text="邮箱", db_comment='邮箱')
    mobile = models.CharField(
        # max_length实际为 15,因为需要加密故存储时需要更大的内存空间
        max_length=255, null=False, default='***********', blank=True,
        verbose_name="电话", help_text="电话", db_comment='电话')
    avatar = models.CharField(
        max_length=255, null=True, blank=True, verbose_name="头像", help_text="头像", db_comment='头像')
    company = models.CharField(
        # max_length实际为 255,因为需要加密故存储时需要更大的内存空间
        max_length=1023, default='Unknown', null=False, blank=True,
        verbose_name="公司名称", help_text="公司名称", db_comment='公司名称')
    account_nick_name = models.CharField(
        max_length=255, db_index=True, verbose_name="账号花名",
        help_text="账号花名", db_comment='账号花名')
    email_is_verified = models.BooleanField(
        default=False, null=False, blank=True,
        verbose_name='邮箱是否验证', help_text='邮箱是否验证', db_comment='邮箱是否验证')
    mobile_is_verified = models.BooleanField(
        default=False, null=False, blank=True,
        verbose_name='手机是否验证', help_text='手机是否验证', db_comment='手机是否验证')
    company_is_verified = models.BooleanField(
        default=False, null=False, blank=True,
        verbose_name='公司是否验证', help_text='公司是否验证', db_comment='公司是否验证')
    ACCOUNT_TYPE = (
        (0, "个人账号"),
        (1, "公司账号"),
        (2, "测试账号"),
        (3, "内部账号"),
    )
    is_master = models.BooleanField(
        default=False, null=False, blank=True,
        verbose_name='是否主账号', help_text='是否主账号', db_comment='是否主账号'
    )
    account_type = models.IntegerField(
        choices=ACCOUNT_TYPE, default=2, null=True, blank=True,
        verbose_name="账号类型", help_text="账号类型", db_comment='账号类型'
    )
    # 关联的客户ID
    customer_id = models.CharField(
        default='', max_length=63, null=True, blank=True,
        verbose_name='客户ID', help_text='客户ID', db_comment='客户ID'
    )

    class Meta:
        db_table = TABLE_PREFIX + "accounts"
        verbose_name = "tenant账户表"
        verbose_name_plural = verbose_name
        unique_together = ("account_name", "account_type", "is_master", "is_deleted",)
        ordering = ("-create_datetime",)


class TenantOpenstackSetting(ChaosCoreTenantModel):
    node = models.CharField(
        max_length=255, db_index=True, verbose_name="Openstack节点",
        help_text="Openstack节点", db_comment='Openstack节点')
    name = models.CharField(
        max_length=255, default='', db_index=True, verbose_name="配置键名称",
        help_text="配置键名称", db_comment='配置键名称')
    key = models.CharField(
        max_length=63, db_index=True, verbose_name="配置键",
        help_text="配置键", db_comment='配置键')
    value = EncryptedCharField(
        max_length=2047, null=False, blank=False, verbose_name="配置值", help_text="配置值",
        db_comment='配置值')
    enabled = models.BooleanField(default=True, verbose_name="启用状态", help_text="启用状态", db_comment="启用状态")

    @staticmethod
    def get_op_distinct_nodes_key():
        key = 'openstack_settings_all_nodes'
        return key

    @staticmethod
    def get_cache_key():
        key = 'openstack_settings_{node}'
        return key

    def __str__(self):
        return f"node: {self.node},key: {self.key}, name: {self.name}"

    @classmethod
    def get_op_distinct_nodes(cls):
        key = cls.get_op_distinct_nodes_key()
        all_nodes = cache.get(key)
        if all_nodes:
            return all_nodes
        node_list = cls.objects.filter(
            name='启用', key='node_is_enable',
            enabled=True, is_deleted=False,
        ).values_list('node',  flat=True).distinct()
        # 去除每个节点的前后空格并去重
        all_nodes = list({node.strip() for node in node_list if node})
        if all_nodes:
            cache.set(key, all_nodes, timeout=3600)  # 缓存有效期为1小时
        return all_nodes

    @classmethod
    def get_op_settings(cls, node='', key=None):
        data = {}
        setting_key = cls.get_cache_key().format(node=node)
        if not node:
            return data
        t_data = cache.get(setting_key)
        if t_data:
            return t_data
        # 无， 则从数据库内获取
        if not key:
            settings = cls.objects.filter(
                node=node, enabled=True, is_deleted=False
            ).all()
        else:
            settings = cls.objects.filter(
                node=node, key=key, enabled=True, is_deleted=False
            ).all()
        for setting in settings:
            data[setting.key] = setting.value
        if data:
            cache.set(setting_key, data, timeout=3600)  # 缓存有效期为1小时
        return data

    @classmethod
    def refresh_op_settings(cls, node='',):
        cache.delete(cls.get_cache_key().format(node=node))
        # 刷新节点列表缓存
        cache.delete(cls.get_op_distinct_nodes_key())

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        cache.delete(self.get_cache_key().format(node=self.node))
        # 刷新节点列表缓存
        cache.delete(self.get_op_distinct_nodes_key())

    def delete(self, *args, **kwargs):
        super().delete(*args, **kwargs)
        cache.delete(self.get_cache_key().format(node=self.node))
        # 刷新节点列表缓存
        cache.delete(self.get_op_distinct_nodes_key())

    class Meta:
        db_table = TABLE_PREFIX + "op_settings"
        verbose_name = "TenantOpenstack配置表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)
        unique_together = ("node", "key", "is_deleted",)


class TenantOpenstackProject(ChaosCoreTenantModel):
    name = models.CharField(
        max_length=63, null=False, blank=False, verbose_name="项目名称", help_text="项目名称")
    enabled = models.BooleanField(
        default=True, null=True, blank=True,
        verbose_name='是否启用', help_text='是否启用', db_comment='是否启用')
    domain_id = models.CharField(
        max_length=63, null=True, blank=True, default='default',
        verbose_name='域ID(仅做记录保留)', help_text='域ID(仅做记录保留)', db_comment='域ID(仅做记录保留)')
    parent_id = models.CharField(
        max_length=63, null=True, blank=True, default='',
        verbose_name='父项目ID(仅做记录保留)', help_text='父项目ID(仅做记录保留)', db_comment='父项目ID(仅做记录保留)')
    account = models.ForeignKey(
        to="TenantAccount",
        db_constraint=True,
        on_delete=models.CASCADE,
        verbose_name="关联tenant-account",
        help_text="关联tenant-account",
        to_field='id',)
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    sync_time = models.DateTimeField(
        blank=True, null=True, verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')

    class Meta:
        db_table = TABLE_PREFIX + "op_projects"
        verbose_name = "TenantOpenstack项目表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class TenantOpenstackVlan(ChaosCoreTenantModel):
    name = models.CharField(
        max_length=63, default=None, null=True, blank=True,
        verbose_name='vlan名称', help_text='vlan名称', db_comment='vlan名称')
    vlan_id = models.CharField(
        max_length=63, default=None, null=False, blank=False,
        verbose_name='vlan_id', help_text='vlan_id', db_comment='vlan_id')
    phy_vlan_type = models.CharField(
        max_length=63, default='vlan', null=True, blank=True,
        verbose_name='物理vlan类型', help_text='物理vlan类型', db_comment='物理vlan类型')
    subnet = models.CharField(
        max_length=255, default=None, null=False, blank=False,
        verbose_name='子网网段', help_text='子网网段', db_comment='子网网段')
    available_ip_pool = models.CharField(
        max_length=255, default=None, null=True, blank=True,
        verbose_name='可用IP池', help_text='可用IP池', db_comment='可用IP池')
    gateway_ip = models.CharField(
        max_length=63, default='0.0.0.0', null=False, blank=False,
        verbose_name='网关IP', help_text='网关IP', db_comment='网关IP'
    )
    enable_dhcp = models.CharField(
        default='on', null=True, blank=True,
        max_length=15, verbose_name='是否启用dhcp', help_text='是否启用dhcp', db_comment='是否启用dhcp')
    network_type = models.CharField(
        default='vlan', null=True, blank=True,
        max_length=63, verbose_name='网络类型', help_text='网络类型', db_comment='网络类型'
    )
    network_id = models.CharField(
        max_length=63, default=None, null=True, blank=True,
        verbose_name='openstack中网络ID', help_text='openstack中网络ID', db_comment='openstack中网络ID')
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    is_used = models.BooleanField(
        default=False, null=False, blank=True,
        verbose_name='是否在用', help_text='是否在用', db_comment='是否在用')
    is_tasking = models.BooleanField(
        default=False, null=False, blank=True,
        verbose_name='是否在创建调度中', help_text='是否在创建调度中', db_comment='是否在创建调度中')
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')

    class Meta:
        db_table = TABLE_PREFIX + "op_vlans"
        verbose_name = "TenantOpenstackVlan表"
        verbose_name_plural = verbose_name
        unique_together = ()
        ordering = ("-create_datetime",)


class TenantOpenstackMetadata(ChaosCoreTenantModel):
    """
    openstack Metadata 元数据
    """
    resource_id = models.CharField(
        max_length=63, null=False, blank=False,
        verbose_name='关联资源ID', db_comment='关联资源ID', help_text='关联资源ID')
    METADATA_TYPE_CHOICES = (
        (0, '实例规格'),
        (1, '实例镜像'),
    )
    type = models.IntegerField(
        blank=True, null=True, choices=METADATA_TYPE_CHOICES,
        verbose_name='类型', db_comment='类型', help_text='类型')
    is_public = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='是否公共', db_comment='是否公共', help_text='是否公共'
    )
    key = models.CharField(
        max_length=255, blank=False, null=False,
        verbose_name='key名称', db_comment='key名称', help_text='key名称')
    value = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='key值', db_comment='key值', help_text='key值')
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    sync_time = models.DateTimeField(
        blank=True, null=True, verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')

    class Meta:
        db_table = TABLE_PREFIX + 'op_metadatas'
        verbose_name = 'TenantResourceMetadata元数据表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']


class TenantOpenstackNetwork(ChaosCoreTenantModel):
    """
    openstack Network 网络
    """
    network_id = models.CharField(
        max_length=255, unique=True, verbose_name='网络ID', db_comment='网络ID', help_text='网络ID')
    name = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='网络名称', db_comment='网络名称', help_text='网络名称')
    subnet_ids = models.JSONField(
        blank=True, null=True, verbose_name='子网ID', db_comment='子网ID', help_text='子网ID')
    mtu = models.IntegerField(
        blank=True, null=True, verbose_name='mtu', db_comment='mtu', help_text='mtu')
    status = models.CharField(
        max_length=63, blank=True, null=True, verbose_name='状态', db_comment='状态', help_text='状态')
    availability_zones = models.JSONField(
        blank=True, null=True, verbose_name='可用区', db_comment='可用区', help_text='可用区')
    is_admin_state_up = models.BooleanField(
        default=False, blank=True, null=True, verbose_name='可管理', db_comment='可管理', help_text='可管理')
    is_router_external = models.BooleanField(
        default=False, blank=True, null=True, verbose_name='外部的', db_comment='外部的', help_text='外部的')
    is_shared = models.BooleanField(
        default=False, blank=True, null=True, verbose_name='共享的', db_comment='共享的', help_text='共享的')
    is_default = models.BooleanField(
        default=False, blank=True, null=True, verbose_name='默认的', db_comment='共享的', help_text='共享的')
    provider_network_type = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='网络类型', db_comment='网络类型', help_text='网络类型')
    provider_physical_network = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='物理网络', db_comment='物理网络', help_text='物理网络')
    provider_segmentation_id = models.IntegerField(
        blank=True, null=True, verbose_name='段ID', db_comment='段ID', help_text='段ID')
    # 下面是公共字段
    created_at = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    sync_time = models.DateTimeField(
        blank=True, null=True, verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')

    class Meta:
        db_table = TABLE_PREFIX + 'op_networks'
        verbose_name = 'TenantOpenstack网络表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']


class TenantOpenstackSubnet(ChaosCoreTenantModel):
    """
    openstack Subnet 子网(隶属于网络下)
    """
    subnet_id = models.CharField(
        max_length=255, unique=True, verbose_name='ID', db_comment='安全组ID', help_text='安全组ID')
    name = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='安全组名称', db_comment='安全组名称', help_text='安全组名称')
    network_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='网络id', db_comment='网络id', help_text='网络id')
    ip_version = models.IntegerField(
        blank=True, null=True, verbose_name='ip_version', db_comment='ip_version', help_text='ip_version')
    is_dhcp_enabled = models.BooleanField(
        default=False, blank=True, null=True, verbose_name='DHCP开启', db_comment='DHCP开启', help_text='DHCP开启')
    gateway_ip = models.CharField(
        max_length=127, blank=True, null=True, verbose_name='GW', db_comment='GW', help_text='GW')
    cidr = models.CharField(
        max_length=127, blank=True, null=True, verbose_name='cidr', db_comment='cidr', help_text='cidr')
    allocation_pools = models.JSONField(
        blank=True, null=True, verbose_name='网络分配池', db_comment='网络分配池', help_text='网络分配池')
    host_routes = models.JSONField(
        blank=True, null=True, verbose_name='主机路由', db_comment='主机路由', help_text='主机路由')
    dns_nameservers = models.JSONField(
        blank=True, null=True, verbose_name='DNS服务器', db_comment='DNS服务器', help_text='DNS服务器')
    # 下面是公共字段
    created_at = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    sync_time = models.DateTimeField(
        blank=True, null=True, verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')

    class Meta:
        db_table = TABLE_PREFIX + 'op_subnets'
        verbose_name = 'TenantOpenstack子网表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']


class TenantOpenstackServer(ChaosCoreTenantModel):
    """
    openstack server instance
    """
    instance_id = models.CharField(
        max_length=127, unique=True, null=True,
        verbose_name='OpenstackInstanceId全局唯一', db_comment='OpenstackInstanceId全局唯一',
        help_text='OpenstackInstanceId全局唯一')
    instance_type = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='实例类型', db_comment='实例类型', help_text='实例类型')
    name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='server名', db_comment='server名', help_text='server名')
    compute_host = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='宿主机', db_comment='宿主机', help_text='宿主机')
    net_name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='网络名称', db_comment='网络名称', help_text='网络名称')
    ipaddr = models.CharField(
        max_length=127, default='', blank=True, null=True,
        verbose_name='IP address', db_comment='IP address', help_text='IP address')
    image_id = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='镜像ID', db_comment='镜像ID', help_text='镜像ID')
    image_name = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='镜像名', db_comment='镜像名', help_text='镜像名')
    image_info = models.JSONField(
        blank=True, null=True, default=dict,
        verbose_name='镜像信息', db_comment='镜像信息', help_text='镜像信息')
    key_name = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='key pair name', db_comment='key pair name', help_text='key pair name')
    flavor_id = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='规格ID', db_comment='规格ID', help_text='规格ID')
    flavor_name = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='flavor_name', db_comment='flavor name', help_text='flavor name')
    flavor_info = models.JSONField(
        blank=True, null=True, default=dict,
        verbose_name='规格信息', db_comment='规格信息', help_text='规格信息')
    vcpus = models.IntegerField(
        blank=True, null=True,
        verbose_name="cpu核心", db_comment='cpu核心', help_text='cpu核心')
    disk = models.IntegerField(
        blank=True, null=True,
        verbose_name="系统盘", db_comment='系统盘', help_text='系统盘')
    ram = models.IntegerField(
        blank=True, null=True,
        verbose_name="内存", db_comment='内存', help_text='内存')
    security_groups = models.JSONField(
        blank=True, null=True,
        verbose_name='安全组', db_comment='安全组', help_text='安全组')
    status = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='状态', db_comment='状态', help_text='状态')
    vm_state = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='vm状态', db_comment='vm状态', help_text='vm状态')
    metadata = models.JSONField(
        blank=True, null=True,
        verbose_name='metadata', db_comment='metadata', help_text='metadata')
    host_id = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='vm_host_id', db_comment='vm_host_id', help_text='vm_host_id'
    )
    hostname = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='主机名', db_comment='主机名', help_text='主机名')
    launched_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='启动时间', db_comment='启动时间', help_text='启动时间')
    private_v4 = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='私有IP', db_comment='私有IP', help_text='私有IP')
    public_v4 = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='公有IP', db_comment='公有IP', help_text='公有IP')
    baremetal_node_id = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='裸金属节点ID', db_comment='裸金属节点ID', help_text='裸金属节点ID')
    # 下面是公共字段
    created_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    expire_at = models.DateTimeField(
        blank=True, null=True,
        verbose_name='过期时间', db_comment='过期时间', help_text='过期时间')
    sync_time = models.DateTimeField(
        blank=True, null=True, auto_now=True,
        verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    create_msg = models.TextField(
        blank=True, null=True,
        verbose_name='创建结果信息', db_comment='创建结果信息', help_text='创建结果信息'
    )
    belong_ticket_id = models.CharField(
        max_length=63, default='', null=True, blank=True,
        verbose_name='所属工单ID', help_text='所属工单ID', db_comment='所属工单ID'
    )
    extr_public_ipv4 = models.CharField(
        max_length=63, default='', blank=True, null=True,
        verbose_name='扩展公有IP', help_text='扩展公有IP', db_comment='扩展公有IP'
    )
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')
    # 2025-01-14 添加
    is_reachable = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='是否可达(Ping)', db_comment='是否可达(Ping)', help_text='是否可达(Ping)'
    )
    last_check_reachable_datetime = models.DateTimeField(
        blank=True, null=True,
        verbose_name='最后检查时间', db_comment='最后检查时间', help_text='最后检查时间'
    )

    # login_service_status = models.BooleanField(
    #     default=False, blank=True, null=True,
    #     verbose_name='登录服务状态', db_comment='登录服务状态', help_text='登录服务状态'
    # )
    # os_type = models.CharField(
    #     max_length=31, blank=True, null=True,
    #     verbose_name='操作系统类型', db_comment='操作系统类型', help_text='操作系统类型'
    # )
    # login_service_port = models.IntegerField(
    #     blank=True, null=True,
    #     verbose_name='登录服务端口', db_comment='登录服务端口', help_text='登录服务端口'
    # )
    is_lock = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='是否锁定', db_comment='是否锁定', help_text='是否锁定'
    )

    def to_operatorcmdb_host(self):
        from dvadmin.celery_workers.sync_openstack_resource_worker import \
            async_openstack_server_to_operator_cmdb_host_worker
        if self.is_deleted:
            return
        async_openstack_server_to_operator_cmdb_host_worker.apply_async(args=(self.id,))

    class Meta:
        db_table = TABLE_PREFIX + 'op_servers'
        verbose_name = 'TenantOpenstack主机表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
        unique_together = ('is_deleted', 'instance_id')


class TenantOPServerSoftware(ChaosCoreTenantModel):
    """主机安装软件表"""
    SOFTWARE_INSTALL_STATUS = (
        ('软件未安装', '软件未安装'),
        ('软件安装中', '软件安装中'),
        ('软件安装调度失败', '软件安装调度失败'),
        ('软件安装完成', '软件安装完成'),
        ('软件安装失败', '软件安装失败'),
    )
    op_server = models.ForeignKey(
        TenantOpenstackServer, to_field='instance_id', blank=True, null=True, db_constraint=False, on_delete=models.CASCADE,
        verbose_name='OpenStack实例ID', help_text='OpenStack实例ID', db_comment='OpenStack实例ID'
    )
    status = models.CharField(
        max_length=63, blank=True, null=True, default='软件未安装',
        verbose_name='软件安装状态', help_text='软件安装状态', db_comment='软件安装状态',
    )
    # 非必填，用于优化查询
    ticket_id = models.CharField(
        max_length=63, blank=True, null=True, default=None,
        verbose_name='工单ID', help_text='工单ID', db_comment='工单ID'
    )
    scheduletask_work_id = models.CharField(
        max_length=63, blank=True, null=True, default=None,
        verbose_name='worker任务ID', help_text='worker任务ID', db_comment='worker任务ID'
    )
    software_options = models.JSONField(
        blank=True, null=True, default=dict,
        verbose_name='软件安装选项', help_text='软件安装选项', db_comment='软件安装选项'
    )
    is_finished = models.BooleanField(
        default=False,
        verbose_name='是否结束', help_text='是否结束', db_comment='是否完成'
    )
    is_success = models.BooleanField(
        default=False,
        verbose_name='是否成功', help_text='是否成功', db_comment='是否成功'
    )
    retry_count = models.IntegerField(
        default=0,
        verbose_name='重试次数', help_text='重试次数', db_comment='重试次数'
    )
    failed_reason = models.TextField(
        blank=True, null=True,
        verbose_name='失败原因', help_text='失败原因', db_comment='失败原因'
    )
    node = models.CharField(
        max_length=63, blank=True, null=True, default='',
        verbose_name='节点', help_text='节点', db_comment='节点'
    )

    class Meta:
        db_table = TABLE_PREFIX + "ops_softwares"
        verbose_name = "主机关联软件表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)
        unique_together = ('op_server', 'id',)



class TenantOpenstackImage(ChaosCoreTenantModel):
    """
    openstack server instance
    """
    image_id = models.CharField(
        max_length=255, unique=True,
        verbose_name='镜像ID', db_comment='镜像ID', help_text='镜像ID')
    name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='镜像名称', db_comment='镜像名称', help_text='镜像名称')
    visibility = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='可见性', db_comment='可见性', help_text='可见性')
    size = models.BigIntegerField(
        blank=True, null=True,
        verbose_name="大小", db_comment='大小', help_text='大小')
    status = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='状态', db_comment='状态', help_text='状态')
    min_ram = models.IntegerField(
        blank=True, null=True,
        verbose_name="最小内存", db_comment='最小内存', help_text='最小内存')
    min_disk = models.IntegerField(
        blank=True, null=True,
        verbose_name="最小磁盘", db_comment='最小磁盘', help_text='最小磁盘')
    image_type = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='镜像类型', db_comment='镜像类型', help_text='镜像类型')
    disk_format = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='磁盘格式', db_comment='磁盘格式', help_text='磁盘格式')
    protected = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='受保护的', db_comment='受保护的', help_text='受保护的')
    project_name = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='项目名称', db_comment='项目名称', help_text='项目名称')
    owner_id = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='owner id', db_comment='owner id', help_text='owner id')
    owner_name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='owner名称', db_comment='owner名称', help_text='owner名称')
    is_to_portal = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='是否展示到前端', db_comment='是否展示到前端', help_text='是否展示到前端')
    system_type = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='系统类型', db_comment='系统类型', help_text='系统类型')
    os_type = models.CharField(
        max_length=127, blank=True, null=True, default='linux',
        verbose_name='操作系统类型', db_comment='操作系统类型', help_text='操作系统类型')
    # 下面是公共字段
    created_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    sync_time = models.DateTimeField(
        blank=True, null=True,
        verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')

    class Meta:
        db_table = TABLE_PREFIX + 'op_images'
        verbose_name = 'TenantOpenstack镜像表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']


class TenantOpenstackFlavor(ChaosCoreTenantModel):
    """
    openstack flavor 实例规格
    """
    flavor_id = models.CharField(
        max_length=255, unique=True,
        verbose_name='实例规格ID', db_comment='实例规格ID', help_text='实例规格ID')
    flavor_type = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='规格类型', db_comment='规格类型', help_text='规格类型')
    name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='规格名称', db_comment='规格名称', help_text='规格名称')
    ram = models.IntegerField(
        blank=True, null=True,
        verbose_name="内存", db_comment='内存', help_text='内存')
    vcpus = models.IntegerField(
        blank=True, null=True,
        verbose_name="cpu", db_comment='cpu', help_text='cpu')
    disk = models.IntegerField(
        blank=True, null=True,
        verbose_name="系统盘", db_comment='系统盘', help_text='系统盘')
    ephemeral = models.IntegerField(
        blank=True, null=True,
        verbose_name="临时存储", db_comment='临时存储', help_text='临时存储')
    swap = models.IntegerField(
        blank=True, null=True,
        verbose_name="swap", db_comment='swap', help_text='swap')
    rxtx_factor = models.CharField(
        max_length=127, blank=True, null=True,
        verbose_name='RX/TX factor', db_comment='RX/TX factor', help_text='RX/TX factor')
    is_public = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='公开', db_comment='公开', help_text='公开')
    is_to_portal = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='门户展示', db_comment='门户展示', help_text='门户展示'
    )
    extra_specs = models.JSONField(
        blank=True, null=True,
        verbose_name='metadata', db_comment='metadata', help_text='metadata')
    project_name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='项目名称', db_comment='项目名称', help_text='项目名称')
    # 下面是公共字段
    sync_time = models.DateTimeField(
        blank=True, null=True,
        verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')

    class Meta:
        db_table = TABLE_PREFIX + 'op_flavors'
        verbose_name = 'TenantOpenstack实例规格表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']


class TenantOpenstackSecurityGroup(ChaosCoreTenantModel):
    """
    openstack SecurityGroup 安全组
    """
    sg_id = models.CharField(
        max_length=255, unique=True,
        verbose_name='安全组ID', db_comment='安全组ID', help_text='安全组ID')
    name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='规格名称', db_comment='规格名称', help_text='规格名称')
    security_group_rules = models.JSONField(
        blank=True, verbose_name='安全组规则', db_comment='安全组规则', help_text='安全组规则')
    is_shared = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='共享的', db_comment='共享的', help_text='共享的')
    # 下面是公共字段
    created_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    sync_time = models.DateTimeField(
        blank=True, null=True,
        verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')

    class Meta:
        db_table = TABLE_PREFIX + 'op_security_groups'
        verbose_name = 'TenantOpenstack安全组表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']


class TenantOpenstackIronicHypervisor(ChaosCoreTenantModel):
    """
    openstack IronicHypervisor 裸金属节点
    """
    ironic_hyper_id = models.CharField(
        max_length=255, unique=True,
        verbose_name='裸金属节点ID', db_comment='裸金属节点ID', help_text='裸金属节点ID')
    name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='名称', db_comment='名称', help_text='名称')
    power_state = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='电源状态', db_comment='电源状态', help_text='电源状态')
    provision_state = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='配置状态', db_comment='配置状态', help_text='配置状态')
    instance_id = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='实例ID', db_comment='实例ID', help_text='实例ID')
    # k8s中的调度节点
    conductor = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='conductor', db_comment='conductor', help_text='conductor')
    driver = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='驱动', db_comment='驱动', help_text='驱动')
    driver_info = models.JSONField(
        blank=True, verbose_name='驱动信息', db_comment='驱动信息', help_text='驱动信息')
    maintenance_reason = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='维护原因', db_comment='维护原因', help_text='维护原因')
    is_protected = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='预留状态', db_comment='预留状态', help_text='预留状态')
    protected_reason = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='预留原因', db_comment='预留原因', help_text='预留原因')
    is_maintenance = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='维护状态', db_comment='维护状态', help_text='维护状态')
    is_automated_clean_enabled = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='自清理状态', db_comment='自清理状态', help_text='自清理状态')
    is_console_enabled = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='是否允许控制台', db_comment='是否允许控制台', help_text='是否允许控制台')
    last_error = models.TextField(
        blank=True, null=True, verbose_name='最后错误', db_comment='最后错误', help_text='最后错误')
    properties = models.JSONField(
        blank=True, null=True, verbose_name='属性', db_comment='属性', help_text='属性')
    resource_class = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='资源类', db_comment='资源类', help_text='资源类')
    network_interface = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='网络接口', db_comment='网络接口', help_text='网络接口'
    )
    management_interface = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='管理接口', db_comment='管理接口', help_text='管理接口')
    # 下面是公共字段
    created_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    sync_time = models.DateTimeField(
        blank=True, null=True,
        verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')
    is_lock = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='是否锁定', db_comment='是否锁定', help_text='是否锁定')

    class Meta:
        db_table = TABLE_PREFIX + 'op_ironic_phys'
        verbose_name = 'TenantOpenstack裸金属节点表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']


class TenantOpenstackIronicHypervisorRegister(ChaosCoreTenantModel):
    """
    裸机节点注册任务表管理
    """
    name = models.CharField(
        max_length=63, null=True, blank=True,
        verbose_name='名称', db_comment='名称', help_text='名称')
    node_resource_class = models.CharField(
        max_length=63, null=True, blank=False, default='ironic-4090',
        verbose_name='裸机信息-资源类', db_comment='裸机信息-资源类', help_text='裸机信息-资源类'
    )
    node_driver = models.CharField(
        max_length=63, null=True, blank=False, default='ipmi',
        verbose_name='裸机信息-节点驱动', db_comment='裸机信息-节点驱动', help_text='裸机信息-节点驱动'
    )
    node_network_interface = models.CharField(
        max_length=63, null=True, blank=False, default='neutron',
        verbose_name='裸机信息-管理接口', db_comment='裸机信息-管理接口', help_text='裸机信息-管理接口'
    )
    # {"cpus": "144","memory_mb": "515072","local_gb": "400",
    #             "cpu_arch": "x86_64",
    #             "capabilities": "boot_option:local,disk_label:gpt"
    #             },
    node_properties = models.JSONField(
        null=True, blank=True, default=dict,
        verbose_name='裸机信息-属性', db_comment='裸机信息-属性', help_text='裸机信息-属性'
    )
    node_driver_ipmi_username = models.CharField(
        max_length=63, null=True, blank=True, default='ADMIN',
        verbose_name='裸机IPMI信息-用户名', db_comment='裸机IPMI信息-用户名', help_text='裸机IPMI信息-用户名'
    )
    node_driver_ipmi_password = models.CharField(
        max_length=63, null=True, blank=True, default='ADMIN',
        verbose_name='裸机IPMI信息-密码', db_comment='裸机IPMI信息-密码', help_text='裸机IPMI信息-密码'
    )
    node_driver_ipmi_address = models.CharField(
        max_length=63, null=True, blank=True,
        verbose_name='裸机IPMI信息-地址', db_comment='裸机IPMI信息-地址', help_text='裸机IPMI信息-地址'
    )
    port_group_mode = models.CharField(
        max_length=63, null=True, blank=False, default='802.3ad',
        verbose_name='端口组-模式', db_comment='端口组-模式', help_text='端口组-模式'
    )
    port_group_standalone_ports_supported = models.BooleanField(
        null=True, blank=True, default=True,
        verbose_name='端口组-SA-Ports', db_comment='端口组-SA-Ports', help_text='端口组-SA-Ports'
    )
    # {"miimon": '100',"xmit_hash_policy": "layer2+3"}
    port_group_properties = models.JSONField(
        null=True, blank=True, default=dict,
        verbose_name='端口组-属性', db_comment='端口组-属性', help_text='端口组-属性'
    )
    port_switch_id = models.CharField(
        max_length=63, null=True, blank=False,
        verbose_name='端口-交换机ID', db_comment='端口-交换机ID', help_text='端口-交换机ID'
    )
    port_switch_info = models.CharField(
        max_length=255, null=True, blank=False,
        verbose_name='端口-交换机信息', db_comment='端口-交换机信息', help_text='端口-交换机信息'
    )
    port_port_id = models.CharField(
        max_length=63, null=True, blank=False,
        verbose_name='端口-VLAN-端口ID', db_comment='端口-VLAN-端口ID', help_text='端口-VLAN-端口ID'
    )
    port_hardware_address = models.CharField(
        max_length=63, null=True, blank=False,
        verbose_name='端口-裸机节点硬件地址', db_comment='端口-裸机节点硬件地址', help_text='端口-裸机节点硬件地址'
    )
    port_network_address = models.CharField(
        max_length=63, null=True, blank=False,
        verbose_name='端口-裸机节点网络目标地址', db_comment='端口-裸机节点网络目标地址', help_text='端口-裸机节点网络目标地址'
    )
    register_status = models.CharField(
        max_length=63, null=True, blank=True, default='未开始',
        verbose_name='注册状态', db_comment='注册状态', help_text='注册状态'
    )
    is_success  = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='是否成功', db_comment='是否成功', help_text='是否成功')
    created_logger_info = models.TextField(
        null=True, blank=True,
        verbose_name='创建日志信息', db_comment='创建日志信息', help_text='创建日志信息'
    )
    created_hyp_ironic_node = models.ForeignKey(
        TenantOpenstackIronicHypervisor, to_field='ironic_hyper_id', related_query_name='op_ironic_node_query', null=True,
        on_delete=models.SET_NULL, db_constraint=False,
        db_comment='所属裸机节点', verbose_name='所属裸机节点', help_text="所属裸机节点",
    )
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='区域节点', help_text='区域节点', db_comment='区域节点'
    )
    sn = models.CharField(
        max_length=255, null=True, blank=True,
        verbose_name='SN', db_comment='SN', help_text='SN'
    )

    class Meta:
        db_table = TABLE_PREFIX + 'op_ironic_phy_registers'
        verbose_name = '裸机节点注册任务表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']


class TenantHisecEnginePublicIP(ChaosCoreTenantModel):
    """
    Hisec防火墙公网IP
    """
    public_ip = models.CharField(
        max_length=63, unique=True, null=False, blank=False, db_index=True,
        verbose_name='公网IP', db_comment='公网IP', help_text='公网IP')
    used = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='使用中', db_comment='使用中', help_text='使用中')
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
    )
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')
    class Meta:
        db_table = TABLE_PREFIX + 'hisec_engine_public_ip'
        verbose_name = 'TenantHisec防火墙公网IP表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']


class TenantHisecEngineNatServerPolicy(ChaosCoreTenantModel):
    """
    Hisec防火墙NAT策略
    """
    name = models.CharField(
        max_length=63, null=True, blank=True,
        verbose_name='名称', db_comment='名称', help_text='名称')
    public_ip = models.ForeignKey(
        TenantHisecEnginePublicIP, to_field='public_ip', related_query_name='hisec_public_ip_query',
        null=True, blank=False, on_delete=models.SET_NULL,
        verbose_name='公网IP', db_comment='公网IP', help_text='公网IP'
    )
    public_port = models.IntegerField(
        null=True, blank=True,
        verbose_name='公网端口', db_comment='公网端口', help_text='公网端口'
    )
    inside_ip = models.CharField(
        max_length=63, null=True, blank=False,
        verbose_name='内网IP', db_comment='内网IP', help_text='内网IP'
    )
    inside_port = models.IntegerField(
        null=True, blank=True,
        verbose_name='内网端口', db_comment='内网端口', help_text='内网端口'
    )
    protocol = models.CharField(
        max_length=15, null=True, blank=True, default='any',
        verbose_name='协议', db_comment='协议', help_text='协议'
    )
    op_server = models.ForeignKey(
        TenantOpenstackServer, to_field='instance_id', related_query_name='op_server_query', null=True,
        on_delete=models.SET_NULL, db_constraint=False,
        db_comment='所属主机', verbose_name='所属主机', help_text="所属主机",
    )
    node = models.CharField(
        max_length=63, default='金华', null=True, blank=True,
        verbose_name='节点', help_text='节点', db_comment='节点'
        )
    project_id = models.CharField(
        max_length=255, null=True, blank=True, default='',
        verbose_name='openstack项目ID', help_text='openstack项目ID', db_comment='openstack项目ID')

    class Meta:
        db_table = TABLE_PREFIX + 'hisec_engine_nat_server_policies'
        verbose_name = 'TenantHisec防火墙NAT策略表'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
