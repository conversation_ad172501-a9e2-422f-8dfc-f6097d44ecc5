# Generated by Django 5.0.6 on 2025-03-17 14:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0012_tenanthisecenginepublicip_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='tenanthisecenginenatserverpolicy',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
        migrations.AddField(
            model_name='tenanthisecenginepublicip',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
    ]
