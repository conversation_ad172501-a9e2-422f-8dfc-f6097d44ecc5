# Generated by Django 5.0.6 on 2024-12-03 11:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0004_tenantopenstackvlan_enable_dhcp_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='tenantopenstackvlan',
            name='enable_dhcp',
            field=models.CharField(blank=True, db_comment='是否启用dhcp', default='on', help_text='是否启用dhcp', max_length=15, null=True, verbose_name='是否启用dhcp'),
        ),
        migrations.AlterField(
            model_name='tenantopenstackvlan',
            name='network_type',
            field=models.CharField(blank=True, db_comment='网络类型', default='vlan', help_text='网络类型', max_length=63, null=True, verbose_name='网络类型'),
        ),
    ]
