# Generated by Django 5.0.6 on 2024-11-20 15:43

import django.db.models.deletion
import dvadmin.utils.custom_encrypt_field
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TenantAccount',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('account_name', models.CharField(db_comment='用户账号', db_index=True, help_text='用户账号', max_length=255, unique=True, verbose_name='用户账号')),
                ('email', dvadmin.utils.custom_encrypt_field.EncryptedCharField(blank=True, db_comment='邮箱', default='<EMAIL>', help_text='邮箱', max_length=1023, verbose_name='邮箱')),
                ('mobile', dvadmin.utils.custom_encrypt_field.EncryptedCharField(blank=True, db_comment='电话', default='***********', help_text='电话', max_length=255, verbose_name='电话')),
                ('avatar', models.CharField(blank=True, db_comment='头像', help_text='头像', max_length=255, null=True, verbose_name='头像')),
                ('company', dvadmin.utils.custom_encrypt_field.EncryptedCharField(blank=True, db_comment='公司名称', default='Unknown', help_text='公司名称', max_length=1023, verbose_name='公司名称')),
                ('account_nick_name', models.CharField(db_comment='账号花名', db_index=True, help_text='账号花名', max_length=255, verbose_name='账号花名')),
                ('email_is_verified', models.BooleanField(blank=True, db_comment='邮箱是否验证', default=False, help_text='邮箱是否验证', verbose_name='邮箱是否验证')),
                ('mobile_is_verified', models.BooleanField(blank=True, db_comment='手机是否验证', default=False, help_text='手机是否验证', verbose_name='手机是否验证')),
                ('company_is_verified', models.BooleanField(blank=True, db_comment='公司是否验证', default=False, help_text='公司是否验证', verbose_name='公司是否验证')),
                ('is_master', models.BooleanField(blank=True, db_comment='是否主账号', default=False, help_text='是否主账号', verbose_name='是否主账号')),
                ('account_type', models.IntegerField(blank=True, choices=[(0, '个人账号'), (1, '公司账号'), (2, '测试账号'), (3, '内部账号')], db_comment='账号类型', default=2, help_text='账号类型', null=True, verbose_name='账号类型')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'tenant账户表',
                'verbose_name_plural': 'tenant账户表',
                'db_table': 'tenant_accounts',
                'ordering': ('-create_datetime',),
                'unique_together': {('account_name', 'account_type', 'is_master', 'is_deleted')},
            },
        ),
        migrations.CreateModel(
            name='TenantAccountGroup',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('name', models.CharField(help_text='名称', max_length=64, verbose_name='名称')),
                ('description', models.TextField(help_text='描述', verbose_name='描述')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'tenant用户组表',
                'verbose_name_plural': 'tenant用户组表',
                'db_table': 'tenant_account_groups',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='TenantMenu',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('icon', models.CharField(blank=True, help_text='菜单图标', max_length=64, null=True, verbose_name='菜单图标')),
                ('name', models.CharField(help_text='菜单名称', max_length=64, verbose_name='菜单名称')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('is_link', models.BooleanField(default=False, help_text='是否外链', verbose_name='是否外链')),
                ('link_url', models.CharField(blank=True, help_text='链接地址', max_length=255, null=True, verbose_name='链接地址')),
                ('is_catalog', models.BooleanField(default=False, help_text='是否目录', verbose_name='是否目录')),
                ('web_path', models.CharField(blank=True, help_text='路由地址', max_length=128, null=True, verbose_name='路由地址')),
                ('component', models.CharField(blank=True, help_text='组件地址', max_length=128, null=True, verbose_name='组件地址')),
                ('component_name', models.CharField(blank=True, help_text='组件名称', max_length=50, null=True, verbose_name='组件名称')),
                ('status', models.BooleanField(blank=True, default=True, help_text='菜单状态', verbose_name='菜单状态')),
                ('cache', models.BooleanField(blank=True, default=False, help_text='是否页面缓存', verbose_name='是否页面缓存')),
                ('visible', models.BooleanField(blank=True, default=True, help_text='侧边栏中是否显示', verbose_name='侧边栏中是否显示')),
                ('is_iframe', models.BooleanField(blank=True, default=False, help_text='框架外显示', verbose_name='框架外显示')),
                ('is_affix', models.BooleanField(blank=True, default=False, help_text='是否固定', verbose_name='是否固定')),
                ('is_public', models.BooleanField(blank=True, db_comment='是否tenant公共菜单', default=False, help_text='是否tenant公共菜单', verbose_name='是否tenant公共菜单')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('parent', models.ForeignKey(blank=True, db_constraint=False, help_text='上级菜单', null=True, on_delete=django.db.models.deletion.CASCADE, to='tenant.tenantmenu', to_field='id', verbose_name='上级菜单')),
            ],
            options={
                'verbose_name': 'tenant菜单表',
                'verbose_name_plural': 'tenant菜单表',
                'db_table': 'tenant_menus',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='TenantMenuButton',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('name', models.CharField(help_text='名称', max_length=64, verbose_name='名称')),
                ('value', models.CharField(help_text='权限值', max_length=64, unique=True, verbose_name='权限值')),
                ('api', models.CharField(help_text='接口地址', max_length=200, verbose_name='接口地址')),
                ('is_public', models.BooleanField(blank=True, db_comment='是否公共', default=False, help_text='是否公共', verbose_name='是否公共')),
                ('method', models.IntegerField(blank=True, default=0, help_text='接口请求方法', null=True, verbose_name='接口请求方法')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('menu', models.ForeignKey(db_constraint=False, help_text='关联菜单', on_delete=django.db.models.deletion.CASCADE, related_name='tenantMenuPermission', to='tenant.tenantmenu', to_field='id', verbose_name='关联菜单')),
            ],
            options={
                'verbose_name': 'tenant菜单权限表',
                'verbose_name_plural': 'tenant菜单权限表',
                'db_table': 'tenant_menu_buttons',
                'ordering': ('-name',),
            },
        ),
        migrations.CreateModel(
            name='TenantAccountGroupPermission',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('permission_type', models.CharField(choices=[('menu_button', '按钮权限'), ('menu', '菜单权限')], db_comment='权限类型', db_index=True, default='menu_button', help_text='权限类型', max_length=63, verbose_name='权限类型')),
                ('account_group', models.ForeignKey(db_constraint=False, help_text='关联tenant用户组', on_delete=django.db.models.deletion.CASCADE, to='tenant.tenantaccountgroup', to_field='id', verbose_name='关联tenant用户组')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('menu', models.ForeignKey(db_constraint=False, help_text='关联tenant菜单', on_delete=django.db.models.deletion.CASCADE, to='tenant.tenantmenu', to_field='id', verbose_name='关联tenant菜单')),
                ('menu_button', models.ForeignKey(db_constraint=False, help_text='关联tenant按钮', on_delete=django.db.models.deletion.CASCADE, to='tenant.tenantmenubutton', to_field='id', verbose_name='关联tenant按钮')),
            ],
            options={
                'verbose_name': 'tenant用户组菜单权限表',
                'verbose_name_plural': 'tenant用户组菜单权限表',
                'db_table': 'tenant_account_group_permissions',
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackFlavor',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('flavor_id', models.CharField(db_comment='实例规格ID', help_text='实例规格ID', max_length=255, unique=True, verbose_name='实例规格ID')),
                ('flavor_type', models.CharField(blank=True, db_comment='规格类型', help_text='规格类型', max_length=63, null=True, verbose_name='规格类型')),
                ('name', models.CharField(blank=True, db_comment='规格名称', help_text='规格名称', max_length=255, null=True, verbose_name='规格名称')),
                ('ram', models.IntegerField(blank=True, db_comment='内存', help_text='内存', null=True, verbose_name='内存')),
                ('vcpus', models.IntegerField(blank=True, db_comment='cpu', help_text='cpu', null=True, verbose_name='cpu')),
                ('disk', models.IntegerField(blank=True, db_comment='系统盘', help_text='系统盘', null=True, verbose_name='系统盘')),
                ('ephemeral', models.IntegerField(blank=True, db_comment='临时存储', help_text='临时存储', null=True, verbose_name='临时存储')),
                ('swap', models.IntegerField(blank=True, db_comment='swap', help_text='swap', null=True, verbose_name='swap')),
                ('rxtx_factor', models.CharField(blank=True, db_comment='RX/TX factor', help_text='RX/TX factor', max_length=127, null=True, verbose_name='RX/TX factor')),
                ('is_public', models.BooleanField(blank=True, db_comment='公开', default=False, help_text='公开', null=True, verbose_name='公开')),
                ('extra_specs', models.JSONField(blank=True, db_comment='metadata', help_text='metadata', null=True, verbose_name='metadata')),
                ('project_id', models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=255, null=True, verbose_name='项目id')),
                ('project_name', models.CharField(blank=True, db_comment='项目名称', help_text='项目名称', max_length=255, null=True, verbose_name='项目名称')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantOpenstack实例规格表',
                'verbose_name_plural': 'TenantOpenstack实例规格表',
                'db_table': 'tenant_op_flavors',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackImage',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('image_id', models.CharField(db_comment='镜像ID', help_text='镜像ID', max_length=255, unique=True, verbose_name='镜像ID')),
                ('name', models.CharField(blank=True, db_comment='镜像名称', help_text='镜像名称', max_length=255, null=True, verbose_name='镜像名称')),
                ('visibility', models.CharField(blank=True, db_comment='可见性', help_text='可见性', max_length=127, null=True, verbose_name='可见性')),
                ('size', models.BigIntegerField(blank=True, db_comment='大小', help_text='大小', null=True, verbose_name='大小')),
                ('status', models.CharField(blank=True, db_comment='状态', help_text='状态', max_length=127, null=True, verbose_name='状态')),
                ('min_ram', models.IntegerField(blank=True, db_comment='最小内存', help_text='最小内存', null=True, verbose_name='最小内存')),
                ('min_disk', models.IntegerField(blank=True, db_comment='最小磁盘', help_text='最小磁盘', null=True, verbose_name='最小磁盘')),
                ('image_type', models.CharField(blank=True, db_comment='镜像类型', help_text='镜像类型', max_length=127, null=True, verbose_name='镜像类型')),
                ('disk_format', models.CharField(blank=True, db_comment='磁盘格式', help_text='磁盘格式', max_length=127, null=True, verbose_name='磁盘格式')),
                ('protected', models.BooleanField(blank=True, db_comment='受保护的', default=False, help_text='受保护的', null=True, verbose_name='受保护的')),
                ('project_id', models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=255, null=True, verbose_name='项目id')),
                ('project_name', models.CharField(blank=True, db_comment='项目名称', help_text='项目名称', max_length=127, null=True, verbose_name='项目名称')),
                ('owner_id', models.CharField(blank=True, db_comment='owner id', help_text='owner id', max_length=255, null=True, verbose_name='owner id')),
                ('owner_name', models.CharField(blank=True, db_comment='owner名称', help_text='owner名称', max_length=255, null=True, verbose_name='owner名称')),
                ('created_at', models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间')),
                ('updated_at', models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantOpenstack镜像表',
                'verbose_name_plural': 'TenantOpenstack镜像表',
                'db_table': 'tenant_op_images',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackIronicHypervisor',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('ironic_hyper_id', models.CharField(db_comment='裸金属节点ID', help_text='裸金属节点ID', max_length=255, unique=True, verbose_name='裸金属节点ID')),
                ('name', models.CharField(blank=True, db_comment='名称', help_text='名称', max_length=255, null=True, verbose_name='名称')),
                ('power_state', models.CharField(blank=True, db_comment='电源状态', help_text='电源状态', max_length=63, null=True, verbose_name='电源状态')),
                ('provision_state', models.CharField(blank=True, db_comment='配置状态', help_text='配置状态', max_length=63, null=True, verbose_name='配置状态')),
                ('instance_id', models.CharField(blank=True, db_comment='实例ID', help_text='实例ID', max_length=63, null=True, verbose_name='实例ID')),
                ('conductor', models.CharField(blank=True, db_comment='conductor', help_text='conductor', max_length=255, null=True, verbose_name='conductor')),
                ('driver', models.CharField(blank=True, db_comment='驱动', help_text='驱动', max_length=63, null=True, verbose_name='驱动')),
                ('driver_info', models.JSONField(blank=True, db_comment='驱动信息', help_text='驱动信息', verbose_name='驱动信息')),
                ('maintenance_reason', models.CharField(blank=True, db_comment='维护原因', help_text='维护原因', max_length=255, null=True, verbose_name='维护原因')),
                ('is_protected', models.BooleanField(blank=True, db_comment='预留状态', default=False, help_text='预留状态', null=True, verbose_name='预留状态')),
                ('protected_reason', models.CharField(blank=True, db_comment='预留原因', help_text='预留原因', max_length=255, null=True, verbose_name='预留原因')),
                ('is_maintenance', models.BooleanField(blank=True, db_comment='维护状态', default=False, help_text='维护状态', null=True, verbose_name='维护状态')),
                ('is_automated_clean_enabled', models.BooleanField(blank=True, db_comment='自清理状态', default=False, help_text='自清理状态', null=True, verbose_name='自清理状态')),
                ('is_console_enabled', models.BooleanField(blank=True, db_comment='是否允许控制台', default=False, help_text='是否允许控制台', null=True, verbose_name='是否允许控制台')),
                ('last_error', models.TextField(blank=True, db_comment='最后错误', help_text='最后错误', null=True, verbose_name='最后错误')),
                ('properties', models.JSONField(blank=True, db_comment='属性', help_text='属性', null=True, verbose_name='属性')),
                ('resource_class', models.CharField(blank=True, db_comment='资源类', help_text='资源类', max_length=63, null=True, verbose_name='资源类')),
                ('network_interface', models.CharField(blank=True, db_comment='网络接口', help_text='网络接口', max_length=63, null=True, verbose_name='网络接口')),
                ('management_interface', models.CharField(blank=True, db_comment='管理接口', help_text='管理接口', max_length=63, null=True, verbose_name='管理接口')),
                ('project_id', models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=127, null=True, verbose_name='项目id')),
                ('created_at', models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间')),
                ('updated_at', models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantOpenstack裸金属节点表',
                'verbose_name_plural': 'TenantOpenstack裸金属节点表',
                'db_table': 'tenant_op_ironic_phys',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackMetadata',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('resource_id', models.CharField(db_comment='关联资源ID', help_text='关联资源ID', max_length=63, verbose_name='关联资源ID')),
                ('type', models.IntegerField(blank=True, choices=[(0, '实例规格'), (1, '实例镜像')], db_comment='类型', help_text='类型', null=True, verbose_name='类型')),
                ('is_public', models.BooleanField(blank=True, db_comment='是否公共', default=False, help_text='是否公共', null=True, verbose_name='是否公共')),
                ('key', models.CharField(db_comment='key名称', help_text='key名称', max_length=255, verbose_name='key名称')),
                ('value', models.CharField(blank=True, db_comment='key值', help_text='key值', max_length=255, null=True, verbose_name='key值')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantResourceMetadata元数据表',
                'verbose_name_plural': 'TenantResourceMetadata元数据表',
                'db_table': 'tenant_op_metadatas',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackNetwork',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('network_id', models.CharField(db_comment='网络ID', help_text='网络ID', max_length=255, unique=True, verbose_name='网络ID')),
                ('name', models.CharField(blank=True, db_comment='网络名称', help_text='网络名称', max_length=255, null=True, verbose_name='网络名称')),
                ('subnet_ids', models.JSONField(blank=True, db_comment='子网ID', help_text='子网ID', null=True, verbose_name='子网ID')),
                ('mtu', models.IntegerField(blank=True, db_comment='mtu', help_text='mtu', null=True, verbose_name='mtu')),
                ('status', models.CharField(blank=True, db_comment='状态', help_text='状态', max_length=63, null=True, verbose_name='状态')),
                ('availability_zones', models.JSONField(blank=True, db_comment='可用区', help_text='可用区', null=True, verbose_name='可用区')),
                ('is_admin_state_up', models.BooleanField(blank=True, db_comment='可管理', default=False, help_text='可管理', null=True, verbose_name='可管理')),
                ('is_router_external', models.BooleanField(blank=True, db_comment='外部的', default=False, help_text='外部的', null=True, verbose_name='外部的')),
                ('is_shared', models.BooleanField(blank=True, db_comment='共享的', default=False, help_text='共享的', null=True, verbose_name='共享的')),
                ('is_default', models.BooleanField(blank=True, db_comment='共享的', default=False, help_text='共享的', null=True, verbose_name='默认的')),
                ('provider_network_type', models.CharField(blank=True, db_comment='网络类型', help_text='网络类型', max_length=255, null=True, verbose_name='网络类型')),
                ('provider_physical_network', models.CharField(blank=True, db_comment='物理网络', help_text='物理网络', max_length=255, null=True, verbose_name='物理网络')),
                ('provider_segmentation_id', models.IntegerField(blank=True, db_comment='段ID', help_text='段ID', null=True, verbose_name='段ID')),
                ('created_at', models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间')),
                ('updated_at', models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('project_id', models.CharField(blank=True, db_comment='项目ID', default=None, help_text='项目ID', max_length=63, null=True, verbose_name='项目ID')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantOpenstack网络表',
                'verbose_name_plural': 'TenantOpenstack网络表',
                'db_table': 'tenant_op_networks',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackProject',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('name', models.CharField(help_text='项目名称', max_length=63, verbose_name='项目名称')),
                ('project_id', models.CharField(blank=True, db_comment='openstack项目ID', default='default', help_text='openstack项目ID', max_length=63, null=True, verbose_name='openstack项目ID')),
                ('enabled', models.BooleanField(blank=True, db_comment='是否启用', default=True, help_text='是否启用', null=True, verbose_name='是否启用')),
                ('domain_id', models.CharField(blank=True, db_comment='域ID(仅做记录保留)', default='default', help_text='域ID(仅做记录保留)', max_length=63, null=True, verbose_name='域ID(仅做记录保留)')),
                ('parent_id', models.CharField(blank=True, db_comment='父项目ID(仅做记录保留)', default='', help_text='父项目ID(仅做记录保留)', max_length=63, null=True, verbose_name='父项目ID(仅做记录保留)')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('account', models.ForeignKey(help_text='关联tenant-account', on_delete=django.db.models.deletion.CASCADE, to='tenant.tenantaccount', to_field='id', verbose_name='关联tenant-account')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantOpenstack项目表',
                'verbose_name_plural': 'TenantOpenstack项目表',
                'db_table': 'tenant_op_projects',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackSecurityGroup',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('sg_id', models.CharField(db_comment='安全组ID', help_text='安全组ID', max_length=255, unique=True, verbose_name='安全组ID')),
                ('name', models.CharField(blank=True, db_comment='规格名称', help_text='规格名称', max_length=255, null=True, verbose_name='规格名称')),
                ('security_group_rules', models.JSONField(blank=True, db_comment='安全组规则', help_text='安全组规则', verbose_name='安全组规则')),
                ('project_id', models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=127, null=True, verbose_name='项目id')),
                ('is_shared', models.BooleanField(blank=True, db_comment='共享的', default=False, help_text='共享的', null=True, verbose_name='共享的')),
                ('created_at', models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间')),
                ('updated_at', models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantOpenstack安全组表',
                'verbose_name_plural': 'TenantOpenstack安全组表',
                'db_table': 'tenant_op_security_groups',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackSetting',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('node', models.CharField(db_comment='Openstack节点', db_index=True, help_text='Openstack节点', max_length=255, verbose_name='Openstack节点')),
                ('name', models.CharField(db_comment='配置键名称', db_index=True, default='', help_text='配置键名称', max_length=255, verbose_name='配置键名称')),
                ('key', models.CharField(db_comment='配置键', db_index=True, help_text='配置键', max_length=63, unique=True, verbose_name='配置键')),
                ('value', dvadmin.utils.custom_encrypt_field.EncryptedCharField(db_comment='配置值', help_text='配置值', max_length=2047, verbose_name='配置值')),
                ('enabled', models.BooleanField(db_comment='启用状态', default=True, help_text='启用状态', verbose_name='启用状态')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantOpenstack配置表',
                'verbose_name_plural': 'TenantOpenstack配置表',
                'db_table': 'tenant_op_settings',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackSubnet',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('subnet_id', models.CharField(db_comment='安全组ID', help_text='安全组ID', max_length=255, unique=True, verbose_name='ID')),
                ('name', models.CharField(blank=True, db_comment='安全组名称', help_text='安全组名称', max_length=255, null=True, verbose_name='安全组名称')),
                ('network_id', models.CharField(blank=True, db_comment='网络id', help_text='网络id', max_length=255, null=True, verbose_name='网络id')),
                ('ip_version', models.IntegerField(blank=True, db_comment='ip_version', help_text='ip_version', null=True, verbose_name='ip_version')),
                ('is_dhcp_enabled', models.BooleanField(blank=True, db_comment='DHCP开启', default=False, help_text='DHCP开启', null=True, verbose_name='DHCP开启')),
                ('gateway_ip', models.CharField(blank=True, db_comment='GW', help_text='GW', max_length=127, null=True, verbose_name='GW')),
                ('cidr', models.CharField(blank=True, db_comment='cidr', help_text='cidr', max_length=127, null=True, verbose_name='cidr')),
                ('allocation_pools', models.JSONField(blank=True, db_comment='网络分配池', help_text='网络分配池', null=True, verbose_name='网络分配池')),
                ('host_routes', models.JSONField(blank=True, db_comment='主机路由', help_text='主机路由', null=True, verbose_name='主机路由')),
                ('dns_nameservers', models.JSONField(blank=True, db_comment='DNS服务器', help_text='DNS服务器', null=True, verbose_name='DNS服务器')),
                ('created_at', models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间')),
                ('updated_at', models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('project_id', models.CharField(blank=True, db_comment='项目ID', default=None, help_text='项目ID', max_length=63, null=True, verbose_name='项目ID')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantOpenstack子网表',
                'verbose_name_plural': 'TenantOpenstack子网表',
                'db_table': 'tenant_op_subnets',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackVlan',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('name', models.CharField(blank=True, db_comment='vlan名称', default=None, help_text='vlan名称', max_length=63, null=True, verbose_name='vlan名称')),
                ('vlan_id', models.CharField(db_comment='vlan_id', default=None, help_text='vlan_id', max_length=63, verbose_name='vlan_id')),
                ('phy_vlan_type', models.CharField(blank=True, db_comment='物理vlan类型', default='vlan', help_text='物理vlan类型', max_length=63, null=True, verbose_name='物理vlan类型')),
                ('subnet', models.CharField(db_comment='子网网段', default=None, help_text='子网网段', max_length=255, verbose_name='子网网段')),
                ('available_ip_pool', models.CharField(blank=True, db_comment='可用IP池', default=None, help_text='可用IP池', max_length=255, null=True, verbose_name='可用IP池')),
                ('gateway_ip', models.CharField(db_comment='网关IP', default='0.0.0.0', help_text='网关IP', max_length=63, verbose_name='网关IP')),
                ('project_id', models.CharField(blank=True, db_comment='项目ID', default=None, help_text='项目ID', max_length=63, null=True, verbose_name='项目ID')),
                ('network_id', models.CharField(blank=True, db_comment='openstack中网络ID', default=None, help_text='openstack中网络ID', max_length=63, null=True, verbose_name='openstack中网络ID')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('is_used', models.BooleanField(blank=True, db_comment='是否在用', default=False, help_text='是否在用', verbose_name='是否在用')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantOpenstackVlan表',
                'verbose_name_plural': 'TenantOpenstackVlan表',
                'db_table': 'tenant_op_vlans',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='TenantOpenstackServer',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('instance_id', models.CharField(db_comment='OpenstackInstanceId全局唯一', help_text='OpenstackInstanceId全局唯一', max_length=127, null=True, unique=True, verbose_name='OpenstackInstanceId全局唯一')),
                ('instance_type', models.CharField(blank=True, db_comment='实例类型', help_text='实例类型', max_length=63, null=True, verbose_name='实例类型')),
                ('name', models.CharField(blank=True, db_comment='server名', help_text='server名', max_length=255, null=True, verbose_name='server名')),
                ('compute_host', models.CharField(blank=True, db_comment='宿主机', help_text='宿主机', max_length=127, null=True, verbose_name='宿主机')),
                ('net_name', models.CharField(blank=True, db_comment='网络名称', help_text='网络名称', max_length=255, null=True, verbose_name='网络名称')),
                ('ipaddr', models.CharField(blank=True, db_comment='IP address', help_text='IP address', max_length=127, null=True, verbose_name='IP address')),
                ('image_id', models.CharField(blank=True, db_comment='镜像ID', help_text='镜像ID', max_length=127, null=True, verbose_name='镜像ID')),
                ('image_name', models.CharField(blank=True, db_comment='镜像名', help_text='镜像名', max_length=127, null=True, verbose_name='镜像名')),
                ('image_info', models.JSONField(blank=True, db_comment='镜像信息', default=dict, help_text='镜像信息', null=True, verbose_name='镜像信息')),
                ('key_name', models.CharField(blank=True, db_comment='key pair name', help_text='key pair name', max_length=127, null=True, verbose_name='key pair name')),
                ('flavor_id', models.CharField(blank=True, db_comment='规格ID', help_text='规格ID', max_length=127, null=True, verbose_name='规格ID')),
                ('flavor_name', models.CharField(blank=True, db_comment='flavor name', help_text='flavor name', max_length=127, null=True, verbose_name='flavor_name')),
                ('flavor_info', models.JSONField(blank=True, db_comment='规格信息', default=dict, help_text='规格信息', null=True, verbose_name='规格信息')),
                ('vcpus', models.IntegerField(blank=True, db_comment='cpu核心', help_text='cpu核心', null=True, verbose_name='cpu核心')),
                ('disk', models.IntegerField(blank=True, db_comment='系统盘', help_text='系统盘', null=True, verbose_name='系统盘')),
                ('ram', models.IntegerField(blank=True, db_comment='内存', help_text='内存', null=True, verbose_name='内存')),
                ('security_groups', models.JSONField(blank=True, db_comment='安全组', help_text='安全组', null=True, verbose_name='安全组')),
                ('status', models.CharField(blank=True, db_comment='状态', help_text='状态', max_length=63, null=True, verbose_name='状态')),
                ('vm_state', models.CharField(blank=True, db_comment='vm状态', help_text='vm状态', max_length=63, null=True, verbose_name='vm状态')),
                ('metadata', models.JSONField(blank=True, db_comment='metadata', help_text='metadata', null=True, verbose_name='metadata')),
                ('host_id', models.CharField(blank=True, db_comment='vm_host_id', help_text='vm_host_id', max_length=63, null=True, verbose_name='vm_host_id')),
                ('hostname', models.CharField(blank=True, db_comment='主机名', help_text='主机名', max_length=127, null=True, verbose_name='主机名')),
                ('launched_at', models.CharField(blank=True, db_comment='启动时间', help_text='启动时间', max_length=255, null=True, verbose_name='启动时间')),
                ('private_v4', models.CharField(blank=True, db_comment='私有IP', help_text='私有IP', max_length=63, null=True, verbose_name='私有IP')),
                ('public_v4', models.CharField(blank=True, db_comment='公有IP', help_text='公有IP', max_length=63, null=True, verbose_name='公有IP')),
                ('baremetal_node_id', models.CharField(blank=True, db_comment='裸金属节点ID', help_text='裸金属节点ID', max_length=63, null=True, verbose_name='裸金属节点ID')),
                ('created_at', models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间')),
                ('updated_at', models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间')),
                ('expire_at', models.DateTimeField(blank=True, db_comment='过期时间', help_text='过期时间', null=True, verbose_name='过期时间')),
                ('sync_time', models.DateTimeField(auto_now=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('project_id', models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=255, null=True, verbose_name='项目id')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('create_msg', models.TextField(blank=True, db_comment='创建结果信息', help_text='创建结果信息', null=True, verbose_name='创建结果信息')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantOpenstack主机表',
                'verbose_name_plural': 'TenantOpenstack主机表',
                'db_table': 'tenant_op_servers',
                'ordering': ['-create_datetime'],
                'unique_together': {('is_deleted', 'instance_id')},
            },
        ),
    ]
