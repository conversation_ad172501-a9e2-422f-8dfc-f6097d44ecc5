# Generated by Django 5.0.6 on 2025-03-14 14:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0011_tenantopserversoftware'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TenantHisecEnginePublicIP',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.Char<PERSON>ield(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('public_ip', models.CharField(db_comment='公网IP', db_index=True, help_text='公网IP', max_length=63, unique=True, verbose_name='公网IP')),
                ('used', models.BooleanField(blank=True, db_comment='使用中', default=False, help_text='使用中', null=True, verbose_name='使用中')),
                ('node', models.CharField(blank=True, db_comment='节点', default='金华', help_text='节点', max_length=63, null=True, verbose_name='节点')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'TenantHisec防火墙公网IP表',
                'verbose_name_plural': 'TenantHisec防火墙公网IP表',
                'db_table': 'tenant_hisec_engine_public_ip',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='TenantHisecEngineNatServerPolicy',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('name', models.CharField(blank=True, db_comment='名称', help_text='名称', max_length=63, null=True, verbose_name='名称')),
                ('public_port', models.IntegerField(blank=True, db_comment='公网端口', help_text='公网端口', null=True, verbose_name='公网端口')),
                ('inside_ip', models.CharField(db_comment='内网IP', help_text='内网IP', max_length=63, null=True, verbose_name='内网IP')),
                ('inside_port', models.IntegerField(blank=True, db_comment='内网端口', help_text='内网端口', null=True, verbose_name='内网端口')),
                ('protocol', models.CharField(blank=True, db_comment='协议', default='any', help_text='协议', max_length=15, null=True, verbose_name='协议')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('op_server', models.ForeignKey(db_comment='所属主机', db_constraint=False, help_text='所属主机', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='op_server_query', to='tenant.tenantopenstackserver', to_field='instance_id', verbose_name='所属主机')),
                ('public_ip', models.ForeignKey(db_comment='公网IP', help_text='公网IP', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='hisec_public_ip_query', to='tenant.tenanthisecenginepublicip', to_field='public_ip', verbose_name='公网IP')),
            ],
            options={
                'verbose_name': 'TenantHisec防火墙NAT策略表',
                'verbose_name_plural': 'TenantHisec防火墙NAT策略表',
                'db_table': 'tenant_hisec_engine_nat_server_policies',
                'ordering': ['-create_datetime'],
                'unique_together': {('name', 'is_deleted')},
            },
        ),
    ]
