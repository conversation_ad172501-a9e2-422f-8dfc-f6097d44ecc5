# Generated by Django 5.0.6 on 2024-12-03 09:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0003_tenantopenstackflavor_is_to_portal'),
    ]

    operations = [
        migrations.AddField(
            model_name='tenantopenstackvlan',
            name='enable_dhcp',
            field=models.CharField(db_comment='是否启用dhcp', default='on', help_text='是否启用dhcp', max_length=15, verbose_name='是否启用dhcp'),
        ),
        migrations.AddField(
            model_name='tenantopenstackvlan',
            name='network_type',
            field=models.CharField(db_comment='网络类型', default='vlan', help_text='网络类型', max_length=63, verbose_name='网络类型'),
        ),
    ]
