# Generated by Django 5.0.6 on 2025-01-16 14:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0009_tenantopenstackmetadata_project_id_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='tenantaccount',
            name='customer_id',
            field=models.CharField(blank=True, db_comment='客户ID', default='', help_text='客户ID', max_length=63, null=True, verbose_name='客户ID'),
        ),
        migrations.AddField(
            model_name='tenantopenstackserver',
            name='is_reachable',
            field=models.BooleanField(blank=True, db_comment='是否可达(Ping)', default=False, help_text='是否可达(Ping)', null=True, verbose_name='是否可达(Ping)'),
        ),
        migrations.AddField(
            model_name='tenantopenstackserver',
            name='last_check_reachable_datetime',
            field=models.DateTimeField(blank=True, db_comment='最后检查时间', help_text='最后检查时间', null=True, verbose_name='最后检查时间'),
        ),
    ]
