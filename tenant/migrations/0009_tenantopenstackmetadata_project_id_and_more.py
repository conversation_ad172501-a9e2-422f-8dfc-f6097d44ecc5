# Generated by Django 5.0.6 on 2025-01-08 15:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0008_alter_tenantopenstackserver_ipaddr'),
    ]

    operations = [
        migrations.AddField(
            model_name='tenantopenstackmetadata',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
        migrations.AddField(
            model_name='tenantopenstackvlan',
            name='is_tasking',
            field=models.BooleanField(blank=True, db_comment='是否在创建调度中', default=False, help_text='是否在创建调度中', verbose_name='是否在创建调度中'),
        ),
        migrations.AlterField(
            model_name='tenantopenstackflavor',
            name='project_id',
            field=models.Char<PERSON>ield(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
        migrations.AlterField(
            model_name='tenantopenstackimage',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
        migrations.AlterField(
            model_name='tenantopenstackironichypervisor',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
        migrations.AlterField(
            model_name='tenantopenstacknetwork',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
        migrations.AlterField(
            model_name='tenantopenstackproject',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
        migrations.AlterField(
            model_name='tenantopenstacksecuritygroup',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
        migrations.AlterField(
            model_name='tenantopenstackserver',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
        migrations.AlterField(
            model_name='tenantopenstacksubnet',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
        migrations.AlterField(
            model_name='tenantopenstackvlan',
            name='project_id',
            field=models.CharField(blank=True, db_comment='openstack项目ID', default='', help_text='openstack项目ID', max_length=255, null=True, verbose_name='openstack项目ID'),
        ),
    ]
