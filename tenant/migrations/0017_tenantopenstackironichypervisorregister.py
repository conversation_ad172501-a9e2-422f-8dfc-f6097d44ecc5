# Generated by Django 5.0.6 on 2025-06-04 15:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0016_tenantopenstackironichypervisor_is_lock_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TenantOpenstackIronicHypervisorRegister',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.Char<PERSON>ield(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('name', models.CharField(blank=True, db_comment='名称', help_text='名称', max_length=63, null=True, verbose_name='名称')),
                ('node_resource_class', models.CharField(db_comment='裸机信息-资源类', default='ironic-4090', help_text='裸机信息-资源类', max_length=63, null=True, verbose_name='裸机信息-资源类')),
                ('node_network_interface', models.CharField(db_comment='裸机信息-管理接口', default='neutron', help_text='裸机信息-管理接口', max_length=63, null=True, verbose_name='裸机信息-管理接口')),
                ('node_properties', models.JSONField(blank=True, db_comment='裸机信息-属性', default=dict, help_text='裸机信息-属性', null=True, verbose_name='裸机信息-属性')),
                ('node_driver_ipmi_username', models.CharField(blank=True, db_comment='裸机IPMI信息-用户名', default='ADMIN', help_text='裸机IPMI信息-用户名', max_length=63, null=True, verbose_name='裸机IPMI信息-用户名')),
                ('node_driver_ipmi_password', models.CharField(blank=True, db_comment='裸机IPMI信息-密码', default='ADMIN', help_text='裸机IPMI信息-密码', max_length=63, null=True, verbose_name='裸机IPMI信息-密码')),
                ('node_driver_ipmi_address', models.CharField(blank=True, db_comment='裸机IPMI信息-地址', help_text='裸机IPMI信息-地址', max_length=63, null=True, verbose_name='裸机IPMI信息-地址')),
                ('port_group_mode', models.CharField(db_comment='端口组-模式', default='802.3ad', help_text='端口组-模式', max_length=63, null=True, verbose_name='端口组-模式')),
                ('port_group_standalone_ports_supported', models.BooleanField(blank=True, db_comment='端口组-SA-Ports', default=True, help_text='端口组-SA-Ports', null=True, verbose_name='端口组-SA-Ports')),
                ('port_group_properties', models.JSONField(blank=True, db_comment='端口组-属性', default=dict, help_text='端口组-属性', null=True, verbose_name='端口组-属性')),
                ('port_switch_id', models.CharField(db_comment='端口-交换机ID', help_text='端口-交换机ID', max_length=63, null=True, verbose_name='端口-交换机ID')),
                ('port_switch_info', models.CharField(db_comment='端口-交换机信息', help_text='端口-交换机信息', max_length=255, null=True, verbose_name='端口-交换机信息')),
                ('port_port_id', models.CharField(db_comment='端口-VLAN-端口ID', help_text='端口-VLAN-端口ID', max_length=63, null=True, verbose_name='端口-VLAN-端口ID')),
                ('port_hardware_address', models.CharField(db_comment='端口-裸机节点硬件地址', help_text='端口-裸机节点硬件地址', max_length=63, null=True, verbose_name='端口-裸机节点硬件地址')),
                ('port_network_address', models.CharField(db_comment='端口-裸机节点网络目标地址', help_text='端口-裸机节点网络目标地址', max_length=63, null=True, verbose_name='端口-裸机节点网络目标地址')),
                ('register_status', models.CharField(blank=True, db_comment='注册状态', default='未开始', help_text='注册状态', max_length=63, null=True, verbose_name='注册状态')),
                ('is_success', models.BooleanField(blank=True, db_comment='是否成功', default=False, help_text='是否成功', null=True, verbose_name='是否成功')),
                ('created_logger_info', models.TextField(blank=True, db_comment='创建日志信息', help_text='创建日志信息', null=True, verbose_name='创建日志信息')),
                ('node', models.CharField(blank=True, db_comment='区域节点', default='金华', help_text='区域节点', max_length=63, null=True, verbose_name='区域节点')),
                ('created_hyp_ironic_node', models.ForeignKey(db_comment='所属裸机节点', db_constraint=False, help_text='所属裸机节点', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='op_ironic_node_query', to='tenant.tenantopenstackironichypervisor', to_field='ironic_hyper_id', verbose_name='所属裸机节点')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '裸机节点注册任务表',
                'verbose_name_plural': '裸机节点注册任务表',
                'db_table': 'tenant_op_ironic_phy_registers',
                'ordering': ['-create_datetime'],
            },
        ),
    ]
