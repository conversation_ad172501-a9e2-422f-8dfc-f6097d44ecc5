# Generated by Django 5.0.6 on 2025-06-20 09:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0018_tenantopenstackironichypervisorregister_node_driver'),
    ]

    operations = [
        migrations.AlterField(
            model_name='tenantopenstacksetting',
            name='key',
            field=models.CharField(db_comment='配置键', db_index=True, help_text='配置键', max_length=63, verbose_name='配置键'),
        ),
        migrations.AlterUniqueTogether(
            name='tenantopenstacksetting',
            unique_together={('node', 'key', 'is_deleted')},
        ),
    ]
