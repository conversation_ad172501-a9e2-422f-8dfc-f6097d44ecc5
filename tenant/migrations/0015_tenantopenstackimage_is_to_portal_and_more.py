# Generated by Django 5.0.6 on 2025-04-02 13:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0014_alter_tenanthisecenginenatserverpolicy_unique_together'),
    ]

    operations = [
        migrations.AddField(
            model_name='tenantopenstackimage',
            name='is_to_portal',
            field=models.BooleanField(blank=True, db_comment='是否展示到前端', default=False, help_text='是否展示到前端', null=True, verbose_name='是否展示到前端'),
        ),
        migrations.AddField(
            model_name='tenantopenstackimage',
            name='os_type',
            field=models.CharField(blank=True, db_comment='操作系统类型', default='linux', help_text='操作系统类型', max_length=127, null=True, verbose_name='操作系统类型'),
        ),
        migrations.AddField(
            model_name='tenantopenstackimage',
            name='system_type',
            field=models.CharField(blank=True, db_comment='系统类型', help_text='系统类型', max_length=127, null=True, verbose_name='系统类型'),
        ),
    ]
