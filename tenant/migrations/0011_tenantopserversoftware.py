# Generated by Django 5.0.6 on 2025-03-07 15:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0010_tenantaccount_customer_id_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TenantOPServerSoftware',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.Char<PERSON>ield(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('tenant_id', models.CharField(blank=True, db_comment='唯一标识ID', help_text='唯一标识ID', max_length=255, null=True, verbose_name='唯一标识ID')),
                ('status', models.CharField(blank=True, db_comment='软件安装状态', default='软件未安装', help_text='软件安装状态', max_length=63, null=True, verbose_name='软件安装状态')),
                ('ticket_id', models.CharField(blank=True, db_comment='工单ID', default=None, help_text='工单ID', max_length=63, null=True, verbose_name='工单ID')),
                ('scheduletask_work_id', models.CharField(blank=True, db_comment='worker任务ID', default=None, help_text='worker任务ID', max_length=63, null=True, verbose_name='worker任务ID')),
                ('software_options', models.JSONField(blank=True, db_comment='软件安装选项', default=dict, help_text='软件安装选项', null=True, verbose_name='软件安装选项')),
                ('is_finished', models.BooleanField(db_comment='是否完成', default=False, help_text='是否结束', verbose_name='是否结束')),
                ('is_success', models.BooleanField(db_comment='是否成功', default=False, help_text='是否成功', verbose_name='是否成功')),
                ('retry_count', models.IntegerField(db_comment='重试次数', default=0, help_text='重试次数', verbose_name='重试次数')),
                ('failed_reason', models.TextField(blank=True, db_comment='失败原因', help_text='失败原因', null=True, verbose_name='失败原因')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('op_server', models.ForeignKey(blank=True, db_comment='OpenStack实例ID', db_constraint=False, help_text='OpenStack实例ID', null=True, on_delete=django.db.models.deletion.CASCADE, to='tenant.tenantopenstackserver', to_field='instance_id', verbose_name='OpenStack实例ID')),
            ],
            options={
                'verbose_name': '主机关联软件表',
                'verbose_name_plural': '主机关联软件表',
                'db_table': 'tenant_ops_softwares',
                'ordering': ('-create_datetime',),
                'unique_together': {('op_server', 'id')},
            },
        ),
    ]
