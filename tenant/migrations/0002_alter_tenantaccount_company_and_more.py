# Generated by Django 5.0.6 on 2024-11-24 14:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='tenantaccount',
            name='company',
            field=models.CharField(blank=True, db_comment='公司名称', default='Unknown', help_text='公司名称', max_length=1023, verbose_name='公司名称'),
        ),
        migrations.AlterField(
            model_name='tenantaccount',
            name='email',
            field=models.CharField(blank=True, db_comment='邮箱', default='<EMAIL>', help_text='邮箱', max_length=1023, verbose_name='邮箱'),
        ),
        migrations.AlterField(
            model_name='tenantaccount',
            name='mobile',
            field=models.CharField(blank=True, db_comment='电话', default='***********', help_text='电话', max_length=255, verbose_name='电话'),
        ),
    ]
