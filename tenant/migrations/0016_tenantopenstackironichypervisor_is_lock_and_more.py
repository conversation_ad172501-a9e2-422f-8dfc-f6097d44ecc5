# Generated by Django 5.0.6 on 2025-04-29 17:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant', '0015_tenantopenstackimage_is_to_portal_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='tenantopenstackironichypervisor',
            name='is_lock',
            field=models.BooleanField(blank=True, db_comment='是否锁定', default=False, help_text='是否锁定', null=True, verbose_name='是否锁定'),
        ),
        migrations.AddField(
            model_name='tenantopenstackserver',
            name='is_lock',
            field=models.BooleanField(blank=True, db_comment='是否锁定', default=False, help_text='是否锁定', null=True, verbose_name='是否锁定'),
        ),
    ]
