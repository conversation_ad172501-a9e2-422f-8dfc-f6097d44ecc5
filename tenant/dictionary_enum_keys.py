
# ########### 资源管理 枚举KEY ####################
# 云资源池->基础配置->裸机节点->端口组模式
CLOUD_RESOURCE_POOL_BAREMETAL_NODE_PORT_GROUP_MODE_KEY = 'cloud_resource_pool:basical_config:baremetral_node:portgroup_mode'
# 云资源池->基础配置->裸机节点->网络接口
CLOUD_RESOURCE_POOL_BAREMETAL_NODE_NETWORK_INTERFACE_KEY = 'cloud_resource_pool:basical_config:baremetral_node:network_interface'
# 云资源池->基础配置->裸机节点->节点驱动
CLOUD_RESOURCE_POOL_BAREMETAL_NODE_DRIVER_KEY = 'cloud_resource_pool:basical_config:baremetral_node:driver'
# 云资源池->基础配置->裸机节点->驱动
CLOUD_RESOURCE_POOL_BAREMETAL_REGISTER_STATUS_KEY = 'cloud_resource_pool:basical_config:baremetral_node:register_status'
# 区域节点简称, 共用区域节点配置
CLOUD_RESOURCE_POOL_BAREMETAL_NODE_REGION_KEY = 'operator_cmdb:host:area_node'

# 手动更新后裸机节点信息
HANDLE_EVENT_UPDATE_BAREMETAL_NODE_KEY = '{node}:handle_event:update_baremetal_node'
AUTO_UPDATE_BAREMETAL_NODE_LAST_AT_TIME_KEY = '{node}:auto_update_baremetal_node:last_at_time'
