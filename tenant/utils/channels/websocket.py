import os
import sys
import json
import base64
import paramiko
from channels.generic.websocket import WebsocketConsumer
from io import Bytes<PERSON>, String<PERSON>
from threading import Thread
from tenant.models import TenantOpenstackServer
from django.http.request import QueryDict
from channels.db import database_sync_to_async
from django.conf import settings

SSH_LOG_PATH = os.path.join(settings.BASE_DIR, 'logs', 'terminal.log')


def get_host_info(id):
    # instance = TenantOpenstackServer.objects.filter(id=id).first()
    instance = None
    if not instance:
        # TODO 暂时全部返回为True
        ssh_info = {
            'host': '***********',
            'port': 22,
            'username': 'root',
            'password': 'xingzai!@#456',
            'type': 0,
            'pkey': None,
        }
        return ssh_info
    # return instance


class TerminalConsumer(WebsocketConsumer):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ssh_client = None
        self.chan = None
        self.id = None
        self.ssh_info = None

    def connect(self):
        params = self.scope["query_string"].decode()
        dict_params = QueryDict(query_string=params, encoding='utf-8')
        self.id = dict_params.get('id', None)

        # if not self.id:
        #     self.close()
        self.ssh_info = get_host_info(self.id)
        if not self.ssh_info:
            self.close()
        self.accept()
        self.init_ssh()

    def get_client(self):
        paramiko.util.log_to_file(SSH_LOG_PATH)
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        print(self.ssh_info)
        if self.ssh_info['type'] == 0:
            ssh.connect(
                hostname=self.ssh_info['host'],
                port=self.ssh_info['port'],
                username=self.ssh_info['username'],
                password=self.ssh_info['password'],
            )
        else:
            tmp_pkey = self.ssh_info['pkey'].encode('utf-8')
            if sys.version_info[0] == 2:
                p_file = BytesIO(tmp_pkey)
            else:
                p_file = StringIO(tmp_pkey)
            pkey = paramiko.RSAKey.from_private_key(p_file)
            ssh.connect(
                hostname=self.ssh_info['host'],
                port=self.ssh_info['port'],
                username=self.ssh_info['username'],
                pkey=pkey
            )
        return ssh

    def loop_read(self):
        while True:
            data = self.chan.recv(1024)
            if not data:
                self.close()
                break
            print(f'Sending data to frontend: {data}')  # 打印发送的数据
            self.send(bytes_data=data)

    def init_ssh(self):
        try:
            self.ssh_client = self.get_client()
            self.chan = self.ssh_client.invoke_shell(term='xterm')
            self.chan.transport.set_keepalive(30)
            rv = Thread(target=self.loop_read)
            rv.start()
        except Exception as e:
            errors = ""
            if 'Authentication failed' in str(e):
                errors = '认证失败{}'.format(e)
            elif 'Bad authentication type' in str(e):
                errors = '认证失败{}'.format(e)
            elif 'Connection reset by peer' in str(e):
                errors = '目标服务器主动拒绝连接'
            elif 'Error reading SSH protocol banner' in str(e):
                errors = '协议头响应超时'
            elif not str(e):
                errors = 'SSH协议握手超时'
            else:
                import traceback
                errorMsg = traceback.format_exc()
                errors = '未知错误: {}'.format(errorMsg)
            self.send(text_data='Exception: %s\r\n' % errors)
            self.close()

    def disconnect(self, close_code):
        try:
            if self.chan:
                self.chan.close()
            if self.ssh_client:
                self.ssh_client.close()
        except:
            pass

    def receive(self, text_data=None, bytes_data=None):
        data = json.loads(text_data or bytes_data)
        if data['type'] == 'ssh_info':
            print('data: %s' % data['data'])
            self.ssh_info = data['data']
            self.init_ssh()
        elif data['type'] == 'terminal':
            decoded_data = base64.b64decode(data['data']['base64']).decode('utf-8')
            if self.chan:
                self.chan.send(decoded_data)
        elif data['type'] == 'resize':
            if self.chan:
                self.chan.resize_pty(width=data['data']['cols'], height=data['data']['rows'])