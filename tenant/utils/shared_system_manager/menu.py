from rest_framework import serializers

from dvadmin.utils.json_response import DetailResponse

from tenant.models import TenantMenu, TenantMenuButton
from tenant.utils.tenant_base.chaos_tenant_serializer import ChaosTenantModelSerializer


class TenantMenuManager(object):

    @staticmethod
    def get_tenant_menu_queryset(request):
        if getattr(request.user, 'is_tenant_master'):
            queryset = TenantMenu.objects.filter(
                is_deleted=False, status=True)
        else:
            # TODO: 子账号获取已关联的菜单,暂仅获取公共页面
            queryset = TenantMenu.objects.filter(
                is_deleted=False, is_public=True, status=True)
        serializer = WebRouterSerializer(queryset, many=True, request=request)
        data = serializer.data
        return data

    @staticmethod
    def get_tenant_menu_button_all_permissions(request):
        """
        获取所有的按钮权限
        :param request:
        :return:
        """
        user_type = request.user.user_type
        is_tenant_master = request.user.is_tenant_master
        if user_type == 1:
            if is_tenant_master:
                queryset = TenantMenuButton.objects.filter(
                    is_deleted=False,
                ).values_list('value', flat=True)
            else:
                # TODO: 子账号获取已关联的按钮权限,暂不支持登录
                queryset = []
        else:
            # 账号有误，直接返回为空
            queryset = []
            # role_id = request.user.role.values_list('id', flat=True)
            # queryset = RoleMenuButtonPermission.objects.filter(
            #     role__in=role_id
            # ).values_list(
            #     'menu_button__value', flat=True
            # ).distinct()
        return DetailResponse(data=queryset)


class WebRouterSerializer(ChaosTenantModelSerializer):
    """
    前端菜单路由的简单序列化器
    """
    path = serializers.CharField(source="web_path")
    title = serializers.CharField(source="name")

    class Meta:
        model = TenantMenu
        fields = (
            'id', 'parent', 'icon', 'sort', 'path', 'name', 'title', 'is_link', 'link_url', 'is_catalog', 'web_path', 'component',
            'component_name', 'cache', 'visible', 'is_iframe', 'is_affix', 'status')
        read_only_fields = ["id"]

