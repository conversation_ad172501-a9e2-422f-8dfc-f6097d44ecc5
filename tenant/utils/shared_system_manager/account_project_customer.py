from tenant.models import TenantOpenstackProject


def project_get_customer_info(project_id, is_detail=False):
    """
    根据项目 ID 获取项目关联的客户信息
    @param project_id : openstack project id
    @param is_detail : 是否获取详细信息
    @rtype: dict or None
    @return : project_detail or None
    """
    # 使用 select_related 获取相关联的账户和客户信息
    project = TenantOpenstackProject.objects.select_related(
        'account',
    ).get(
        project_id=project_id,
        is_deleted=False,
    )
    account = project.account
    if account and not account.is_deleted:
        if is_detail:
            return {
                'project_id': project_id,
                'account_id': account.id,
                'customer_id': account.customer_id,
                'account_info': account.to_dict,
                'project_info': project.to_dict,
            }
        else:
            return {
                'project_id': project_id,
                'account_id': account.id,
                'customer_id': account.customer_id,
            }

