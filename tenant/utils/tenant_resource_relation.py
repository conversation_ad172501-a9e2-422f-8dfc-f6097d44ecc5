from application.logger import logger

from tenant.models import (
    TenantOpenstackIronicHypervisor,
    TenantOpenstackServer
)
from operatorcmdb.models import Host

def get_operatorcmdb_host_info(op_instance_info):
    """
    获取 operatorcmdb 中的主机id
    params op_instance_id: TenantOpenstackServer 中的实例ID, instance_id
    """
    try:
        node = op_instance_info["node"]
        server_info = TenantOpenstackServer.objects.filter(
            instance_id=op_instance_info["op_instance_id"],
            node=node,
            is_deleted=False,
        ).first()
        if not server_info:
            logger.warning(f'<get_operatorcmdb_host_info>未找到 node: {node}, instance_id 为'
                           f' {op_instance_info["op_instance_id"]} '
                           f'的主机信息')
        ironic_hyper_info = TenantOpenstackIronicHypervisor.objects.filter(
            ironic_hyper_id=server_info.baremetal_node_id,
            node=node,
            is_deleted=False,
        ).first()
        if not ironic_hyper_info:
            logger.warning(f'<get_operatorcmdb_host_info>未找到  node: {node}, ironic_hyper_id 为 {server_info.baremetal_node_id} 的裸金属节点信息')
        host_info = Host.objects.filter(
            ip_bmc=ironic_hyper_info.name,
            host_type='裸金属',
            area=node,
            is_deleted=False,
        ).first()
        if not host_info:
            logger.warning(f'<get_operatorcmdb_host_info>未找到 node: {node}, ip_bmc 为 {ironic_hyper_info.name} 的裸金属节点信息')
        return host_info
    except Exception as e:
        logger.error(f'<get_operatorcmdb_host_info>instance_info: {op_instance_info}获取 operatorcmdb 中的主机信息失败, '
                     f'error: {e}')
        return None

def get_operatorcmdb_host_ids(op_instance_infos):
    """
    获取 operatorcmdb 中的主机ids
    params op_instance_infos: openstack 实例信息 [{"op_instance_id": "xxxxx", "node": "金华-111"}]
    """
    operatorcmdb_host_ids = []
    for op_instance_info in op_instance_infos:
        host_info = get_operatorcmdb_host_info(op_instance_info=op_instance_info)
        if host_info:
            operatorcmdb_host_ids.append(host_info.id)
    return operatorcmdb_host_ids

def from_ticket_get_operatorcmdb_instance_ids(ticket_id):
    server_infos = TenantOpenstackServer.objects.filter(
        belong_ticket_id=ticket_id,
        is_deleted=False,
    ).all()
    op_instance_infos = [{"op_instance_id": i.instance_id, "node": i.node} for i in server_infos if i.instance_id]
    operatorcmdb_host_ids = get_operatorcmdb_host_ids(op_instance_infos=op_instance_infos)
    return operatorcmdb_host_ids

def get_tenant_op_server_info(op_instance_id):
    """
    获取 TenantOpenstackServer 中的 Id
    """
    try:
        server_info = TenantOpenstackServer.objects.filter(
            instance_id=op_instance_id,
            is_deleted=False,
        ).first()
        if not server_info:
            logger.warning(f'<get_tenant_op_server_info>未找到 instance_id 为 {op_instance_id} 的主机信息')
        return server_info
    except Exception as e:
        logger.error(f'<get_tenant_op_server_info> error: {str(e)}')
