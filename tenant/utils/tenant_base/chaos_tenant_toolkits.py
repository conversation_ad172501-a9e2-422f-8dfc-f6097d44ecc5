"""
<AUTHOR>
@Date    ：2024/11/8
"""
from tenant.models import TenantOpenstackProject


def get_tenant_id_from_project(project_id):
    # Implement logic to fetch tenant ID from the project ID
    # This is a placeholder function and should be replaced with actual implementation
    obj = TenantOpenstackProject.objects.filter(
        project_id=project_id,
        is_deleted=False,
    ).first()
    if obj:
        return obj.tenant_id
    return f"unknown-tenant-{project_id}"
