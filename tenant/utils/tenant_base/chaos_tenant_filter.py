from django.db.models import Q
from rest_framework.filters import BaseFilterBackend
from application.logger import logger
from tenant.utils.tenant_base.chaos_tenant_user import get_user_profile


class ChaosTenantCoreModelFilterBackend(BaseFilterBackend):
    """
    自定义时间范围和项目ID过滤器
    """

    def filter_queryset(self, request, queryset, view):
        # 获取时间范围过滤参数
        create_datetime_after = request.query_params.get('create_datetime_after', None)
        create_datetime_before = request.query_params.get('create_datetime_before', None)
        update_datetime_after = request.query_params.get('update_datetime_after', None)
        update_datetime_before = request.query_params.get('update_datetime_before', None)

        # 初始化过滤条件
        filters = Q(is_deleted=False)

        # 检查模型是否存在 project_id 字段
        has_project_id_field = False
        try:
            if hasattr(queryset.model, '_meta'):
                queryset.model._meta.get_field('project_id')
                has_project_id_field = True
        except Exception:
            pass

        # 处理用户类型和项目ID
        user_type = getattr(request.user, 'user_type', None)
        account_id = getattr(request.user, 'account_id', None)

        if user_type == 1:  # 假设 user_type == 1 表示租户主账号
            project_ids = []
            try:
                user_profile = get_user_profile(account_id=account_id)
                if user_profile and 'projects' in user_profile:
                    project_ids = [project['project_id'] for project in user_profile['projects']]
                else:
                    logger.warning(f"No projects found for account_id: {account_id}")
            except Exception as e:
                logger.warning(f"Error getting projects for account_id: {account_id}, Exception: {str(e)}")
                queryset = queryset.none()
            # 如果有 project_ids 且模型中有 project_id 字段，则添加 project_id__in 过滤条件
            if project_ids and has_project_id_field:
                filters &= Q(project_id__in=project_ids)

        # 处理时间范围过滤
        if any([
            create_datetime_after,
            create_datetime_before,
            update_datetime_after,
            update_datetime_before,
        ]):
            create_filter = Q()
            if create_datetime_after and create_datetime_before:
                create_filter &= Q(create_datetime__gte=create_datetime_after) & Q(
                    create_datetime__lte=create_datetime_before)
            elif create_datetime_after:
                create_filter &= Q(create_datetime__gte=create_datetime_after)
            elif create_datetime_before:
                create_filter &= Q(create_datetime__lte=create_datetime_before)

            update_filter = Q()
            if update_datetime_after and update_datetime_before:
                update_filter &= Q(update_datetime__gte=update_datetime_after) & Q(
                    update_datetime__lte=update_datetime_before)
            elif update_datetime_after:
                update_filter &= Q(update_datetime__gte=update_datetime_after)
            elif update_datetime_before:
                update_filter &= Q(update_datetime__lte=update_datetime_before)

            # 结合时间范围过滤条件
            filters &= create_filter & update_filter

        # 应用所有过滤条件
        if queryset is None:
            return self.model.objects.none()
        try:
            filtered_queryset = queryset.filter(filters)
            # logger.info(f"Filtered QuerySet: {filtered_queryset.query}")  # 打印生成的SQL查询语句
            return filtered_queryset
        except Exception as e:
            logger.error(f"Error applying filters: {str(e)}")
            return self.model.objects.none()

# class DataLevelPermissionsFilter(BaseFilterBackend):
#     """
#     数据 级权限过滤器
#     0. 获取用户的部门id，没有部门则返回空
#     1. 判断过滤的数据是否有创建人所在部门 "creator" 字段,没有则返回全部
#     2. 如果用户没有关联角色则返回本部门数据
#     3. 根据角色的最大权限进行数据过滤(会有多个角色，进行去重取最大权限)
#     3.1 判断用户是否为超级管理员角色/如果有1(所有数据) 则返回所有数据
#
#     4. 只为仅本人数据权限时只返回过滤本人数据，并且部门为自己本部门(考虑到用户会变部门，只能看当前用户所在的部门数据)
#     5. 自定数据权限 获取部门，根据部门过滤
#     """
#
#     def filter_queryset(self, request, queryset, view):
#         """
#         接口白名单是否认证数据权限
#         """
#         api = request.path  # 当前请求接口
#         method = request.method  # 当前请求方法
#         methodList = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
#         method = methodList.index(method)
#         # ***接口白名单***
#         api_white_list = ApiWhiteList.objects.filter(enable_datasource=False).values(
#             permission__api=F("url"), permission__method=F("method")
#         )
#         api_white_list = [
#             str(item.get("permission__api").replace("{id}", ".*?"))
#             + ":"
#             + str(item.get("permission__method"))
#             for item in api_white_list
#             if item.get("permission__api")
#         ]
#         for item in api_white_list:
#             new_api = f"{api}:{method}"
#             matchObj = re.match(item, new_api, re.M | re.I)
#             if matchObj is None:
#                 continue
#             else:
#                 return queryset
#         """
#         判断是否为超级管理员:
#         如果不是超级管理员,则进入下一步权限判断
#         """
#         if request.user.is_superuser == 0:
#             return self._extracted_from_filter_queryset_33(request, queryset, api, method)
#         else:
#             return queryset
#
#     # TODO Rename this here and in `filter_queryset`
#     def _extracted_from_filter_queryset_33(self, request, queryset, api, method):
#         # 0. 获取用户的部门id，没有部门则返回空
#         user_dept_id = getattr(request.user, "dept_id", None)
#         if not user_dept_id:
#             return queryset.none()
#
#         # 1. 判断过滤的数据是否有创建人所在部门 "dept_belong_id" 字段
#         if not getattr(queryset.model, "dept_belong_id", None):
#             return queryset
#
#         # 2. 如果用户没有关联角色则返回本部门数据
#         if not hasattr(request.user, "role"):
#             return queryset.filter(dept_belong_id=user_dept_id)
#
#         # 3. 根据所有角色 获取所有权限范围
#         # (0, "仅本人数据权限"),
#         # (1, "本部门及以下数据权限"),
#         # (2, "本部门数据权限"),
#         # (3, "全部数据权限"),
#         # (4, "自定数据权限")
#         re_api = api
#         _id = request.parser_context["kwargs"].get('id')
#         if _id:  # 判断是否是单例查询
#             re_api = re.sub(_id, '{id}', api)
#         role_id_list = request.user.role.values_list('id', flat=True)
#         role_permission_list = RoleMenuButtonPermission.objects.filter(
#             role__in=role_id_list,
#             role__status=1,
#             menu_button__api=re_api,
#             menu_button__method=method).values(
#             'data_range'
#         )
#         dataScope_list = []  # 权限范围列表
#         for ele in role_permission_list:
#             # 判断用户是否为超级管理员角色/
#             # 如果拥有[全部数据权限]则返回所有数据
#             if ele.get("data_range") == 3:
#                 return queryset
#             dataScope_list.append(ele.get("data_range"))
#         dataScope_list = list(set(dataScope_list))
#
#         # 4. 只为仅本人数据权限时只返回过滤本人数据，并且部门为自己本部门(考虑到用户会变部门，只能看当前用户所在的部门数据)
#         if 0 in dataScope_list:
#             return queryset.filter(
#                 creator=request.user, dept_belong_id=user_dept_id
#             )
#
#         # 5. 自定数据权限 获取部门，根据部门过滤
#         dept_list = []
#         for ele in dataScope_list:
#             if ele == 1:
#                 dept_list.append(user_dept_id)
#                 dept_list.extend(
#                     get_dept(
#                         user_dept_id,
#                     )
#                 )
#             elif ele == 2:
#                 dept_list.append(user_dept_id)
#             elif ele == 4:
#                 dept_ids = RoleMenuButtonPermission.objects.filter(
#                     role__in=role_id_list,
#                     role__status=1,
#                     data_range=4).values_list(
#                     'dept__id', flat=True
#                 )
#                 dept_list.extend(dept_ids)
#         if queryset.model._meta.model_name == 'dept':
#             return queryset.filter(id__in=list(set(dept_list)))
#         return queryset.filter(dept_belong_id__in=list(set(dept_list)))



