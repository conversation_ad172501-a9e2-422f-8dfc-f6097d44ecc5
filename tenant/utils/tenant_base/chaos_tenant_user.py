from django.core.cache import cache

from dvadmin.utils.json_response import ErrorResponse

from tenant.utils.tenant_base.redis_keys.user_key import USER_PROFILE_INFO_KEY
from tenant.models import TenantOpenstackProject, TenantAccount


def get_user_profile(account_id):
    expire_time = 3600*8
    data_key = USER_PROFILE_INFO_KEY.format(account_id=account_id)
    user_profile = cache.get(data_key)
    if not account_id:
        return ErrorResponse('用户<AccountIdNotProvider>参数不能为空!')
    if not user_profile:
        if account_id:
            tenant_account = TenantAccount.objects.filter(
                id=account_id,
                is_deleted=False,
            ).first()
            if not tenant_account:
                return ErrorResponse('账号配置有误，请联系管理员!')
            tenant_projects = TenantOpenstackProject.objects.filter(
                account_id=tenant_account.id,
                is_deleted=False,
            ).all()
            if tenant_projects:
                user_profile = {
                    'projects': [project._to_dict() for project in tenant_projects],
                    'tenant_account': tenant_account._to_dict()
                }
                # 默认8小时过期
                cache.set(data_key, user_profile, timeout=expire_time)
                return user_profile
    else:
        return user_profile
