from django.db import models

from dvadmin.utils.chaos_models import ChaosCoreModel


class ChaosCoreTenantModel(ChaosCoreModel):
    """
    核心标准抽象模型模型,可直接继承使用
    增加审计字段, 覆盖字段时, 字段名称请勿修改, 必须统一审计字段名称
    """
    dept_belong_id = models.CharField(
        max_length=255, help_text="数据归属部门", null=True, blank=True, verbose_name="数据归属部门",
        db_comment='数据归属部门')
    tenant_id = models.CharField(
        max_length=255, null=True, blank=True,
        verbose_name="唯一标识ID", help_text="唯一标识ID", db_comment='唯一标识ID')

    def get_self_user_info(self):
        return {
            'creator_id': self.modifier,
            'modifier': self.modifier,
            'tenant_id': self.tenant_id,
            'dept_belong_id': self.dept_belong_id,
        }

    class Meta:
        abstract = True
        verbose_name = 'tenant核心模型'
        verbose_name_plural = verbose_name
