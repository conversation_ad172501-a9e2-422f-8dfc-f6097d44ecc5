"""
@Remark: 自定义视图集
"""
from django.db import transaction
from django_filters import DateTimeFromToRangeFilter
from django_filters.rest_framework import FilterSet
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework.viewsets import ModelViewSet

from dvadmin.utils.filters import DataLevelPermissionsFilter
from dvadmin.utils.chaos_import_export_mixin import ExportSerializerMixin, ImportSerializerMixin
from dvadmin.utils.json_response import SuccessResponse, ErrorResponse, DetailResponse
from dvadmin.utils.permission import CustomPermission
from dvadmin.utils.models import get_custom_app_models, CoreModel
from dvadmin.system.models import FieldPermission, MenuField

from tenant.utils.tenant_base.chaos_tenant_filter import ChaosTenantCoreModelFilterBackend

from django_restql.mixins import QueryArgumentsMixin

from dvadmin.utils.row_permission_filter import RowPermissionsFilter


class ChaosTenantModelViewSet(ModelViewSet, ImportSerializerMixin, ExportSerializerMixin, QueryArgumentsMixin):
    """
    自定义的ModelViewSet:
    统一标准的返回格式;新增,查询,修改可使用不同序列化器
    (1)ORM性能优化, 尽可能使用values_queryset形式
    (2)xxx_serializer_class 某个方法下使用的序列化器(xxx=create|update|list|retrieve|destroy)
    (3)filter_fields = '__all__' 默认支持全部model中的字段查询(除json字段外)
    (4)import_field_dict={} 导入时的字段字典 {model值: model的label}
    (5)export_field_label = [] 导出时的字段
    """
    lookup_field = 'id'
    values_queryset = None
    ordering_fields = '__all__'
    create_serializer_class = None
    update_serializer_class = None
    filter_fields = '__all__'
    search_fields = ()
    extra_filter_class = [ChaosTenantCoreModelFilterBackend,]
    # data_level_filter_class = DataLevelPermissionsFilter
    # row_filter_class = RowPermissionsFilter
    permission_classes = [CustomPermission,]
    import_field_dict = {}
    export_field_label = {}
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': '',
    }
    signal_filed = None

    def filter_queryset(self, queryset):
        for backend in set(set(self.filter_backends) | set(self.extra_filter_class or [])):
            queryset = backend().filter_queryset(self.request, queryset, self)
        # 行权限无需再次验证数据权限，故放置后面进行再进行验证
        # row_permission_queryset = self.row_filter_class().filter_queryset(self.request, queryset, self)
        # queryset = self.data_level_filter_class().filter_queryset(self.request, queryset, self)

        # if self.request.user.is_superuser != 1 and row_permission_queryset.count():
        #     queryset = queryset | row_permission_queryset
        return queryset

    def get_queryset(self):
        if getattr(self, 'values_queryset', None):
            return self.values_queryset
        return super().get_queryset()

    def get_serializer_class(self):
        action_serializer_name = f"{self.action}_serializer_class"
        action_serializer_class = getattr(self, action_serializer_name, None)
        if action_serializer_class:
            return action_serializer_class
        return super().get_serializer_class()

    # 通过many=True直接改造原有的API，使其可以批量创建
    def get_serializer(self, *args, **kwargs):
        serializer_class = self.get_serializer_class()
        kwargs.setdefault('context', self.get_serializer_context())
        # 全部以可见字段为准
        can_see = self.get_menu_field(serializer_class)
        # 排除掉序列化器级的字段
        # sub_set = set(serializer_class._declared_fields.keys()) - set(can_see)
        # for field in sub_set:
        #     serializer_class._declared_fields.pop(field)
        # if not self.request.user.is_superuser:
        #     serializer_class.Meta.fields = can_see
        # 在分页器中使用
        self.request.permission_fields = can_see
        if isinstance(self.request.data, list):
            with transaction.atomic():
                return serializer_class(many=True, *args, **kwargs)
        else:
            return serializer_class(*args, **kwargs)

    def get_menu_field(self, serializer_class):
        """获取字段权限"""
        finded = False
        for model in get_custom_app_models():
            if model['object'] is serializer_class.Meta.model:
                finded = True
                break
        if finded is False:
            return []
        return MenuField.objects.filter(model=model['model']
                                        ).values('field_name', 'title')

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, request=request)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return DetailResponse(data=serializer.data, msg="新增成功")

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True, request=request)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True, request=request)
        return SuccessResponse(data=serializer.data, msg="获取成功")

    def retrieve(self, request, *args, **kwargs):
        # 暂不使用此方法
        queryset = self.get_queryset()
        if self.signal_filed:
            instance = queryset.filter(
                **{
                    self.signal_filed: self.kwargs['id'],
                    'is_deleted': False,
                }
            ).first()
            if not instance:
                instance = self.get_object()
        else:
            instance = queryset.filter(
                id=self.kwargs['id'],
                is_deleted=False
            ).first()
        if not instance:
            return DetailResponse(msg="数据不存在")
        serializer = self.get_serializer(instance)
        return DetailResponse(data=serializer.data, msg="获取成功")

    # def retrieve(self, request, *args, **kwargs):
    #     instance = self.get_object()
    #     serializer = self.get_serializer(instance)
    #     return DetailResponse(data=serializer.data, msg="获取成功")

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, request=request, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}
        return DetailResponse(data=serializer.data, msg="更新成功")

    def _get_list_by_ids(self, request, *args, **kwargs):
        """根据ID列表查询"""
        if not self.api_list_by_ids_dict['is_opened']:
            return ErrorResponse(data={}, msg='此接口未配置显示信息')
        base_filters = {}
        ids = request.query_params.getlist('ids')
        # is_all = request.query_params.getlist('is_all', False)
        if ids:
            base_filters.update({f"{self.api_list_by_ids_dict['id_field']}__in": ids})
        for key, value in request.query_params.items():
            if key in ['ids', 'is_all']:
                 continue
            base_filters.update({key: value})
        queryset = self.filter_queryset(self.get_queryset()).filter(**base_filters).values(
            self.api_list_by_ids_dict['id_field'], self.api_list_by_ids_dict['name_field'] or 'description')
        return SuccessResponse(data=queryset, msg="获取成功")

    ids = openapi.Parameter(
        'ids',
        openapi.IN_QUERY,
        description="资源id列表",
        type=openapi.TYPE_ARRAY,
        items=openapi.Items(type=openapi.TYPE_STRING),
        collectionFormat='multi'
    )

    @swagger_auto_schema(method='GET', manual_parameters=[ids,], operation_summary='根据 ids 查询列表')
    @action(methods=['get'], detail=False)
    def get_list_by_ids(self, request, *args, **kwargs):
        return self._get_list_by_ids(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return DetailResponse(data=[], msg="删除成功")

    keys = openapi.Schema(description='主键列表', type=openapi.TYPE_ARRAY, items=openapi.TYPE_STRING)

    @swagger_auto_schema(request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['keys'],
        properties={'keys': keys}
    ), operation_summary='批量删除')
    @action(methods=['delete'], detail=False)
    def multiple_delete(self, request, *args, **kwargs):
        request_data = request.data
        keys = request_data.get('keys', None)
        if keys:
            self.get_queryset().filter(id__in=keys).update(is_deleted=True)
            return SuccessResponse(data=[], msg="删除成功")
        else:
            return ErrorResponse(msg="未获取到keys字段")
