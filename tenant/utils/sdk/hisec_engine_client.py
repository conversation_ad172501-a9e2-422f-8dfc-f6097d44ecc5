import requests
import xmltodict

from requests.auth import HTTPBasic<PERSON>uth

from application.logger import logger
from tenant.utils.sdk.op_node_manage import OPNodeManage


class HisecEngineClient(object):
    def __init__(
            self,
            host=None,
            port=None,
            username=None,
            password=None,
            protocol='http',
            env = 'development',
            node=None,
    ):
        self.env = env
        self.node = node
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.protocol = protocol
        self.base_path = 'restconf/data'
        self.base_rest_url = f'{self.protocol}://{self.host}:{self.port}/{self.base_path}'
        self.headers = {
            "Cache-Control": "no-cache,no-store",
            "Connection": "Keep-Alive",
            "Accept": "application/yang-data+xml",
            "Content-Type": "application/yang-data+xml",
        }
        self.auth = None
        self.product_code_path = {
            'nat_server': 'huawei-nat-server:nat-server'
        }
        self.init_client(self.node)

    def init_client(self, env='development'):
        op_node_manage = OPNodeManage()
        self.node = op_node_manage.verify_node(self.node)
        if not self.node:
            raise Exception(f'当前 Node: {self.node}, 未找到此节点配置!!!!!!')
        if env != 'development':
            node_settings = op_node_manage.get_openstack_settings(node=self.node)
            self.host = node_settings['HISEC_HOST']
            self.port = node_settings['HISEC_PORT']
            self.username = node_settings['HISEC_USERNAME']
            self.password = node_settings['HISEC_PASSWORD']
            self.protocol = node_settings['HISEC_PROTOCOL']
            self.base_rest_url = f'{self.protocol}://{self.host}:{self.port}/{self.base_path}'
            self.auth = HTTPBasicAuth(self.username, self.password)

    @staticmethod
    def get_nat_server_payload_data(project_name=None, public_ip=None, public_port=None, inside_ip=None,
                                    inside_port=None):
        if not public_ip or not inside_ip:
            raise Exception('public_ip and inside_ip is needed')
        if not public_port:
            payload_data = {
                'server-mapping': {
                    'name': f'chaos_{project_name}_{inside_ip}_none_port',
                    'route-enable': True,
                    'inside-vpn-name': 'public',
                    'global': {
                        'start-ip': public_ip
                    },
                    'inside': {
                        'start-ip': inside_ip
                    }
                }
            }
        else:
            if not inside_port:
                raise Exception('inside_port needed')
            payload_data = {
                'server-mapping': {
                    'name': f'chaos_{project_name}_{inside_ip}_public{public_port}_interval{inside_port}',
                    'route-enable': True,
                    'inside-vpn-name': 'public',
                    'protocol': 'tcp',
                    'global': {
                        'start-ip': public_ip
                    },
                    'global-port': {
                        'start-port': public_port
                    },
                    'inside': {
                        'start-ip': inside_ip
                    },
                    'inside-port': {
                        'start-port': inside_port
                    }
                }
            }
        logger.info(f'<nat_server_payload_data>: {payload_data}')
        return payload_data

    def get_all_nat_servers(self):
        url = f'{self.base_rest_url}/{self.product_code_path["nat_server"]}'
        logger.info(f'<get_all_nat_servers><{url}>')
        response = requests.get(
            url, headers=self.headers,
            auth=self.auth
        )
        logger.info(f'<get_all_nat_servers><{url}>: {response.status_code}')
        dict_response = xmltodict.parse(response.text)
        logger.info(f'<get_all_nat_servers_response>: {dict_response}')
        return response.status_code, dict_response

    def create_nat_server(self, project_name, public_ip, inside_ip, public_port=None, inside_port=None):
        url = f'{self.base_rest_url}/{self.product_code_path["nat_server"]}/server-mappings'
        payload_data = self.get_nat_server_payload_data(
            project_name=project_name,
            public_ip=public_ip,
            inside_ip=inside_ip,
            public_port=public_port,
            inside_port=inside_port
        )
        data = xmltodict.unparse(payload_data)
        response = requests.post(
            url, headers=self.headers, data=data,
            auth=self.auth,
        )
        if int(response.status_code) != 201:
            logger.error(f'<create_nat_server><{url}>: {response.text}')
            re_data = xmltodict.parse(response.text)
            return 400, re_data
        else:
            return 200, payload_data

    def edit_nat_server(self, source_nat_server_name, project_name, public_ip, inside_ip, public_port=None, inside_port=None):
        if not source_nat_server_name:
            return 400, {}
        payload_data = self.get_nat_server_payload_data(
            project_name=project_name,
            public_ip=public_ip,
            public_port=public_port,
            inside_ip=inside_ip,
            inside_port=inside_port
        )
        logger.info(f'<edit_nat_server_payload_data>: {payload_data}')
        # 【Important】修改 nat server 策略时，需要保证 新name 和 原始name 一致
        payload_data['server-mapping']['name'] = source_nat_server_name
        url = f'{self.base_rest_url}/{self.product_code_path["nat_server"]}/server-mappings/server-mapping={source_nat_server_name}'
        logger.info(f'<edit_nat_server_url><{url}>')
        data = xmltodict.unparse(payload_data)
        response = requests.put(
            url, headers=self.headers, data=data,
            auth=self.auth,
        )
        logger.info(f'<edit_nat_server><{url}>: {response.status_code}')
        if int(response.status_code) != 204:
            logger.error(f'<edit_nat_server><{url}>: {response.text}')
            re_data = xmltodict.parse(response.text)
            return 400, re_data
        else:
            return 200, payload_data

    def get_nat_server(self, name):
        url = f'{self.base_rest_url}/{self.product_code_path["nat_server"]}/server-mappings/server-mapping={name}'
        response = requests.get(
            url, headers=self.headers,
            auth=self.auth
        )
        logger.info(f'<get_nat_server><{url}>: {response.status_code}')
        dict_response = xmltodict.parse(response.text)
        return response.status_code, dict_response

    def delete_nat_server(self, name):
        if not name:
            return 400, {}
        url = f'{self.base_rest_url}/{self.product_code_path["nat_server"]}/server-mappings/server-mapping={name}'
        response = requests.delete(
            url, headers=self.headers,
            auth=self.auth
        )

        if int(response.status_code) != 204:
            logger.error(f'<delete_nat_server><{url}>: {response.text}')
            re_data = xmltodict.parse(response.text)
            return 400, re_data
        else:
            logger.info(f'<delete_nat_server><{url}>: {response.status_code}')
            return 200, {}


if __name__ == '__main__':
    import os
    import django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
    django.setup()
    admin_client = HisecEngineClient(node="金华-Test")
    # res = admin_client.get_all_nat_servers()
    # print(res)
    # res = admin_client.get_nat_server('caoxiangpeng-testing_***********')
    # print(res)
    # res = admin_client.create_nat_server(
    #     project_name='caoxiangpeng-testing',
    #     public_ip='***************',
    #     inside_ip='***********',
    #     public_port=8000,
    #     inside_port=8000
    # )
    # print(res)
    # res = admin_client.edit_nat_server(
    #     source_nat_server_name='chaos_caoxiangpeng-testing_***********',
    #     project_name='caoxiangpeng-testing',
    #     public_ip='***************',
    #     inside_ip='***********',
    #     public_port=8001,
    #     inside_port=8002
    # )
    # print(res)
    # res = admin_client.delete_nat_server('chaos_caoxiangpeng-testing_***********11111111111111111111111111')
    # print(res)
