from gc import garbage

from application.logger import logger


from tenant.utils.sdk.openstack_client import OpenstackAdminClient
from tenant.utils.sdk.op_baremetal_node.baremetal_node_config import config


class OpenstackBaremetalNodeManager(OpenstackAdminClient):
    """
    OpenStack 文档地址
    https://docs.openstack.org/api-ref/baremetal/?expanded=create-node-detail
    """
    def __init__(self, debug=False, auth_type='v3applicationcredential', node=None):
        super().__init__(debug, auth_type, node)

    def create_baremetal_node(self, name, resource_class, driver, driver_info, network_interface, properties, **kwargs):
        data = self.conn.baremetal.create_node(
            name=name,
            resource_class=resource_class,
            # 节点驱动
            driver=driver,
            driver_info=driver_info,
            network_interface=network_interface,
            properties=properties,
            **kwargs
            )
        logger.info('register_baremetal_node: Response %s' % str(dict(data)))
        return data

    def get_baremetal_node_detail(self, node_uuid, fields=None):
        data = self.conn.baremetal.get_node(node_uuid, fields=fields)
        logger.info('get_baremetal_node_detail: Response %s' % str(dict(data)))
        return data

    def list_baremetal_node(self):
        print(config['node_info'])
        nodes = self.conn.baremetal.nodes()
        print(self.auth_url)
        print('create_project: Response %s' % str(dict(nodes)))
        return nodes

    def create_baremetal_node_port_group(self, node_uuid):
        data = self.conn.baremetal.create_port_group(
            # 网络模式
            mode='802.3ad',
            # SA ports 释义：指定在该端口组中的端口是否可以用作独立端口。
            standalone_ports_supported=True,
            node_uuid=node_uuid,
            name=config['node_info']['name'],
            address=config['mac1'],
            properties=config['port_group_info']['properties']
            )
        logger.info('create_node_port_group: Response %s' % str(dict(data)))
        return data

    def create_baremetal_node_port(self, node_uuid, port_group_uuid, address):
        data = self.conn.baremetal.create_port(
            node_uuid=node_uuid,
            portgroup_uuid=port_group_uuid,
            address=address,
            # PXE 启动, 默认为 True
            pxe_enabled=True,
            local_link_connection={
                "switch_id": config["switch_id"],
                "switch_info": config["switch_info"],
                "port_id": config["port_id"],
                }
            )
        logger.info('create_node_port: Response %s' % str(dict(data)))
        return data

    def create_2node_port(self, node_uuid, port_group_uuid, hardware_address, target_address):
        """
        创建端口组内的端口
        :param str node_uuid: The UUID or Name of the node.
        :param str port_group_uuid: UUID of the Portgroup this resource belongs to.
        :param str hardware_address: 裸机物理 mac 地址, 即常用的 mac1 address
        :param str target_address: 交换机/网络 mac 地址, 即常用的 mac2 address
        """
        data = self.conn.baremetal.create_port(
            node_uuid=node_uuid,
            portgroup_uuid=port_group_uuid,
            address=hardware_address,
            # PXE 启动, 默认为 True
            pxe_enabled=True,
            local_link_connection={
                "switch_id": config["switch_id"],
                "switch_info": config["switch_info"],
                "port_id": config["port_id"],
            }
        )
        logger.info('create_node_port1: Response %s' % str(dict(data)))
        data = self.conn.baremetal.create_port(
            node_uuid=node_uuid,
            portgroup_uuid=port_group_uuid,
            address=target_address,
            # PXE 启动, 默认为 True
            pxe_enabled=True,
            local_link_connection={
                "switch_id": config["switch_id"],
                "switch_info": config["switch_info"],
                "port_id": config["port_id"],
                }
            )
        logger.info('create_node_port2: Response %s' % str(dict(data)))

    def register_full_proc_baremetal_node(self):
        """
        创建端口组内的端口
        :param str node_uuid: The UUID or Name of the node.
        :param str port_group_uuid: UUID of the Portgroup this resource belongs to.
        :param str address: 裸机物理 mac 地址, 即常用的 mac1 address
        """
        # 创建裸机节点基础信息
        res = self.create_baremetal_node()


if __name__ == '__main__':
    import os
    import django

    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
    django.setup()
    op_admin = OpenstackBaremetalNodeManager(debug=True)

    # # 注册裸机节点
    # res = op_admin.register_baremetal_node()
    # print(res)
    # res = op_admin.list_baremetal_node()
    # print(res)

    # 创建端口组
    temp_node_uuid = '5b0c0bd7-3f77-44c8-93e8-8f92f9aee39f'
    # res = op_admin.create_node_port_group(node_uuid=temp_node_uuid)
    # print(res)
    # 创建端口
    temp_node_port_group_uuid = 'e4bd79d1-6dd1-4bd0-b67a-def081e96dfd'
    # 创建第一个端口
    # res = op_admin.create_node_port(node_uuid=temp_node_uuid, port_group_uuid=temp_node_port_group_uuid, address=config['mac1'])
    # print(res)
    # 创建第二个端口
    # res = op_admin.create_node_port(
    #     node_uuid=temp_node_uuid, port_group_uuid=temp_node_port_group_uuid, address=config['mac2']
    #     )
    # print(res)


    # 获取裸机详情
    res = op_admin.get_baremetal_node_detail(node_uuid=temp_node_uuid)
    print(res)




