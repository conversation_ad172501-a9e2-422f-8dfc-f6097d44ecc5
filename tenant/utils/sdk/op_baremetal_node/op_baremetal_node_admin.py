import json
import time
from ipaddress import IPv4Address
from netaddr import EUI, AddrFormatError
from application.logger import logger
from tenant.models import TenantOpenstackIronicHypervisorRegister

from tenant.utils.sdk.openstack_client import OpenstackAdminClient


"""
# 裸机节点端口组-网络模式（可选）
https://www.kernel.org/doc/Documentation/networking/bonding.txt
- balance-rr
- active-backup ==> 原始默认
- balance-xor
- broadcast
- 802.3ad ==> 实际情况的默认
- balance-tlb
- balance-alb
"""


class OpenstackBaremetalNodeManager(OpenstackAdminClient):
    """
    OpenStack 文档地址
    https://docs.openstack.org/api-ref/baremetal/?expanded=create-node-detail
    """

    def __init__(self, debug=False, auth_type='v3applicationcredential', node=None):
        super().__init__(debug, auth_type, node)

    def _node_exists(self, identifier):
        """检查节点是否已存在（通过名称或UUID）"""
        try:
            # 尝试获取节点详情，成功即表示存在
            self.conn.baremetal.get_node(identifier)
            return True
        except Exception:
            return False

    def create_baremetal_node(self, name, resource_class, driver, driver_info, network_interface, properties, **kwargs):
        """
        创建裸机节点基础信息
        :param str name: 节点名称
        :param str resource_class: 资源类
        :param str driver: 节点驱动
        :param dict driver_info: IPMI 信息配置
        :param str network_interface: 网络接口
        :param dict properties: 裸机节点属性配置
        """
        if self._node_exists(name):
            logger.error('register_baremetal_node: 节点名称已存在')
            return
        data = self.conn.baremetal.create_node(
            name=name,
            resource_class=resource_class,
            # 节点驱动
            driver=driver,
            driver_info=driver_info,
            network_interface=network_interface,
            properties=properties,
            **kwargs
            )
        logger.info('register_baremetal_node: Response %s' % str(dict(data)))
        return dict(data)

    def delete_baremetal_node(self, node_uuid):
        """
        删除裸机节点
        :param str node_uuid: 节点id 或名称
        """
        if not self._node_exists(node_uuid):
            logger.error('delete_baremetal_node: 节点不存在')
            return
        data = self.conn.baremetal.delete_node(node_uuid)
        logger.info('delete_baremetal_node: Response %s' % str(dict(data)))
        return dict(data)

    def get_baremetal_node_detail(self, node_uuid, fields=None, to_dict=True):
        """
        获取裸机节点信息详情
        :param str node_uuid: 节点id 或名称
        :param fields: 指定获取参数，即 仅获取指定的 key 名称
        :param to_dict: 返回 dict 响应数据
        """
        data = self.conn.baremetal.get_node(node_uuid, fields=fields)
        logger.info('get_baremetal_node_detail: Response %s' % str(dict(data)))
        if to_dict:
            return dict(data)
        else:
            return data

    def list_baremetal_nodes(self):
        """
        获取裸机节点信息列表
        """
        nodes = self.conn.baremetal.nodes()
        logger.info('list_baremetal_nodes: Response %s' % str(dict(nodes)))
        return dict(nodes)

    def create_baremetal_node_port_group(
            self, node_uuid, name, hardware_address, properties, mode='802.3ad',
            standalone_ports_supported=True, **kwargs
            ):
        """
        创建裸机节点的端口组
        :param str node_uuid: UUID of the Node this resource belongs to.
        :param str name: Human-readable identifier for the Portgroup resource. May be undefined.
        :param str hardware_address: Physical hardware address of this Portgroup, typically the hardware MAC address.
        :param dict properties: Key/value properties related to the port group’s configuration.
        :param str mode: 网络模式, Mode of the port group. For possible values, refer to
        https://www.kernel.org/doc/Documentation/networking/bonding.txt. If not specified in a request to create a
        port group, it will be set to the value of the [DEFAULT]default_portgroup_mode configuration option. When
        set, can not be removed from the port group.
        :param bool standalone_ports_supported: SA ports 释义：指定在该端口组中的端口是否可以用作独立端口.
        """
        data = self.conn.baremetal.create_port_group(
            mode=mode,
            standalone_ports_supported=standalone_ports_supported,
            node_uuid=node_uuid,
            name=name,
            address=hardware_address,
            properties=properties,
            **kwargs
            )
        logger.info('create_baremetal_node_port_group: Response %s' % str(dict(data)))
        return dict(data)

    def create_baremetal_node_port(
            self,
            node_uuid,
            port_group_uuid,
            address,
            local_link_connection,
            pxe_enabled=True,
            **kwargs
            ):
        """
        创建端口组下的端口
        :param str node_uuid: The UUID or Name of the node.
        :param str port_group_uuid: UUID of the Portgroup this resource belongs to.
        :param str address: Physical hardware address of this network Port, typically the hardware MAC address.
        :param dict local_link_connection: The Port binding profile. If specified, must contain switch_id (only a MAC
        address or an OpenFlow based datapath_id of the switch are accepted in this field) and port_id (identifier of
        the physical port on the switch to which node’s port is connected to) fields. switch_info is an optional
        string field to be used to store any vendor-specific information.
        :param bool pxe_enabled: PXE 启动, 默认为 True
        """
        data = self.conn.baremetal.create_port(
            node_uuid=node_uuid,
            portgroup_uuid=port_group_uuid,
            address=address,
            pxe_enabled=pxe_enabled,
            local_link_connection=local_link_connection,
            **kwargs
            )
        logger.info('create_baremetal_node_port: Response %s' % str(dict(data)))
        return dict(data)

    @staticmethod
    def read_json_file(file_name):
        with open(file_name, 'r', encoding='utf-8') as fp:
            data = fp.read()
            return json.loads(data)

    def read_csv_file(self, file_name):
        pass
    def read_execl_file(self, file_name):
        pass

    def adapt_verify_filename_config(self, file_name=None):
        """
        适配配置文件
        :param file_name: 配置文件名称
        """
        config_info = self.read_json_file(file_name=file_name)
        sn = config_info.get("sn")
        default_node_properties = {
            "cpus": "144",
            "memory_mb": "515072",
            "local_gb": "400",
            "cpu_arch": "x86_64",
            "capabilities": "boot_option:local,disk_label:gpt"
            }
        if sn:
            default_node_properties["sn"] = sn
        default_node_driver = "ipmi"
        default_node_network_interface = "neutron"
        node_info = {
            "name": config_info["name"],
            "resource_class": config_info["node_info"]["resource_class"],
            "properties": config_info["node_info"].get("properties", default_node_properties),
            "driver": config_info["node_info"].get("driver", default_node_driver),
            "driver_info": config_info["node_info"]["driver_info"],
            "network_interface": config_info["node_info"].get("network_interface", default_node_network_interface),
            }
        default_port_group_properties = {
            "miimon": '100',
            "xmit_hash_policy": "layer2+3"
            }
        default_port_group_mode = "802.3ad"
        port_group_info = {
            "name": config_info["name"],
            "hardware_address": config_info["node_port_info"]["hardware_address"],
            "properties": config_info["node_port_info"].get("properties", default_port_group_properties),
            "mode": config_info["node_port_info"].get("mode", default_port_group_mode),
            "standalone_ports_supported": config_info["node_port_info"].get("standalone_ports_supported", True),
            }
        local_link_connection = {
            "switch_id": config_info["node_port_info"]["switch_id"],
            "switch_info": config_info["node_port_info"]["switch_info"],
            "port_id": config_info["node_port_info"]["port_id"]
            }
        # 裸机节点物理机端口的信息
        hardware_port_info = {
            "address": config_info["node_port_info"]["hardware_address"],
            "local_link_connection": local_link_connection,
            "pxe_enabled": config_info["node_port_info"].get("pxe_enabled", True),
            }
        network_port_info = {
            "address": config_info["node_port_info"]["network_address"],
            "local_link_connection": local_link_connection,
            "pxe_enabled": config_info["node_port_info"].get("pxe_enabled", True),
            }
        return {
            "node_info": node_info,
            "port_group_info": port_group_info,
            "hardware_port_info": hardware_port_info,
            "network_port_info": network_port_info,
            }

    @staticmethod
    def adapt_verify_ironic_hyper_register_config(config_map_info=None):
        """
        适配配置文件
        :param config_map_info: 从 TenantOpenstackHypervisorRegister 获取配置信息
        """
        config_info  = config_map_info or {}
        sn = config_info.get("sn")
        default_node_properties = {
            "cpus": "144",
            "memory_mb": "515072",
            "local_gb": "400",
            "cpu_arch": "x86_64",
            "capabilities": "boot_option:local,disk_label:gpt"
            }
        if sn:
            default_node_properties["sn"] = sn
        default_node_driver = "ipmi"
        default_node_network_interface = "neutron"
        default_port_group_properties = {
            "miimon": '100',
            "xmit_hash_policy": "layer2+3"
            }
        default_port_group_mode = "802.3ad"

        # 处理节点属性配置信息
        node_properties = config_info.get("node_properties", default_node_properties)
        if node_properties:
            if isinstance(node_properties, str):
                if node_properties.startswith('{'):
                    node_properties = json.loads(node_properties)
                    if sn:
                        node_properties["sn"] = sn

        node_info = {
            "name": config_info["name"],
            "resource_class": config_info["node_resource_class"],
            "properties": node_properties,
            "driver": config_info.get("node_driver", default_node_driver),
            "driver_info": {
                "ipmi_username": config_info["node_driver_ipmi_username"],
                "ipmi_password": config_info["node_driver_ipmi_password"],
                "ipmi_address": config_info["node_driver_ipmi_address"]
                },
            "network_interface": config_info.get("node_network_interface", default_node_network_interface),
            }

        # 处理节点端口属性配置信息
        port_group_properties = config_info.get("port_group_properties", default_port_group_properties)
        if port_group_properties:
            if isinstance(port_group_properties, str):
                if port_group_properties.startswith('{'):
                    port_group_properties = json.loads(port_group_properties)
        port_group_info = {
            "name": config_info["name"],
            "hardware_address": config_info["port_hardware_address"],
            "properties": port_group_properties,
            "mode": config_info.get("port_group_mode", default_port_group_mode),
            "standalone_ports_supported": config_info.get("port_group_standalone_ports_supported", True),
            }
        local_link_connection = {
            "switch_id": config_info["port_switch_id"],
            "switch_info": config_info["port_switch_info"],
            "port_id": config_info["port_port_id"]
            }
        # 裸机节点物理机端口的信息
        hardware_port_info = {
            "address": config_info["port_hardware_address"],
            "local_link_connection": local_link_connection,
            "pxe_enabled": config_info.get("port_pxe_enabled", True),
            }
        network_port_info = {
            "address": config_info["port_network_address"],
            "local_link_connection": local_link_connection,
            "pxe_enabled": config_info.get("port_pxe_enabled", True),
            }
        data = {
            "node_info": node_info,
            "port_group_info": port_group_info,
            "hardware_port_info": hardware_port_info,
            "network_port_info": network_port_info,
            }
        print(data)
        return data

    def validate_config_info(self, config_info):
        """
        验证配置信息
        :param dict config_info: 配置信息
        """
        if not config_info:
            return False
        # 验证ipv4
        if not self.validate_config_map_ipv4(config_info["node_info"]["driver_info"]["ipmi_address"]):
            print('验证ipv4失败')
            return {}
        if not self.validate_config_map_mac_address(config_info["hardware_port_info"]["address"]):
            print('验证hardware_address mac地址失败')
            return {}
        if not self.validate_config_map_mac_address(config_info["network_port_info"]["address"]):
            print('验证network_address mac地址失败')
            return {}
        if not self.validate_config_map_mac_address(config_info["hardware_port_info"]["local_link_connection"]["switch_id"]):
            print('验证switch_id mac地址失败')
            return {}
        return config_info

    @staticmethod
    def get_ironic_hypervisor_register_info(register_id):
        obj = TenantOpenstackIronicHypervisorRegister.objects.filter(
            id=register_id,
            register_status='未开始',
            is_deleted=False,
            ).first()
        if not obj:
            logger.error(f'未开始的注册任务不存在,[注册任务ID:{register_id}]')
            return {}
        return obj.to_dict

    @staticmethod
    def validate_config_map_ipv4(value):
        try:
            IPv4Address(value)
            return True
        except ValueError:
            return False

    @staticmethod
    def validate_config_map_mac_address(value):
        try:
            EUI(value)
            return True
        except ValueError:
            return False


    def validate_node_info(self, node_uuid):
        try:
            response = self.conn.baremetal.validate_node(node_uuid)
            logger.info('validate_node_info: Response %s' % str(dict(response)))
            dict_response = dict(response)
            dict_response["success"] = True
            return dict_response
        except Exception as e:
            logger.error('validate_node_info: Response %s' % str(e))
            return {
                "success": False,
                "message": str(e),
                }

    def set_node_maintenance(self, node_uuid, reason="Maintenance by Admin", is_maintenance=True):
        """
        设置裸机节点的维护模式
        :param str node_uuid: The UUID or Name of the node.
        :param str reason: 维护原因
        :param bool is_maintenance: 是否维护模式
        """
        if is_maintenance:
            reason = "Maintenance by Admin"
            data = self.conn.baremetal.set_node_maintenance(node_uuid, reason=reason)
        else:
            data = self.conn.baremetal.unset_node_maintenance(node_uuid)
        logger.info('set_node_maintenance: Response %s' % str(dict(data)))
        return dict(data)

    def set_node_provision_state(self, node_uuid, target_state, configdrive=None):
        """
        安全设置节点供应状态
        Ironic 节点状态转换的正确顺序:
        enroll → manageable → available → active → available → manageable → enroll

        :param str node_uuid: 节点UUID
        :param str target_state: 目标状态 (manage, provide, active, deleted等)
        :param str configdrive: 可选的配置驱动信息
        :return: bool 操作是否成功
        """
        try:
            # 获取当前节点状态
            node = self.conn.baremetal.get_node(node_uuid)
            current_state = node.provision_state.lower()
            logger.info(f" 当前节点状态: {current_state}")
            if current_state == 'active':
                logger.error(f" 节点处于active状态，无法进行状态转换")
                return False

            # 特殊处理: available → manageable 需要先进入维护模式
            if current_state == 'available' and target_state.lower() == 'manage':
                logger.info(" 节点处于available状态，需要先进入维护模式")
                self.conn.baremetal.set_node_maintenance(
                    node_uuid,
                    reason="State transition to manageable"
                    )
                logger.info(" 节点已进入维护模式")

            # 构建参数字典
            kwargs = {'target': target_state.lower()}
            if configdrive:
                kwargs['configdrive'] = configdrive

                # 执行状态转换
            self.conn.baremetal.set_node_provision_state(node_uuid, **kwargs)
            logger.info(f" 已将节点状态设置为: {target_state}")

            # 等待状态转换完成
            self._wait_for_provision_state(node_uuid, target_state.lower())
            return True

        except Exception as e:
            logger.error(f" 状态设置失败: {str(e)}", exc_info=True)
            return False

    def manage_node_state_power(self, node_uuid, target_state, timeout=None):
        """
        裸机节点电源状态管理（精简优化版）

        :param node_uuid: 节点ID或名称
        :param target_state: 目标状态（power on/power off/rebooting/soft power off/soft rebooting）
        :param timeout: 超时时间（秒）
        :return: 操作结果字典
        """
        # 有效状态映射（外部API格式 → 内部格式）
        STATE_MAP = {
            'power on': 'power on',
            'power off': 'power off',
            'rebooting': 'rebooting',
            'soft power off': 'soft power off',
            'soft rebooting': 'soft rebooting'
            }

        result = {
            'success': False,
            'current_state': None,
            'target_state': target_state,
            'changed': False,
            'message': ''
            }

        try:
            # 验证目标状态
            if target_state not in STATE_MAP:
                raise ValueError(f"无效电源状态，可选: {list(STATE_MAP.keys())}")

            # 获取当前状态
            node = self.conn.baremetal.get_node(node_uuid)
            logger.info(f" 当前节点信息[node]:{node}")
            if node.provision_state.lower() == 'active':
                raise ValueError("节点已处于活动状态，请先将节点状态设置为非活动状态,禁止操作！")
            current_state = node.power_state
            result['current_state'] = current_state

            # 检查是否已是目标状态
            if current_state == STATE_MAP[target_state]:
                result.update(
                    {
                        'success': True,
                        'message': f"已是目标状态: {target_state}"
                        }
                    )
                return result

            # 执行电源操作
            request = {'target': target_state}
            if timeout:
                request['timeout'] = timeout

            self.conn.baremetal.set_node_power_state(node_uuid, **request)

            # 等待状态变更（如果指定超时）
            if timeout:
                final_state = self._wait_for_power_state(
                    node_uuid,
                    STATE_MAP[target_state],
                    timeout
                    )
                result['current_state'] = final_state
                result['message'] = f"最终状态: {final_state}"
            else:
                result['message'] = "状态变更请求已提交"

            result.update(
                {
                    'success': True,
                    'changed': True
                    }
                )

        except Exception as e:
            result['message'] = f"电源操作失败: {str(e)}"
            logger.error(result['message'])

        return result

    def _wait_for_power_state(self, node_uuid, target_state, timeout):
        """等待电源状态变更（内部方法）"""
        start = time.time()
        last_state = None

        while time.time() - start < timeout:
            node = self.conn.baremetal.get_node(node_uuid)
            current = node.power_state

            if current != last_state:
                logger.info(f" 状态变更: {last_state or 'None'} → {current}")
                last_state = current

            if current == target_state:
                return current

            if node.last_error:
                raise Exception(f"电源操作错误: {node.last_error}")

            time.sleep(5)

        raise Exception(f"超时未达到 {target_state}，当前状态: {last_state}")


    def manage_node_state_transition(self, node_uuid, target_state, configdrive=None):
        """
        管理节点状态转换的完整方法
        :param str node_uuid: 节点UUID
        :param str target_state: 目标状态 (enroll, manage, provide, active, deleted)
        :param str configdrive: 可选的配置驱动信息
        :return: bool 操作是否成功
        """
        # 定义状态转换映射（操作指令→实际目标状态）
        STATE_TRANSITION_MAP = {
            'enroll': 'enroll',
            'manage': 'manageable',
            'provide': 'available',
            'active': 'active',
            'available': 'available',
            }

        # 定义有效状态转换路径
        VALID_TRANSITIONS = {
            'enroll': ['manage', 'available'],
            'manageable': ['available', 'provide'],
            'available': ['active', 'manage'],
            }

        try:
            # 获取当前节点状态
            node = self.conn.baremetal.get_node(node_uuid)
            current_state = node.provision_state.lower()
            logger.info(f" 当前节点状态: {current_state}")

            # 检查目标状态是否有效
            if target_state.lower() not in VALID_TRANSITIONS.get(current_state, []):
                error_msg = f"无效的状态转换: 从 {current_state} 到 {target_state}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # 特殊处理流程
            if current_state == 'enroll' and target_state.lower() == 'manage':
                return self._enroll_to_manageable(node_uuid)
            elif current_state == 'enroll' and target_state.lower() == 'available':
                return self._enroll_to_available(node_uuid)
            elif current_state == 'available' and target_state.lower() == 'manage':
                return self._available_to_manageable(node_uuid)
            elif current_state == 'manageable' and target_state.lower() in ['provide', 'available']:
                return self._manageable_to_available(node_uuid)

            # 标准状态转换流程
            kwargs = {'target': target_state.lower()}
            if configdrive:
                kwargs['configdrive'] = configdrive

            self.conn.baremetal.set_node_provision_state(node_uuid, **kwargs)
            logger.info(f" 已将节点状态设置为: {target_state}")

            expected_state = STATE_TRANSITION_MAP.get(target_state.lower(), target_state.lower())
            self._wait_for_provision_state(node_uuid, expected_state)
            return True

        except Exception as e:
            logger.error(f" 状态转换失败: {str(e)}", exc_info=True)
            return False

    def _enroll_to_manageable(self, node_uuid):
        """从 enroll 状态转换到 manageable 状态"""
        try:
            logger.info(" 执行 enroll → manageable 转换")

            # 1. 验证节点信息
            # 1. 首先验证节点
            validation_data = self.validate_node_info(node_uuid)
            logger.warning(f"【节点验证失败】，{str(validation_data)}")

            # 2. 转换为 manageable 状态
            self.conn.baremetal.set_node_provision_state(node_uuid, target='manage')

            # 3. 等待转换完成
            self._wait_for_provision_state(node_uuid, 'manageable')
            return True

        except Exception as e:
            logger.error(f"enroll  → manageable 转换失败: {str(e)}")
            return False

    def _enroll_to_available(self, node_uuid):
        """从 enroll 状态直接转换到 available 状态"""
        try:
            logger.info(" 执行 enroll → available 转换")

            # 1. 首先验证节点
            validation_data = self.validate_node_info(node_uuid)
            logger.warning(f"【节点验证失败】，{str(validation_data)}")

            # 2. 转换为 manageable 状态
            self.conn.baremetal.set_node_provision_state(node_uuid, target='manage')
            self._wait_for_provision_state(node_uuid, 'manageable')

            # 3. 然后转换为 available 状态
            self.conn.baremetal.set_node_provision_state(node_uuid, target='provide')
            self._wait_for_provision_state(node_uuid, 'available')

            logger.info("enroll  → available 转换成功")
            return True

        except Exception as e:
            logger.error(f"enroll  → available 转换失败: {str(e)}")
            return False

    def _manageable_to_available(self, node_uuid):
        """从 manageable 状态转换到 available 状态"""
        try:
            logger.info(" 执行 manageable → available 转换")

            # 1. 转换为 available 状态 (使用 provide 指令)
            self.conn.baremetal.set_node_provision_state(node_uuid, target='provide')

            # 2. 等待转换完成 (实际会转为 available)
            self._wait_for_provision_state(node_uuid, 'available')
            return True

        except Exception as e:
            logger.error(f"manageable  → available 转换失败: {str(e)}")
            return False

    def _available_to_manageable(self, node_uuid):
        """从 available 状态转换到 manageable 状态"""
        try:
            logger.info(" 执行 available → manageable 转换")

            # 1. 进入维护模式
            self.conn.baremetal.set_node_maintenance(
                node_uuid,
                reason="Transition to manageable"
                )

            # 2. 转换为 manageable 状态
            self.conn.baremetal.set_node_provision_state(node_uuid, target='manage')

            # 3. 等待转换完成
            self._wait_for_provision_state(node_uuid, 'manageable')

            # 4. 退出维护模式
            self.conn.baremetal.unset_node_maintenance(node_uuid)
            return True

        except Exception as e:
            logger.error(f"available  → manageable 转换失败: {str(e)}")
            return False

    def _wait_for_provision_state(self, node_uuid, target_state, timeout=600):
        """
        等待节点达到指定供应状态
        :param str node_uuid: 节点UUID
        :param str target_state: 目标状态
        :param int timeout: 超时时间(秒)
        """
        start_time = time.time()
        last_state = None
        current_state = None

        while time.time() - start_time < timeout:
            node = self.conn.baremetal.get_node(node_uuid)
            current_state = node.provision_state.lower()

            if current_state != last_state:
                logger.info(f" 状态变化: {last_state or 'None'} → {current_state}")
                last_state = current_state

            if current_state == target_state.lower():
                logger.info(f" 已达到目标状态: {target_state}")
                return True

            if current_state == 'error':
                error_msg = f"节点进入错误状态: {node.last_error}"
                logger.error(error_msg)
                raise Exception(error_msg)

            time.sleep(5)

        raise Exception(f"状态转换超时: 当前状态 {current_state}, 期望状态 {target_state}")

    def start_register_ironic_hypervisor_worker(self, register_id, is_validate_provision_state=True):
        try:
            confing_map_info = self.get_ironic_hypervisor_register_info(register_id=register_id)
            confing_map_info  = self.adapt_verify_ironic_hyper_register_config(config_map_info=confing_map_info)
            verified_config_info = self.validate_config_info(confing_map_info)
            if verified_config_info:
                logger.info(f" 验证注册信息成功")
                return self.register_full_proc_baremetal_node(
                    dry_run=False, node_config_info=confing_map_info,
                    is_validate_provision_state=is_validate_provision_state,
                    )
            else:
                logger.warning(f" 验证注册信息失败")
                return False, "验证注册信息失败", None
        except Exception as e:
            logger.warning(f" 获取注册信息失败: {str(e)}")
            return False, "获取注册信息失败", None

    def register_full_proc_baremetal_node(self, dry_run=True, node_config_info=None, is_validate_provision_state=True):
        """
        注册裸机节点
        """
        # 创建裸机节点基础信息
        if node_config_info is None:
            node_config_info = {}
        try:
            if not node_config_info:
                logger.warning("配置信息有误")
                return False, "配置信息有误", None
            logger.info(f'格式化后的裸机节点信息为：{json.dumps(node_config_info, indent=2)}')
            if dry_run:
                logger.info('dry_run 模式下，不执行创建裸机节点操作.')
                return True, "dry_run 模式下，不执行创建裸机节点操作.", None
            is_created = self._node_exists(node_config_info["node_info"]["name"])
            if is_created:
                logger.warning("节点已存在")
                return False, "节点已存在", None
            created_node_res_data = self.create_baremetal_node(**node_config_info["node_info"])
            created_node_group_res_data = self.create_baremetal_node_port_group(node_uuid=created_node_res_data["id"], **node_config_info["port_group_info"])
            # 创建第一个端口信息
            self.create_baremetal_node_port(
                node_uuid=created_node_res_data["id"],
                port_group_uuid=created_node_group_res_data["id"],
                **node_config_info["hardware_port_info"]
                )
            # 创建第二个端口信息
            self.create_baremetal_node_port(
                node_uuid=created_node_res_data["id"],
                port_group_uuid=created_node_group_res_data["id"],
                **node_config_info["network_port_info"]
                )

            logger.info('注册节点操作完成.')
            # 设置裸机节点的 provisioning 状态 为 available
            if is_validate_provision_state:
                self.manage_node_state_transition(node_uuid=created_node_res_data["id"], target_state='available')
            return True, '注册节点操作完成.', created_node_res_data
        except Exception as e:
            logger.error(f'注册节点操作失败，错误信息为：{str(e)}')
            return False, '注册节点操作失败.', None


if __name__ == '__main__':
    import os
    import django
    # TODO: 此功能较为特殊，由于网络共用问题，配置文件需要确认是否有被正确使用或者配置，配置错误可能影响生产环境机器使用。
    # TODO: 没有确切的未使用的机器及网口信息，【禁止】在生产环境或测试环境测试！！！！！！！！！！！

    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
    django.setup()
    op_admin = OpenstackBaremetalNodeManager(debug=True)
    node_detail_info = op_admin.get_baremetal_node_detail(node_uuid='xxx')
    print(f'node_detail_info: {node_detail_info}')
    # op_admin.register_full_proc_baremetal_node()
    # op_admin.register_full_proc_baremetal_node(dry_run=False)
    # temp_uuid = '862cc6f8-2507-48d8-b991-c1667a732415'
    # 验证指定节点信息
    # op_admin.validate_node_info(node_uuid=temp_uuid)
    # 设置裸机节点的维护模式
    # op_admin.set_node_maintenance(node_uuid=temp_uuid, reason="测试")
    #  设置裸机节点的 provisioning 状态
    # op_admin.manage_node_state_transition(node_uuid=temp_uuid, target_state='provide')
