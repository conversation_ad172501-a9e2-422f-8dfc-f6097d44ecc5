config = {
    "name": "**********",
    "port_group_info": {
        "mode": "802.3ad",
        # SA ports 释义：指定在该端口组中的端口是否可以用作独立端口。
        "standalone_ports_supported": True,
        "properties": {
            "miimon": '100',
            "xmit_hash_policy": "layer2+3"
            }
        },
    "node_port_info": {
        "switch_id": "c4:69:f0:46:7a:90",
        "switch_info": "ASW-D4-3.EM101-D01E01-38U-CE6857F-VM-G1-AB",
        "port_id": "Eth-Trunk27",
        # 同 mac1 裸机节点物理 mac 地址
        "hardware_address": "A0:36:9F:53:B5:68",
        # 同 mac2 网络节点虚拟 mac 地址
        "network_address": "A0:36:9F:53:B5:6A",
        },
    "node_info": {
        "resource_class": "ironic-4090",
        "network_interface": "neutron",
        "properties": {
            "cpus": "144",
            "memory_mb": "515072",
            "local_gb": "400",
            "cpu_arch": "x86_64",
            "capabilities": "boot_option:local,disk_label:gpt"
            },
        "driver_info": {
            "ipmi_username": "ADMIN",
            "ipmi_password": "ADMIN",
            # 此 ipmi_port 不需要填，我们有证书，填了会抛（无法正常跳转）504错误，
            # "ipmi_port": "80",
            "ipmi_address": "**********",
            # 暂不进行配置 ipmi_protocol_version 以排查此参数造成的异常问题
            # "ipmi_protocol_version": "2.0"
            }
        },

    }


