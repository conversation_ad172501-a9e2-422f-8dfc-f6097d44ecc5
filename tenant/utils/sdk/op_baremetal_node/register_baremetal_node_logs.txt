[2025-05-15 15:42:33][root.register_full_proc_baremetal_node():440] [INFO] 格式化后的裸机节点信息为：{
  "name": "**********",
  "resource_class": "ironic-4090",
  "properties": {
    "cpus": "144",
    "memory_mb": "515072",
    "local_gb": "400",
    "cpu_arch": "x86_64",
    "capabilities": "boot_option:local,disk_label:gpt"
  },
  "driver": "ipmi",
  "driver_info": {
    "ipmi_username": "ADMIN",
    "ipmi_password": "ADMIN",
    "ipmi_address": "**********"
  },
  "network_interface": "neutron"
}
[2025-05-15 15:42:35][root.create_baremetal_node():38] [INFO] register_baremetal_node: Response {'allocation_id': None, 'owner': None, 'boot_mode': None, 'chassis_id': None, 'clean_step': {}, 'conductor': 'infra1', 'conductor_group': '', 'created_at': '2025-05-15T07:42:35+00:00', 'deploy_step': {}, 'description': None, 'driver': 'ipmi', 'driver_info': {'ipmi_username': 'ADMIN', 'ipmi_password': '******', 'ipmi_address': '**********'}, 'driver_internal_info': {}, 'extra': {}, 'fault': None, 'id': '52cb7e01-a988-4388-a23e-efc885133924', 'instance_info': {}, 'instance_id': None, 'is_automated_clean_enabled': None, 'is_console_enabled': False, 'is_maintenance': False, 'is_protected': False, 'is_retired': False, 'is_secure_boot': None, 'last_error': None, 'lessee': None, 'links': [{'href': 'https://************:6385/v1/nodes/52cb7e01-a988-4388-a23e-efc885133924', 'rel': 'self'}, {'href': 'https://************:6385/nodes/52cb7e01-a988-4388-a23e-efc885133924', 'rel': 'bookmark'}], 'maintenance_reason': None, 'name': '**********', 'parent_node': None, 'ports': [{'href': 'https://************:6385/v1/nodes/52cb7e01-a988-4388-a23e-efc885133924/ports', 'rel': 'self'}, {'href': 'https://************:6385/nodes/52cb7e01-a988-4388-a23e-efc885133924/ports', 'rel': 'bookmark'}], 'port_groups': [{'href': 'https://************:6385/v1/nodes/52cb7e01-a988-4388-a23e-efc885133924/portgroups', 'rel': 'self'}, {'href': 'https://************:6385/nodes/52cb7e01-a988-4388-a23e-efc885133924/portgroups', 'rel': 'bookmark'}], 'power_state': None, 'properties': {'cpus': '144', 'memory_mb': '515072', 'local_gb': '400', 'cpu_arch': 'x86_64', 'capabilities': 'boot_option:local,disk_label:gpt'}, 'protected_reason': None, 'provision_state': 'enroll', 'retired_reason': None, 'raid_config': {}, 'reservation': None, 'resource_class': 'ironic-4090', 'service_step': None, 'shard': None, 'states': [{'href': 'https://************:6385/v1/nodes/52cb7e01-a988-4388-a23e-efc885133924/states', 'rel': 'self'}, {'href': 'https://************:6385/nodes/52cb7e01-a988-4388-a23e-efc885133924/states', 'rel': 'bookmark'}], 'target_provision_state': None, 'target_power_state': None, 'target_raid_config': {}, 'traits': [], 'updated_at': None, 'bios_interface': 'no-bios', 'boot_interface': 'ipxe', 'console_interface': 'no-console', 'deploy_interface': 'direct', 'firmware_interface': None, 'inspect_interface': 'inspector', 'management_interface': 'ipmitool', 'network_interface': 'neutron', 'power_interface': 'ipmitool', 'raid_interface': 'no-raid', 'rescue_interface': 'no-rescue', 'storage_interface': 'noop', 'vendor_interface': 'ipmitool', 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': None, 'project': Munch({'id': '491b74a318724e988d09bbe90f076a46', 'name': None, 'domain_id': None, 'domain_name': None})})}
[2025-05-15 15:42:35][root.create_baremetal_node_port_group():84] [INFO] create_baremetal_node_port_group: Response {'address': 'a0:36:9f:53:b5:68', 'created_at': '2025-05-15T07:42:35.874621+00:00', 'extra': {}, 'name': '**********', 'id': '71c2f960-9753-4863-8a88-853f1c8215ce', 'internal_info': {}, 'is_standalone_ports_supported': True, 'links': [{'href': 'https://************:6385/v1/portgroups/71c2f960-9753-4863-8a88-853f1c8215ce', 'rel': 'self'}, {'href': 'https://************:6385/portgroups/71c2f960-9753-4863-8a88-853f1c8215ce', 'rel': 'bookmark'}], 'mode': '802.3ad', 'node_id': '52cb7e01-a988-4388-a23e-efc885133924', 'ports': [{'href': 'https://************:6385/v1/portgroups/71c2f960-9753-4863-8a88-853f1c8215ce/ports', 'rel': 'self'}, {'href': 'https://************:6385/portgroups/71c2f960-9753-4863-8a88-853f1c8215ce/ports', 'rel': 'bookmark'}], 'properties': {'miimon': '100', 'xmit_hash_policy': 'layer2+3'}, 'updated_at': None, 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': None, 'project': Munch({'id': '491b74a318724e988d09bbe90f076a46', 'name': None, 'domain_id': None, 'domain_name': None})})}
[2025-05-15 15:42:35][root.create_baremetal_node_port():115] [INFO] create_baremetal_node_port: Response {'address': 'a0:36:9f:53:b5:68', 'created_at': '2025-05-15T07:42:36+00:00', 'extra': {}, 'id': 'aef548ab-6ac8-4eb0-8d10-05ce840e70ef', 'internal_info': {}, 'is_pxe_enabled': True, 'is_smartnic': False, 'links': [{'href': 'https://************:6385/v1/ports/aef548ab-6ac8-4eb0-8d10-05ce840e70ef', 'rel': 'self'}, {'href': 'https://************:6385/ports/aef548ab-6ac8-4eb0-8d10-05ce840e70ef', 'rel': 'bookmark'}], 'local_link_connection': {'switch_id': 'c4:69:f0:46:7a:90', 'switch_info': 'ASW-D4-3.EM101-D01E01-38U-CE6857F-VM-G1-AB', 'port_id': 'Eth-Trunk27'}, 'name': None, 'node_id': '52cb7e01-a988-4388-a23e-efc885133924', 'physical_network': None, 'port_group_id': '71c2f960-9753-4863-8a88-853f1c8215ce', 'updated_at': None, 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': None, 'project': Munch({'id': '491b74a318724e988d09bbe90f076a46', 'name': None, 'domain_id': None, 'domain_name': None})})}
[2025-05-15 15:42:35][root.create_baremetal_node_port():115] [INFO] create_baremetal_node_port: Response {'address': 'a0:36:9f:53:b5:6a', 'created_at': '2025-05-15T07:42:36+00:00', 'extra': {}, 'id': 'ed352d00-cb06-4a52-bc21-6dcd29228318', 'internal_info': {}, 'is_pxe_enabled': True, 'is_smartnic': False, 'links': [{'href': 'https://************:6385/v1/ports/ed352d00-cb06-4a52-bc21-6dcd29228318', 'rel': 'self'}, {'href': 'https://************:6385/ports/ed352d00-cb06-4a52-bc21-6dcd29228318', 'rel': 'bookmark'}], 'local_link_connection': {'switch_id': 'c4:69:f0:46:7a:90', 'switch_info': 'ASW-D4-3.EM101-D01E01-38U-CE6857F-VM-G1-AB', 'port_id': 'Eth-Trunk27'}, 'name': None, 'node_id': '52cb7e01-a988-4388-a23e-efc885133924', 'physical_network': None, 'port_group_id': '71c2f960-9753-4863-8a88-853f1c8215ce', 'updated_at': None, 'location': Munch({'cloud': 'defaults', 'region_name': 'RegionOne', 'zone': None, 'project': Munch({'id': '491b74a318724e988d09bbe90f076a46', 'name': None, 'domain_id': None, 'domain_name': None})})}
[2025-05-15 15:42:35][root.register_full_proc_baremetal_node():459] [INFO] 注册节点操作完成.
[2025-05-15 15:42:36][root.manage_node_state_transition():280] [INFO]  当前节点状态: enroll
[2025-05-15 15:42:36][root._enroll_to_available():338] [INFO]  执行 enroll → available 转换
[2025-05-15 15:42:37][root.validate_node_info():186] [ERROR] validate_node_info: Response Validation failed for required interfaces of node 52cb7e01-a988-4388-a23e-efc885133924: deploy (Node 52cb7e01-a988-4388-a23e-efc885133924 failed to validate deploy image info. Some parameters were missing. Missing are: ['instance_info.image_source'])
[2025-05-15 15:42:37][root._enroll_to_available():342] [WARNING] 【节点验证失败】，{'success': False, 'message': "Validation failed for required interfaces of node 52cb7e01-a988-4388-a23e-efc885133924: deploy (Node 52cb7e01-a988-4388-a23e-efc885133924 failed to validate deploy image info. Some parameters were missing. Missing are: ['instance_info.image_source'])"}
[2025-05-15 15:42:38][root._wait_for_provision_state():416] [INFO]  状态变化: None → verifying
[2025-05-15 15:42:43][root._wait_for_provision_state():416] [INFO]  状态变化: verifying → manageable
[2025-05-15 15:42:43][root._wait_for_provision_state():420] [INFO]  已达到目标状态: manageable
[2025-05-15 15:42:44][root._wait_for_provision_state():416] [INFO]  状态变化: None → available
[2025-05-15 15:42:44][root._wait_for_provision_state():420] [INFO]  已达到目标状态: available
[2025-05-15 15:42:44][root._enroll_to_available():352] [INFO] enroll  → available 转换成功
