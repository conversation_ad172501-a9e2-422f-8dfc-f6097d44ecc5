import unittest
from tenant.utils.sdk.openstack_client import OpenstackAdminClient

import os
from django.conf import settings
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()


class TestOpenStackAdminClient(unittest.TestCase):

    def setUp(self):
        self.admin_client = OpenstackAdminClient(debug=True)
        self.test_project_name = 'test-project'
        self.test_project_description = 'Test Project Description'

    def test_get_all_projects(self):
        projects = self.admin_client.get_all_projects()
        self.assertIsInstance(projects, list)
        for project in projects:
            self.assertIsNotNone(project.id)
            self.assertIsNotNone(project.name)

    def test_create_and_delete_project(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        self.assertIsNotNone(project.id)
        self.assertEqual(project.name, self.test_project_name)
        self.assertEqual(project.description, self.test_project_description)

        deleted_project = self.admin_client.delete_project(project.id)
        self.assertIsNone(deleted_project)

    def test_update_project(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        new_name = 'updated-test-project'
        new_description = 'Updated Test Project Description'
        updated_project = self.admin_client.update_project(project.id, name=new_name, description=new_description)
        self.assertEqual(updated_project.name, new_name)
        self.assertEqual(updated_project.description, new_description)

        self.admin_client.delete_project(project.id)

    def test_assign_project_to_admin(self):
        # 创建测试项目
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        self.assertIsNotNone(project.id)

        # 添加 admin 用户到此项目
        result = self.admin_client.assign_project_to_admin(project.id,)
        self.assertIsNone(result)

    def test_update_project_compute_quotas(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        compute_quotas = {
            'cores': 20,
            'instances': 10,
            'ram': 20480  # 单位为 MB
        }
        updated_quotas = self.admin_client.update_project_compute_quotas_set(project.id, **compute_quotas)
        self.assertIsNotNone(updated_quotas)

        self.admin_client.delete_project(project.id)

    def test_update_project_network_quotas(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        network_quotas = {
            'network': 10,
            'subnet': 20,
            'port': 50,
            'router': 5,
            'floatingip': 20,
            'security_group': 20,
            'security_group_rule': 100
        }
        updated_quotas = self.admin_client.update_project_network_quotas_set(project.id, **network_quotas)
        self.assertIsNotNone(updated_quotas)

        self.admin_client.delete_project(project.id)

    def test_update_project_volume_quotas(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        volume_quotas = {
            'volumes': 10,
            'gigabytes': 1000  # 单位为 GB
        }
        updated_quotas = self.admin_client.update_project_volume_quotas_set(project.id, **volume_quotas)
        self.assertIsNotNone(updated_quotas)

        self.admin_client.delete_project(project.id)

    def test_update_project_block_storage_quotas(self):
        project = self.admin_client.create_project(self.test_project_name, self.test_project_description)
        block_storage_quotas = {
            'volumes': 10,
            'gigabytes': 1000  # 单位为 GB
        }
        updated_quotas = self.admin_client.update_project_block_storage_quotas_set(project.id, **block_storage_quotas)
        self.assertIsNotNone(updated_quotas)

        self.admin_client.delete_project(project.id)

    def tearDown(self):
        # 清理测试项目
        projects = self.admin_client.get_all_projects()
        for project in projects:
            if project.name == self.test_project_name:
                self.admin_client.delete_project(project.id)


if __name__ == "__main__":
    unittest.main()

