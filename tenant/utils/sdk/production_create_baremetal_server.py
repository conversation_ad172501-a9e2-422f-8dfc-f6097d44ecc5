# """
# <AUTHOR>
# @Date    ：2024/11/23
# """
# import json
# import time
#
# from openstack import connect
# from openstack import enable_logging
# from openstack.exceptions import ResourceFailure, HttpException
# from django.conf import settings
# from application.logger import logger
#
#
# class OpenstackAdminClient(object):
#     """
#     此OpenStack客户端仅适配于以admin用户来管理所有项目、用户、资源的情况,禁用域的概念
#     """
#
#     def __init__(self, debug=False, auth_type='v3applicationcredential', node='金华'):
#         conf_node = getattr(settings, 'OPENSTACK_NODE', '金华')
#         if conf_node != node:
#             self.node = conf_node
#         else:
#             self.node = node
#         from conf.env_development import PRODUCT_CONFIG
#         self.auth_url = PRODUCT_CONFIG['auth_url']
#         self.region_name = PRODUCT_CONFIG['region_name']
#         self.project_name = PRODUCT_CONFIG['project_name']
#         self.username = PRODUCT_CONFIG['username']
#         self.password = PRODUCT_CONFIG['password']
#         self.user_domain_name = PRODUCT_CONFIG['user_domain_name']
#         self.project_domain_name = PRODUCT_CONFIG['project_domain_name']
#         self.auth_type = auth_type
#         self.openstack_application_credential_id = PRODUCT_CONFIG['openstack_application_credential_id']
#         self.openstack_application_credential_secret = PRODUCT_CONFIG['openstack_application_credential_secret']
#         self.admin_user_id = PRODUCT_CONFIG['admin_user_id']
#         self.admin_role_id = PRODUCT_CONFIG['admin_role_id']
#
#         if not debug:
#             enable_logging(debug=False)
#         self.conn = self.get_conn()
#
#     def get_conn(self):
#         if self.auth_type == 'v3applicationcredential':
#             conn = connect(
#                 region_name=self.region_name,
#                 auth_type=self.auth_type,
#                 auth_url=self.auth_url,
#                 application_credential_id=self.openstack_application_credential_id,
#                 application_credential_secret=self.openstack_application_credential_secret,
#                 verify=False  # 关闭SSL验证
#             )
#             return conn
#         # password 认证
#         auth = {
#             'auth_url': self.auth_url,
#             'project_name': self.project_name,
#             'username': self.username,
#             'password': self.password,
#             'user_domain_name': self.user_domain_name,
#             'project_domain_name': self.project_domain_name
#         }
#         conn = connect(
#             region_name=self.region_name,
#             auth=auth,
#             verify=False  # 关闭SSL验证
#         )
#         return conn
#
#     def get_project_scope_conn(self, project_id):
#         auth = {
#             'auth_url': self.auth_url,
#             'project_id': project_id,
#             'username': self.username,
#             'password': self.password,
#             'user_domain_name': self.user_domain_name,
#             'project_domain_name': self.project_domain_name
#         }
#         conn = connect(
#             region_name=self.region_name,
#             auth=auth,
#             verify=False  # 关闭SSL验证
#         )
#         return conn
#
#     def get_all_projects(self):
#         """
#         获取此账号下所有项目列表
#         :return: server list
#         """
#         projects = self.conn.identity.projects()
#         return list(projects)
#
#     def create_project(self, name, description):
#         """
#         创建项目
#         :param name: 项目名称
#         :param description: 项目描述
#         :return:
#         """
#         project = self.conn.identity.create_project(
#             name=name,
#             description=description
#         )
#         return project
#
#     def delete_project(self, project_id):
#         """
#         删除项目
#         :param project_id: 项目ID
#         :return: project
#         """
#         project = self.conn.identity.delete_project(project_id)
#         return project
#
#     def update_project(self, project_id, name=None, description=None):
#         """
#         更新项目
#         :param project_id: 项目ID
#         :param name: 新名称
#         :param description: 新描述
#         :return: project
#         """
#         project = self.conn.identity.update_project(project_id, name=name, description=description)
#         return project
#
#     def get_admin_user_id(self):
#         admin_user_id = self.admin_user_id
#         if not admin_user_id:
#             admin_user = self.conn.identity.find_user('admin', domain_id=self.user_domain_name)
#             admin_user_id = admin_user.id
#         return admin_user_id
#
#     def get_admin_role_id(self):
#         admin_role_id = self.admin_role_id
#         if not admin_role_id:
#             admin_role = self.conn.identity.find_role('admin')
#             admin_role_id = admin_role.id
#         return admin_role_id
#
#     def assign_project_to_admin(self, project_id, ):
#         admin_user_id = self.get_admin_user_id()
#         admin_role_id = self.get_admin_role_id()
#         res = self.conn.identity.assign_project_role_to_user(
#             project=project_id,
#             user=admin_user_id,
#             role=admin_role_id
#         )
#         return res
#
#     def update_project_compute_quotas_set(self, project_id, **compute_quotas):
#         """
#         更新项目计算配额
#         :param project_id: 项目ID
#         :param compute_quotas: 计算配额
#         :return:
#         """
#         res = self.conn.compute.update_quota_set(project_id, **compute_quotas)
#         return res
#
#     def update_project_network_quotas_set(self, project_id, **network_quotas):
#         """
#         更新项目网络配额(暂不使用)
#         :param project_id: 项目ID
#         :param network_quotas: 网络配额
#         :return:
#         """
#         res = self.conn.network.update_quota(project_id, **network_quotas)
#         return res
#
#     def update_project_volume_quotas_set(self, project_id, **volume_quotas):
#         """
#         更新项目存储配额(暂不使用)
#         :param project_id: 项目ID
#         :param volume_quotas: 存储配额
#         :return:
#         """
#         res = self.conn.volume.update_quota_set(project_id, **volume_quotas)
#         return res
#
#     def update_project_block_storage_quotas_set(self, project_id, **block_storage_quotas):
#         """
#         更新项目存储配额(暂不使用)
#         :param project_id: 项目ID
#         :param block_storage_quotas: 块存储配额
#         :return:
#         """
#         res = self.conn.block_storage.update_quota_set(project_id, **block_storage_quotas)
#         return res
#
#     def get_all_flavors(self):
#         """
#         获取所有可用Flavor
#         :return: flavor list
#         """
#         flavors = self.conn.compute.flavors()
#         return list(flavors)
#
#     def get_flavor_detail(self, flavor_id):
#         """
#         获取Flavor详情
#         :param flavor_id: Flavor ID
#         :return: flavor detail
#         :raise ValueError: If flavor ID is not provided.
#         """
#         if not flavor_id:
#             raise ValueError("Flavor ID is required.")
#         flavor = self.conn.compute.get_flavor(flavor_id)
#         return dict(flavor)
#
#     def get_all_images(self):
#         """
#         获取所有可用Image
#         :return: image list
#         """
#         images = self.conn.image.images()
#         return list(images)
#
#     def get_image_detail(self, image_id):
#         """
#         获取Image详情
#         :param image_id: Image ID
#         :return: image detail
#         :raise ValueError: If image ID is not provided.
#         """
#         if not image_id:
#             raise ValueError("Image ID is required.")
#         image = self.conn.image.get_image(image_id)
#         return dict(image)
#
#     def get_all_servers(self):
#         """
#         获取所有可用Server
#         :return: server list
#         """
#         servers = self.conn.compute.servers()
#         return list(servers)
#
#     def get_project_servers(self, all_projects=False, project_id=''):
#         """
#         根据项目id获取该项目下的所有主机
#         :param all_projects: 是否为所有项目,为真时，项目ID失效
#         :param project_id: 项目ID
#         :return: server list
#         """
#         if all_projects:
#             servers = self.conn.compute.servers(all_projects=all_projects)
#         else:
#             servers = self.conn.compute.servers(project_id=project_id)
#         return list(servers)
#
#     def get_server_detail(self, instance_id):
#         """
#
#         """
#         if not instance_id:
#             raise ValueError("Server ID is required.")
#         server = self.conn.compute.get_server(instance_id)
#         return server
#
#     def set_server_metadata(self, instance_id, metadata: dict):
#         """
#         设置主机元数据
#         :param instance_id: 主机ID
#         :param metadata: 元数据
#         :return:
#         """
#         if not instance_id:
#             raise ValueError("Server ID is required.")
#         response = self.conn.compute.set_server_metadata(instance_id, **metadata)
#         return response
#
#     def sync_image_flavor_to_metadata(self):
#         """
#         同步所有镜像、规格信息到云主机实例中
#         """
#         import json
#         # 获取所有主机
#         servers = self.get_project_servers(all_projects=True)
#         # 获取所有规格信息
#         flavor_dict = {}
#         flavors = self.get_all_flavors()
#         for flavor in flavors:
#             flavor_dict[flavor.name] = {
#                 'id': flavor.id,
#             }
#         for server in servers:
#             # 获取镜像详情
#             temp_server_image_id = getattr(server.image, 'id')
#             if not temp_server_image_id:
#                 temp_server_image_id = getattr(server.metadata, 'image_id', None)
#                 if not temp_server_image_id:
#                     image_detail = {'id': 'none_image_id'}
#                 else:
#                     image_detail = admin_client.get_image_detail(temp_server_image_id)
#             else:
#                 image_detail = admin_client.get_image_detail(temp_server_image_id)
#             # 获取镜像详情
#             temp_server_flavor_id = getattr(server.flavor, 'id')
#             if not temp_server_flavor_id:
#                 temp_server_flavor_id = getattr(server.metadata, 'flavor_id', None)
#                 if not temp_server_flavor_id:
#                     flavor_detail = {'id': 'none_flavor_id'}
#                 else:
#                     flavor_detail = admin_client.get_flavor_detail(flavor_dict.get(temp_server_flavor_id))
#             else:
#                 flavor_detail = admin_client.get_flavor_detail(flavor_dict.get(temp_server_flavor_id))
#             # 保存metadata
#             metadata = {
#                 'image_id': image_detail['id'],
#                 'flavor_id': flavor_detail['id'],
#             }
#             self.set_server_metadata(server.id, metadata)
#
#     def create_baremetal_server(
#         self,
#         project_id,
#         name,
#         flavor_id,
#         image_id,
#         network_id,
#         security_group_names=None,
#         description='',
#         zone="nova",
#         scheduler_hints=None,
#         **kwargs
#     ):
#         """
#         创建裸金属服务器
#         :param project_id: 项目ID
#         :param name: 名称
#         :param description: 描述
#         :param flavor_id: 规格ID
#         :param image_id: 镜像ID
#         :param network_id: 网络ID
#         :param security_group_names: 安全组名称列表 # ['default', 'testing-001']
#         :param zone: 逻辑域
#         :param scheduler_hints: 定制化脚本
#         :param kwargs: 其他参数
#         :return: 虚拟机
#         :raises: OpenStackAPIError, NovaException, NoValidHost, InvalidParameterValue, InvalidInput, Invalid, or
#             NoValidHost, InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue, or Invalid, or NoValidHost,
#             InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue,
#         """
#         if scheduler_hints is None:
#             scheduler_hints = {}
#         if security_group_names is None:
#             security_group_names = ["default"]
#         if not project_id:
#             raise ValueError("Project ID is required.")
#         new_conn = self.get_project_scope_conn(project_id=project_id)
#         create_params = kwargs
#         security_groups = [{"name": sg_name} for sg_name in security_group_names]
#         if not isinstance(scheduler_hints, dict):
#             raise TypeError("scheduler_hints must be Dict.")
#         create_params.update({
#             "name": name,
#             "availability_zone": zone,
#             "config_drive": True,
#             "description": description,
#             "user_data": "",
#             "disk_config": "AUTO",
#             "security_groups": security_groups,
#             "scheduler_hints": scheduler_hints,
#             "image_id": image_id,
#             "flavor_id": flavor_id,
#             "metadata": kwargs.get("metadata", {}),
#             "create_volume_default": True,
#             "hide_create_volume": False,
#             "networks": [{"uuid": network_id}],
#         })
#         # keypair validation
#         if kwargs.get("key_name"):
#             create_params.update({"key_name": kwargs["key_name"]})
#         vm = new_conn.compute.create_server(**create_params)
#         return vm
#
#     def create_multi_baremetal_server(
#             self,
#             project_id,
#             name,
#             flavor_id,
#             image_id,
#             network_id,
#             security_group_names=None,
#             description='',
#             zone="nova",
#             scheduler_hints=None,
#             instance_count=1,
#             **kwargs
#     ):
#         """
#         创建裸金属服务器
#         :param project_id: 项目ID
#         :param name: 名称
#         :param description: 描述
#         :param flavor_id: 规格ID
#         :param image_id: 镜像ID
#         :param network_id: 网络ID
#         :param security_group_names: 安全组名称列表 # ['default', 'testing-001']
#         :param zone: 逻辑域
#         :param scheduler_hints: 定制化脚本
#         :param instance_count: 实例数量
#         :param kwargs: 其他参数
#         :return: 虚拟机
#         :raises: OpenStackAPIError, NovaException, NoValidHost, InvalidParameterValue, InvalidInput, Invalid, or
#             NoValidHost, InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue, or Invalid, or NoValidHost,
#             InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue, or Invalid, or NoValidHost, InvalidInputValue,
#         """
#         result = []
#         for i in range(int(instance_count)):
#             vm = self.create_baremetal_server(
#                 project_id=project_id,
#                 name=f"{name}_{i + 1}",
#                 flavor_id=flavor_id,
#                 image_id=image_id,
#                 network_id=network_id,
#                 security_group_names=security_group_names,
#                 description=description,
#                 zone=zone,
#                 scheduler_hints=scheduler_hints,
#                 **kwargs
#             )
#             result.append(vm)
#         return result
#
#     def watch_server_create_status(self, instance_id):
#         """
#         持续查询创建任务状态
#         :param instance_id: 实例ID
#         :return: status
#         """
#         import time
#         while True:
#             try:
#                 server = self.conn.compute.get_server(instance_id)
#                 status = server.status
#                 logger.warning(f"当前状态: {status}")
#
#                 if status == 'ACTIVE':
#                     logger.warning("裸金属节点已成功激活")
#                     break
#                 elif status == 'ERROR':
#                     logger.warning("裸金属节点创建失败")
#                     # 获取详细的错误信息
#                     fault = getattr(server, 'fault', None)
#                     if fault:
#                         logger.warning(f"故障详情: {fault}")
#                     break
#             except Exception as e:
#                 logger.warning(f"查询状态失败: {e}")
#                 break
#
#             # 每隔几秒查询一次状态
#             time.sleep(10)
#
#     def check_server_task(self, server_id):
#         """
#         检查实例是否有其他任务正在执行。
#         :param server_id: 实例ID
#         :return: 布尔值，是否可以继续操作
#         :raises: ValueError
#         """
#         try:
#             # 获取实例信息
#             server = self.conn.compute.get_server(server_id)
#             if not server:
#                 raise ValueError(f"Server with ID {server_id} not found.")
#
#             # 检查实例状态
#             if server.status not in ['ACTIVE', 'SHUTOFF']:
#                 raise ValueError(
#                     f"Server {server_id} is in {server.status} state. Please wait until it is 运行中 or 已关机."
#                 )
#
#             # 检查任务状态（如果支持）
#             task_state = getattr(server, 'OS-EXT-STS:task_state', None)
#             if task_state:
#                 raise ValueError(
#                     f"Server {server_id} is currently performing a task: {task_state}. Please wait until the task is completed."
#                 )
#
#             logger.info(f"Server {server_id} is ready for operation.")
#             return True
#
#         except ValueError as ve:
#             logger.error(f"Check server task failed: {ve}")
#             raise
#         except Exception as e:
#             logger.error(f"Unexpected error while checking server task: {e}")
#             raise
#
#     def update_server_name_and_desc(self, server_id, name="", description=""):
#         """
#         更新主机名称和描述[Actions.update_server]
#         修改实例的元数据，如名称，不会影响实例的运行状态或性能。
#         :param server_id: 实例ID
#         :param name: 新名称
#         :param description: 新描述
#         :return: 布尔值、消息、主机信息
#         :raises: ValueError
#         """
#         code = False
#         msg = ''
#         data = ''
#         try:
#             self.check_server_task(server_id)
#             update_data = {}
#             if name:
#                 update_data['name'] = name
#             if description:
#                 update_data['description'] = description
#             response = self.conn.compute.update_server(server_id, **update_data)
#             if response:
#                 code = True
#                 msg = "主机信息更新成功"
#                 data = dict(response)
#                 return code, msg, data
#             else:
#                 raise ValueError(f"Failed to retrieve updated information for server {server_id}.")
#         except Exception as e:
#             error_info = f'更新主机失败，错误详情:{str(e)}'
#             logger.error(error_info)
#             code = False
#             msg = error_info
#             data = {}
#             return code, msg, data
#
#     def stop_server(self, server_id):
#         """
#         关闭主机/关机主机[Actions.stop_server]
#         关闭实例会停止其实例的运行，但保留其所有配置和数据。
#         :param server_id: 实例ID
#         """
#         code = False
#         msg = ''
#         data = {}
#         try:
#             self.check_server_task(server_id)
#             self.conn.compute.stop_server(server_id)
#             logger.info(f"关机 {server_id} 成功")
#             code = True
#             msg = "关闭主机成功"
#         except Exception as e:
#             logger.error(f"关机 {server_id} 失败：{str(e)}")
#             msg = f"关机失败,错误详情:{str(e)}"
#         return code, msg, data
#
#     def start_server(self, server_id):
#         """
#         开启主机/开机主机[Actions.start_server]
#         将实例从停止状态恢复到运行状态，开始消耗计算资源。
#         :param server_id: 实例ID
#         """
#         code = False
#         msg = ''
#         data = {}
#         try:
#             self.check_server_task(server_id)
#             self.conn.compute.start_server(server_id)
#             logger.info(f"开启 {server_id} 成功")
#             code = True
#             msg = "开启主机成功"
#         except Exception as e:
#             logger.error(f"开启 {server_id} 失败：{str(e)}")
#             msg = f"开启失败, 错误详情:{str(e)}"
#         return code, msg, data
#
#     def reboot_server(self, server_id, reboot_type="SOFT"):
#         """
#         重启主机[Actions.reboot_server]
#         重启实例并执行特定的操作。
#         1. [软重启], 操作系统有机会正常关闭服务并保存状态，适用于计划内的维护或更新。
#         2. [硬重启], 立即重启实例，不给操作系统机会来优雅地关闭服务。这可能用于恢复因软件故障而无法响应的实例。
#         :param server_id: 实例ID
#         :param reboot_type: 重启类型，SOFT(重启) or HARD(硬重启)
#         """
#         code = False
#         msg = ''
#         data = {}
#         try:
#             self.check_server_task(server_id)
#             self.conn.compute.reboot_server(server_id, reboot_type=reboot_type)
#             logger.info(f"重启 {server_id} 成功")
#             code = True
#             msg = "重启主机成功"
#         except Exception as e:
#             logger.error(f"重启 {server_id} 失败：{str(e)}")
#             msg = f"重启失败, 错误详情:{str(e)}"
#         return code, msg, data
#
#     def rebuild_server(self, server_id, image_id, name="", description=""):
#         """
#         重建主机[Actions.rebuild_server]
#         重建实例会用新的镜像覆盖现有实例的磁盘，同时保持实例的ID、IP地址和其他配置不变。
#         重建实例相当于格式化磁盘并安装一个新的操作系统镜像，常用于快速部署新环境或修复严重损坏的操作系统。
#         :param server_id: 实例ID
#         :param image_id: 新镜像ID
#         :param name: 新名称，为空时使用原始名称
#         :param description: 新描述，为空时使用原始描述
#         :return: 布尔值、消息、主机信息
#         :raises: ValueError
#         """
#         code = False
#         msg = ''
#         data = {}
#         try:
#             self.check_server_task(server_id)
#             if not image_id:
#                 raise ValueError("New image ID is required.")
#             update_data = {}
#             if name:
#                 update_data['name'] = name
#             if description:
#                 update_data['description'] = description
#             self.conn.compute.rebuild_server(server_id, image_id, **update_data)
#             logger.info(f"重建 {server_id} 成功")
#             code = True
#             msg = "重建主机成功"
#         except Exception as e:
#             logger.error(f"重建 {server_id} 失败：{str(e)}")
#             msg = f"重建失败, 错误详情:{str(e)}"
#         return code, msg, data
#
#     def get_all_baremetal_nodes(self, is_detail_list=False, fields=None):
#         """
#         获取所有裸金属节点
#         :param is_detail_list: 是否返回detail列表
#         :param fields: 查询返回的字段
#         :return: node list
#         """
#         nodes = self.conn.baremetal.nodes()
#         if is_detail_list:
#             nodes = [self.get_baremetal_node_detail(node_uuid=node.id, fields=fields) for node in nodes]
#         return list(nodes)
#
#     def get_baremetal_node_detail(self, node_uuid, fields=None):
#         """
#         获取裸金属节点详情
#         :param node_uuid: 节点UUID
#         :param fields: 查询返回的字段
#         :return: node detail
#         """
#         if fields is None:
#             node_detail = self.conn.baremetal.get_node(node_uuid)
#         else:
#             node_detail = self.conn.baremetal.get_node(node_uuid, fields=fields)
#         return node_detail
#
#     def get_project_networks(self, project_id='', is_all=False):
#         """
#         获取所有网络
#         :param project_id: 项目ID, 当is_all为假时，project_id 必填
#         :param is_all: 是否获取所有项目的网络
#         :return: network list
#         """
#         if not project_id and not is_all:
#             raise ValueError("Project ID is required.")
#         if is_all:
#             networks = self.conn.network.networks()
#         else:
#             networks = self.conn.network.list_networks(project_id=project_id)
#         return list(networks)
#
#     @staticmethod
#     def split_ip_pools(ip_pools):
#         """
#         分割获取ip池
#         :param ip_pools: 多段IP池字符串，用'\r\n'或者'\n'分隔,如下所示
#             "***********,*************\r\n192.168.10.5,**************"
#             "************,**************\n192.168.10.5,**************"
#             "************,**************"
#         :return: 单段ip pool列表
#         """
#         allocation_ip_pools = []
#         if '\r\n' in ip_pools:
#             temp_ip_pools = ip_pools.split('\r\n')
#         elif '\n' in ip_pools:
#             temp_ip_pools = ip_pools.split('\n')
#         else:
#             temp_ip_pools = [ip_pools]
#         for ip_pool in temp_ip_pools:
#             if ',' not in ip_pool:
#                 raise ValueError(f"Invalid IP pool format: {ip_pool}")
#             ip_pool_info = ip_pool.split(',')
#             start_ip, end_ip = ip_pool_info
#             allocation_ip_pools.append({
#                 'start': start_ip.strip(),
#                 'end': end_ip.strip(),
#             })
#         return allocation_ip_pools
#
#     def create_network(
#         self,
#         project_id,
#         network_name,
#         cidr,
#         gateway_ip,
#         allocation_pools,
#         sub_name='',
#         ip_version=4,
#         enable_dhcp="on",
#         dns_nameservers="***************",
#         admin_state="on",
#         with_subnet="on",
#         mtu=1450
#     ):
#         """
#         普通用户-创建网络
#         :param project_id: 项目 ID
#         :param network_name: 网络名称
#         :param cidr: CIDR 网段
#         :param gateway_ip: 网关 IP
#         :param allocation_pools: 分配地址池
#         :param sub_name: 子网名称
#         :param ip_version: IP 版本
#         :param enable_dhcp: DHCP 开关
#         :param dns_nameservers: DNS 服务器
#         :param admin_state: 状态
#         :param with_subnet: 创建子网
#         :param mtu: MTU 默认 1450
#         :return: network
#         """
#         msg = ''
#         if not project_id:
#             raise ValueError("Project ID is required.")
#         new_conn = self.get_project_scope_conn(project_id=project_id)
#         if not sub_name:
#             sub_name = cidr.split('/')[0].replace('.', '-')
#
#         try:
#             # 创建网络
#             network = new_conn.network.create_network(
#                 name=network_name,
#                 project_id=project_id,
#                 admin_state_up=(admin_state == "on"),
#                 mtu=mtu
#             )
#
#             # 格式化IP池为 SDK 可读取的数据格式
#             allocation_pools = self.split_ip_pools(allocation_pools)
#
#             if with_subnet == "on":
#                 # 创建子网
#                 subnet = new_conn.network.create_subnet(
#                     name=sub_name or network_name + '_subnet',
#                     network_id=network.id,
#                     ip_version=ip_version,
#                     cidr=cidr,
#                     gateway_ip=gateway_ip,
#                     enable_dhcp=(enable_dhcp == "on"),
#                     allocation_pools=allocation_pools,
#                     dns_nameservers=[dns_nameservers]
#                 )
#                 return dict(network), dict(subnet), msg
#             else:
#                 return dict(network), {}, msg
#
#         except (ResourceFailure, Exception) as e:
#             # 发生错误时删除已创建的网络
#             try:
#                 new_conn.network.delete_network(network.id)
#                 msg = f'【创建失败】原因：{str(e)}'
#                 logger.warning(f"Network [<NAME:{network_name}><ID:{network.id}>] deleted due to error: {e}")
#                 return {}, {}, msg
#             except Exception as delete_error:
#                 logger.error(f"Failed to delete network {network.id}: {delete_error}")
#                 raise e  # 重新抛出原始异常
#
#     def create_signal_vlan_network(
#         self,
#         project_id,
#         vlan_id,
#         network_name,
#         admin_state='on',
#         physical_network='vlan',
#         mtu=1450,
#
#     ):
#         """
#         管理员-创建VLAN 网络，暂由于不知什么原因，需要多次创建或等待网络创建任务成功。
#         :param project_id: 项目 ID
#         :param vlan_id: VLAN_ID
#         :param network_name: 网络名称
#         :param physical_network: 物理网络此系统内定义为 vlan
#         :param admin_state: 状态
#         :param mtu: MTU 默认 1450
#         :return: network
#         """
#         msg = '创建失败'
#         if not project_id:
#             raise ValueError("Project ID is required.")
#
#         # 创建网络
#         provider_params = {
#             'provider:network_type': 'vlan',
#             'provider:physical_network': physical_network,
#             'provider:segmentation_id': vlan_id,
#         }
#         search_physical_vlan_network_params = {
#             'project_id': project_id,
#             'name': network_name,
#             'mtu': mtu
#         }
#         network = {
#             'id': '创建失败'
#         }
#         search_physical_vlan_network_params.update(provider_params)
#         try:
#             # 查询待创建的网络是否已存在
#             network = self.search_physical_vlan_network_only_one(**search_physical_vlan_network_params)
#             if network:
#                 raise Exception('网络已存在')
#             print('provider_params:', provider_params)
#             network = self.conn.network.create_network(
#                 name=network_name,
#                 project_id=project_id,
#                 admin_state_up=(admin_state == "on"),
#                 mtu=mtu,
#                 **provider_params
#             )
#             print('****************')
#             print(network)
#             return dict(network), True
#         except HttpException as e:
#             logger.warning(f'Timeout while creating network')
#
#             if e.status_code in [409, 504]:
#                 time.sleep(30)
#                 return {'is_waiting': True}, True
#             else:
#                 raise Exception(f'Network [<NAME:{network_name}><ID:{network.get("id")}><Detail:{str(e)}>]')
#
#     def search_physical_vlan_network_only_one(self, **kwargs):
#         """
#         获取仅一个网络详情
#         :param kwargs:id: 网络 ID
#         :param kwargs:project_id: 项目 ID
#         :param kwargs:name: 网络名称
#         :param kwargs:provider:network_type: 网络类型
#         :param kwargs:provider:segmentation_id: 段ID
#         :param kwargs:provider:physical_network: 物理网络类型
#         :return: network
#         """
#         networks = self.conn.network.get_network(**kwargs)
#         if len(networks) == 1:
#             return dict(networks[0])
#         elif len(networks) > 1:
#             msg = f'Network [<NAME:{kwargs.get("name")}><ID:{kwargs.get("id")}>] Had Too Many Network Information'
#             logger.error(msg)
#
#             raise ValueError(msg)
#         else:
#             msg = f'Network [<NAME:{kwargs.get("name")}><ID:{kwargs.get("id")}>] Not Found'
#             logger.error(msg)
#             return []
#
#     def search_network_list(self, **kwargs):
#         """
#         获取网络详情
#         :param kwargs:id: 网络 ID
#         :param kwargs:project_id: 项目 ID
#         :param kwargs:name: 网络名称
#         :param kwargs:provider:network_type: 网络类型
#         :param kwargs:provider:segmentation_id: 段ID
#         :param kwargs:provider:physical_network: 物理网络类型
#         :return: network
#         """
#         networks = self.conn.network.get_network(**kwargs)
#         return networks
#
#     def create_subnet(
#             self,
#             network_id,
#             subnetwork_name,
#             cidr,
#             gateway_ip,
#             ip_version=4,
#             enable_dhcp="on",
#             allocation_pools=None,
#             dns_nameservers="***************"
#     ):
#         # 创建子网
#         subnet = self.conn.network.create_subnet(
#             name=subnetwork_name,
#             network_id=network_id,
#             ip_version=ip_version,
#             cidr=cidr,
#             gateway_ip=gateway_ip,
#             enable_dhcp=(enable_dhcp == "on"),
#             allocation_pools=allocation_pools,
#             dns_nameservers=[dns_nameservers]
#         )
#         return subnet
#
#     def create_vlan_network(
#         self,
#         project_id,
#         vlan_id,
#         network_name,
#         cidr,
#         gateway_ip,
#         allocation_pools,
#         physical_network='vlan',
#         sub_name='',
#         ip_version=4,
#         enable_dhcp="on",
#         dns_nameservers="***************",
#         admin_state="on",
#         with_subnet="on",
#         mtu=1450
#     ):
#         """
#         管理员-创建网络
#         :param project_id: 项目 ID
#         :param vlan_id: VLAN_ID
#         :param network_name: 网络名称
#         :param cidr: CIDR 网段
#         :param gateway_ip: 网关 IP
#         :param allocation_pools: 分配地址池
#         :param physical_network: 物理网络此系统内定义为 vlan
#         :param sub_name: 子网名称
#         :param ip_version: IP 版本
#         :param enable_dhcp: DHCP 开关
#         :param dns_nameservers: DNS 服务器
#         :param admin_state: 状态
#         :param with_subnet: 创建子网
#         :param mtu: MTU 默认 1450
#         :return: network
#         """
#         msg = ''
#         if not project_id:
#             raise ValueError("Project ID is required.")
#         if not sub_name:
#             sub_name = cidr.split('/')[0].replace('.', '-')
#
#         try:
#             # 创建网络
#             provider_params = {
#                 'provider:network_type': 'vlan',
#                 'provider:physical_network': physical_network,
#                 'provider:segmentation_id': vlan_id,
#             }
#             print('provider_params:', provider_params)
#             network = self.conn.network.create_network(
#                 name=network_name,
#                 project_id=project_id,
#                 admin_state_up=(admin_state == "on"),
#                 mtu=mtu,
#                 **provider_params
#             )
#             print('****************')
#             print(network)
#             # 格式化IP池为 SDK 可读取的数据格式
#             allocation_pools = self.split_ip_pools(allocation_pools)
#
#             if with_subnet == "on":
#                 # 创建子网
#                 subnet = self.conn.network.create_subnet(
#                     name=sub_name or network_name + '_subnet',
#                     network_id=network.id,
#                     ip_version=ip_version,
#                     cidr=cidr,
#                     gateway_ip=gateway_ip,
#                     enable_dhcp=(enable_dhcp == "on"),
#                     allocation_pools=allocation_pools,
#                     dns_nameservers=[dns_nameservers]
#                 )
#                 return dict(network), dict(subnet), msg
#             else:
#                 return dict(network), {}, msg
#
#         except (ResourceFailure, Exception) as e:
#             # 发生错误时删除已创建的网络
#             try:
#                 self.conn.network.delete_network(network.id)
#                 msg = f'【创建失败】原因：{str(e)}'
#                 logger.warning(f"Network [<NAME:{network_name}><ID:{network.id}>] deleted due to error: {e}")
#                 return {}, {}, msg
#             except Exception as delete_error:
#                 logger.error(f"Failed to delete network {network.id}: {delete_error}")
#                 raise e  # 重新抛出原始异常
#
#     def get_project_security_groups(self, project_id='', is_all=False):
#         """
#         获取项目下的安全组
#         :param project_id: 项目ID
#         :param is_all: 是否获取所有安全组
#         :return: security_groups
#         """
#         if not project_id and not is_all:
#             raise ValueError("Project ID is required.")
#         if is_all:
#             # 获取所有安全组
#             security_groups = self.conn.network.security_groups()
#         else:
#             # 获取指定项目的安全组
#             security_groups = self.conn.network.security_groups(project_id=project_id)
#         return list(security_groups)
#
#     def get_ironic_hypervisor_nodes(self):
#         """
#         获取Ironic hypervisor节点
#         :return: hypervisor nodes
#         """
#         hypervisor_nodes = self.conn.compute.hypervisors()
#         return list(hypervisor_nodes)
#
#     def get_ports(self, project_id=None, server_id=None, **kwargs):
#         """
#         查询指定项目的端口，并且关联到指定服务器
#         :param project_id: 项目 ID
#         :param server_id: 服务器 ID
#         :return: ports
#         """
#         ports = []
#         search_params = {}
#         if not project_id and not server_id:
#             raise ValueError("Project ID and Server ID are required.")
#         if project_id:
#             search_params['project_id'] = project_id
#         if server_id:
#             search_params['device_id'] = server_id
#         # 获取指定项目的端口，并且关联到指定服务器
#         search_params.update(kwargs)
#         try:
#             ports = list(self.conn.network.ports(**search_params))
#         except Exception as e:
#             logger.error(f"OpenStack API GetPortsError: {str(e)}")
#             raise e
#         return ports
#
#
# if __name__ == '__main__':
#     import os
#     import django
#
#     os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
#     django.setup()
#     admin_client = OpenstackAdminClient(debug=True)
#
#     temp_project_id = '8f788892a6b444e69c618031b66a1240'
#
#     # 获取项目下的端口信息
#     temp_server_id = 'c7eb7e76-1009-4785-9e14-a2377c5b9b5b'
#     res = admin_client.get_ports(temp_project_id, temp_server_id)
#     print(res)
#     for i in res:
#         print(f'ID: {i.id}, Name: {i.name}')
#
#
#     # # 关机
#     # # 主机名：测试主机操作_1
#     # t_server_id = 'b2af22a7-ddb9-4c0e-93ac-2afa7d1614e6'
#     # res = admin_client.stop_server(t_server_id)
#     # print(res)
#
#     # # 开机
#     # # 主机名：测试主机操作_1
#     # t_server_id = 'b2af22a7-ddb9-4c0e-93ac-2afa7d1614e6'
#     # res = admin_client.start_server(t_server_id)
#     # print(res)
#
#     # # 软重启
#     # # 主机名：测试主机操作_1
#     # t_server_id = 'b2af22a7-ddb9-4c0e-93ac-2afa7d1614e6'
#     # res = admin_client.reboot_server(t_server_id, reboot_type='SOFT')
#     # print(res)
#
#     # # 硬重启
#     # # 主机名：测试主机操作_1
#     # t_server_id = 'b2af22a7-ddb9-4c0e-93ac-2afa7d1614e6'
#     # res = admin_client.reboot_server(t_server_id, reboot_type='HARD')
#     # print(res)
#
#     # # 更新描述或名称
#     # # 主机名：测试主机操作_1
#     # t_server_id = 'b2af22a7-ddb9-4c0e-93ac-2afa7d1614e6'
#     # res = admin_client.update_server_name_and_desc(t_server_id, name='更新之后的测试啦111', description='haha')
#     # print(res)
#
#     # # 重置实例
#     # # 主机名：测试主机操作_1
#     # t_server_id = 'b2af22a7-ddb9-4c0e-93ac-2afa7d1614e6'
#     # res = admin_client.rebuild_server(
#     #     t_server_id,
#     #     image_id='a2483e38-3f70-47ec-82cd-53c7afe9091c',
#     #     name='更新之后的测试啦222',
#     #     # description='hasdaha'
#     # )
#     # print(res)
#
#     # 创建带有物理网络的网络
#     # temp_cidr = '***********/24'
#     # temp_gateway = '*************'
#     # temp_allocation_ip = '***********,*************'
#
#     # network, subnet, msg = admin_client.create_vlan_network(
#     #     project_id=temp_project_id,
#     #     network_name='Vlanif2002',
#     #     vlan_id=2002,
#     #     cidr=temp_cidr,
#     #     gateway_ip=temp_gateway,
#     #     allocation_pools=temp_allocation_ip,
#     # )
#     # print(network)
#     # print(subnet)
#     # print(msg)
#
#     # # 获取裸金属所有节点
#     # res = admin_client.get_all_projects()
#     # logger.warning(res)
#
#     # # 创建裸金属服务器时【指定裸金属节点】
#     # test_name = 'openstack_api_created_testing'
#     # test_description = 'API测试创建裸金属<ironic_node_uuid>，确认过'
#     # test_flavor_id = "c6d9b737-6f78-41d7-8786-ee0949da40c1"
#     # test_image_id = "a2483e38-3f70-47ec-82cd-53c7afe9091c"
#     # test_network_id = '6e2d51af-fa4b-4742-b59e-665b622c98f8'
#     # test_security_group_names = [
#     #     'default'
#     # ]
#     # # 指定的node节点
#     # baremetal_node_uuid = '8134af37-2050-4dcf-9fc8-5ff886cd33e2'
#     # baremetal_node_name = '**********'
#     # # properties = {'capabilities:node': baremetal_node_uuid}
#     # res = admin_client.create_multi_baremetal_server(
#     #     project_id=test_project_id,
#     #     name=test_name,
#     #     description=test_description,
#     #     flavor_id=test_flavor_id,
#     #     image_id=test_image_id,
#     #     network_id=test_network_id,
#     #     security_group_names=test_security_group_names,
#     #     instance_count=2,
#     # )
#     # logger.warning(res)
#     # for i in res:
#     #     print(i)
#     #     print(i.id)
#
#     # # 监听主机状态
#     # admin_client.watch_server_create_status(instance_id=res.id)
#
#
#
#
#
