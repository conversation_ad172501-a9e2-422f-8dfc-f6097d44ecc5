from application.logger import logger
from tenant.models import TenantOpenstackSetting

class OPNodeManage(object):
    """
    OpenStack节点管理
    """
    def __init__(self):
        self.all_nodes = self.get_all_nodes() or []

    def get_openstack_settings(self, node):
        """Return the openstack settings"""
        node = self.verify_node(node=node)
        node_settings = self.verify_node_settings(node=node)
        if not node_settings:
            raise Exception(f'【节点管理<{node}>配置验证】错误!!!')
        return node_settings

    def verify_node(self, node):
        """Verify the node"""
        node = node.strip()
        if node not in self.all_nodes:
            logger.error(f'【节点管理】未找到节点:{node}, <all_nodes: {self.all_nodes}>')
            return False
        return node

    @staticmethod
    def get_all_nodes():
        all_nodes = TenantOpenstackSetting.get_op_distinct_nodes()
        return all_nodes

    def verify_node_settings(self, node):
        """Verify the node settings"""
        node_settings = TenantOpenstackSetting.get_op_settings(node=node)
        if not node_settings:
            logger.error(f'【节点管理】未找到节点:{node}, <all_nodes: {self.all_nodes}>,配置信息为空!!!')
            return False
        # 验证OpenStack配置信息
        if not self.check_op_node_settings(node_settings):
            return False
        # 验证防火墙必须配置信息
        if not self.check_op_hisect_engine(node_settings):
            return False
        return node_settings

    @staticmethod
    def check_op_node_settings(config_mapping):
        """Check the node settings for openstack 必须配置信息"""
        # 必须配置信息
        must_config_keys = [
            'USERNAME',
            'PASSWORD',
            'PROJECT_NAME',
            'PROJECT_DOMAIN_NAME',
            'USER_DOMAIN_NAME',
            'AUTH_URL',
            'REGION_NAME',
            'ADMIN_USER_ID',
            'ADMIN_ROLE_ID',
            'APPLICATION_CREDENTIAL_ID',
            'APPLICATION_CREDENTIAL_SECRET',
        ]
        if not config_mapping:
            return False

        # 检查所有必填 key 是否存在，并且对应的值非空
        for key in must_config_keys:
            if key not in config_mapping or not config_mapping[key]:
                logger.error(f"OpenStack 节点配置缺失或为空: {key}")
                return False
        return True

    @staticmethod
    def check_op_hisect_engine(config_mapping):
        """Check the node settings for 防火墙所需的必须配置"""
        # 必须配置信息
        must_config_keys = [
            'HISEC_HOST',
            'HISEC_USERNAME',
            'HISEC_PASSWORD',
            'HISEC_PORT',
            'HISEC_PROTOCOL',
            ]
        if not config_mapping:
            return False
        # 检查所有必填 key 是否存在，并且对应的值非空
        for key in must_config_keys:
            if key not in config_mapping or not config_mapping[key]:
                logger.error(f"防火墙节点配置缺失或为空: {key}")
                return False
        return True


if __name__ == '__main__':
    import os
    import django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
    django.setup()
    # from tenant.models import TenantOpenstackSetting
    op_manage = OPNodeManage()
    # TenantOpenstackSetting.refresh_op_settings('金华-Test')
    print(op_manage.all_nodes)
    # print(op_manage.get_openstack_settings('金华-1'))


