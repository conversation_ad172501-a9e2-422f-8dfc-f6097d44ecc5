from tenant.models import TenantAccount, TenantOpenstackProject


def get_tenant_account_projects(account_id, is_project_ids=True):
    # Implement logic to fetch tenant account's associated projects
    # This is a placeholder function and should be replaced with actual implementation

    projects = TenantOpenstackProject.objects.filter(
        account_id=account_id,
        is_deleted=False,
    ).all()

    if is_project_ids:
        return [project.project_id for project in projects]

    return projects


def get_project_account(project_id):
    # Implement logic to fetch tenant account's associated projects
    # This is a placeholder function and should be replaced with actual implementation

    project = TenantOpenstackProject.objects.filter(
        project_id=project_id,
        is_deleted=False,
    ).first()

    if not project:
        return None
    return project.account
