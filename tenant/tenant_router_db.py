class TenantRouter:
    """
    A router to control all database operations on models in the
    adminapp and customerapp applications.
    """
    def db_for_read(self, model, **hints):
        """
        Reads go to a specific database based on the app label.
        """
        if 'system' in model._meta.app_label:
            return 'default'
        if 'tenant' in model._meta.app_label:
            return 'tenant'
        return None

    def db_for_write(self, model, **hints):
        """
        Writes go to a specific database based on the app label.
        """
        if 'system' in model._meta.app_label:
            return 'default'
        if 'tenant' in model._meta.app_label:
            return 'tenant'
        return None

    def allow_relation(self, obj1, obj2, **hints):
        """
        Relations between objects are allowed if both objects are
        in the same database.
        """
        # db1 = self.db_for_read(obj1.__class__)
        # db2 = self.db_for_read(obj2.__class__)
        # print('$$$$$$$$$$$$$$$$$$$$$$$$$$$$', obj1.__class__)
        # print('$$$$$$$$$$$$$$$$$$$$$$$$$$$$', obj2.__class__)
        # if db1 and db2:
        #     return db1 == db2
        # return None
        return True

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        All migrations for adminapp models end up in the default database,
        and all migrations for tenant and tenantportal models end up in the secondary database.
        """
        if 'tenant' in app_label:
            return db == 'tenant'
        else:
            return None
