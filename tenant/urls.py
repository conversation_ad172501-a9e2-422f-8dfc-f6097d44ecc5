"""
tenant 模块路由
"""
from django.urls import path
from rest_framework import routers

from tenant.views.tenant_openstack_project import TenantOpenstackProjectViewSet
from tenant.views.tenant_account import TenantAccountViewSet
from tenant.views.tenant_openstack_setting import TenantOpenstackSettingViewSet
from tenant.views.tenant_openstack_vlan import TenantOpenstackVlanViewSet
from tenant.views.tenant_openstack_flavor import TenantOpenstackFlavorViewSet
from tenant.views.tenant_openstack_server import TenantOpenstackServerViewSet
from tenant.views.tenant_openstack_image import TenantOpenstackImageViewSet
from tenant.views.tenant_openstack_network import TenantOpenstackNetworkViewSet
from tenant.views.tenant_openstack_ironic_hypervisor import TenantOpenstackIronicHypervisorViewSet
from tenant.views.tenant_openstack_ironic_register import TenantOpenstackIronicHypervisorRegisterViewSet
from tenant.views.tenant_openstack_security_group import TenantOpenstackSecurityGroupViewSet
from tenant.views.tenant_hisec_engine_nat_server_policy import TenantHisecEngineNatServerPolicyViewSet
from tenant.views.tenant_hisec_engine_public_ip import TenantHisecEnginePublicIPViewSet

# 特殊自定义 API
from tenant.serializers.tenant_openstack_server import CreateBaremetalServerView


resource_url = routers.SimpleRouter()
resource_url.register(r'tenant-project', TenantOpenstackProjectViewSet)
resource_url.register(r'tenant-account', TenantAccountViewSet)
resource_url.register(r'tenant-op-setting', TenantOpenstackSettingViewSet)
resource_url.register(r'tenant-op-vlan', TenantOpenstackVlanViewSet)
resource_url.register(r'tenant-op-flavor', TenantOpenstackFlavorViewSet)
resource_url.register(r'tenant-op-server', TenantOpenstackServerViewSet)
resource_url.register(r'tenant-op-image', TenantOpenstackImageViewSet)
resource_url.register(r'tenant-op-network', TenantOpenstackNetworkViewSet)
resource_url.register(r'tenant-op-ironic-hypervisor', TenantOpenstackIronicHypervisorViewSet)
resource_url.register(r'tenant-op-ironic-hypervisor-register', TenantOpenstackIronicHypervisorRegisterViewSet)
resource_url.register(r'tenant-op-security-group', TenantOpenstackSecurityGroupViewSet)
resource_url.register(r'tenant-hisec-natserver-policy', TenantHisecEngineNatServerPolicyViewSet)
resource_url.register(r'tenant-hisec-public-ip', TenantHisecEnginePublicIPViewSet)


app_name = 'tenant'


urlpatterns = [
    path('create_baremetal_server/', CreateBaremetalServerView.as_view(), name='create_baremetal_server'),
    # path('resource/machine_room/', MachineRoomViewSet.as_view('get', 'list')),
    # path('resource/machine_room/<id>/', MachineRoomViewSet.as_view()),
]
urlpatterns += resource_url.urls
