from rest_framework import serializers


from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.serializers import CustomModelSerializer

from dvadmin.system.models import Post


class PostSerializer(CustomModelSerializer):
    """
    日志-序列化器
    """

    class Meta:
        model = Post
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


class PostViewSet(CustomModelViewSet):
    """
    客户信息接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Post.objects.order_by('-create_datetime')
    serializer_class = PostSerializer
    # permission_classes = []
