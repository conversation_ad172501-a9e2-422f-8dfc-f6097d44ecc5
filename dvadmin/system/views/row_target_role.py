from importlib import import_module


from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.serializers import CustomModelSerializer
from dvadmin.system.models import RowTargetRole, RowOriginRole
from dvadmin.utils.json_response import SuccessResponse, DetailResponse, ErrorResponse

from rest_framework.decorators import action
from rest_framework import serializers
from rest_framework.permissions import IsAuthenticated

from dvadmin.utils.resource_permissions.interval_resource_values_config import INTERVAL_RESOURCE_VALUES_CONFIG
from dvadmin.system.views.menu_field import MenuFieldViewSet


class RowTargetRoleSerializer(CustomModelSerializer):
    """
    行用户权限查询-序列化器
    """
    class Meta:
        model = RowTargetRole
        fields = "__all__"
        read_only_fields = ["id"]


class RowOriginRoleSerializer(CustomModelSerializer):
    """
    行用户权限查询-序列化器
    """
    class Meta:
        model = RowOriginRole
        fields = "__all__"
        read_only_fields = ["id", "is_deleted",]


class RowTargetRoleCreateUpdateSerializer(CustomModelSerializer):
    """
    行用户权限更新-序列化器
    """

    class Meta:
        model = RowTargetRole
        fields = '__all__'


class RowTargetRoleGetRowRoleIdsSerializer(CustomModelSerializer):
    """
    获取行用户权限列表-序列化器
    """
    row_id = serializers.CharField(max_length=63, required=True)

    def get_user_ids(self, params):
        queryset = RowTargetRole.objects.filter(
            row_id=self.request.query_params.get('row_id__icontains', None)
        )
        return queryset

    class Meta:
        model = RowTargetRole
        fields = ["row_id", "user_ids"]


class RowTargetRoleViewSet(CustomModelViewSet):
    """
    行数据用户权限管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = RowTargetRole.objects.all()
    serializer_class = RowTargetRoleSerializer
    create_serializer_class = RowTargetRoleCreateUpdateSerializer
    get_row_user_ids_class = RowTargetRoleGetRowRoleIdsSerializer
    extra_filter_class = []

    @action(methods=["GET"], detail=False, permission_classes=[IsAuthenticated])
    def get_row_role_ids(self, request, *args, **kwargs):
        """获取资源已授予的用户列表"""
        queryset = RowTargetRole.objects.filter(
            row_id=request.query_params.get('row_id__icontains', None)
        ).values('role_id')
        RowTargetRoleGetRowRoleIdsSerializer()
        res = set([i['role_id'] for i in queryset])
        return DetailResponse(data=res, msg="获取成功")

    @action(methods=["GET"], detail=False, permission_classes=[IsAuthenticated])
    def get_role_row_ids(self, request, *args, **kwargs):
        """获取用户已被授予的资源列表"""
        queryset = RowTargetRole.objects.filter(
            role=request.query_params.get('role', None)
        ).values('row_id')
        res = set([i['row_id'] for i in queryset])
        return DetailResponse(data=res, msg="获取成功")

    @action(methods=["GET"], detail=False, permission_classes=[IsAuthenticated])
    def get_rows_info(self, request, *args, **kwargs):
        """获取用户已被授予的资源列表"""
        params = request.query_params
        model_name = params.get('row_origin', 'Unknown')
        if not model_name:
            return ErrorResponse(code=4000, msg='数据源选择有误!')
        module_name, package_name = model_name.rsplit('.', 1)
        search = params.get('search')
        resource_model = import_module(f'{module_name}.models'.format(module_name=module_name))
        resource_data = getattr(resource_model, package_name)
        origin_model = resource_data.objects.values()
        return DetailResponse(data=origin_model, msg="获取成功")

    @action(methods=["GET"], detail=False, permission_classes = [IsAuthenticated])
    def get_role_row_statistics(self, request, *args, **kwargs):
        """获取角色已授权的资源列表统计"""
        data = {}
        try:
            app_name = request.query_params.getlist('app_name')
        except Exception:
            app_name = request.query_params.get('app_name')
        query_params = request.query_params.dict()
        query_params['app_name'] = app_name
        role_id = request.query_params.get('role_id')
        if not role_id:
            return ErrorResponse(msg='角色设置有误')

        search = query_params.get('search')
        resource_interval_enabled = query_params.get('resource_interval_enabled', False)
        models_query = {
            'app_name': app_name,
            'search': search,
            'resource_interval_enabled': resource_interval_enabled,
        }
        enabled_models = MenuFieldViewSet.get_custom_enabled_models(models_query)
        for model_config in enabled_models['models']:
            row_origin = '{app}.{key}'.format(**model_config)
            had_role_perm_count = RowTargetRole.objects.filter(
                role_id=role_id,
                row_origin=row_origin
            ).count()
            module_name = model_config['app']
            package_name = model_config['key']
            resource_model = import_module(f'{module_name}.models'.format(module_name=module_name))
            resource_data = getattr(resource_model, package_name)
            all_role_perm_count = resource_data.objects.count()
            data.update({
                model_config['key']: {
                    'had_role_perm_count': had_role_perm_count,
                    'all_role_perm_count': all_role_perm_count
                }

            })
        return SuccessResponse(data=data)

    @action(methods=["POST"], detail=False, permission_classes=[IsAuthenticated])
    def add_role_row_ids(self, request, *args, **kwargs):
        """获取用户已被授予的资源列表"""
        body = request.data
        row_ids = body.get('row_ids', [])
        row_origin = body.get('row_origin', '')
        role_id = body.get('role_id', -1)
        if row_origin not in INTERVAL_RESOURCE_VALUES_CONFIG.keys():
            return ErrorResponse(code=4000, msg='数据源未开启数据认证！')

        # 记录某类资源类型关联到角色中
        if row_ids:
            add_params = {
                'row_origin': row_origin,
                'role': role_id,
                'is_open_row_permission': True
            }
            obj = RowOriginRole.objects.filter(
                **add_params
            ).values('id')
            if not obj:
                serializer = RowOriginRoleSerializer(data=add_params, request=request)
                if serializer.is_valid():
                    serializer.save()
        for row_id in row_ids:
            add_data = {
                'row_id': row_id,
                'row_origin': body.get('row_origin'),
                'role': body.get('role_id')
            }

            obj = RowTargetRole.objects.filter(
                row_id=row_id,
                row_origin=row_origin,
                role_id=role_id
            ).values('id')
            if not obj:
                serializer = RowTargetRoleCreateUpdateSerializer(data=add_data, request=request)
                if serializer.is_valid():
                    serializer.save()
        return DetailResponse(data={}, msg="授权成功")

    @action(methods=["POST"], detail=False, permission_classes=[IsAuthenticated])
    def revoke_role_row_ids(self, request, *args, **kwargs):
        """取消用户授予的资源列表"""
        body = request.data
        row_ids = body.get('row_ids', [])
        row_origin = body.get('row_origin', '')
        role_id = body.get('role_id', -1)
        if row_origin not in INTERVAL_RESOURCE_VALUES_CONFIG.keys():
            return ErrorResponse(code=4000, msg='数据源未开启数据认证！')
        for row_id in row_ids:
            RowTargetRole.objects.filter(
                row_id=row_id,
                row_origin=row_origin,
                role_id=role_id
            ).delete()
        # 若无资源，删除 某类资源类型关联到角色中
        obj = RowOriginRole.objects.filter(
            row_origin=row_origin,
            role_id=role_id,
            is_external=False
        )
        if obj.count() > 0:
            obj.delete()
        return DetailResponse(data={}, msg="取消授权成功")

