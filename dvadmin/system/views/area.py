# -*- coding: utf-8 -*-
from django.db.models import Q
from rest_framework import serializers
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

from dvadmin.system.models import Area
from dvadmin.utils.json_response import DetailResponse, ErrorResponse, SuccessResponse
from dvadmin.utils.serializers import CustomModelSerializer
from dvadmin.utils.viewset import CustomModelViewSet


class AreaSerializer(CustomModelSerializer):
    """
    地区-序列化器
    """
    pcode_count = serializers.SerializerMethodField(read_only=True)
    hasChild = serializers.SerializerMethodField()
    def get_pcode_count(self, instance: Area):
        return Area.objects.filter(pcode=instance).count()
    def get_hasChild(self, instance):
        hasChild = Area.objects.filter(pcode=instance.code)
        if hasChild:
            return True
        return False
    class Meta:
        model = Area
        fields = "__all__"
        read_only_fields = ["id"]


class AreaCreateUpdateSerializer(CustomModelSerializer):
    """
    地区管理 创建/更新时的列化器
    """

    class Meta:
        model = Area
        fields = '__all__'


class AreaViewSet(CustomModelViewSet):
    """
    地区管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Area.objects.all()
    serializer_class = AreaSerializer
    extra_filter_class = []

    def get_queryset(self):
        self.request.query_params._mutable = True
        params = self.request.query_params
        page = params.get('page', None)
        limit = params.get('limit', None)
        equal_fields = ['level', 'page', 'limit', 'pcode', 'code', "id"]
        contain_fields = ['name', 'pinyin']
        # 动态添加查询条件， 并且取消页面参数
        query = Q()
        for k, v in params.items():
            if k == 'page':
                continue
            if k == 'limit':
                continue
            if k in equal_fields:
                if v:
                    query &= Q(**{k: v})
            if k in contain_fields:
                if v:
                    query &= Q(**{f'{k}__icontains': v})
        if page:
            del params['page']
        if limit:
            del params['limit']
        if params and query:
            queryset = self.queryset.filter(query, enable=True)
        else:
            queryset = self.queryset.filter(enable=True)
        return queryset

    @action(methods=["GET"], detail=False, permission_classes=[IsAuthenticated])
    def search_level_all_areas(self, request, *args, **kwargs):
        """
        获取用户已被授予的资源列表
        params pcode string:
        params level integer:
        """
        self.request.query_params._mutable = True
        params = request.query_params

        # 动态添加查询条件， 并且取消页面参数
        query = Q()
        for k, v in params.items():
            if k == 'page':
                continue
            if k == 'limit':
                continue
            if v:
                query &= Q(**{k: v})
        res = Area.objects.filter(query, enable=True).values()
        return SuccessResponse(data=res, msg="获取成功")

