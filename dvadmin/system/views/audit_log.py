from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

from dvadmin.system.models import AuditLog
from dvadmin.utils.serializers import CustomModelSerializer
from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.json_response import DetailResponse


class AuditLogSerializer(CustomModelSerializer):
    """
    登录日志权限-序列化器
    """

    class Meta:
        model = AuditLog
        fields = "__all__"
        read_only_fields = ["id"]


class AuditLogViewSet(CustomModelViewSet):
    """
    登录日志接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = AuditLog.objects.all()
    serializer_class = AuditLogSerializer
    extra_filter_class = []

    resource_id = openapi.Parameter(
        'resource_id',
        openapi.IN_QUERY,
        description="资源id",
        type=openapi.TYPE_STRING,
        required=True,
        # items=openapi.Items(type=openapi.TYPE_STRING),
        # collectionFormat=''
    )
    limit = openapi.Parameter(
        'limit',
        openapi.IN_QUERY,
        description="限制输出日志条目",
        type=openapi.TYPE_STRING,
        required=False,
        # items=openapi.Items(type=openapi.TYPE_STRING),
        # collectionFormat=''
    )

    @swagger_auto_schema(method='GET', manual_parameters=[resource_id, limit], operation_summary='根据 资源id 查询改对象的操作日志')
    @action(methods=["GET"], detail=False, permission_classes=[IsAuthenticated])
    def get_resource_logs(self, request, *args, **kwargs):
        """获取资源已授予的用户列表"""
        limit = request.query_params.get('limit', 20)
        queryset = AuditLog.objects.filter(
            resource_id=request.query_params.get('resource_id', None)
        ).order_by('-create_datetime').all()[:limit]
        serializer = AuditLogSerializer(queryset, many=True, request=request)

        return DetailResponse(data=serializer.data, msg="获取成功")
