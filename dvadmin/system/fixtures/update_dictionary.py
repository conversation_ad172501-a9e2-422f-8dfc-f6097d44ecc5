"""
旧 OPA 项目下，主机数据迁移到 Chaos 平台内
"""
import datetime
import json
import os
import sys
import django


# 设置Django环境
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "application.settings.interval_admin")
django.setup()

from dvadmin.utils.update_migrate_serializers import UpdateMigrateInitialize
from dvadmin.system.fixtures.initSerializer import DictionaryInitSerializer


class MigrateDictionary(UpdateMigrateInitialize):
    def __init__(self, dictionaries_folder=None):
        super().__init__()
        self.dictionaries_folder = dictionaries_folder
        self.list_data = []

    def read_file(self):
        with open(self.dictionaries_folder, 'r', encoding='utf-8') as f:
            temp_data = f.read()
            data = json.loads(temp_data)['rows']
        self.list_data = data

    def read_all_data(self):
        # 存储所有读取到的 JSON 数据
        all_data = []

        directory_path = os.path.dirname(os.path.abspath(__file__))
        if not self.dictionaries_folder:
            all_dictionaries = os.path.join(directory_path, 'updated_dictionaries')
        else:
            all_dictionaries = os.path.join(directory_path, self.dictionaries_folder)
        # 遍历指定文件夹
        for root, dirs, files in os.walk(all_dictionaries):
            for file in files:
                if file.endswith('.json'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            all_data.extend(data)
                            print(f"Read file: {file_path}")
                    except json.JSONDecodeError as e:
                        print(f"Error reading file {file_path}: {e}")
                    except IOError as e:
                        print(f"IOError reading file {file_path}: {e}")

        return all_data

    def init_dictionary(self):
        """
        初始化字典表
        """
        self.init_base(DictionaryInitSerializer, unique_fields=['value', 'parent', ])

    def format_data(self):
        formatted_data = []
        return formatted_data

    def run_main(self):
        self.init_dictionary()


if __name__ == '__main__':
    MigrateDictionary().run_main()
