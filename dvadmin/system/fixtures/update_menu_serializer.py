"""
<AUTHOR>
@Date    ：2024/12/18
"""
import os

from rest_framework import serializers

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
import django

django.setup()
from dvadmin.system.models import (
     <PERSON>u, MenuButton, MenuField
)
from dvadmin.utils.serializers import CustomModelSerializer


class MenuButtonInitSerializer(CustomModelSerializer):
    """
    初始化菜单按钮-序列化器
    """

    class Meta:
        model = MenuButton
        fields = ['id', 'name', 'value', 'api', 'method', 'menu']
        read_only_fields = ["id"]


class MenuFieldInitSerializer(CustomModelSerializer):
    """
    初始化列权限-序列化器
    """

    class Meta:
        model = MenuField
        fields = ['id', 'menu', 'field_name', 'title', 'model']
        read_only_fields = ["id"]


class MenuInitSerializer(CustomModelSerializer):
    """
    递归深度获取数信息(用于生成初始化json文件)
    """
    name = serializers.CharField(required=False)
    children = serializers.SerializerMethodField()
    menu_button = serializers.SerializerMethodField()
    menu_field = serializers.SerializerMethodField()

    def get_children(self, obj: Menu):
        data = []
        instance = Menu.objects.filter(parent_id=obj.id)
        if instance:
            serializer = MenuInitSerializer(instance=instance, many=True)
            data = serializer.data
        return data

    def get_menu_button(self, obj: Menu):
        data = []
        instance = obj.menuPermission.order_by('method')
        if instance:
            data = list(instance.values('name', 'value', 'api', 'method'))
        return data

    def get_menu_field(self, obj: Menu):
        data = []
        instance = obj.menufield_set.order_by('field_name')
        if instance:
            data = list(instance.values('field_name', 'title', 'model'))
        return data

    def save(self, **kwargs):
        instance = super().save(**kwargs)
        # print(instance.id)
        children = self.initial_data.get('children')
        menu_button = self.initial_data.get('menu_button')
        # print('menu_button', menu_button)
        # print('children', children)
        # print(instance)
        menu_field = self.initial_data.get('menu_field')
        # 菜单表
        if children:
            for menu_data in children:
                menu_data['parent'] = instance.id
                filter_data = {
                    "name": menu_data['name'],
                    "web_path": menu_data['web_path'],
                    "component": menu_data['component'],
                    "component_name": menu_data['component_name'],
                }
                instance_obj = Menu.objects.filter(**filter_data).first()
                # if instance_obj and not self.initial_data.get('reset'):
                #     continue
                serializer = MenuInitSerializer(instance_obj, data=menu_data, request=self.request)
                serializer.is_valid(raise_exception=True)
                serializer.save()
        # 菜单按钮
        if menu_button:
            for menu_button_data in menu_button:
                menu_button_data['menu'] = instance.id
                filter_data = {
                    "menu": menu_button_data['menu'],
                    "value": menu_button_data['value']
                }
                instance_obj = MenuButton.objects.filter(**filter_data).first()
                serializer = MenuButtonInitSerializer(instance_obj, data=menu_button_data, request=self.request)
                serializer.is_valid(raise_exception=True)
                serializer.save()
        # 列权限
        if menu_field:
            for field_data in menu_field:
                field_data['menu'] = instance.id
                filter_data = {
                    'menu': field_data['menu'],
                    'field_name': field_data['field_name'],
                    'model': field_data['model']
                }
                instance_obj = MenuField.objects.filter(**filter_data).first()
                serializer = MenuFieldInitSerializer(instance_obj, data=field_data, request=self.request)
                serializer.is_valid(raise_exception=True)
                serializer.save()
        return instance

    class Meta:
        model = Menu
        fields = ['name', 'icon', 'sort', 'is_link', 'is_catalog', 'web_path', 'component', 'component_name', 'status',
                  'cache', 'visible', 'parent', 'children', 'menu_button', 'menu_field', 'creator', 'dept_belong_id']
        extra_kwargs = {
            'creator': {'write_only': True},
            'dept_belong_id': {'write_only': True}
        }
        read_only_fields = ['id', 'children']




