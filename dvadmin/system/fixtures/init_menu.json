[{"name": "系统管理", "icon": "iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 1, "is_link": false, "is_catalog": true, "web_path": "/system", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "系统配置", "icon": "iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 1, "is_link": false, "is_catalog": true, "web_path": "/system/configuration", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "菜单管理", "icon": "iconfont icon-caidan", "sort": 1, "is_link": false, "is_catalog": false, "web_path": "/system/menu", "component": "system/menu/index", "component_name": "menu", "status": true, "cache": false, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "menu:Search", "api": "/api/system/menu/", "method": 0}, {"name": "详情", "value": "menu:Retrieve", "api": "/api/system/menu/{id}/", "method": 0}, {"name": "查询所有", "value": "menu:SearchAll", "api": "/api/system/menu/get_all_menu/", "method": 0}, {"name": "路由", "value": "menu:router", "api": "/api/system/menu/web_router/", "method": 0}, {"name": "查询按钮权限", "value": "btn:Search", "api": "/api/system/menu_button/", "method": 0}, {"name": "查询列权限", "value": "column:Search", "api": "/api/system/column/", "method": 0}, {"name": "新增", "value": "menu:Create", "api": "/api/system/menu/", "method": 1}, {"name": "上移", "value": "menu:MoveUp", "api": "/api/system/menu/mode_up/", "method": 1}, {"name": "下移", "value": "menu:MoveDown", "api": "/api/system/menu/mode_down/", "method": 1}, {"name": "新增按钮权限", "value": "btn:Create", "api": "/api/system/menu_button/", "method": 1}, {"name": "新增列权限", "value": "column:Create", "api": "/api/system/column/", "method": 1}, {"name": "自动匹配列权限", "value": "column:Match", "api": "/api/system/column/auto_match_fields/", "method": 1}, {"name": "编辑", "value": "menu:Update", "api": "/api/system/menu/{id}/", "method": 2}, {"name": "修改按钮权限", "value": "btn:Update", "api": "/api/system/menu_button/{id}/", "method": 2}, {"name": "编辑列权限", "value": "column:Update", "api": "/api/system/column/{id}/", "method": 2}, {"name": "删除", "value": "menu:Delete", "api": "/api/system/menu/{id}/", "method": 3}, {"name": "删除按钮权限", "value": "btn:Delete", "api": "/api/system/menu_button/{id}/", "method": 3}, {"name": "删除列权限", "value": "column:Delete", "api": "/api/system/column/{id}/", "method": 3}], "menu_field": []}, {"name": "部门管理", "icon": "ele-OfficeBuilding", "sort": 2, "is_link": false, "is_catalog": false, "web_path": "/system/dept", "component": "system/dept/index", "component_name": "dept", "status": true, "cache": false, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "dept:Search", "api": "/api/system/dept/", "method": 0}, {"name": "详情", "value": "dept:Retrieve", "api": "/api/system/dept/{id}/", "method": 0}, {"name": "查询所有", "value": "dept:SearchAll", "api": "/api/system/dept/all_dept/", "method": 0}, {"name": "懒加载查询所有", "value": "dept:LazySearchAll", "api": "/api/system/dept/dept_lazy_tree/", "method": 0}, {"name": "头信息", "value": "dept:HeaderInfo", "api": "/api/system/dept/dept_info/", "method": 0}, {"name": "新增", "value": "dept:Create", "api": "/api/system/dept/", "method": 1}, {"name": "上移", "value": "dept:MoveUp", "api": "/api/system/dept/mode_up/", "method": 1}, {"name": "下移", "value": "dept:MoveDown", "api": "/api/system/dept/mode_down/", "method": 1}, {"name": "编辑", "value": "dept:Update", "api": "/api/system/dept/{id}/", "method": 2}, {"name": "删除", "value": "dept:Delete", "api": "/api/system/dept/{id}/", "method": 3}], "menu_field": []}, {"name": "角色管理", "icon": "ele-ColdDrink", "sort": 3, "is_link": false, "is_catalog": false, "web_path": "/system/role", "component": "system/role/index", "component_name": "role", "status": true, "cache": false, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "role:Search", "api": "/api/system/role/", "method": 0}, {"name": "详情", "value": "role:Retrieve", "api": "/api/system/role/{id}/", "method": 0}, {"name": "权限配置", "value": "role:Permission", "api": "/api/system/role/{id}/", "method": 0}, {"name": "新增", "value": "role:Create", "api": "/api/system/role/", "method": 1}, {"name": "编辑", "value": "role:Update", "api": "/api/system/role/{id}/", "method": 2}, {"name": "保存", "value": "role:Save", "api": "/api/system/role/{id}/", "method": 2}, {"name": "删除", "value": "role:Delete", "api": "/api/system/role/{id}/", "method": 3}, {"name": "行权限配置", "value": "role:row<PERSON><PERSON><PERSON>", "api": "/api/system/row_permission/get_role_row_ids/", "method": 0}, {"name": "取消行权限", "value": "role:revokeRowPermission", "api": "/api/system/row_permission/revoke_role_row_ids/", "method": 1}, {"name": "新增行权限配置", "value": "role:addRowPermission", "api": "/api/system/row_permission/add_role_row_ids/", "method": 1}], "menu_field": [{"field_name": "create_datetime", "title": "创建时间", "model": "Role"}, {"field_name": "creator", "title": "创建人", "model": "Role"}, {"field_name": "dept_belong_id", "title": "数据归属部门", "model": "Role"}, {"field_name": "description", "title": "描述", "model": "Role"}, {"field_name": "id", "title": "Id", "model": "Role"}, {"field_name": "key", "title": "权限字符", "model": "Role"}, {"field_name": "modifier", "title": "修改人", "model": "Role"}, {"field_name": "name", "title": "角色名称", "model": "Role"}, {"field_name": "sort", "title": "角色顺序", "model": "Role"}, {"field_name": "status", "title": "角色状态", "model": "Role"}, {"field_name": "update_datetime", "title": "修改时间", "model": "Role"}]}, {"name": "用户管理", "icon": "iconfont icon-icon-", "sort": 4, "is_link": false, "is_catalog": false, "web_path": "/system/user", "component": "system/user/index", "component_name": "user", "status": true, "cache": false, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "user:Search", "api": "/api/system/user/", "method": 0}, {"name": "详情", "value": "user:Retrieve", "api": "/api/system/user/{id}/", "method": 0}, {"name": "新增", "value": "user:Create", "api": "/api/system/user/", "method": 1}, {"name": "导出", "value": "user:Export", "api": "/api/system/user/export/", "method": 1}, {"name": "导入", "value": "user:Import", "api": "/api/system/user/import/", "method": 1}, {"name": "编辑", "value": "user:Update", "api": "/api/system/user/{id}/", "method": 2}, {"name": "重设密码", "value": "user:ResetPassword", "api": "/api/system/user/{id}/reset_password/", "method": 2}, {"name": "重置密码", "value": "user:De<PERSON><PERSON><PERSON>ass<PERSON>", "api": "/api/system/user/{id}/reset_to_default_password/", "method": 2}, {"name": "删除", "value": "user:Delete", "api": "/api/system/user/{id}/", "method": 3}, {"name": "预览权限", "value": "user:previewMenuButtonPermissions", "api": "/api/system/user/get_user_menu_button_permission/", "method": 0}, {"name": "预览行权限", "value": "user:previewRowPermissions", "api": "/api/system/user/get_user_row_permission/", "method": 0}], "menu_field": [{"field_name": "avatar", "title": "头像", "model": "Users"}, {"field_name": "create_datetime", "title": "创建时间", "model": "Users"}, {"field_name": "creator", "title": "创建人", "model": "Users"}, {"field_name": "dept", "title": "所属部门", "model": "Users"}, {"field_name": "dept_belong_id", "title": "数据归属部门", "model": "Users"}, {"field_name": "description", "title": "描述", "model": "Users"}, {"field_name": "email", "title": "邮箱", "model": "Users"}, {"field_name": "gender", "title": "性别", "model": "Users"}, {"field_name": "id", "title": "Id", "model": "Users"}, {"field_name": "mobile", "title": "电话", "model": "Users"}, {"field_name": "modifier", "title": "修改人", "model": "Users"}, {"field_name": "name", "title": "姓名", "model": "Users"}, {"field_name": "update_datetime", "title": "修改时间", "model": "Users"}, {"field_name": "username", "title": "用户账号", "model": "Users"}, {"field_name": "user_type", "title": "用户类型", "model": "Users"}]}, {"name": "数据权限", "icon": "ele-ColdDrink", "sort": 5, "is_link": false, "is_catalog": false, "web_path": "/system/dataPermission", "component": "system/dataPermission/index", "component_name": "数据权限", "status": true, "cache": false, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "dataPermission:Search", "api": "/api/system/role/", "method": 0}, {"name": "详情", "value": "dataPermission:Retrieve", "api": "/api/system/role/{id}/", "method": 0}, {"name": "权限配置", "value": "dataPermission:Permission", "api": "/api/system/role/{id}/", "method": 0}, {"name": "新增", "value": "dataPermission:Create", "api": "/api/system/role/", "method": 1}, {"name": "编辑", "value": "dataPermission:Update", "api": "/api/system/role/{id}/", "method": 2}, {"name": "保存", "value": "dataPermission:Save", "api": "/api/system/role/{id}/", "method": 2}, {"name": "删除", "value": "dataPermission:Delete", "api": "/api/system/role/{id}/", "method": 3}, {"name": "查询数据权限", "value": "dataPermission:rowPermission:getRoleRowIds", "api": "/api/system/row_permission/get_role_row_ids/", "method": 0}, {"name": "角色数据信息", "value": "dataPermission:rowPermission:getRowsInfo", "api": "/api/system/row_permission/get_rows_info/", "method": 0}, {"name": "获取数据表信息", "value": "dataPermission:rowPermission:getModels", "api": "/api/system/column/get_models/", "method": 0}, {"name": "数据权限概览", "value": "dataPermission:rowPermission:getRoleRowStatistics", "api": "/api/system/row_permission/get_role_row_statistics/", "method": 0}, {"name": "取消数据权限", "value": "dataPermission:rowPermission:revokeRowPermission", "api": "/api/system/row_permission/revoke_role_row_ids/", "method": 1}, {"name": "新增数据权限", "value": "dataPermission:rowPermission:addRowPermission", "api": "/api/system/row_permission/add_role_row_ids/", "method": 1}], "menu_field": []}, {"name": "岗位管理", "icon": "iconfont icon-g<PERSON><PERSON><PERSON><PERSON>", "sort": 6, "is_link": false, "is_catalog": false, "description": "岗位管理", "web_path": "/system/post", "component": "system/post/index", "component_name": "岗位管理", "status": true, "cache": true, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "system:post:Search", "api": "/api/system/post/", "method": 0}, {"name": "查看", "value": "system:post:Retrieve", "api": "/api/system/post/{id}/", "method": 0}, {"name": "添加", "value": "system:post:Create", "api": "/api/system/post/", "method": 1}, {"name": "编辑", "value": "system:post:Update", "api": "/api/system/post/{id}/", "method": 2}, {"name": "删除", "value": "system:post:Delete", "api": "/api/system/post/{id}/", "method": 3}], "menu_field": []}, {"name": "行角色权限", "icon": "iconfont icon-quanxian", "sort": 7, "is_link": false, "is_catalog": false, "description": "行角色权限", "web_path": "/system/rowPermission/role", "component": "system/rowPermission/rowTargetRole/index", "component_name": "行角色权限", "status": true, "cache": true, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "system:roleTargetPermission:Search", "api": "/api/system/row_permission/", "method": 0}, {"name": "查看", "value": "system:roleTargetPermission:Retrieve", "api": "/api/system/row_permission/{id}/", "method": 0}, {"name": "添加", "value": "system:roleTargetPermission:Create", "api": "/api/system/row_permission/", "method": 1}, {"name": "编辑", "value": "system:roleTargetPermission:Update", "api": "/api/system/row_permission/{id}/", "method": 2}, {"name": "删除", "value": "system:roleTargetPermission:Delete", "api": "/api/system/row_permission/{id}/", "method": 3}], "menu_field": []}, {"name": "消息中心", "icon": "iconfont icon-x<PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 8, "is_link": false, "is_catalog": false, "web_path": "/system/messageCenter", "component": "system/messageCenter/index", "component_name": "messageCenter", "status": true, "cache": false, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "messageCenter:Search", "api": "/api/system/message_center/", "method": 0}, {"name": "详情", "value": "messageCenter:Retrieve", "api": "/api/system/message_center/{id}/", "method": 0}, {"name": "新增", "value": "messageCenter:Create", "api": "/api/system/message_center/", "method": 1}, {"name": "编辑", "value": "messageCenter:Update", "api": "/api/system/message_center/{id}/", "method": 2}, {"name": "删除", "value": "messageCenter:Delete", "api": "/api/system/menu/{id}/", "method": 3}], "menu_field": [{"field_name": "content", "title": "内容", "model": "MessageCenter"}, {"field_name": "create_datetime", "title": "创建时间", "model": "MessageCenter"}, {"field_name": "creator", "title": "创建人", "model": "MessageCenter"}, {"field_name": "dept_belong_id", "title": "数据归属部门", "model": "MessageCenter"}, {"field_name": "description", "title": "描述", "model": "MessageCenter"}, {"field_name": "id", "title": "Id", "model": "MessageCenter"}, {"field_name": "modifier", "title": "修改人", "model": "MessageCenter"}, {"field_name": "target_type", "title": "目标类型", "model": "MessageCenter"}, {"field_name": "title", "title": "标题", "model": "MessageCenter"}, {"field_name": "update_datetime", "title": "修改时间", "model": "MessageCenter"}]}, {"name": "接口白名单", "icon": "ele-SetUp", "sort": 9, "is_link": false, "is_catalog": false, "web_path": "/system/apiWhiteList", "component": "system/whiteList/index", "component_name": "whiteList", "status": true, "cache": false, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "api_white_list:Search", "api": "/api/system/api_white_list/", "method": 0}, {"name": "详情", "value": "api_white_list:Retrieve", "api": "/api/system/api_white_list/{id}/", "method": 0}, {"name": "新增", "value": "api_white_list:Create", "api": "/api/system/api_white_list/", "method": 1}, {"name": "编辑", "value": "api_white_list:Update", "api": "/api/system/api_white_list/{id}/", "method": 2}, {"name": "删除", "value": "api_white_list:Delete", "api": "/api/system/api_white_list/{id}/", "method": 3}], "menu_field": [{"field_name": "create_datetime", "title": "创建时间", "model": "ApiWhiteList"}, {"field_name": "creator", "title": "创建人", "model": "ApiWhiteList"}, {"field_name": "dept_belong_id", "title": "数据归属部门", "model": "ApiWhiteList"}, {"field_name": "description", "title": "描述", "model": "ApiWhiteList"}, {"field_name": "enable_datasource", "title": "激活数据权限", "model": "ApiWhiteList"}, {"field_name": "id", "title": "Id", "model": "ApiWhiteList"}, {"field_name": "method", "title": "接口请求方法", "model": "ApiWhiteList"}, {"field_name": "modifier", "title": "修改人", "model": "ApiWhiteList"}, {"field_name": "update_datetime", "title": "修改时间", "model": "ApiWhiteList"}, {"field_name": "url", "title": "url", "model": "ApiWhiteList"}]}], "menu_button": [], "menu_field": []}, {"name": "常规配置", "icon": "iconfont icon-configure", "sort": 2, "is_link": false, "is_catalog": true, "web_path": "/system/generalConfig", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "系统配置", "icon": "iconfont icon-system", "sort": 0, "is_link": false, "is_catalog": false, "web_path": "/config", "component": "system/config/index", "component_name": "config", "status": true, "cache": false, "visible": true, "parent": 10, "children": [], "menu_button": [{"name": "查询", "value": "system_config:Search", "api": "/api/system/system_config/", "method": 0}, {"name": "详情", "value": "system_config:Retrieve", "api": "/api/system/system_config/{id}/", "method": 0}, {"name": "新增", "value": "system_config:Create", "api": "/api/system/system_config/", "method": 1}, {"name": "编辑", "value": "system_config:Update", "api": "/api/system/system_config/{id}/", "method": 2}, {"name": "删除", "value": "system_config:Delete", "api": "/api/system/system_config/{id}/", "method": 3}], "menu_field": []}, {"name": "字典管理", "icon": "iconfont icon-dict", "sort": 1, "is_link": false, "is_catalog": false, "web_path": "/dictionary", "component": "system/dictionary/index", "component_name": "dictionary", "status": true, "cache": false, "visible": true, "parent": 10, "children": [], "menu_button": [{"name": "查询", "value": "dictionary:Search", "api": "/api/system/dictionary/", "method": 0}, {"name": "详情", "value": "dictionary:Retrieve", "api": "/api/system/dictionary/{id}/", "method": 0}, {"name": "新增", "value": "dictionary:Create", "api": "/api/system/dictionary/", "method": 1}, {"name": "编辑", "value": "dictionary:Update", "api": "/api/system/dictionary/{id}/", "method": 2}, {"name": "删除", "value": "dictionary:Delete", "api": "/api/system/dictionary/{id}/", "method": 3}], "menu_field": [{"field_name": "color", "title": "颜色", "model": "Dictionary"}, {"field_name": "create_datetime", "title": "创建时间", "model": "Dictionary"}, {"field_name": "creator", "title": "创建人", "model": "Dictionary"}, {"field_name": "dept_belong_id", "title": "数据归属部门", "model": "Dictionary"}, {"field_name": "description", "title": "描述", "model": "Dictionary"}, {"field_name": "id", "title": "Id", "model": "Dictionary"}, {"field_name": "is_value", "title": "是否为value值", "model": "Dictionary"}, {"field_name": "label", "title": "字典名称", "model": "Dictionary"}, {"field_name": "modifier", "title": "修改人", "model": "Dictionary"}, {"field_name": "parent", "title": "父级", "model": "Dictionary"}, {"field_name": "remark", "title": "备注", "model": "Dictionary"}, {"field_name": "sort", "title": "显示排序", "model": "Dictionary"}, {"field_name": "status", "title": "状态", "model": "Dictionary"}, {"field_name": "type", "title": "数据值类型", "model": "Dictionary"}, {"field_name": "update_datetime", "title": "修改时间", "model": "Dictionary"}, {"field_name": "value", "title": "字典编号", "model": "Dictionary"}]}, {"name": "地区管理", "icon": "iconfont icon-Area", "sort": 2, "is_link": false, "is_catalog": false, "web_path": "/areas", "component": "system/areas/index", "component_name": "areas", "status": true, "cache": false, "visible": true, "parent": 10, "children": [], "menu_button": [{"name": "查询", "value": "area:Search", "api": "/api/system/area/", "method": 0}, {"name": "详情", "value": "area:Retrieve", "api": "/api/system/area/{id}/", "method": 0}, {"name": "新增", "value": "area:Create", "api": "/api/system/area/", "method": 1}, {"name": "编辑", "value": "area:Update", "api": "/api/system/area/{id}/", "method": 2}, {"name": "删除", "value": "area:Delete", "api": "/api/system/area/{id}/", "method": 3}], "menu_field": [{"field_name": "code", "title": "地区编码", "model": "Area"}, {"field_name": "create_datetime", "title": "创建时间", "model": "Area"}, {"field_name": "creator", "title": "创建人", "model": "Area"}, {"field_name": "dept_belong_id", "title": "数据归属部门", "model": "Area"}, {"field_name": "description", "title": "描述", "model": "Area"}, {"field_name": "enable", "title": "是否启用", "model": "Area"}, {"field_name": "id", "title": "Id", "model": "Area"}, {"field_name": "initials", "title": "首字母", "model": "Area"}, {"field_name": "level", "title": "地区层级(1省份 2城市 3区县 4乡级)", "model": "Area"}, {"field_name": "modifier", "title": "修改人", "model": "Area"}, {"field_name": "name", "title": "名称", "model": "Area"}, {"field_name": "pcode", "title": "父地区编码", "model": "Area"}, {"field_name": "pinyin", "title": "拼音", "model": "Area"}, {"field_name": "update_datetime", "title": "修改时间", "model": "Area"}]}, {"name": "附件管理", "icon": "iconfont icon-file", "sort": 3, "is_link": false, "is_catalog": false, "web_path": "/file", "component": "system/fileList/index", "component_name": "file", "status": true, "cache": false, "visible": true, "parent": 10, "children": [], "menu_button": [{"name": "详情", "value": "file:Retrieve", "api": "/api/system/file/{id}/", "method": 0}, {"name": "查询", "value": "file:Search", "api": "/api/system/file/", "method": 0}, {"name": "编辑", "value": "file:Update", "api": "/api/system/file/{id}/", "method": 1}, {"name": "删除", "value": "file:Delete", "api": "/api/system/file/{id}/", "method": 3}], "menu_field": [{"field_name": "create_datetime", "title": "创建时间", "model": "FileList"}, {"field_name": "creator", "title": "创建人", "model": "FileList"}, {"field_name": "dept_belong_id", "title": "数据归属部门", "model": "FileList"}, {"field_name": "description", "title": "描述", "model": "FileList"}, {"field_name": "engine", "title": "引擎", "model": "FileList"}, {"field_name": "file_url", "title": "文件地址", "model": "FileList"}, {"field_name": "id", "title": "Id", "model": "FileList"}, {"field_name": "md5sum", "title": "文件md5", "model": "FileList"}, {"field_name": "mime_type", "title": "Mime类型", "model": "FileList"}, {"field_name": "modifier", "title": "修改人", "model": "FileList"}, {"field_name": "name", "title": "名称", "model": "FileList"}, {"field_name": "size", "title": "文件大小", "model": "FileList"}, {"field_name": "update_datetime", "title": "修改时间", "model": "FileList"}, {"field_name": "url", "title": "url", "model": "FileList"}]}], "menu_button": [], "menu_field": []}, {"name": "审计日志", "icon": "iconfont icon-rizhi", "sort": 3, "is_link": false, "is_catalog": true, "web_path": "/system/log", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "登录日志", "icon": "iconfont icon-g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 1, "is_link": false, "is_catalog": false, "web_path": "/loginLog", "component": "system/log/loginLog/index", "component_name": "loginLog", "status": true, "cache": false, "visible": true, "parent": 15, "children": [], "menu_button": [{"name": "查询", "value": "login_log:Search", "api": "/api/system/login_log/", "method": 0}, {"name": "详情", "value": "login_log:Retrieve", "api": "/api/system/login_log/{id}/", "method": 0}], "menu_field": [{"field_name": "agent", "title": "agent信息", "model": "LoginLog"}, {"field_name": "area_code", "title": "区域代码", "model": "LoginLog"}, {"field_name": "browser", "title": "浏览器名", "model": "LoginLog"}, {"field_name": "city", "title": "城市", "model": "LoginLog"}, {"field_name": "continent", "title": "州", "model": "LoginLog"}, {"field_name": "country", "title": "国家", "model": "LoginLog"}, {"field_name": "country_code", "title": "简称", "model": "LoginLog"}, {"field_name": "country_english", "title": "英文全称", "model": "LoginLog"}, {"field_name": "create_datetime", "title": "创建时间", "model": "LoginLog"}, {"field_name": "creator", "title": "创建人", "model": "LoginLog"}, {"field_name": "dept_belong_id", "title": "数据归属部门", "model": "LoginLog"}, {"field_name": "description", "title": "描述", "model": "LoginLog"}, {"field_name": "district", "title": "县区", "model": "LoginLog"}, {"field_name": "id", "title": "Id", "model": "LoginLog"}, {"field_name": "ip", "title": "登录ip", "model": "LoginLog"}, {"field_name": "isp", "title": "运营商", "model": "LoginLog"}, {"field_name": "latitude", "title": "纬度", "model": "LoginLog"}, {"field_name": "login_type", "title": "登录类型", "model": "LoginLog"}, {"field_name": "longitude", "title": "经度", "model": "LoginLog"}, {"field_name": "modifier", "title": "修改人", "model": "LoginLog"}, {"field_name": "os", "title": "操作系统", "model": "LoginLog"}, {"field_name": "province", "title": "省份", "model": "LoginLog"}, {"field_name": "update_datetime", "title": "修改时间", "model": "LoginLog"}, {"field_name": "username", "title": "登录用户名", "model": "LoginLog"}]}, {"name": "操作日志", "icon": "iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 2, "is_link": false, "is_catalog": false, "web_path": "/operationLog", "component": "system/log/operationLog/index", "component_name": "operationLog", "status": true, "cache": false, "visible": true, "parent": 15, "children": [], "menu_button": [{"name": "详情", "value": "operation_log:Retrieve", "api": "/api/system/operation_log/{id}/", "method": 0}, {"name": "查询", "value": "operation_log:Search", "api": "/api/system/operation_log/", "method": 0}], "menu_field": [{"field_name": "create_datetime", "title": "创建时间", "model": "OperationLog"}, {"field_name": "creator", "title": "创建人", "model": "OperationLog"}, {"field_name": "dept_belong_id", "title": "数据归属部门", "model": "OperationLog"}, {"field_name": "description", "title": "描述", "model": "OperationLog"}, {"field_name": "id", "title": "Id", "model": "OperationLog"}, {"field_name": "json_result", "title": "返回信息", "model": "OperationLog"}, {"field_name": "modifier", "title": "修改人", "model": "OperationLog"}, {"field_name": "request_body", "title": "请求参数", "model": "OperationLog"}, {"field_name": "request_browser", "title": "请求浏览器", "model": "OperationLog"}, {"field_name": "request_ip", "title": "请求ip地址", "model": "OperationLog"}, {"field_name": "request_method", "title": "请求方式", "model": "OperationLog"}, {"field_name": "request_modular", "title": "请求模块", "model": "OperationLog"}, {"field_name": "request_msg", "title": "操作说明", "model": "OperationLog"}, {"field_name": "request_os", "title": "操作系统", "model": "OperationLog"}, {"field_name": "request_path", "title": "请求地址", "model": "OperationLog"}, {"field_name": "response_code", "title": "响应状态码", "model": "OperationLog"}, {"field_name": "status", "title": "响应状态", "model": "OperationLog"}, {"field_name": "update_datetime", "title": "修改时间", "model": "OperationLog"}]}], "menu_button": [], "menu_field": []}], "menu_button": [], "menu_field": []}, {"name": "基础资源", "icon": "ele-OfficeBuilding", "sort": 2, "is_link": false, "is_catalog": true, "web_path": "/resource", "component": "", "component_name": "", "status": true, "cache": true, "visible": true, "parent": null, "children": [{"name": "IDC机房", "icon": "iconfont icon-di<PERSON><PERSON>-shu<PERSON>", "sort": 1, "is_link": false, "is_catalog": true, "web_path": "/idcManage", "component": "", "component_name": "", "status": true, "cache": true, "visible": true, "parent": null, "children": [{"name": "机房管理", "icon": "iconfont icon-di<PERSON>o", "sort": 1, "is_link": false, "is_catalog": false, "description": "机房管理页面", "web_path": "/resource/machineRoom", "component": "resource/machineRoom/index", "component_name": "machineRoom", "status": true, "cache": true, "visible": true, "parent": 17, "children": [], "menu_button": [{"name": "查询", "value": "resource:machineRoom:Search", "api": "/api/resource/machine_room/", "method": 0}, {"name": "查看", "value": "resource:machineRoom:Retrieve", "api": "/api/resource/machine_room/{id}/", "method": 0}, {"name": "添加", "value": "resource:machineRoom:Create", "api": "/api/resource/machine_room/", "method": 1}, {"name": "编辑", "value": "resource:machineRoom:Update", "api": "/api/resource/machine_room/{id}/", "method": 2}, {"name": "删除", "value": "resource:machineRoom:Delete", "api": "/api/resource/machine_room/{id}/", "method": 3}, {"name": "导入", "value": "resource:machineRoom:Import", "api": "/api/resource/machine_room/import_data/", "method": 1}], "menu_field": []}, {"name": "包间管理", "icon": "fa fa-object-group", "sort": 2, "is_link": false, "is_catalog": false, "description": "包间页面", "web_path": "/resource/privateRoom", "component": "/resource/privateRoom/index", "component_name": "privateRoom", "status": true, "cache": true, "visible": true, "parent": 17, "children": [], "menu_button": [{"name": "查询", "value": "resource:privateRoom:Search", "api": "/api/resource/private_room/", "method": 0}, {"name": "查看", "value": "resource:privateRoom:Retrieve", "api": "/api/resource/private_room/{id}/", "method": 0}, {"name": "添加", "value": "resource:privateRoom:Create", "api": "/api/resource/private_room/", "method": 1}, {"name": "编辑", "value": "resource:privateRoom:Update", "api": "/api/resource/private_room/{id}/", "method": 2}, {"name": "删除", "value": "resource:privateRoom:Delete", "api": "/api/resource/private_room/{id}/", "method": 3}, {"name": "导入", "value": "resource:privateRoom:Import", "api": "/api/resource/private_room/import_data/", "method": 1}], "menu_field": []}, {"name": "机柜管理", "icon": "iconfont icon-shen<PERSON><PERSON><PERSON>", "sort": 2, "is_link": false, "is_catalog": false, "description": "机柜管理", "web_path": "/resource/idcRackMachine", "component": "resource/idcRackMachine/index", "component_name": "机柜管理", "status": true, "cache": true, "visible": true, "parent": 17, "children": [], "menu_button": [{"name": "查询", "value": "resource:idcRackMachine:Search", "api": "/api/resource/idc_rack_machine/", "method": 0}, {"name": "查看", "value": "resource:idcRackMachine:Retrieve", "api": "/api/resource/idc_rack_machine/{id}/", "method": 0}, {"name": "添加", "value": "resource:idcRackMachine:Create", "api": "/api/resource/idc_rack_machine/", "method": 1}, {"name": "编辑", "value": "resource:idcRackMachine:Update", "api": "/api/resource/idc_rack_machine/{id}/", "method": 2}, {"name": "删除", "value": "resource:idcRackMachine:Delete", "api": "/api/resource/idc_rack_machine/{id}/", "method": 3}, {"name": "导入", "value": "resource:idcRackMachine:Import", "api": "/api/resource/idc_rack_machine/import_data/", "method": 1}], "menu_field": []}], "menu_button": []}, {"name": "资产管理", "icon": "iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 2, "is_link": false, "is_catalog": true, "description": "物理资源资产管理（物理类型的服务器）", "web_path": "/resource/physicalAsset", "component": "", "component_name": "物理资源资产管理", "status": true, "cache": true, "visible": true, "parent": 17, "children": [{"name": "服务器管理", "icon": "iconfont icon-diannao1", "sort": 1, "is_link": false, "is_catalog": false, "description": "物理服务器管理", "web_path": "/resource/physicalAsset/physicalServerMachine", "component": "resource/physicalAsset/physicalServerMachine/index", "component_name": "物理服务器管理", "status": true, "cache": true, "visible": true, "parent": 17, "children": [], "menu_button": [{"name": "查询", "value": "resource:physicalAsset:physicalServerMachine:Search", "api": "/api/resource/physical_asset/physical_server_machine/", "method": 0}, {"name": "查看", "value": "resource:physicalAsset:physicalServerMachine:Retrieve", "api": "/api/resource/physical_asset/physical_server_machine/{id}/", "method": 0}, {"name": "添加", "value": "resource:physicalAsset:physicalServerMachine:Create", "api": "/api/resource/physical_asset/physical_server_machine/", "method": 1}, {"name": "编辑", "value": "resource:physicalAsset:physicalServerMachine:Update", "api": "/api/resource/physical_asset/physical_server_machine/{id}/", "method": 2}, {"name": "删除", "value": "resource:physicalAsset:physicalServerMachine:Delete", "api": "/api/resource/physical_asset/physical_server_machine/{id}/", "method": 3}, {"name": "导入", "value": "resource:physicalAsset:physicalServerMachine:Import", "api": "/api/resource/physical_asset/physical_server_machine/import_data/", "method": 1}], "menu_field": []}, {"name": "网络设备管理", "icon": "iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 2, "is_link": false, "is_catalog": false, "description": "网络设备管理", "web_path": "/resource/physicalAsset/networkHardware", "component": "resource/physicalAsset/networkHardware/index", "component_name": "网络设备管理", "status": true, "cache": true, "visible": true, "parent": 17, "children": [], "menu_button": [{"name": "查询", "value": "resource:physicalAsset:networkHardware:Search", "api": "/api/resource/physical_asset/network_hardware/", "method": 0}, {"name": "查看", "value": "resource:physicalAsset:networkHardware:Retrieve", "api": "/api/resource/physical_asset/network_hardware/{id}/", "method": 0}, {"name": "添加", "value": "resource:physicalAsset:networkHardware:Create", "api": "/api/resource/physical_asset/network_hardware/", "method": 1}, {"name": "编辑", "value": "resource:physicalAsset:networkHardware:Update", "api": "/api/resource/physical_asset/network_hardware/{id}/", "method": 2}, {"name": "删除", "value": "resource:physicalAsset:networkHardware:Delete", "api": "/api/resource/physical_asset/network_hardware/{id}/", "method": 3}, {"name": "导入", "value": "resource:physicalAsset:networkHardware:Import", "api": "/api/resource/physical_asset/network_hardware/import_data/", "method": 1}], "menu_field": []}, {"name": "配件管理", "icon": "iconfont icon-zujian", "sort": 3, "is_link": false, "is_catalog": false, "description": "配件管理", "web_path": "/resource/physicalAsset/holisticAccessory", "component": "resource/physicalAsset/holisticAccessory/index", "component_name": "配件管理", "status": true, "cache": true, "visible": true, "parent": 17, "children": [], "menu_button": [{"name": "查询", "value": "resource:physicalAsset:holisticAccessory:Search", "api": "/api/resource/physical_asset/holistic_accessory/", "method": 0}, {"name": "查看", "value": "resource:physicalAsset:holisticAccessory:Retrieve", "api": "/api/resource/physical_asset/holistic_accessory/{id}/", "method": 0}, {"name": "添加", "value": "resource:physicalAsset:holisticAccessory:Create", "api": "/api/resource/physical_asset/holistic_accessory/", "method": 1}, {"name": "编辑", "value": "resource:physicalAsset:holisticAccessory:Update", "api": "/api/resource/physical_asset/holistic_accessory/{id}/", "method": 2}, {"name": "删除", "value": "resource:physicalAsset:holisticAccessory:Delete", "api": "/api/resource/physical_asset/holistic_accessory/{id}/", "method": 3}, {"name": "导入", "value": "resource:physicalAsset:holisticAccessory:Import", "api": "/api/resource/physical_asset/holistic_accessory/import_data/", "method": 1}], "menu_field": []}, {"name": "耗材管理", "icon": "iconfont icon-crew_feature", "sort": 4, "is_link": false, "is_catalog": false, "description": "耗材管理", "web_path": "/resource/physicalAsset/generalConsumable", "component": "resource/physicalAsset/generalConsumable/index", "component_name": "耗材管理", "status": true, "cache": true, "visible": true, "parent": 17, "children": [], "menu_button": [{"name": "查询", "value": "resource:physicalAsset:generalConsumable:Search", "api": "/api/resource/physical_asset/general_consumable/", "method": 0}, {"name": "查看", "value": "resource:physicalAsset:generalConsumable:Retrieve", "api": "/api/resource/physical_asset/general_consumable/{id}/", "method": 0}, {"name": "添加", "value": "resource:physicalAsset:generalConsumable:Create", "api": "/api/resource/physical_asset/general_consumable/", "method": 1}, {"name": "编辑", "value": "resource:physicalAsset:generalConsumable:Update", "api": "/api/resource/physical_asset/general_consumable/{id}/", "method": 2}, {"name": "删除", "value": "resource:physicalAsset:generalConsumable:Delete", "api": "/api/resource/physical_asset/general_consumable/{id}/", "method": 3}, {"name": "导入", "value": "resource:physicalAsset:generalConsumable:Import", "api": "/api/resource/physical_asset/general_consumable/import_data/", "method": 1}], "menu_field": []}], "menu_button": [], "menu_field": []}, {"name": "运维管理", "icon": "iconfont icon-siweida<PERSON>u", "sort": 3, "is_link": false, "is_catalog": true, "web_path": "/operatorCMDB", "component": "", "component_name": "", "status": true, "cache": true, "visible": true, "parent": null, "children": [{"name": "主机管理", "icon": "iconfont icon-di<PERSON><PERSON>-shu<PERSON>", "sort": 1, "is_link": false, "is_catalog": false, "web_path": "/operatorCMDB/host", "component": "operatorCMDB/host/index", "component_name": "VirtualHost", "status": true, "cache": true, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "operatorcmdb:host:Search", "api": "/api/operatorcmdb/host/", "method": 0}, {"name": "查看", "value": "operatorcmdb:host:Re<PERSON>eve", "api": "/api/operatorcmdb/host/{id}/", "method": 0}, {"name": "添加", "value": "operatorcmdb:host:<PERSON><PERSON>", "api": "/api/operatorcmdb/host/", "method": 1}, {"name": "编辑", "value": "operatorcmdb:host:Update", "api": "/api/operatorcmdb/host/{id}/", "method": 2}, {"name": "删除", "value": "operatorcmdb:host:Delete", "api": "/api/operatorcmdb/host/{id}/", "method": 3}, {"name": "批量删除", "value": "operatorcmdb:host:MultipleDelete", "api": "/api/operatorcmdb/host/multiple_delete/", "method": 3}, {"name": "导入", "value": "operatorcmdb:host:<PERSON><PERSON><PERSON>", "api": "/api/operatorcmdb/host/import_data/", "method": 1}], "menu_field": []}], "menu_button": [], "menu_field": []}], "menu_button": [], "menu_field": []}, {"name": "采销管理", "icon": "fa fa-handshake-o", "sort": 3, "is_link": false, "is_catalog": true, "web_path": "/purchaseSalesManage", "component": "", "component_name": "", "status": true, "cache": true, "visible": true, "parent": null, "children": [{"name": "客户管理", "icon": "fa fa-address-book-o", "sort": 1, "is_link": false, "is_catalog": false, "web_path": "/purchaseSalesManage/customer", "component": "customer/customer/index", "component_name": "Customer", "status": true, "cache": true, "visible": true, "parent": 1, "children": [], "menu_button": [{"name": "查询", "value": "purchaseSalesManage:customer:Search", "api": "/api/customer/customer/", "method": 0}, {"name": "查看", "value": "purchaseSalesManage:customer:Retrieve", "api": "/api/customer/customer/{id}/", "method": 0}, {"name": "添加", "value": "purchaseSalesManage:customer:Create", "api": "/api/customer/customer/", "method": 1}, {"name": "编辑", "value": "purchaseSalesManage:customer:Update", "api": "/api/customer/customer/{id}/", "method": 2}, {"name": "删除", "value": "purchaseSalesManage:customer:Delete", "api": "/api/customer/customer/{id}/", "method": 3}, {"name": "导入", "value": "purchaseSalesManage:customer:Import", "api": "/api/customer/customer/import_data/", "method": 1}], "menu_field": []}], "menu_button": [], "menu_field": []}]