"""
<AUTHOR>
@Date    ：2024/11/28
"""

import datetime
import json
import os
import sys

import django

from dvadmin.utils.update_migrate_serializers import UpdateMigrateInitialize
from dvadmin.system.fixtures.update_tenant_menu_serializer import TenantMenuInitSerializer


# 设置Django环境
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "application.settings.interval_admin")
django.setup()


class MigrateTenantMenu(UpdateMigrateInitialize):
    def __init__(self, menus_folder=None):
        super().__init__(reset=True)  # 调用父类的构造函数
        self.menus_folder = menus_folder
        self.list_data = []

    def read_file(self):
        with open(self.menus_folder, 'r', encoding='utf-8') as f:
            temp_data = f.read()
            data = json.loads(temp_data)['rows']
        self.list_data = data

    def read_all_menus(self):
        # 存储所有读取到的 JSON 数据
        all_data = []

        directory_path = os.path.dirname(os.path.abspath(__file__))
        if not self.menus_folder:
            all_menus = os.path.join(directory_path, 'updated_tenant_menus')
        else:
            all_menus = os.path.join(directory_path, self.menus_folder)
        # 遍历指定文件夹
        for root, dirs, files in os.walk(all_menus):
            for file in files:
                if file.endswith('.json'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            all_data.extend(data)
                            print(f"Read file: {file_path}")
                    except json.JSONDecodeError as e:
                        print(f"Error reading file {file_path}: {e}")
                    except IOError as e:
                        print(f"IOError reading file {file_path}: {e}")

        return all_data

    def init_base(self, Serializer, unique_fields=None, migrated_data=None):
        model = Serializer.Meta.model
        all_data = migrated_data or []
        for data in all_data:
            filter_data = {}
            # 配置过滤条件,如果有唯一标识字段则使用唯一标识字段，否则使用全部字段
            if unique_fields:
                for field in unique_fields:
                    if field in data:
                        filter_data[field] = data[field]
            else:
                for key, value in data.items():
                    if isinstance(value, list) or value == None or value == '':
                        continue
                    filter_data[key] = value
            instance = model.objects.filter(**filter_data).first()
            data["reset"] = self.reset
            print(data)
            serializer = Serializer(instance, data=data, request=self.request)
            serializer.is_valid(raise_exception=True)
            serializer.save()
        print(f"[{self.app}][{model._meta.model_name}]初始化完成")

    def init_menu(self):
        """
        初始化字典表
        """
        migrated_data = self.read_all_menus()
        print(migrated_data)
        self.init_base(
            TenantMenuInitSerializer,
            unique_fields=['name', 'parent', 'web_path'],
            migrated_data=migrated_data
        )

    def format_data(self):
        formatted_data = []
        return formatted_data

    def run_main(self):
        # all_menus = self.read_all_menus()
        self.init_menu()
        # print(all_menus)


if __name__ == '__main__':
    MigrateTenantMenu().run_main()
