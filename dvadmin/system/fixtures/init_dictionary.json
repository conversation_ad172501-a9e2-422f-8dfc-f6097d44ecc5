[{"label": "启用/禁用-布尔值", "value": "button_status_bool", "parent": null, "type": 0, "color": null, "is_value": false, "status": true, "sort": 1, "remark": null, "children": [{"label": "启用", "value": "true", "parent": 1, "type": 6, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "禁用", "value": "false", "parent": 1, "type": 6, "color": "danger", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "系统按钮", "value": "system_button", "parent": null, "type": 0, "color": null, "is_value": false, "status": true, "sort": 2, "remark": null, "children": [{"label": "新增", "value": "Create", "parent": 4, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "编辑", "value": "Update", "parent": 4, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "删除", "value": "Delete", "parent": 4, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "详情", "value": "Retrieve", "parent": 4, "type": 0, "color": "info", "is_value": true, "status": true, "sort": 4, "remark": null, "children": []}, {"label": "查询", "value": "Search", "parent": 4, "type": 0, "color": "warning", "is_value": true, "status": true, "sort": 5, "remark": null, "children": []}, {"label": "保存", "value": "Save", "parent": 4, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 6, "remark": null, "children": []}, {"label": "导入", "value": "Import", "parent": 4, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 7, "remark": null, "children": []}, {"label": "导出", "value": "Export", "parent": 4, "type": 0, "color": "warning", "is_value": true, "status": true, "sort": 8, "remark": null, "children": []}]}, {"label": "启用/禁用-数字值", "value": "button_status_number", "parent": null, "type": 0, "color": null, "is_value": false, "status": true, "sort": 3, "remark": null, "children": [{"label": "启用", "value": "1", "parent": 13, "type": 1, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "禁用", "value": "0", "parent": 13, "type": 1, "color": "danger", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "是/否-布尔值", "value": "button_whether_bool", "parent": null, "type": 0, "color": null, "is_value": false, "status": true, "sort": 4, "remark": null, "children": [{"label": "是", "value": "true", "parent": 16, "type": 6, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "否", "value": "false", "parent": 16, "type": 6, "color": "danger", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "是/否-数字值", "value": "button_whether_number", "parent": null, "type": 0, "color": null, "is_value": false, "status": true, "sort": 5, "remark": null, "children": [{"label": "是", "value": "1", "parent": 19, "type": 1, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "否", "value": "2", "parent": 19, "type": 1, "color": "danger", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "用户类型", "value": "user_type", "parent": null, "type": 0, "color": null, "is_value": false, "status": true, "sort": 6, "remark": null, "children": [{"label": "后台用户", "value": "0", "parent": 22, "type": 1, "color": null, "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "前台用户", "value": "1", "parent": 22, "type": 1, "color": null, "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "表单类型", "value": "config_form_type", "parent": null, "type": 0, "color": null, "is_value": false, "status": true, "sort": 7, "remark": null, "children": [{"label": "text", "value": "0", "parent": 25, "type": 1, "color": null, "is_value": true, "status": true, "sort": 0, "remark": null, "children": []}, {"label": "textarea", "value": "3", "parent": 25, "type": 1, "color": "", "is_value": true, "status": true, "sort": 0, "remark": null, "children": []}, {"label": "number", "value": "10", "parent": 25, "type": 1, "color": "", "is_value": true, "status": true, "sort": 0, "remark": null, "children": []}, {"label": "datetime", "value": "1", "parent": 25, "type": 1, "color": null, "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "date", "value": "2", "parent": 25, "type": 1, "color": null, "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "time", "value": "15", "parent": 25, "type": 1, "color": "", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "select", "value": "4", "parent": 25, "type": 1, "color": null, "is_value": true, "status": true, "sort": 4, "remark": null, "children": []}, {"label": "checkbox", "value": "5", "parent": 25, "type": 1, "color": null, "is_value": true, "status": true, "sort": 5, "remark": null, "children": []}, {"label": "radio", "value": "6", "parent": 25, "type": 1, "color": null, "is_value": true, "status": true, "sort": 6, "remark": null, "children": []}, {"label": "switch", "value": "9", "parent": 25, "type": 1, "color": "", "is_value": true, "status": true, "sort": 6, "remark": null, "children": []}, {"label": "文件附件", "value": "8", "parent": 25, "type": 1, "color": "", "is_value": true, "status": true, "sort": 7, "remark": null, "children": []}, {"label": "图片(单张)", "value": "7", "parent": 25, "type": 1, "color": "", "is_value": true, "status": true, "sort": 8, "remark": null, "children": []}, {"label": "图片(多张)", "value": "12", "parent": 25, "type": 1, "color": "", "is_value": true, "status": true, "sort": 9, "remark": null, "children": []}, {"label": "数组", "value": "11", "parent": 25, "type": 1, "color": "", "is_value": true, "status": true, "sort": 11, "remark": null, "children": []}, {"label": "关联表", "value": "13", "parent": 25, "type": 1, "color": "", "is_value": true, "status": true, "sort": 13, "remark": null, "children": []}, {"label": "关联表(多选)", "value": "14", "parent": 25, "type": 1, "color": "", "is_value": true, "status": true, "sort": 14, "remark": null, "children": []}]}, {"label": "性别", "value": "gender", "parent": null, "type": 0, "color": null, "is_value": false, "status": true, "sort": 8, "remark": null, "children": [{"label": "未知", "value": "0", "parent": 42, "type": 1, "color": null, "is_value": true, "status": true, "sort": 0, "remark": null, "children": []}, {"label": "男", "value": "1", "parent": 42, "type": 1, "color": null, "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "女", "value": "2", "parent": 42, "type": 1, "color": null, "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "岗位状态", "value": "system:post:status", "parent": null, "type": 0, "color": null, "is_value": false, "status": true, "sort": 9, "remark": null, "children": [{"label": "离职", "value": 0, "parent": 43, "type": 1, "color": null, "is_value": true, "status": true, "sort": 0, "remark": null, "children": []}, {"label": "在职", "value": 1, "parent": 44, "type": 1, "color": null, "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}]}, {"label": "行权限-权限类型", "value": "row_permission:permission_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 9, "remark": "行权限管理->权限类型->radio->单选", "children": [{"label": "拒绝", "value": 3, "parent": 1, "type": 1, "color": "info", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "允许", "value": 2, "parent": 1, "type": 1, "color": "success", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "允许所有", "value": 1, "parent": 1, "type": 1, "color": "danger", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}]}, {"label": "包间管理-状态", "value": "private_room:status", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 10, "remark": "资源管理->包间管理->状态", "children": [{"label": "规划中", "value": "planning", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "在建中", "value": "building", "parent": 1, "type": 0, "color": "info", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "使用中", "value": "using", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "已解约", "value": "terminated", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 4, "remark": null, "children": []}]}, {"label": "包间管理-包间类型", "value": "private_room:private_room_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 11, "remark": "资源管理->包间管理->包间类型", "children": [{"label": "业务包间", "value": "business", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "功能包间", "value": "feature", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "IDC机柜管理-网络运营商", "value": "idc_rack_machine:net_operator_business", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 12, "remark": "资源管理->IDC机柜管理->网络运营商", "children": [{"label": "中国移动", "value": "中国移动", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "中国联通", "value": "中国联通", "parent": 1, "type": 0, "color": "info", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "中国电信", "value": "中国电信", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "中国铁通", "value": "中国铁通", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 4, "remark": null, "children": []}]}, {"label": "IDC机柜管理-机柜类型", "value": "idc_rack_machine:rack_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 13, "remark": "资源管理->IDC机柜管理-机柜类型", "children": [{"label": "服务器机柜", "value": "服务器机柜", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "网络机柜", "value": "网络机柜", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "IDC机柜管理-机柜物理形态", "value": "idc_rack_machine:rack_physics_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 14, "remark": "资源管理->IDC机柜管理-机柜物理形态", "children": [{"label": "风冷机柜", "value": "风冷机柜", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "液冷机柜", "value": "液冷机柜", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "IDC机柜管理-网络类型", "value": "idc_rack_machine:network_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 15, "remark": "资源管理->IDC机柜管理->网络类型", "children": [{"label": "10G", "value": "10G", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "20G", "value": "20G", "parent": 1, "type": 0, "color": "info", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "25G", "value": "25G", "parent": 1, "type": 0, "color": "info", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "50G", "value": "50G", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 4, "remark": null, "children": []}, {"label": "100G", "value": "100G", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 5, "remark": null, "children": []}, {"label": "1000G", "value": "1000G", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 6, "remark": null, "children": []}]}, {"label": "IDC机柜管理-模建状态", "value": "idc_rack_machine:construction_status", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 16, "remark": "资源管理->IDC机柜管理->模建状态", "children": [{"label": "已结束", "value": "已结束", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "未规划", "value": "未规划", "parent": 1, "type": 0, "color": "info", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "建设中", "value": "建设中", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}]}, {"label": "IDC机柜管理-上电状态", "value": "idc_rack_machine:power_status", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 17, "remark": "资源管理->IDC机柜管理->上电状态", "children": [{"label": "关", "value": "关", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "开", "value": "开", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "IDC机柜管理-电源类型", "value": "idc_rack_machine:power_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 18, "remark": "资源管理->IDC机柜管理->电源类型", "children": [{"label": "AC220-AC220", "value": "AC220-AC220", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "AC220-HVDC", "value": "AC220-HVDC", "parent": 1, "type": 0, "color": "info", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "HVDC-HVDC", "value": "HVDC-HVDC", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "AC380-AC380", "value": "AC380-AC380", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 4, "remark": null, "children": []}]}, {"label": "IDC机柜管理-电源类型", "value": "idc_rack_machine:power_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 18, "remark": "资源管理->IDC机柜管理->电源类型", "children": [{"label": "AC220-AC220", "value": "AC220-AC220", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "AC220-HVDC", "value": "AC220-HVDC", "parent": 1, "type": 0, "color": "info", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "HVDC-HVDC", "value": "HVDC-HVDC", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "AC380-AC380", "value": "AC380-AC380", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 4, "remark": null, "children": []}]}, {"label": "IDC机柜管理-柜位状态", "value": "idc_rack_machine:rack_status", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 19, "remark": "资源管理->IDC机柜管理->柜位状态", "children": [{"label": "闲置中", "value": "闲置中", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "使用中", "value": "使用中", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "IDC机柜管理-安全域", "value": "idc_rack_machine:security_domain", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 20, "remark": "资源管理->IDC机柜管理->安全域", "children": [{"label": "XZ-PRODUCT", "value": "XZ-PRODUCT", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "XZ-TESTING", "value": "XZ-TESTING", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "IDC机柜管理-逻辑区域", "value": "idc_rack_machine:logical_zone", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 21, "remark": "资源管理->IDC机柜管理->逻辑区域", "children": [{"label": "计算", "value": "计算", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "通用", "value": "通用", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "存储", "value": "存储", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}]}, {"label": "物理资产管理-设备类型", "value": "physical_asset:machine_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 22, "remark": "资源管理->物理资产管理-设备类型", "children": [{"label": "服务器", "value": "服务器", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "网络设备", "value": "网络设备", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "配件管理-资产归属", "value": "physical_asset:belong", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 23, "remark": "资源管理->资产管理-(通用)资产归属", "children": [{"label": "星在科技", "value": "星在科技", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "星载科技", "value": "星载科技", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "综合耗材管理-设备类型", "value": "physical_asset:general_consumable:machine_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 24, "remark": "资源管理->资产管理-耗材管理-设备类型", "children": [{"label": "非成品网线", "value": "非成品网线", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "MPO光缆", "value": "MPO光缆", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "AOC", "value": "AOC", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "光纤跳线", "value": "光纤跳线", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 4, "remark": null, "children": []}, {"label": "配线架", "value": "配线架", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 5, "remark": null, "children": []}, {"label": "转换模块", "value": "转换模块", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 6, "remark": null, "children": []}, {"label": "理线器", "value": "理线器", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 7, "remark": null, "children": []}]}, {"label": "配件管理-设备类型", "value": "physical_asset:holistic_accessory:machine_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 25, "remark": "资源管理->资产管理-配件管理-设备类型", "children": [{"label": "光模块", "value": "光模块", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "硬盘", "value": "硬盘", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "内存条", "value": "内存条", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}]}, {"label": "服务器管理-设备类型", "value": "physical_asset:physical_server_machine:machine_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 26, "remark": "资源管理->资产管理-服务器管理-设备类型", "children": [{"label": "服务器", "value": "服务器", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}]}, {"label": "网络设备管理-设备类型", "value": "physical_asset:network_hardware:machine_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 27, "remark": "资源管理->资产管理-网络设备管理-设备类型", "children": [{"label": "网络设备", "value": "网络设备", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}]}, {"label": "运维管理-主机管理-主机类型", "value": "operator_cmdb:host:host_type", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 28, "remark": "运维管理-主机管理-主机类型", "children": [{"label": "裸金属", "value": "裸金属", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "虚拟机", "value": "虚拟机", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}]}, {"label": "运维管理-主机管理-主机状态", "value": "operator_cmdb:host:host_status", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 29, "remark": "运维管理-主机管理-主机状态", "children": [{"label": "启用", "value": "启用", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "空闲", "value": "空闲", "parent": 1, "type": 0, "color": "info", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "静默", "value": "静默", "parent": 1, "type": 0, "color": "warning", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "下线", "value": "下线", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 4, "remark": null, "children": []}]}, {"label": "运维管理-主机管理-操作系统", "value": "operator_cmdb:host:os", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 29, "remark": "运维管理-主机管理-操作系统", "children": [{"label": "Ubuntu 22.04", "value": "Ubuntu 22.04", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "Ubuntu 20.04", "value": "Ubuntu 20.04", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "CentOS 8", "value": "CentOS 8", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}, {"label": "CentOS 7.9", "value": "CentOS 7.9", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 4, "remark": null, "children": []}, {"label": "Debian 11.8", "value": "Debian 11.8", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 5, "remark": null, "children": []}, {"label": "Rocky 8.9", "value": "Rocky 8.9", "parent": 1, "type": 0, "color": "primary", "is_value": true, "status": true, "sort": 6, "remark": null, "children": []}]}, {"label": "运维管理-主机管理-GPU型号", "value": "operator_cmdb:host:gpu_model", "parent": null, "type": 0, "color": "#409EFF", "is_value": false, "status": true, "sort": 29, "remark": "运维管理-主机管理-GPU型号", "children": [{"label": "A800-<PERSON>le", "value": "A800-<PERSON>le", "parent": 1, "type": 0, "color": "danger", "is_value": true, "status": true, "sort": 1, "remark": null, "children": []}, {"label": "4090", "value": "4090", "parent": 1, "type": 0, "color": "warning", "is_value": true, "status": true, "sort": 2, "remark": null, "children": []}, {"label": "3090", "value": "3090", "parent": 1, "type": 0, "color": "success", "is_value": true, "status": true, "sort": 3, "remark": null, "children": []}]}]