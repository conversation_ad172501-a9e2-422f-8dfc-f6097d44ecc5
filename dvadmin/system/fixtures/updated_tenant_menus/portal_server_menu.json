[{"name": "云产品及服务", "icon": "ele-<PERSON>y", "sort": 6, "is_link": false, "is_catalog": true, "web_path": "/tenant/producter_and_services", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "弹性计算", "icon": "iconfont icon-LoggedinPC", "sort": 1, "is_link": false, "is_catalog": true, "web_path": "/tenant/producter_and_services/elasti-computes", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "裸金属", "icon": "iconfont icon-di<PERSON><PERSON>-shu<PERSON>", "sort": 1, "is_link": false, "is_catalog": false, "web_path": "/tenant/producter_and_services/elasti-computes/baremetal", "component": "portalTenants/portal/portalServer/index", "component_name": "裸金属主机列表", "status": true, "cache": false, "visible": true, "parent": null, "children": [], "menu_button": [{"name": "查询", "value": "tenant:baremetal:Search", "api": "/api/tenants/tenant-op-server/", "method": 0}, {"name": "查看", "value": "tenant:baremetal:Retrieve", "api": "/api/tenants/tenant-op-server/{id}/", "method": 0}, {"name": "添加", "value": "tenant:baremetal:<PERSON><PERSON>", "api": "/api/tenants/create_baremetal_server/", "method": 1}, {"name": "编辑", "value": "tenant:baremetal:Update", "api": "/api/tenants/tenant-op-server/{id}/", "method": 2}, {"name": "删除", "value": "tenant:baremetal:Delete", "api": "/api/tenants/tenant-op-server/{id}/", "method": 3}, {"name": "导入", "value": "tenant:baremetal:Import", "api": "/api/tenants/tenant-op-server/import_data/", "method": 1}, {"name": "获取规格", "value": "tenant:baremetal:FlavorSearch", "api": "/api/tenants/tenant-op-flavor/", "method": 0}, {"name": "获取镜像", "value": "tenant:baremetal:ImageSearch", "api": "/api/tenants/tenant-op-image/", "method": 0}, {"name": "获取账号", "value": "tenant:baremetal:TenantAccountSearch", "api": "/api/tenants/tenant-account/", "method": 0}, {"name": "获取项目", "value": "tenant:baremetal:TenantProjectSearch", "api": "/api/tenants/tenant-project/", "method": 0}, {"name": "获取网络", "value": "tenant:baremetal:TenantNetworkSearch", "api": "/api/tenants/tenant-op-network/", "method": 0}, {"name": "获取安全组", "value": "tenant:baremetal:TenantSecurityGroupSearch", "api": "/api/tenants/tenant-op-security-group/", "method": 0}, {"name": "获取裸金属主机", "value": "tenant:baremetal:TenantBaremetalServerSearch", "api": "/api/tenants/tenant-op-server/", "method": 0}, {"name": "获取订单列表", "value": "tenant:baremetal:TenantListTicket", "api": "/api/ticket/ticket/", "method": 0}, {"name": "关机", "value": "tenant:baremetal:StopServer", "api": "/api/tenants/tenant-op-server/{id}/stop_server/", "method": 1}, {"name": "开机", "value": "tenant:baremetal:StartServer", "api": "/api/tenants/tenant-op-server/{id}/start_server/", "method": 1}, {"name": "编辑实例", "value": "tenant:baremetal:updateServerNameOrDescription", "api": "/api/tenants/tenant-op-server/{id}/update_server_name_or_desc/", "method": 1}, {"name": "重启", "value": "tenant:baremetal:Rebooterver", "api": "/api/tenants/tenant-op-server/{id}/reboot_server/", "method": 1}, {"name": "重建实例", "value": "tenant:baremetal:RebuildServer", "api": "/api/tenants/tenant-op-server/{id}/rebuild_server/", "method": 1}], "menu_field": []}], "menu_button": [], "menu_field": []}], "menu_button": [], "menu_field": []}]