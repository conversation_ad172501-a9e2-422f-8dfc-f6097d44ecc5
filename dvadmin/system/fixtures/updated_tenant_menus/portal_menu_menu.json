[{"name": "订单管理", "icon": "iconfont icon-zidingyibuju", "sort": 8, "is_link": false, "is_catalog": true, "web_path": "/workerTIcketOrder", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "我的订单", "icon": "iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 3, "is_link": false, "is_catalog": false, "web_path": "/workerTIcketOrder/ticket/myself", "component": "ticket/ticket/myTicket/index", "component_name": "我的订单", "status": true, "cache": false, "visible": true, "parent": null, "children": [], "menu_button": [{"name": "查询", "value": "ticket:myselfTicket:Search", "api": "/api/ticket/ticket/", "method": 0}, {"name": "查看", "value": "ticket:myselfTicket:Retrieve", "api": "/api/ticket/ticket/{id}/", "method": 0}, {"name": "添加", "value": "ticket:myselfTicket:Create", "api": "/api/ticket/ticket/", "method": 1}, {"name": "编辑", "value": "ticket:myselfTicket:Update", "api": "/api/ticket/ticket/{id}/", "method": 2}, {"name": "删除", "value": "ticket:myselfTicket:Delete", "api": "/api/ticket/ticket/{id}/", "method": 3}, {"name": "查看报告", "value": "ticket:myselfTicket:viewReportTicket", "api": "/api/ticket/ticket/get_current_ticket_tasks/", "method": 0}], "menu_field": []}, {"name": "裸金属服务器订单申请", "icon": "iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 4, "is_link": false, "is_catalog": false, "web_path": "/workerTIcketOrder/ticket/myself", "component": "ticket/ticket/myTicket/index", "component_name": "裸金属服务器订单申请", "status": true, "cache": false, "visible": false, "parent": null, "children": [], "menu_button": [{"name": "添加", "value": "ticket:createBaremetalServerTicket:Create", "api": "/api/ticket/ticket/", "method": 1}, {"name": "获取规格", "value": "ticket:createBaremetalServerTicket:ListFlavor", "api": "/api/tenants/tenant-op-flavor/", "method": 0}, {"name": "获取镜像", "value": "ticket:createBaremetalServerTicket:ListImage", "api": "/api/tenants/tenant-op-image/", "method": 0}], "menu_field": []}, {"name": "裸金属服务器订单报告", "icon": "iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 6, "is_link": false, "is_catalog": false, "web_path": "/portalTenants/ticket/:ticket_id/:step/reportResult", "component": "ticket/ticket/resultReport", "component_name": "裸金属服务器订单报告", "status": true, "cache": false, "visible": false, "parent": null, "children": [], "menu_button": [{"name": "获取规格", "value": "ticket:ticketResultReport:FlavorSearch", "api": "/api/tenants/tenant-op-flavor/", "method": 0}, {"name": "获取镜像", "value": "ticket:ticketResultReport:ImageSearch", "api": "/api/tenants/tenant-op-image/", "method": 0}, {"name": "获取账号", "value": "ticket:ticketResultReport:TenantAccountSearch", "api": "/api/tenants/tenant-account/", "method": 0}, {"name": "获取项目", "value": "ticket:ticketResultReport:TenantProjectSearch", "api": "/api/tenants/tenant-project/", "method": 0}, {"name": "获取网络", "value": "ticket:ticketResultReport:TenantNetworkSearch", "api": "/api/tenants/tenant-op-network/", "method": 0}, {"name": "获取安全组", "value": "ticket:ticketResultReport:TenantSecurityGroupSearch", "api": "/api/tenants/tenant-op-security-group/", "method": 0}, {"name": "获取裸金属主机", "value": "ticket:ticketResultReport:TenantBaremetalServerSearch", "api": "/api/tenants/tenant-op-server/", "method": 0}], "menu_field": []}], "menu_button": [], "menu_field": []}]