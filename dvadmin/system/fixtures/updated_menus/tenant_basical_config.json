[{"name": "云资源池", "icon": "iconfont icon-shuju1", "sort": 6, "is_link": false, "is_catalog": true, "web_path": "/tenants/cloud-resources", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "网络配置", "icon": "iconfont icon-xing<PERSON>u", "sort": 1, "is_link": false, "is_catalog": true, "web_path": "/tenants/cloud-resources/network-vlan", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "Vlan配置", "icon": "iconfont icon-dongtai", "sort": 1, "is_link": false, "is_catalog": false, "web_path": "/tenants/cloud-resources/network-vlan/phy-vlans", "component": "portalTenants/tenantOpenstackVlan/index", "component_name": "Vlan配置", "status": true, "cache": false, "visible": true, "parent": null, "children": [], "menu_button": [{"name": "查询", "value": "tenant:adminPhysicalVlan:Search", "api": "/api/tenants/tenant-op-vlan/", "method": 0}, {"name": "查看", "value": "tenant:adminPhysicalVlan:Retrieve", "api": "/api/tenants/tenant-op-vlan/{id}/", "method": 0}, {"name": "添加", "value": "tenant:adminPhysicalVlan:Create", "api": "/api/tenants/tenant-op-vlan/", "method": 1}, {"name": "编辑", "value": "tenant:adminPhysicalVlan:Update", "api": "/api/tenants/tenant-op-vlan/{id}/", "method": 2}, {"name": "删除", "value": "tenant:adminPhysicalVlan:Delete", "api": "/api/tenants/tenant-op-vlan/{id}/", "method": 3}, {"name": "批量删除", "value": "tenant:adminPhysicalVlan:MultipleDelete", "api": "/api/tenants/tenant-op-vlan/multiple_delete/", "method": 3}, {"name": "导入", "value": "tenant:adminPhysicalVlan:Import", "api": "/api/tenants/tenant-op-vlan/import_data/", "method": 1}, {"name": "创建Openstack物理网络Vlan", "value": "tenant:adminPhysicalVlan:createOpenstackVlanNetwork", "api": "/api/tenants/tenant-op-vlan/create_openstack_physical_vlan_network/", "method": 1}], "menu_field": []}], "menu_button": [], "menu_field": []}, {"name": "基础配置", "icon": "iconfont icon-wenducanshu-05", "sort": 2, "is_link": false, "is_catalog": true, "web_path": "/tenants/openstack-settings/", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "openstack配置", "icon": "iconfont icon-shen<PERSON><PERSON><PERSON>", "sort": 1, "is_link": false, "is_catalog": false, "web_path": "/tenants/openstack-settings/settings", "component": "portalTenants/tenantOpenstackSetting/index", "component_name": "openstack配置", "status": true, "cache": false, "visible": true, "parent": null, "children": [], "menu_button": [{"name": "查询", "value": "tenant:adminOpenstackSettings:Search", "api": "/api/tenants/tenant-op-setting/", "method": 0}, {"name": "查看", "value": "tenant:adminOpenstackSettings:Retrieve", "api": "/api/tenants/tenant-op-setting/{id}/", "method": 0}, {"name": "添加", "value": "tenant:adminOpenstackSettings:Create", "api": "/api/tenants/tenant-op-setting/", "method": 1}, {"name": "编辑", "value": "tenant:adminOpenstackSettings:Update", "api": "/api/tenants/tenant-op-setting/{id}/", "method": 2}, {"name": "删除", "value": "tenant:adminOpenstackSettings:Delete", "api": "/api/tenants/tenant-op-setting/{id}/", "method": 3}, {"name": "批量删除", "value": "tenant:adminOpenstackSettings:MultipleDelete", "api": "/api/tenants/tenant-op-setting/multiple_delete/", "method": 3}, {"name": "导入", "value": "tenant:adminOpenstackSettings:Import", "api": "/api/tenants/tenant-op-setting/import_data/", "method": 1}], "menu_field": []}, {"name": "openstack项目", "icon": "iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>qi", "sort": 2, "is_link": false, "is_catalog": false, "web_path": "/tenants/openstack-settings/openstack-project", "component": "portalTenants/tenantProject/index", "component_name": "openstack项目", "status": true, "cache": false, "visible": true, "parent": null, "children": [], "menu_button": [{"name": "查询", "value": "tenant:adminOpenstackProject:Search", "api": "/api/tenants/tenant-project/", "method": 0}, {"name": "查看", "value": "tenant:adminOpenstackProject:Retrieve", "api": "/api/tenants/tenant-project/{id}/", "method": 0}, {"name": "添加", "value": "tenant:adminOpenstackProject:Create", "api": "/api/tenants/tenant-project/", "method": 1}, {"name": "编辑", "value": "tenant:adminOpenstackProject:Update", "api": "/api/tenants/tenant-project/{id}/", "method": 2}, {"name": "删除", "value": "tenant:adminOpenstackProject:Delete", "api": "/api/tenants/tenant-project/{id}/", "method": 3}, {"name": "批量删除", "value": "tenant:adminOpenstackProject:MultipleDelete", "api": "/api/tenants/tenant-project/multiple_delete/", "method": 3}, {"name": "导入", "value": "tenant:adminOpenstackProject:Import", "api": "/api/tenants/tenant-project/import_data/", "method": 1}], "menu_field": []}], "menu_button": [], "menu_field": []}], "menu_button": [], "menu_field": []}]