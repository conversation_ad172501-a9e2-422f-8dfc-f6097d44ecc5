[{"name": "订单管理", "icon": "iconfont icon-zidingyibuju", "sort": 8, "is_link": false, "is_catalog": true, "web_path": "/workerTIcketOrder", "component": "", "component_name": "", "status": true, "cache": false, "visible": true, "parent": null, "children": [{"name": "订单列表", "icon": "iconfont icon--<PERSON><PERSON><PERSON><PERSON>", "sort": 1, "is_link": false, "is_catalog": false, "web_path": "/workerTIcketOrder/ticket/list", "component": "ticket/ticket/index", "component_name": "订单列表", "status": true, "cache": false, "visible": true, "parent": null, "children": [], "menu_button": [{"name": "查询", "value": "ticket:ticket:Search", "api": "/api/ticket/ticket/", "method": 0}, {"name": "查看", "value": "ticket:ticket:Retrieve", "api": "/api/ticket/ticket/{id}/", "method": 0}, {"name": "添加", "value": "ticket:ticket:Create", "api": "/api/ticket/ticket/", "method": 1}, {"name": "编辑", "value": "ticket:ticket:Update", "api": "/api/ticket/ticket/{id}/", "method": 2}, {"name": "删除", "value": "ticket:ticket:Delete", "api": "/api/ticket/ticket/{id}/", "method": 3}, {"name": "导入", "value": "ticket:ticket:Import", "api": "/api/ticket/ticket/import_data/", "method": 1}, {"name": "审批订单", "value": "ticket:ticket:viewApprovalTicket", "api": "/api/ticket/ticket/approval_ticket_task/", "method": 1}, {"name": "查看报告", "value": "ticket:ticket:viewReportTicket", "api": "/api/ticket/ticket/get_current_ticket_tasks/", "method": 0}], "menu_field": []}, {"name": "我的待办", "icon": "iconfont icon-ca<PERSON>an", "sort": 2, "is_link": false, "is_catalog": false, "web_path": "/workerTIcketOrder/ticket/myself/toDoList", "component": "ticket/ticket/myTicketToDoList/index", "component_name": "我的代办", "status": true, "cache": false, "visible": true, "parent": null, "children": [], "menu_button": [{"name": "查询", "value": "ticket:myselfTicketToDoList:Search", "api": "/api/ticket/ticket/", "method": 0}, {"name": "查看", "value": "ticket:myselfTicketToDoList:Retrieve", "api": "/api/ticket/ticket/{id}/", "method": 0}, {"name": "添加", "value": "ticket:myselfTicketToDoList:Create", "api": "/api/ticket/ticket/", "method": 1}, {"name": "编辑", "value": "ticket:myselfTicketToDoList:Update", "api": "/api/ticket/ticket/{id}/", "method": 2}, {"name": "删除", "value": "ticket:myselfTicketToDoList:Delete", "api": "/api/ticket/ticket/{id}/", "method": 3}, {"name": "导入", "value": "ticket:myselfTicketToDoList:Import", "api": "/api/ticket/ticket/import_data/", "method": 1}, {"name": "审批订单", "value": "ticket:myselfTicketToDoList:viewApprovalTicket", "api": "/api/ticket/ticket/approval_ticket_task/", "method": 1}, {"name": "查看报告", "value": "ticket:myselfTicketToDoList:viewReportTicket", "api": "/api/ticket/ticket/get_current_ticket_tasks/", "method": 0}], "menu_field": []}, {"name": "我的订单", "icon": "iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 3, "is_link": false, "is_catalog": false, "web_path": "/workerTIcketOrder/ticket/myself", "component": "ticket/ticket/myTicket/index", "component_name": "我的订单", "status": true, "cache": false, "visible": true, "parent": null, "children": [], "menu_button": [{"name": "查询", "value": "ticket:myselfTicket:Search", "api": "/api/ticket/ticket/", "method": 0}, {"name": "查看", "value": "ticket:myselfTicket:Retrieve", "api": "/api/ticket/ticket/{id}/", "method": 0}, {"name": "添加", "value": "ticket:myselfTicket:Create", "api": "/api/ticket/ticket/", "method": 1}, {"name": "编辑", "value": "ticket:myselfTicket:Update", "api": "/api/ticket/ticket/{id}/", "method": 2}, {"name": "删除", "value": "ticket:myselfTicket:Delete", "api": "/api/ticket/ticket/{id}/", "method": 3}, {"name": "导入", "value": "ticket:myselfTicket:Import", "api": "/api/ticket/ticket/import_data/", "method": 1}, {"name": "审批订单", "value": "ticket:myselfTicket:viewApprovalTicket", "api": "/api/ticket/ticket/approval_ticket_task/", "method": 1}, {"name": "查看报告", "value": "ticket:myselfTicket:viewReportTicket", "api": "/api/ticket/ticket/get_current_ticket_tasks/", "method": 0}], "menu_field": []}, {"name": "裸金属服务器订单申请", "icon": "iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 4, "is_link": false, "is_catalog": false, "web_path": "/workerTIcketOrder/ticket/myself", "component": "ticket/ticket/myTicket/index", "component_name": "裸金属服务器订单申请", "status": true, "cache": false, "visible": false, "parent": null, "children": [], "menu_button": [{"name": "添加", "value": "ticket:createBaremetalServerTicket:Create", "api": "/api/ticket/ticket/", "method": 1}, {"name": "获取账号", "value": "ticket:createBaremetalServerTicket:ListAccount", "api": "/api/tenants/tenant-account/", "method": 0}, {"name": "获取规格", "value": "ticket:createBaremetalServerTicket:ListFlavor", "api": "/api/tenants/tenant-op-flavor/", "method": 0}, {"name": "获取镜像", "value": "ticket:createBaremetalServerTicket:ListImage", "api": "/api/tenants/tenant-op-image/", "method": 0}], "menu_field": []}, {"name": "裸金属服务器订单审批", "icon": "iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 5, "is_link": false, "is_catalog": false, "web_path": "/portalTenants/ticket/:ticket_id/:step", "component": "ticket/ticket/step1ApprovalTicket", "component_name": "裸金属服务器订单审批", "status": true, "cache": false, "visible": false, "parent": null, "children": [], "menu_button": [{"name": "获取规格", "value": "ticket:step1ApprovalTicket:FlavorSearch", "api": "/api/tenants/tenant-op-flavor/", "method": 0}, {"name": "获取镜像", "value": "ticket:step1ApprovalTicket:ImageSearch", "api": "/api/tenants/tenant-op-image/", "method": 0}, {"name": "获取账号", "value": "ticket:step1ApprovalTicket:TenantAccountSearch", "api": "/api/tenants/tenant-account/", "method": 0}, {"name": "获取项目", "value": "ticket:step1ApprovalTicket:TenantProjectSearch", "api": "/api/tenants/tenant-project/", "method": 0}, {"name": "获取网络", "value": "ticket:step1ApprovalTicket:TenantNetworkSearch", "api": "/api/tenants/tenant-op-network/", "method": 0}, {"name": "获取安全组", "value": "ticket:step1ApprovalTicket:TenantSecurityGroupSearch", "api": "/api/tenants/tenant-op-security-group/", "method": 0}, {"name": "获取裸金属主机", "value": "ticket:step1ApprovalTicket:TenantBaremetalServerSearch", "api": "/api/tenants/tenant-op-server/", "method": 0}, {"name": "审批", "value": "ticket:step1ApprovalTicket:TenantBaremetalServerApproval", "api": "/api/ticket/ticket/approval_ticket_task/", "method": 1}, {"name": "获取订单信息", "value": "ticket:step1ApprovalTicket:TenantBaremetalServerGetTicketInfo", "api": "/api/ticket/ticket/get_current_ticket_tasks/", "method": 0}], "menu_field": []}, {"name": "裸金属服务器订单报告", "icon": "iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 6, "is_link": false, "is_catalog": false, "web_path": "/portalTenants/ticket/:ticket_id/:step/reportResult", "component": "ticket/ticket/resultReport", "component_name": "裸金属服务器订单报告", "status": true, "cache": false, "visible": false, "parent": null, "children": [], "menu_button": [{"name": "获取规格", "value": "ticket:ticketResultReport:FlavorSearch", "api": "/api/tenants/tenant-op-flavor/", "method": 0}, {"name": "获取镜像", "value": "ticket:ticketResultReport:ImageSearch", "api": "/api/tenants/tenant-op-image/", "method": 0}, {"name": "获取账号", "value": "ticket:ticketResultReport:TenantAccountSearch", "api": "/api/tenants/tenant-account/", "method": 0}, {"name": "获取项目", "value": "ticket:ticketResultReport:TenantProjectSearch", "api": "/api/tenants/tenant-project/", "method": 0}, {"name": "获取网络", "value": "ticket:ticketResultReport:TenantNetworkSearch", "api": "/api/tenants/tenant-op-network/", "method": 0}, {"name": "获取安全组", "value": "ticket:ticketResultReport:TenantSecurityGroupSearch", "api": "/api/tenants/tenant-op-security-group/", "method": 0}, {"name": "获取裸金属主机", "value": "ticket:ticketResultReport:TenantBaremetalServerSearch", "api": "/api/tenants/tenant-op-server/", "method": 0}, {"name": "修复完成确认", "value": "ticket:ticketResultReport:TenantBaremetalServerManualFixed", "api": "/api/ticket/ticket/update_ticket_status/", "method": 1}, {"name": "完成订单", "value": "ticket:ticketResultReport:TenantBaremetalServerManualFinished", "api": "/api/ticket/ticket/update_ticket_status/", "method": 1}, {"name": "生成报告", "value": "ticket:ticketResultReport:TenantBaremetalServerManualGenReport", "api": "/api/ticket/ticket/update_ticket_status/", "method": 1}], "menu_field": []}], "menu_button": [], "menu_field": []}]