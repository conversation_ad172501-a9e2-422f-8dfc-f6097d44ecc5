"""
迁移 OPA 旧主机信息

"""
import os

import django

from django.core.management import BaseCommand


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()


from operatorcmdb.utils.manual_migrates.migrate_operatorcmdb_hosts import MigrateHosts


def main():
    MigrateHosts().run_main()


class Command(BaseCommand):
    """
    项目初始化命令: python manage.py init
    """

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):

        print(f"正在准备迁移SQL文件...")

        main()
        print("数据迁移完成！")
