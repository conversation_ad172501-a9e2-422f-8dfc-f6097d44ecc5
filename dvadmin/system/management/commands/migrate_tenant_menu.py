"""
<AUTHOR>
@Date    ：2024/11/29
迁移 新 菜单配置
"""

import os

import django

from django.core.management import BaseCommand


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
django.setup()


from dvadmin.system.fixtures.update_tenant_menu import MigrateTenantMenu


def main():
    MigrateTenantMenu().run_main()


class Command(BaseCommand):
    """
    项目初始化命令: python manage.py init
    """

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):

        print(f"正在准备迁移菜单文件...")

        main()
        print("数据迁移完成！")
