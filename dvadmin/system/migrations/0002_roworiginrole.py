# Generated by Django 5.0.6 on 2024-08-01 17:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RowOriginRole',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('row_origin', models.CharField(db_comment='资源类型', db_index=True, help_text='资源类型', max_length=255, null=True, verbose_name='资源类型')),
                ('is_external', models.BooleanField(blank=True, db_comment='是否为外部数据', default=False, help_text='是否为外部数据', verbose_name='是否为外部数据')),
                ('is_open_row_permission', models.BooleanField(db_comment='是否开启行权限', default=False, help_text='是否开启行权限', verbose_name='是否开启行权限')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('role', models.ForeignKey(db_comment='角色ID', help_text='角色ID', max_length=63, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='row_origin_role', to='system.role', verbose_name='角色ID')),
            ],
            options={
                'verbose_name': '资源类型角色表',
                'verbose_name_plural': '资源类型角色表',
                'db_table': 'chaos_system_row_origin_role',
            },
        ),
    ]
