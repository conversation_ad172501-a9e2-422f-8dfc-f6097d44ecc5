# Generated by Django 5.0.6 on 2024-09-24 16:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0004_role_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('resource_id', models.CharField(db_comment='资源ID', db_index=True, default=None, help_text='资源ID', max_length=63, verbose_name='资源ID')),
                ('old_values', models.JSONField(blank=True, db_comment='变更信息旧值', default=None, help_text='变更信息旧值', null=True, verbose_name='变更信息旧值')),
                ('new_values', models.JSONField(blank=True, db_comment='变更信息新值', default=None, help_text='变更信息新值', null=True, verbose_name='变更信息新值')),
                ('action', models.CharField(blank=True, db_comment='动作', default=None, help_text='动作', max_length=63, verbose_name='动作')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '资源变更日志表',
                'verbose_name_plural': '资源变更日志表',
                'db_table': 'chaos_system_audit_logs',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
