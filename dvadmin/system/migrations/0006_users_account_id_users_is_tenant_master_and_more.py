# Generated by Django 5.0.6 on 2024-11-05 15:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0005_auditlog'),
    ]

    operations = [
        migrations.AddField(
            model_name='users',
            name='account_id',
            field=models.CharField(blank=True, db_comment='关联的tenant子账号ID', help_text='关联的tenant子账号ID', max_length=255, null=True, verbose_name='关联的tenant子账号ID'),
        ),
        migrations.AddField(
            model_name='users',
            name='is_tenant_master',
            field=models.BooleanField(blank=True, db_comment='是否为tenant主账号', default=False, help_text='是否为tenant主账号', null=True, verbose_name='是否为tenant主账号'),
        ),
        migrations.AddField(
            model_name='users',
            name='tenant_id',
            field=models.CharField(blank=True, db_comment='tenant唯一标识ID', help_text='tenant唯一标识ID', max_length=255, null=True, verbose_name='tenant唯一标识ID'),
        ),
    ]
