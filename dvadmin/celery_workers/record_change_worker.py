from application.celery import celery
from application.logger import logging_func_time
from dvadmin.utils.chaos_record_changes import ChaosRecordChanges


@celery.task
@logging_func_time
def record_changes(model_info=None, user_info=None, previous_instance=None, current_instance=None):
    ChaosRecordChanges(model_info, user_info, previous_instance, current_instance).record_changes()


@celery.task
@logging_func_time
def record_log_action(action, user_info, resource_id, changes=None):
    ChaosRecordChanges.log_action(action, user_info, resource_id, changes)
