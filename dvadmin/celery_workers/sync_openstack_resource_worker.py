"""
<AUTHOR>
@Date    ：2024/11/19
"""

from application.celery import celery
from application.logger import logging_func_time
from tenant.serializers.tenant_openstack_image import sync_tenant_openstack_image
from tenant.serializers.tenant_openstack_flavor import sync_tenant_openstack_flavor
from tenant.serializers.tenant_openstack_ironic_hypervisor import sync_tenant_openstack_ironic_hypervisor
from tenant.views.tenant_openstack_ironic_register import multi_register_baremetal_node
from tenant.serializers.tenant_openstack_project import sync_tenant_openstack_project
from tenant.serializers.tenant_openstack_network import sync_tenant_openstack_network
from tenant.serializers.tenant_openstack_security_group import sync_tenant_openstack_security_group
from tenant.serializers.tenant_openstack_server import (
    sync_tenant_openstack_server,
    wait_for_server_finished,
    single_sync_openstack_server,
    async_multi_create_baremetal_servers,
)
from tenant.serializers.tenant_openstack_server import tenant_openstack_server_to_operator_cmdb_hosts
from tenant.serializers.tenant_openstack_vlan import wait_physical_vlan_network_create_finished
from tenant.utils.sdk.openstack_client import OpenstackAdminClient
from ticket.serializers.task import (
    wait_for_ticket_finished,
    ticket_assign_public_ip,
)
from ticket.serializers.ticket import ticket_create_baremetal_server_task
from tenant.serializers.tenant_openstack_vlan import single_create_openstack_physical_vlan_network
from tenant.serializers.tenant_openstack_ironic_hypervisor import sync_tenant_openstack_ironic_hypervisor_to_operator_cmdb_hosts


@celery.task
@logging_func_time
def sync_tenant_openstack_image_worker(nodes: list = None):
    sync_tenant_openstack_image(nodes =  nodes)


@celery.task
@logging_func_time
def sync_tenant_openstack_flavor_worker(nodes: list = None):
    sync_tenant_openstack_flavor(nodes =  nodes)


@celery.task
@logging_func_time
def sync_tenant_openstack_ironic_hypervisor_worker(nodes: list = None):
    sync_tenant_openstack_ironic_hypervisor(nodes=nodes)

@celery.task
@logging_func_time
def sync_multi_register_baremetal_node_worker(register_ids=None):
    multi_register_baremetal_node(register_ids=register_ids or [])

@celery.task
@logging_func_time
def sync_tenant_openstack_project_worker(nodes: list = None):
    sync_tenant_openstack_project(nodes = nodes)


@celery.task
@logging_func_time
def sync_tenant_openstack_network_worker(nodes: any = None):
    sync_tenant_openstack_network(nodes = nodes)


@celery.task
@logging_func_time
def sync_tenant_openstack_security_group_worker(nodes: any = None):
    sync_tenant_openstack_security_group(nodes = nodes)


@celery.task
@logging_func_time
def sync_tenant_openstack_server_worker(nodes: list = None):
    sync_tenant_openstack_server(nodes = nodes)


@celery.task
@logging_func_time
def wait_for_tenant_openstack_server_finished_worker(instance_id, node=None):
    wait_for_server_finished(instance_id=instance_id, node=node)


@celery.task
@logging_func_time
def single_sync_openstack_server_worker(instance_id, expire_at=None, belong_ticket_id=None, node=None):
    single_sync_openstack_server(instance_id=instance_id, expire_at=expire_at, belong_ticket_id=belong_ticket_id,
                                 node=node)


@celery.task
@logging_func_time
def single_wait_openstack_physical_vlan_network_create_finished_worker(physical_network_search_data, node=None):
    wait_physical_vlan_network_create_finished(physical_network_search_data=physical_network_search_data, node=node)


@celery.task
@logging_func_time
def sync_openstack_physical_vlan_network_create_worker(created_data, node=None):
    """异步创建物理网络（不包括子网创建任务）"""
    admin_client = OpenstackAdminClient(node=node)
    admin_client.create_signal_vlan_network(**created_data)


@celery.task
@logging_func_time
def async_single_create_openstack_physical_vlan_network_worker(
    admin_client, system_physical_vlan_network_id,
    timeout_seconds_range, node,
):
    single_create_openstack_physical_vlan_network(
        admin_client=admin_client,
        system_physical_vlan_network_id=system_physical_vlan_network_id,
        timeout_seconds_range=timeout_seconds_range,
        node=node,  # 任务所属的节点
    )

@celery.task
@logging_func_time
def wait_for_ticket_finished_worker(task_id):
    wait_for_ticket_finished(task_id)


@celery.task
@logging_func_time
def ticket_create_baremetal_server_task_worker(task_id, form_data):
    ticket_create_baremetal_server_task(task_id=task_id, form_data=form_data)


@celery.task
@logging_func_time
def async_multi_create_baremetal_servers_worker(data):
    async_multi_create_baremetal_servers(data)


@celery.task
@logging_func_time
def async_multi_create_baremetal_servers_worke1111(data):
    async_multi_create_baremetal_servers(data)


@celery.task
@logging_func_time
def async_openstack_server_to_operator_cmdb_host_worker(server_id):
    tenant_openstack_server_to_operator_cmdb_hosts(server_id=server_id)


@celery.task
@logging_func_time
def ticket_assign_public_ip_worker(ticket_id):
    ticket_assign_public_ip(ticket_id)


@celery.task
@logging_func_time
def async_tenant_openstack_ironic_hypervisor_to_operator_cmdb_hosts_worker(
        node=None,
        op_ironic_hypervisor_id=None,
        op_ironic_hypervisor_data=None,
        is_wait=False,
    ):
    sync_tenant_openstack_ironic_hypervisor_to_operator_cmdb_hosts(
        node=node,
        op_ironic_hypervisor_id=op_ironic_hypervisor_id,
        op_ironic_hypervisor_data=op_ironic_hypervisor_data,
        is_wait=is_wait,
        )
