from application.celery import celery
from application.logger import logging_func_time
from contract.serializers.contract import (
  check_contract_expired,
  check_contract_is_archive,
  check_contract_is_sign
)
from contract.serializers.payment_period_history import (
  check_payment_period_overdue,
  check_payment_period_collection_current
)

@celery.task
@logging_func_time
def check_contract_expired_worker():
    check_contract_expired()

@celery.task
@logging_func_time
def check_contract_is_archive_worker():
    check_contract_is_archive()

@celery.task
@logging_func_time
def check_contract_is_sign_worker():
    check_contract_is_sign()

@celery.task
@logging_func_time
def check_payment_period_overdue_worker():
    check_payment_period_overdue()

@celery.task
@logging_func_time
def check_payment_period_collection_current_worker():
    check_payment_period_collection_current()