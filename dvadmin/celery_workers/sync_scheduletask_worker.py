"""
<AUTHOR>
@Date    ：2024/12/16
"""
import time
import json,os
import tempfile
import celery as cele

from application.celery import celery
from application.logger import logging_func_time,logger
from scheduletask.config.const import *
from scheduletask.models import Work
from tenant.models import TenantOPServerSoftware
from ticket.models import Ticket
from scheduletask.utils.ansible_runner_handler import AnsibleRunnerWrapper


class BaseTask(cele.Task):
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        work_id = args[0]['id']
        Work.objects.filter(id=work_id).update(status=3)


@celery.task(base=BaseTask)
@logging_func_time
def run(data):
    try:
        logger.info(f"【开始消费任务】任务详情 : {data}")
        work_id = data['id']
        inventory_data = data['inventory']
        ticket_id = None
        creator_id = None
        # 创建一个唯一的临时 JSON 文件
        temp_inv_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        with open(temp_inv_file.name, "w") as f:
            json.dump(inventory_data, f)

        logger.info(f"【生成inventory临时文件】: {temp_inv_file.name}")
        ansible_runner_obj = AnsibleRunnerWrapper(inventory=temp_inv_file.name, private_data_dir=data['private_data_dir'])
        ansible_runner_obj.run_async()

        Work.objects.filter(id=work_id).update(status=1)

        # 输出日志路径
        dormant_stdout_time,dormat_work_time= 0,0
        while not ansible_runner_obj.stdout_path:
            if dormant_stdout_time >= CHAOS_ANSIBLE_TIMEOUT:
                raise RuntimeError("Get the log path timed out...")
            time.sleep(1)
            dormant_stdout_time += 1
        
        Work.objects.filter(id=work_id).update(stdout_path=ansible_runner_obj.stdout_path)

        # 当订单过来的任务 => 更改创建人信息
        # 1. 获取当前work的tenant => ticket_id
        # 2. 根据ticket_id => creator_id
        # 3. 更新work
        tenant_soft_query = TenantOPServerSoftware.objects.filter(scheduletask_work_id=work_id)
        logger.info(f"【消费任务中】软件详情 : {tenant_soft_query}")
        if tenant_soft_query:
          ticket_id = tenant_soft_query[0].ticket_id
          creator_id = Ticket.objects.get(ticket_id=ticket_id).creator_id
          Work.objects.filter(id=work_id).update(creator_id=creator_id)
          logger.info(f"【消费任务中】: 更新创建人ID {creator_id}")


        # 输出任务状态
        while ansible_runner_obj.status in CHAOS_ANSIBLE_TASK_RUNNING_STATU_LIST:
            if dormat_work_time >= CHAOS_ANSIBLE_TIMEOUT:
                break
            time.sleep(1)
            dormat_work_time += 1

        if ansible_runner_obj.status != "failed":
            Work.objects.filter(id=work_id).update(status=2)
        else:
            Work.objects.filter(id=work_id).update(status=3)
            
    finally:
        # 清除临时文件
        if os.path.exists(temp_inv_file.name):
          logger.info(f"【清除inventory临时文件】: {temp_inv_file.name}")
          os.remove(temp_inv_file.name)