from application.celery import celery
from application.logger import logging_func_time
from operatorcmdb.serializers.host import (
    check_operator_cmdb_host_quite_buffer,
    check_operator_cmdb_host_expire_at,
)
from tenant.serializers.tenant_openstack_server import (
    check_tenant_openstack_server_expired,
    check_tenant_openstack_server_reachable,
    wait_create_openstack_server_software_install,
)
from ticket.serializers.ticket import (
    wait_ticket_software_install_finished_task
)


@celery.task
@logging_func_time
def check_operator_cmdb_host_quite_buffer_worker():
    check_operator_cmdb_host_quite_buffer()


@celery.task
@logging_func_time
def check_operator_cmdb_host_expire_at_worker():
    check_operator_cmdb_host_expire_at()


@celery.task
@logging_func_time
def check_tenant_openstack_server_expired_worker():
    check_tenant_openstack_server_expired()


@celery.task
@logging_func_time
def check_tenant_openstack_server_reachable_worker():
    check_tenant_openstack_server_reachable()


@celery.task
@logging_func_time
def wait_create_openstack_server_software_install_worker():
    wait_create_openstack_server_software_install()
    wait_ticket_software_install_finished_task()
