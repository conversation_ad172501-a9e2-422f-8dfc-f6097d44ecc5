from datetime import datetime, date
from django.apps import apps

from decimal import Decimal

from dvadmin.system.models import AuditLog
from application.important_audit_config import AUDIT_LOG_CONFIG
from application.logger import logger
from application.dispatch import get_dictionary_values


NONE_RECORD_FIELD_NAME = [
    'id', 'seq',
    'creator', 'modifier', 'dept_belong_id',
    'update_datetime', 'create_datetime', 'is_deleted'
]


class AuditLogConfiguration(object):

    @staticmethod
    def get_model_all_using_fields(source_model):
        return AUDIT_LOG_CONFIG.get(source_model, {}).get('using_fields', {}).keys()

    @staticmethod
    def get_model_all_exclude_fields(source_model):
        return AUDIT_LOG_CONFIG.get(source_model, {}).get('exclude_fields', [])

    @staticmethod
    def get_field_config(source_model):
        return AUDIT_LOG_CONFIG.get(source_model, {})

    @staticmethod
    def get_field_using_fields(source_model):
        return AUDIT_LOG_CONFIG.get(source_model, {}).get('using_fields', {})

    @staticmethod
    def get_field_relation_model_config(source_model, relation_model):
        return AUDIT_LOG_CONFIG.get(source_model, {}).get('using_fields', {}).get(relation_model, {})


class HandlerRecordValue(object):
    def __init__(self, model_info, field_name, value):
        self.model_info = model_info
        self.field_name = field_name
        self.value = value

    def handle(self):
        field_config = self.get_field_config()
        if not field_config:
            return self.value
        handler_func = getattr(self, f'handle_{field_config.get("type")}', None)
        if not handler_func:
            return self.value
        value = handler_func(field_config=field_config)
        return value

    def get_model(self, relation_model_info=None):
        model = apps.get_model(
            app_label=self.model_info['model_info']['app_label'],
            model_name=self.model_info['model_info']['object_name']
        )
        if relation_model_info:
            if '.' in relation_model_info:
                model = apps.get_model(
                    app_label=relation_model_info.split('.')[0],
                    model_name=relation_model_info.split('.')[1]
                )
        return model

    def get_field_config(self):
        """获取模型的审计日志配置"""
        model_path = '%s.%s' % (
            self.model_info['model_info']['app_label'],
            self.model_info['model_info']['object_name']
        )
        return AuditLogConfiguration.get_field_relation_model_config(model_path, self.field_name)

    def handle_foreign_key(self, field_config):
        model = self.get_model(field_config['relation_model'])
        _filter = {field_config['key']: self.value}
        obj = model.objects.filter(**_filter).order_by('-update_datetime').first()
        if not obj:
            return self.value
        return getattr(obj, field_config['value'])

    def handle_dictionary(self, field_config):
        default_value = field_config.get('default', 'self')
        if default_value == 'self':
            default_value = self.value
        dictionary_dict = {}
        try:
            dictionary_config = get_dictionary_values(field_config['dictionary_key'])
            [dictionary_dict.update({i['value']: i['label']}) for i in dictionary_config['children']]
        except Exception as e:
            logger.warning(f'No dictionary or Dictionary configuration Error:'
                           f'<field_config: {str(field_config)}>\n<ErrorDetail: {str(e)}>')
        if not dictionary_dict:
            logger.warning(f'Dictionary is Blank!:<field_config: {str(field_config)}>')
        value = dictionary_dict.get(self.value, default_value)
        return value

    def handle_enum(self, field_config):
        default_value = field_config.get('default', 'self')
        if default_value == 'self':
            default_value = self.value
        enum_config = field_config.get('enum_config', {})
        if not enum_config:
            logger.warning(f'No enum or Enum configuration Error: <field_config: {str(field_config)}>')
            return default_value
        value = enum_config.get(self.value, default_value)
        return value


class ChaosRecordChanges(object):
    def __init__(self, model_info, user_info, previous_instance=None, current_instance=None):
        self.model_info = model_info
        self.source_model = f'{self.model_info["model_info"]["app_label"]}.{self.model_info["model_info"]["object_name"]}'
        self.user_info = user_info
        self.previous_instance = previous_instance
        self.current_instance = current_instance

    @staticmethod
    def __field_to_value__(value):
        if not value:
            return ''
        elif isinstance(value, datetime):
            current_value = value.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(value, date):
            current_value = value.strftime('%Y-%m-%d')
        elif isinstance(value, Decimal):
            current_value = str(value)
        else:
            current_value = value
        return current_value

    def get_field_verbose_name(self, field_name):
        """获取字段的 verbose_name """
        try:
            return self.model_info['field_names'].get(field_name, '')
        except Exception as e:
            logger.warning(f'<FieldNameNotFound:{str(e)}>')
            return field_name

    def record_changes(self):
        """记录字段的变更"""
        new_values = []
        old_values = []
        action = '更新'
        all_using_fields = AuditLogConfiguration.get_model_all_using_fields(self.source_model)
        all_exclude_fields = AuditLogConfiguration.get_model_all_exclude_fields(self.source_model)
        if self.previous_instance:
            for field_name in self.model_info['field_names'].keys():
                if field_name in NONE_RECORD_FIELD_NAME:
                    continue
                if field_name in all_exclude_fields:
                    # 跳过无需记录的配置的字段
                    continue
                current_value = self.current_instance.get(field_name)
                previous_value = self.previous_instance.get(field_name)
                # 处理为 json 数据, 避免 json 时，出现对象无法转换的问题
                current_value = self.__field_to_value__(current_value)
                previous_value = self.__field_to_value__(previous_value)

                if current_value != previous_value:
                    temp_new_value = {
                        'field_name': field_name,
                        'field_cn_name': self.get_field_verbose_name(field_name),
                        'value': current_value
                    }
                    temp_old_value = {
                        'field_name': field_name,
                        'field_cn_name': self.get_field_verbose_name(field_name),
                        'value': previous_value
                    }
                    # 若需要记录到变更日志内，处理为前端可读的信息
                    if field_name in all_using_fields:
                        current_display_value = HandlerRecordValue(
                            model_info=self.model_info, field_name=field_name, value=current_value
                        ).handle()
                        temp_new_value.update({
                            'value': current_display_value,
                            'source_value': current_value,
                        })

                        previous_display_value = HandlerRecordValue(
                            model_info=self.model_info, field_name=field_name, value=previous_value
                        ).handle()
                        temp_old_value.update({
                            'value': previous_display_value,
                            'source_value': previous_value,
                        })
                    new_values.append(temp_new_value)
                    old_values.append(temp_old_value)
        else:
            action = '创建'
            for field_name in self.model_info['field_names']:
                if field_name in NONE_RECORD_FIELD_NAME:
                    continue
                current_value = self.current_instance.get(field_name)
                current_value = self.__field_to_value__(current_value)
                # 若需要记录到变更日志内，处理为前端可读的信息
                temp_new_value = {
                    'field_name': field_name,
                    'field_cn_name': self.get_field_verbose_name(field_name),
                    'value': current_value,
                }
                if field_name in all_using_fields:
                    current_display_value = HandlerRecordValue(
                        model_info=self.model_info, field_name=field_name, value=current_value
                    ).handle()
                    temp_new_value.update({
                        'value': current_display_value,
                        'source_value': current_value,
                    })
                new_values.append(temp_new_value)
        if new_values or old_values:
            self.log_action(
                action=action,
                user_info=self.user_info,
                resource_id=self.current_instance['id'],
                changes={
                    'new_values': new_values,
                    'old_values': old_values,
                },
            )

    @staticmethod
    def log_action(action, user_info, resource_id, changes=None):
        """将变更记录到日志或其他存储方式中"""
        if not changes:
            changes = {}
        AuditLog.objects.create(
            action=action,
            old_values=changes.get('old_values', []),
            new_values=changes.get('new_values', []),
            resource_id=resource_id,
            **user_info
        )
        logger.info(f'<action:{action}><resource_id:{resource_id}><changes_values:{str(changes)}>')















