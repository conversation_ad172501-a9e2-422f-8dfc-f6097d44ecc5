import os
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT
from application.settings.base import BASE_DIR
from application.settings.base import MEDIA_ROOT


class BaremetalServerReport(object):
    def __init__(
        self,
        server_info,
        source_id='',
        ticket_id='',
        ssl_vpn_info=None,
        template_name='',
    ):
        self.server_info = server_info
        self.base_template_path = os.path.join(BASE_DIR, 'dvadmin', 'utils', 'my_py_docx', 'docx_templates')

        if not ssl_vpn_info:
            # VPN 信息
            self.ssl_vpn_info = {
                'address': '',
                'port': '',
                'username': '',
                'password': ''
            }
        if not template_name:
            # 基础模版文件名称
            template_name = 'baremetalServerReportTemplate.docx'
        self.template_path = os.path.join(self.base_template_path, template_name)
        self.doc = Document(self.template_path)
        self.base_output_path = os.path.join(BASE_DIR, MEDIA_ROOT, 'baremetal_reports_backup', source_id,)
        if not os.path.exists(self.base_output_path):
            os.makedirs(self.base_output_path)
        output_file_path = os.path.join(self.base_output_path, f'{ticket_id}.docx')
        if os.path.exists(output_file_path):
            # 获取文件名，加入最后 _v1
            i = 1
            while os.path.exists(f"{output_file_path}_v{i}.docx"):
                i += 1
            output_file_path += f"_v{i}.docx"
        print(output_file_path)
        self.output_path = output_file_path

    @staticmethod
    def set_cell_text(
            cell,
            text,
            bold=False,
            font_size=Pt(12),
            horizontal_alignment=WD_PARAGRAPH_ALIGNMENT.CENTER,
            vertical_alignment=WD_CELL_VERTICAL_ALIGNMENT.CENTER
    ):
        # 清除单元格中的所有段落
        for paragraph in cell.paragraphs:
            paragraph.clear()

        # 获取单元格中的第一个段落或创建一个新的段落
        paragraph = cell.paragraphs[0] if cell.paragraphs else cell.add_paragraph()

        # 设置段落对齐方式（水平居中）
        paragraph.alignment = horizontal_alignment

        # 添加新的文本
        run = paragraph.add_run(text)

        # 设置字体名称、大小和是否加粗
        run.font.name = '仿宋'
        run.font.size = font_size
        run.bold = bold

        # 设置单元格的垂直对齐方式
        cell.vertical_alignment = vertical_alignment

    def set_baremetal_server_table(self, servers):
        # 创建一个1行4列的表格
        table = self.doc.add_table(rows=1, cols=4)

        # 在第一行添加一些内容，并设置标头加粗居中
        hdr_cells = table.rows[0].cells
        headers = ['规格', '配置', 'IP', '账号/密码']
        for i, header in enumerate(headers):
            self.set_cell_text(hdr_cells[i], header, bold=True, font_size=Pt(14))  # 标头使用14磅字体

        # 添加更多行，并设置内容居中，字体为小四号（12磅），并且垂直居中
        for server in servers:
            row_cells = table.add_row().cells
            for i, key in enumerate(['flavor_name', 'image_name', 'ipaddr', 'credentials']):
                if key == 'ipaddr':
                    if server.get("extr_public_ipv4", ""):
                        content = f'内网IP: {server[key]}\n公网IP: {server["extr_public_ipv4"]}'
                    else:
                        content = f'内网IP: {server[key]}'
                elif key == 'credentials':
                    if server.get('credentials'):
                        content = server['credentials']
                    else:
                        content = 'xingzai / xingzai!@#456'
                elif key == 'flavor_name':
                    if '4090' in server['flavor_name']:
                        content = 'RTX4090'
                    elif '3090' in server['flavor_name']:
                        content = 'RTX3090'
                    else:
                        content = server['flavor_name']
                else:
                    content = server[key]
                if '裸金属' in content:
                    content = content.replace('裸金属', '')
                    content = content.replace('-', '')
                self.set_cell_text(row_cells[i], content, font_size=Pt(12))  # 内容使用12磅字体

        # 返回整个表格对象，以便可以插入到指定位置
        return table

    def insert_ssl_vpn_info(self, para):
        """
        在指定段落后插入 SSL VPN 地址和登录信息
        """
        # 创建一个新的段落并插入 SSL VPN 信息
        new_para = self.doc.add_paragraph()

        # 使用自定义函数来设置 SSL VPN 信息的格式
        self.set_paragraph_text(new_para, f"SSL VPN地址：{self.ssl_vpn_info['address']}:{self.ssl_vpn_info['port']}\n")
        self.set_paragraph_text(new_para,
                                f"用户名/密码：{self.ssl_vpn_info['username']}/{self.ssl_vpn_info['password']}")

        # 确保新段落插入在当前段落后
        para._p.addnext(new_para._element)

    @staticmethod
    def set_paragraph_text(paragraph, text, font_size=Pt(12)):
        """
        设置段落文本及其格式
        """
        run = paragraph.add_run(text)
        run.font.name = '仿宋'
        run.font.size = font_size

    def write(self):
        inserted_table = False
        inserted_vpn = False

        for para in self.doc.paragraphs:
            if '服务器信息' in para.text and not inserted_table:
                # 创建服务器信息表格并获取其对象
                table = self.set_baremetal_server_table(self.server_info)

                # 将表格插入到当前段落后
                para._p.addnext(table._tbl)

                inserted_table = True  # 标记表格已插入

            if 'VPN连接方式' in para.text and not inserted_vpn:
                # 插入 SSL VPN 地址
                self.insert_ssl_vpn_info(para)
                inserted_vpn = True  # 标记SSL VPN信息已插入

            if inserted_table and inserted_vpn:
                break  # 两个都插入完毕后退出循环

        # 保存文档
        self.doc.save(self.output_path)
        return self.output_path


if __name__ == '__main__':
    # 示例数据
    t_server_info = [
        {
            'flavor_name': 'RTX4090',
            'image_name': 'ISO: Ubuntu 22.04\nCUDA: 12.04',
            'ipaddr': '***********',
            'extr_public_ipv4': '***************',
            'credentials': 'xingzai / 123456'
        },
        {
            'flavor_name': 'RTX3090',
            'image_name': 'ISO: CentOS 8\nCUDA: 11.2',
            'ipaddr': '***********',
            'extr_public_ipv4': '',
            'credentials': 'xingzai / 123456'
        }
    ]

    t_ssl_vpn_info = {
        'address': '***************',
        'port': '1002',
        'username': 'admin01',
        'password': '123456'
    }

    # 创建报告对象并生成文档
    report = BaremetalServerReport(t_server_info, 'T2024122300001', t_ssl_vpn_info)
    report.write()
