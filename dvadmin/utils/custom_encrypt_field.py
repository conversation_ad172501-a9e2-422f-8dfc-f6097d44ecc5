"""
<AUTHOR>
@Date    ：2024/10/25
"""

from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from cryptography.fernet import InvalidToken


class EncryptedCharField(models.CharField):
    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        try:
            if isinstance(value, str):
                value = value.encode()
            return self.cipher.decrypt(value.decode()).decode()
        except InvalidToken:
            raise ValidationError(_('Invalid token'))

    def get_prep_value(self, value):
        if value is None:
            return value
        return self.cipher.encrypt(value.encode()).decode()

    @property
    def cipher(self):
        from django.conf import settings
        return settings.FERNET_INSTANCE
