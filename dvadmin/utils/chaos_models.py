from uuid import uuid4
from django.db import models
from django.contrib.auth import get_user_model

from application.settings import base
from application.important_audit_config import AUDIT_LOG_CONFIG
from dvadmin.celery_workers.record_change_worker import record_changes, record_log_action
from dvadmin.utils.chaos_record_changes import ChaosR<PERSON>ordChang<PERSON>, NONE_RECORD_FIELD_NAME


class ChaosBaseCoreModel(models.Model):
    """
    核心标准抽象模型模型,可直接继承使用
    增加审计字段, 覆盖字段时, 字段名称请勿修改, 必须统一审计字段名称
    """
    seq = models.BigAutoField(
        primary_key=True, help_text="seq", verbose_name="seq", db_comment='主键Seq')
    id = models.CharField(
        max_length=63, default='', unique=True, db_index=True, help_text="Id", verbose_name="Id", db_comment='Id')
    description = models.CharField(
        max_length=255, verbose_name="描述", null=True, blank=True, help_text="描述", db_comment='描述')
    creator = models.ForeignKey(
        to=base.AUTH_USER_MODEL, to_field='id', related_query_name='creator_query', null=True,
        verbose_name='创建人', help_text="创建人", on_delete=models.SET_NULL,
        db_constraint=False, db_comment='创建人')
    modifier = models.CharField(
        max_length=255, null=True, blank=True, help_text="修改人",
        verbose_name="修改人", db_comment='修改人')
    update_datetime = models.DateTimeField(
        auto_now=True, null=True, blank=True, help_text="修改时间", verbose_name="修改时间", db_comment='修改时间')
    create_datetime = models.DateTimeField(
        auto_now_add=True, null=True, blank=True, help_text="创建时间", verbose_name="创建时间", db_comment='创建时间')
    is_deleted = models.BooleanField(
        verbose_name="是否软删除", help_text='是否软删除', default=False, db_index=True, db_comment='是否软删除')

    @classmethod
    def get_db_table(cls):
        return cls._meta.db_table

    def get_previous_instance(self):
        """获取上一次保存的实例，用于比较字段变化"""
        return type(self).objects.filter(id=self.id).order_by('-update_datetime').first()

    def save(self, *args, **kwargs):
        previous_instance = None
        if not self.id:
            t_db_table = self.get_db_table().split('_', 1)[1]
            if t_db_table[-1] == 's':
                t_db_table = t_db_table[:-1]
            self.id = '{table_name}-{uuid_str}'.format(table_name=t_db_table, uuid_str=str(uuid4().hex))
            # 优化保存ID超位的情况
            self.id = self.id[:63]
        else:
            previous_instance = self.get_previous_instance()
            if previous_instance:
                previous_instance = previous_instance.to_dict
        # 保存数据
        super().save(*args, **kwargs)
        # 如果是更新操作，则记录更新者，并记录变更
        if self.is_audit_log_enabled():
            record_changes.delay(
                model_info=self.get_model_info(),
                user_info=self.get_self_user_info(),
                previous_instance=previous_instance,
                current_instance=self.to_dict)

    def get_self_user_info(self):
        return {
            'creator_id': self.creator,
            'modifier': self.modifier,
            # 'dept_belong_id': self.dept_belong_id,
        }

    def _to_dict(self, is_continue_public_fields=True):
        data = {}
        for field in self._meta.fields:
            field_name = field.attname

            if is_continue_public_fields and field_name in NONE_RECORD_FIELD_NAME:
                continue
            value = getattr(self, field_name, None)
            value = ChaosRecordChanges.__field_to_value__(value)
            data[field_name] = value
        return data

    @property
    def to_dict(self):
        is_continue_public_fields = True
        data = {}
        for field in self._meta.fields:
            field_name = field.attname

            if not is_continue_public_fields and field_name in NONE_RECORD_FIELD_NAME:
                continue
            value = getattr(self, field_name, None)
            value = ChaosRecordChanges.__field_to_value__(value)
            data.update({field_name: value})
        return data

    def get_model_info(self):
        tmp_fields = {}
        [tmp_fields.update(
            {field.attname: getattr(field, 'verbose_name', '未配置')}
        ) for field in self._meta.fields]
        model_info = {
            'field_names': tmp_fields,
            'model_info': {
                'app_label': self._meta.app_label,
                'object_name': self._meta.object_name,
            },
        }
        return model_info

    def delete(self, using=None, keep_parents=False):
        # Override the default delete method to perform a soft delete
        self.is_deleted = True
        if self.is_audit_log_enabled():
            record_log_action.delay(
                '删除',
                self.get_self_user_info(),
                resource_id=self.id,
            )
        self.save(using=using)

    def is_audit_log_enabled(self):
        """检查当前模型是否开启了审计日志"""
        model_path = '%s.%s' % (self._meta.app_label, self._meta.object_name)
        return AUDIT_LOG_CONFIG.get(model_path, {}).get('is_enabled', False)

    class Meta:
        abstract = True
        verbose_name = '核心模型'
        verbose_name_plural = verbose_name


class ChaosCoreModel(ChaosBaseCoreModel):
    dept_belong_id = models.CharField(
        max_length=255, help_text="数据归属部门", null=True, blank=True, verbose_name="数据归属部门",
        db_comment='数据归属部门')

    def get_self_user_info(self):
        return {
            'creator_id': self.modifier,
            'modifier': self.modifier,
            'dept_belong_id': self.dept_belong_id,
        }

    class Meta:
        abstract = True
        verbose_name = 'Chaos核心模型'
        verbose_name_plural = verbose_name
