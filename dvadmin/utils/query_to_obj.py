from django.forms.models import model_to_dict
from django.db.models import QuerySet,Model
from django.http import QueryDict
from typing import Union

def query2Obj(item) -> Union[dict, list]:
    if isinstance(item, QuerySet):
        # 遍历 QuerySet 并将每个对象的动态属性添加到字典中
        result = []
        for obj in item:
            obj_dict = model_to_dict(obj)  # 转换模型字段为字典
            # 动态属性添加到字典中
            obj_dict.update({key: value for key, value in vars(obj).items() if key not in obj_dict})
            obj_dict.pop('_state', None)  # 移除 Django 内部的 _state 字段
            result.append(obj_dict)
        return result

    elif isinstance(item, Model):
        # 单个模型实例的处理
        obj_dict = model_to_dict(item)  # 转换模型字段为字典
        # 动态属性添加到字典中
        obj_dict.update({key: value for key, value in vars(item).items() if key not in obj_dict})
        obj_dict.pop('_state', None)  # 移除 Django 内部的 _state 字段
        return obj_dict

    elif isinstance(item, QueryDict):
        # 处理 QueryDict 转换为字典
        return item.dict()

    else:
        # 对于其他类型直接返回原始项
        return item
