import requests
from application.logger import logger


class PrometheusClient(object):
    def __init__(self,host='', port='', protocol='http', env='product', node='金华'):
        self.env = env
        self.node = node
        self.host = host
        self.port = port
        self.protocol = protocol
        self.base_url = f"{self.protocol}://{self.host}:{self.port}"
        self.query_url = f"{self.base_url}/api/v1/query"
        self.init_client()
        self.region_maps = {
            '金华': 'jinhua',
            '上海': 'shanghai',
            '全部': '__ALL__',
        }

    def get_regions(self):
        return self.region_maps.get(self.node, [])

    def filter_regions(self, regions):
        if not regions:
            return self.region_maps.get(self.node, [])
        else:
            return [region for region in regions if region in self.region_maps.keys()]

    def init_client(self):
        if self.env == 'product':
            self.host = '**********'
            self.port = 9090
            self.base_url = f"{self.protocol}://{self.host}:{self.port}"
            self.query_url = f"{self.base_url}/api/v1/query"

    def query(self, query, time=None):
        """
        params query: 替换为你的 PromQL 查询
        params time: 查询时间
        """
        params = {
            "query": query,
            "time": time,
        }
        try:
            response = requests.get(self.query_url, params=params)
            response.raise_for_status()
            data = response.json()
        except Exception as e:
            logger.error(f"Error fetching data from Prometheus: {e}")
            return {
                'status': "failed",
                'data': {
                    'result': []
                },
            }
        return data

    def query_top10_power_watts(self, area_node="全部"):
        # 金华区域的region标签包含['jinhua'] 及无区域信息
        # 上海区域的region标签包含['shanghai']
        if area_node not in self.region_maps.keys():
            logger.error(f"Invalid region: {area_node}")
            return []
        query = "node:ipmi_power_watts:sum"
        if area_node == "上海":
            search_region = self.region_maps[area_node]
            query += f"{{region=\"{search_region}\"}}"
        query = query
        data = self.query(query)
        hosts_power_watts = []
        if data["status"] == "success":
            results = data["data"]["result"]
            actual_results = []
            if area_node == '金华':
                for result in results:
                    if result.get('metric', {}).get('region', 'Unknown') != self.region_maps['上海']:
                        actual_results.append(result)
            else:
                actual_results = results
            for result in actual_results:
                # TODO 暂不取机柜信息
                # rack_info = f'{result.get("metric", {}).get("room", "N/A")}.{result["metric"].get("cabinet", "N/A")}'
                instance = result.get("metric", {}).get("instance", "N/A")

                power = result.get("value", [0, 0])[1]  # 值在数组的第二个位置
                hosts_power_watts.append(
                    {
                        # "rack_info": rack_info,
                        "instance": instance,
                        "功率(W)": int(power),
                    })
        sorted_data = sorted(hosts_power_watts, key=lambda x: x["功率(W)"], reverse=True)
        return sorted(sorted_data[:10], key=lambda x: x["功率(W)"], reverse=False)

if __name__ == "__main__":
    client = PrometheusClient()
    response = client.query_top10_power_watts('上海')
    print(response)
