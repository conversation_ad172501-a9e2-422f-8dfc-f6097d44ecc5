from netmiko import ConnectHandler
from netaddr import EUI, mac_unix_expanded, AddrFormatError, IPAddress
import re
from time import sleep
from typing import List, Dict, Union, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='huawei_switch.log'
)
logger = logging.getLogger(__name__)


class SwitchManager:
    def __init__(
            self, ip: str, username: str, password: str,
            port: int = 22, device_type: str = "huawei",
            timeout: int = 10, verbose: bool = False,
            max_retries: int = 3
            ):
        """
        初始化交换机管理对象

        Args:
            ip: 交换机IP地址
            username: 登录用户名
            password: 登录密码
            port: SSH端口，默认为22
            device_type: 设备类型，支持'huawei'和'h3c'
            timeout: 连接超时时间(秒)
            verbose: 是否打印详细日志
            max_retries: 最大重试次数
        """
        self.ip = ip
        self.port = port
        self.username = username
        self.password = password
        self.device_type = device_type.lower()
        self.timeout = timeout
        self.verbose = verbose
        self.max_retries = max_retries
        self.net_connect = None
        self._connect()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()

    def __del__(self):
        self.disconnect()

    def _connect(self) -> None:
        """建立SSH连接（带重试机制）"""
        device_map = {
            'huawei': 'huawei',
            'h3c': 'hp_comware',
            }

        if self.device_type not in device_map:
            raise ValueError(f"Unsupported device type: {self.device_type}.  Supported: {list(device_map.keys())}")

        for attempt in range(1, self.max_retries + 1):
            try:
                self.net_connect = ConnectHandler(
                    device_type=device_map[self.device_type],
                    host=self.ip,
                    username=self.username,
                    password=self.password,
                    port=self.port,
                    timeout=self.timeout,
                    verbose=self.verbose,
                    )
                if self.verbose:
                    logger.info(f"Successfully  connected to {self.ip}  ({self.device_type.upper()})")
                return
            except Exception as e:
                if attempt == self.max_retries:
                    raise ConnectionError(f"Failed to connect after {self.max_retries}  attempts: {str(e)}")
                sleep(2 ** attempt)  # 指数退避

    def is_alive(self) -> bool:
        """检查连接是否仍然活跃"""
        return self.net_connect.is_alive() if self.net_connect else False

    def confirm(self) -> bool:
        """确认交换机是否正常连接"""
        try:
            result = self.net_connect.find_prompt()
            return True if result else False
        except Exception:
            return False

    def disconnect(self) -> None:
        """断开SSH连接"""
        if self.net_connect:
            self.net_connect.disconnect()
            if self.verbose:
                logger.info(f"Disconnected  from {self.ip}:{self.port}")
            self.net_connect = None

    def get_version(self) -> Dict[str, str]:
        """获取交换机版本信息"""
        commands = {
            'huawei': 'display version',
            'h3c': 'display version',
            }

        output = self._send_command(commands[self.device_type])

        if self.device_type == 'huawei':
            version_info = {
                'vendor': 'Huawei',
                'model': self._extract_pattern(r'^.*?(S[0-9]+[A-Z]?).*?$', output, 1),
                'software_version': self._extract_pattern(r'VRP \(R\) software.*?Version (.*?)\s', output),
                'uptime': self._extract_pattern(r'uptime is (.*?)\n', output),
                }
        else:  # H3C
            version_info = {
                'vendor': 'H3C',
                'model': self._extract_pattern(r'^.*?(S[0-9]+[A-Z]?).*?$', output, 1),
                'software_version': self._extract_pattern(r'Version (.*?)\s', output),
                'uptime': self._extract_pattern(r'uptime is (.*?)\n', output),
                }

        return version_info

    def get_physical_interfaces(self) -> List[Dict[str, str]]:
        """
        获取物理接口信息
        返回字段：
        - name: 接口名称
        - status: 物理状态 (up/down/admin down)
        - protocol: 协议状态 (up/down)
        - mac: MAC地址 (统一格式：00-E0-FC-12-34-56)
        - speed: 标准化速率 (100Mbps/1Gbps/10Gbps)
        """
        brief_output = self._send_command('display interface brief')
        interfaces = []

        # 修正后的正则表达式
        interface_pattern = r'^(GigabitEthernet|XGigabitEthernet|Ethernet|GE|XGE)[^ ]+'

        for line in brief_output.split('\n'):
            if re.match(interface_pattern, line):
                parts = re.split(r'\s+', line.strip())
                if len(parts) >= 4:
                    interface = {
                        'name': parts[0],
                        'status': 'admin down' if 'ADM' in parts[1] else parts[1].lower(),
                        'protocol': parts[2].lower(),
                        'speed': self._parse_speed(parts[3]) if len(parts) > 3 else 'N/A',
                        'mac': self._get_interface_mac(parts[0])
                        }
                    interfaces.append(interface)
        return interfaces

    def get_eth_trunks(self) -> List[Dict[str, Union[str, List[str]]]]:
        """
        获取Eth-Trunk聚合接口信息
        返回字段：
        - name: 聚合口名称
        - status: 状态
        - protocol: 协议状态
        - members: 成员端口列表
        """
        output = self._send_command('display eth-trunk brief')
        trunks = []

        # 增强正则兼容性
        trunk_pattern = r'^(Eth-Trunk|Bridge-Aggregation)\d+'

        for line in output.split('\n'):
            if re.match(trunk_pattern, line):
                parts = re.split(r'\s+', line.strip())
                if len(parts) >= 4:
                    trunk = {
                        'name': parts[0],
                        'status': parts[1].lower(),
                        'protocol': parts[2].lower(),
                        'members': self._get_trunk_members(parts[0])
                        }
                    trunks.append(trunk)
        return trunks

    def _get_interface_mac(self, interface: str) -> str:
        """获取并标准化接口MAC地址
        # 增强版正则表达式，匹配以下格式：
        # 00e0-fc12-3456 (华为标准)
        # 00e0.fc12.3456  (常见格式)
        # 00e0fc-123456 (H3C某些型号)
        # 00e0fc123456 (无分隔符)
        re ==>
        1.  # 00e0-fc12-3456 或 00e0.fc12.3456
        2.  # 00e0fc-123456
        3.  # 00e0fc123456
        """
        output = self._send_command(f'display interface {interface}')

        # 最终优化版正则
        mac_pattern = r"""
            Hardware[^\n]*?
            (
                (?:[0-9a-fA-F]{4}[-.][0-9a-fA-F]{4}[-.][0-9a-fA-F]{4})
                |
                (?:[0-9a-fA-F]{6}-[0-9a-fA-F]{6})
                |
                (?:[0-9a-fA-F]{12})
            )
        """
        match = re.search(mac_pattern, output, re.VERBOSE)
        return self._standardize_mac(match.group(1)) if match else 'N/A'

    @staticmethod
    def _standardize_mac(raw: str) -> str:
        """内部标准化方法"""
        try:
            return str(EUI(raw, dialect=mac_unix_expanded))
        except AddrFormatError:
            # 处理特殊格式
            clean = raw.lower().replace('.',  '-')
            if '-' not in clean and len(clean) == 12:
                return '-'.join([clean[i:i+2] for i in range(0, 12, 2)])
            return clean

    @staticmethod
    def _standardize_ip(ip_str: str) -> str:
        """支持IPv4/IPv6标准化"""
        try:
            return str(IPAddress(ip_str))
        except (AddrFormatError, ValueError):
            return ip_str

    def _get_trunk_members(self, trunk: str) -> List[str]:
        """获取聚合口成员列表（修复正则表达式空格问题）"""
        output = self._send_command(f'display eth-trunk {trunk.split("Eth-Trunk")[-1]}')
        return re.findall(r'(GigabitEthernet|XGigabitEthernet)[^ ]+', output)

    @staticmethod
    def _parse_speed(speed: str) -> str:
        """标准化速率显示"""
        speed_map = {
            '100M': '100Mbps',
            '1000M': '1Gbps',
            '10G': '10Gbps',
            '25G': '25Gbps',
            '40G': '40Gbps',
            '100G': '100Gbps',
            }
        return speed_map.get(speed.upper(), speed)

    def _send_command(self, command: str, delay_factor: float = 1.0) -> str:
        """安全发送命令"""
        if not self.net_connect:
            raise ConnectionError("Not connected to switch")
        try:
            return self.net_connect.send_command(
                command,
                delay_factor=delay_factor,
                strip_prompt=False,
                strip_command=False
                ).strip()
        except Exception as e:
            raise RuntimeError(f"Command failed: {str(e)}")

    def execute_command(self, command: str, delay_factor: float = 1.0) -> str:
        """执行任意命令并返回结果"""
        return self._send_command(command, delay_factor)

    @staticmethod
    def _extract_pattern(pattern: str, text: str, group: int = 1) -> str:
        """从文本中提取匹配模式的内容"""
        match = re.search(pattern, text, re.MULTILINE)
        return match.group(group).strip() if match else "N/A"


if __name__ == '__main__':
    # 测试示例
    import os
    try:
        with (SwitchManager(os.environ.get('SWITCH_IP'),
                            os.environ.get('SWITCH_USERNAME'),
                            os.environ.get('SWITCH_PASSWORD'),
                            port=10222,
        verbose=True) as mgr):
            print("\n=== 物理接口 ===")
            for iface in mgr.get_physical_interfaces():
                print(f"{iface['name']} {iface['status']} {iface['protocol']} MAC: {iface['mac']}")

            print("\n=== Eth-Trunk ===")
            for trunk in mgr.get_eth_trunks():
                print(f"{trunk['name']} {trunk['status']} 成员: {', '.join(trunk['members'])}")

    except Exception as e:
        logger.error(f"Error:  {str(e)}")