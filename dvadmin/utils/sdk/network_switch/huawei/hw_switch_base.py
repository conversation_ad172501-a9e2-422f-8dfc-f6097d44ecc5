from netmiko import ConnectHandler
from netaddr import EUI, mac_unix_expanded, AddrFormatError
import re
from time import sleep
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='huawei_switch.log'
    )
logger = logging.getLogger(__name__)


class HuaweiSwitchBase:
    def __init__(
            self, ip: str, username: str, password: str,
            port: int = 22, timeout: int = 10,
            verbose: bool = False, max_retries: int = 3,
            device_type: str = "huawei",
            command_timeout: int = 60,
            is_development: bool = True,
            ):
        """
        华为交换机基础SDK（自动记录Prompt版本）

        Args:
            ip: 交换机IP地址
            username: 只读权限用户名
            password: 密码
            port: SSH端口，默认为22
            timeout: 连接超时时间(秒)
            verbose: 是否打印详细日志
            max_retries: 最大重试次数
            device_type: 设备类型（默认huawei）
            command_timeout: 命令执行超时时间(秒)
        """

        self.logger = logger
        self.ip = ip
        self.port = port
        self.username = username
        self.password = password
        self.timeout = timeout
        self.verbose = verbose
        self.max_retries = max_retries
        self.device_type = device_type
        self.command_timeout = command_timeout
        self.net_connect = None
        self.actual_prompt = None  # 存储实际设备提示符
        self.is_development = is_development
        self._connect()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()

    def __del__(self):
        self.disconnect()

    def _connect(self) -> None:
        """建立SSH连接（带重试机制）"""
        print(f'device_type: {self.device_type}')
        print(self.is_development)
        if self.is_development:
            return None
        login_device = {
            'device_type': self.device_type,
            'host': self.ip,
            'username': self.username,
            'password': self.password,
            'port': self.port,
            'timeout': self.timeout,
            'session_timeout': self.timeout * 2,
            'verbose': self.verbose,
            'global_delay_factor': 3,
            'fast_cli': True,
            }
        login_device.update(
            {'session_log': f'{self.ip}.log'}
            )
        if self.device_type == "huawei_vrp8":
            # VRP8建议禁用fast_cli
            login_device.update({
                'fast_cli': False
                })
        for attempt in range(1, self.max_retries + 1):
            try:
                self.net_connect  = ConnectHandler(**login_device)

                # 获取并存储设备提示符
                # 获取实际提示符（不带$符号）
                self.actual_prompt = self.net_connect.find_prompt().strip()
                if self.verbose:
                    logger.info(f" 成功连接到华为交换机 {self.ip} ，设备提示符: {self.actual_prompt}")
                return
            except Exception as e:
                if attempt == self.max_retries:
                    raise ConnectionError(f"连接尝试 {attempt}/{self.max_retries} 次后仍无法连接:"
                                          f" {str(e)}")
                sleep(2 ** attempt)  # 指数退避

    def is_alive(self) -> bool:
        """检查连接是否仍然活跃"""
        if self.is_development:
            return True
        return self.net_connect.is_alive() if self.net_connect else False

    def confirm(self) -> bool:
        """确认交换机是否正常连接"""
        if self.is_development:
            return True
        try:
            result = self.net_connect.find_prompt()
            return True if result else False
        except Exception as exc:
            logger.error(f" 连接确认失败: {str(exc)}")
            return False

    def disconnect(self) -> None:
        """断开SSH连接"""
        if self.is_development:
            logger.info("模拟交换机客户端已断开SSH连接")
            return None
        if self.net_connect:
            self.net_connect.disconnect()
            if self.verbose:
                logger.info(f" 已断开与 {self.ip}:{self.port}  的连接")
            self.net_connect = None

    # def _send_command(self, command: str, delay_factor: float = 2.0) -> str:
    #     """
    #     发送命令（自动使用记录的prompt）
    #
    #     Args:
    #         command: 要执行的命令
    #         delay_factor: 延迟因子
    #
    #     Returns:
    #         命令输出结果
    #     """
    #     if not self.net_connect:
    #         raise ConnectionError("未连接到交换机")
    #
    #     try:
    #         output = self.net_connect.send_command(
    #             command,
    #             delay_factor=delay_factor,
    #             read_timeout=self.command_timeout,
    #             strip_prompt=False,
    #             strip_command=False
    #             )
    #         if self.verbose:
    #             logger.info(f" 命令执行成功: {command}")
    #             logger.info(f" 命令输出: {output}")
    #         return output
    #     except Exception as e:
    #         logger.error(f" 命令执行失败: {command}, 错误: {str(e)}")
    #         raise RuntimeError(f"命令执行失败: {str(e)}")

    def _send_command(self, command: str, delay_factor: float = 2.0, max_pages: int = 200) -> str:
        """
        增强版命令发送（支持翻页/超时控制/安全退出）

        Args:
            command: 要执行的命令
            delay_factor: 延迟因子
            max_pages: 最大翻页次数（防止死循环）

        Returns:
            完整输出结果
        """
        if not self.net_connect:
            raise ConnectionError("未连接到交换机")

        try:
            output = ""
            # 首次发送命令
            current_output = self.net_connect.send_command_timing(
                command,
                delay_factor=delay_factor,
                read_timeout=self.command_timeout,
                strip_prompt=False,
                strip_command=False
                )
            output += current_output

            # 翻页处理（带安全限制）
            page_count = 0
            while page_count < max_pages:
                if "---- More ----" not in current_output:
                    break  # 无翻页提示时退出

                # 发送空格继续翻页
                current_output = self.net_connect.send_command_timing(
                    " ",
                    delay_factor=delay_factor,
                    read_timeout=self.command_timeout,
                    strip_prompt=False,
                    strip_command=False
                    )
                output += current_output
                page_count += 1

                # 检查是否返回提示符（提前退出）
                if self.actual_prompt and self.actual_prompt in current_output:
                    break

            if page_count >= max_pages:
                logger.warning(f" 翻页次数达到上限 {max_pages}，强制退出")

            if self.verbose:
                logger.info(f" 命令执行成功: {command}")
            return output.strip()

        except Exception as e:
            logger.error(f" 命令执行失败: {command}, 错误: {str(e)}")
            raise RuntimeError(f"命令执行异常: {str(e)}")

    def send_safe_command(self, command: str, delay_factor: float = 2.0) -> str:
        """
        安全发送只读命令（带权限检查）

        Args:
            command: 要执行的命令
            delay_factor: 延迟因子

        Returns:
            命令输出结果

        Raises:
            PermissionError: 如果尝试执行非只读命令
        """
        # 安全检查 - 防止执行非只读命令
        if re.search(
                r'\b(?:config |system-view|undo|reset|reboot|shutdown|format|commit)\b',
                command, re.IGNORECASE
                ):
            raise PermissionError("此SDK只支持只读操作")

        return self._send_command(command, delay_factor)

    @staticmethod
    def _standardize_mac(raw: str) -> str:
        """标准化MAC地址格式为00-E0-FC-12-34-56"""
        try:
            return str(EUI(raw, dialect=mac_unix_expanded))
        except AddrFormatError:
            # 处理各种可能的格式
            clean = re.sub(r'[^0-9a-fA-F]', '', raw.lower())
            if len(clean) == 12:
                return '-'.join([clean[i:i + 2] for i in range(0, 12, 2)])
            return raw.upper()

    @staticmethod
    def _parse_speed(speed: str) -> str:
        """标准化速率显示"""
        speed_map = {
            '100M': '100Mbps',
            '1000M': '1Gbps',
            '10G': '10Gbps',
            '25G': '25Gbps',
            '40G': '40Gbps',
            '100G': '100Gbps',
            'AUTO': 'auto',
            'UNKNOWN': 'unknown'
            }
        return speed_map.get(speed.upper(), speed)

    @staticmethod
    def _extract_pattern(pattern: str, text: str, group: int = 1, default: str = "N/A") -> str:
        """从文本中提取匹配模式的内容"""
        match = re.search(pattern, text, re.MULTILINE)
        return match.group(group).strip() if match else default
