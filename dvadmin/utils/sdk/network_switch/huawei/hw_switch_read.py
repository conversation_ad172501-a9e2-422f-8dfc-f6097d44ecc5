import os
import re
from typing import Dict, List, Union
from dvadmin.utils.sdk.network_switch.huawei.hw_switch_base import HuaweiSwitchBase



class MockSwitchReader:
    """模拟交换机读取器，用于本地测试"""

    @staticmethod
    def get_version() -> Dict[str, str]:
        """返回示例版本信息"""
        return {
            'vendor': '华为',
            'model': 'S8C',
            'software_version': 'N/A',
            'uptime': '483 days, 19 hours, 47 minutes'
            }

    @staticmethod
    def get_physical_interfaces() -> List[Dict[str, str]]:
        """返回示例物理接口信息"""
        return [
            {'name': '100GE1/0/3', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '100GE1/0/4', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '100GE1/0/5', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '100GE1/0/6', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/10', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/16', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/18', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/23', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/24', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'FA16:3EEA:57EF'},
            {'name': '25GE1/0/25', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/26', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/27', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/28', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/29', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/30', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/31', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/32', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/35', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/36', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/37', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/38', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/39', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/40', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/41', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/42', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/43', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '100GE1/0/7', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '100GE1/0/8', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/8', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/9', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/33', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/34', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/1', 'status': 'up', 'protocol': 'up', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/2', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/3', 'status': 'up', 'protocol': 'up', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/4', 'status': 'up', 'protocol': 'up', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/5', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/6', 'status': 'up', 'protocol': 'up', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/17', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/19', 'status': 'up', 'protocol': 'up', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/20', 'status': 'up', 'protocol': 'up', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/21', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/22', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/11', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/12', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/13', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/14', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/15', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '25GE1/0/44', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'AC61:75E5:2A8C'},
            {'name': '25GE1/0/45', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'F44C:7F5D:B592'},
            {'name': '25GE1/0/46', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '25GE1/0/47', 'status': 'up', 'protocol': 'up', 'speed': '0%', 'mac': '049F:CAD4:49D1'},
            {'name': '25GE1/0/48', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': '346A:C224:3B55'},
            {'name': '25GE1/0/7', 'status': 'down', 'protocol': 'down', 'speed': '0%', 'mac': 'N/A'},
            {'name': '100GE1/0/1', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            {'name': '100GE1/0/2', 'status': 'up', 'protocol': 'up', 'speed': '0.01%', 'mac': 'N/A'},
            ]

    @staticmethod
    def get_eth_trunks() -> List[Dict[str, any]]:
        """返回示例Eth-Trunk信息"""
        return [
            {
                'name': 'Eth-Trunk1', 'status': 'up',
                'members': ['100GE1/0/8(h)', '100GE1/0/8', '100GE1/0/7', '100GE1/0/7(hr)']
                },
            {'name': 'Eth-Trunk7', 'status': 'down', 'members': ['25GE1/0/8']},
            {'name': 'Eth-Trunk8', 'status': 'down', 'members': ['25GE1/0/9']},
            {'name': 'Eth-Trunk9', 'status': 'down', 'members': ['25GE1/0/33', '25GE1/0/34']},
            {'name': 'Eth-Trunk11', 'status': 'up', 'members': ['25GE1/0/1', '25GE1/0/1(hr)']},
            {'name': 'Eth-Trunk12', 'status': 'up', 'members': ['25GE1/0/2(hr)', '25GE1/0/2']},
            {'name': 'Eth-Trunk13', 'status': 'up', 'members': ['25GE1/0/3(hr)', '25GE1/0/3']},
            {'name': 'Eth-Trunk14', 'status': 'up', 'members': ['25GE1/0/4(hr)', '25GE1/0/4']},
            {'name': 'Eth-Trunk15', 'status': 'up', 'members': ['25GE1/0/5(hr)', '25GE1/0/5']},
            {'name': 'Eth-Trunk16', 'status': 'down', 'members': ['25GE1/0/6']},
            {'name': 'Eth-Trunk17', 'status': 'up', 'members': ['25GE1/0/17', '25GE1/0/17(hr)']},
            {'name': 'Eth-Trunk19', 'status': 'up', 'members': ['25GE1/0/19(hr)', '25GE1/0/19']},
            {'name': 'Eth-Trunk20', 'status': 'up', 'members': ['25GE1/0/20', '25GE1/0/20(hr)']},
            {'name': 'Eth-Trunk21', 'status': 'up', 'members': ['25GE1/0/21', '25GE1/0/21(hr)']},
            {'name': 'Eth-Trunk22', 'status': 'down', 'members': ['25GE1/0/22']},
            {'name': 'Eth-Trunk24', 'status': 'down', 'members': []},
            {'name': 'Eth-Trunk27', 'status': 'down', 'members': []},
            {'name': 'Eth-Trunk28', 'status': 'down', 'members': []},
            {'name': 'Eth-Trunk30', 'status': 'up', 'members': ['25GE1/0/11', '25GE1/0/11(hr)']},
            {'name': 'Eth-Trunk31', 'status': 'up', 'members': ['25GE1/0/12', '25GE1/0/12(hr)']},
            {'name': 'Eth-Trunk32', 'status': 'up', 'members': ['25GE1/0/13', '25GE1/0/13(hr)']},
            {'name': 'Eth-Trunk33', 'status': 'up', 'members': ['25GE1/0/14', '25GE1/0/14(hr)']},
            {'name': 'Eth-Trunk34', 'status': 'up', 'members': ['25GE1/0/15', '25GE1/0/15(hr)']},
            {'name': 'Eth-Trunk44', 'status': 'down', 'members': ['25GE1/0/44']},
            {'name': 'Eth-Trunk45', 'status': 'down', 'members': ['25GE1/0/45']},
            {'name': 'Eth-Trunk46', 'status': 'down', 'members': ['25GE1/0/46']},
            {'name': 'Eth-Trunk47', 'status': 'down', 'members': ['25GE1/0/47']},
            {'name': 'Eth-Trunk48', 'status': 'down', 'members': ['25GE1/0/48']},
            {'name': 'Eth-Trunk77', 'status': 'down', 'members': ['25GE1/0/7']},
            {'name': 'Eth-Trunk100', 'status': 'up', 'members': ['100GE1/0/2(r)', '100GE1/0/2', '100GE1/0/1']},
            ]


class HuaweiSwitchReader(HuaweiSwitchBase):
    def __init__(self, ip: str, username: str, password: str, port: int = 22, timeout: int = 10,
                 verbose: bool = False, max_retries: int = 3, device_type="huawei", is_development=True):

        self.is_development = is_development
        super().__init__(
            ip=ip, username=username, password=password, port=port,
            timeout=timeout, verbose=verbose, max_retries=max_retries,
            device_type=device_type, is_development=is_development
            )
        if self.is_development:
            self.logger.info("使用模拟数据")
        else:
            pass
        self.mac_address_table = {}

    def get_version(self) -> Dict[str, str]:
        """获取交换机版本信息"""
        if self.is_development:
            return MockSwitchReader.get_version()
        command = "display version"
        output = self.send_safe_command(command)
        version_info = {}
        if 'huawei' in self.device_type:
            version_info = {
                'vendor': '华为',
                'model': self._extract_pattern(r'^.*?(S[0-9]+[A-Z]?).*?$', output, 1),
                'software_version': self._extract_pattern(r'VRP \(R\) software.*?Version (.*?)\s', output),
                'uptime': self._extract_pattern(r'uptime is (.*?)\n', output),
                }
        return version_info

    def get_physical_interfaces(self) -> List[Dict[str, str]]:
        """
        获取物理接口信息
        返回字段：
        - name: 接口名称 (如 GigabitEthernet0/0/1)
        - status: 物理状态 (up/down/admin down)
        - protocol: 协议状态 (up/down)
        - mac: MAC地址 (统一格式：00-E0-FC-12-34-56)
        - speed: 标准化速率 (100Mbps/1Gbps/10Gbps)
        """
        if self.is_development:
            return MockSwitchReader.get_physical_interfaces()
        command = 'display interface brief'
        brief_output = self.send_safe_command(command)
        if self.verbose:
            self.logger.info(f"{command} output: {brief_output}")
        interfaces = []

        # 匹配华为交换机常见接口类型
        interface_pattern = r'^\s*([A-Za-z0-9]+[GE]\d+(?:/\d+)+(?:\([a-zA-Z0-9\-_]+\))?)'

        # 获取 mac_地址表
        self.mac_address_table = self.get_mac_address_table()
        self.logger.info(f"MAC地址表: {self.mac_address_table}")
        print(f"MAC地址表： {self.mac_address_table}")

        for line in brief_output.split('\n'):
            if re.match(interface_pattern, line.strip()):
                parts = re.split(r'\s+', line.strip())
                if len(parts) >= 4:
                    interface = {
                        'name': parts[0],
                        'status': 'admin down' if 'ADM' in parts[1] else parts[1].lower(),
                        'protocol': parts[2].lower(),
                        'speed': self._parse_speed(parts[3]) if len(parts) > 3 else 'N/A',
                        'mac': self.mac_address_table.get(parts[0], 'N/A')
                        }
                    interfaces.append(interface)
        return interfaces

    def get_eth_trunks(self) -> List[Dict[str, Union[str, List[str]]]]:
        """
        获取Eth-Trunk聚合接口信息
        返回字段：
        - name: 聚合口名称
        - status: 状态
        - protocol: 协议状态
        - members: 成员端口列表
        """
        if self.is_development:
            return MockSwitchReader.get_eth_trunks()
        command = 'display eth-trunk brief'
        output = self.send_safe_command(command)
        trunks = []

        # 增强正则兼容性
        trunk_pattern = r'^(Eth-Trunk|Bridge-Aggregation)\d+'

        for line in output.split('\n'):
            if re.match(trunk_pattern, line):
                parts = re.split(r'\s+', line.strip())
                if len(parts) >= 4:
                    trunk = {
                        'name': parts[0],
                        'mode': parts[1].lower(),
                        'status': parts[2].lower(),
                        'members': self._get_trunk_members(parts[0])
                        }
                    trunks.append(trunk)
        return trunks

    def get_vlans(self) -> List[Dict[str, str]]:
        """
        获取VLAN信息
        返回字段:
        - vlan_id: VLAN ID
        - name: VLAN名称
        - status: 状态
        - ports: 成员端口列表
        """
        output = self.send_safe_command('display vlan')
        vlans = []

        current_vlan = None
        for line in output.split('\n'):
            if match := re.match(r'^VLAN ID\s*:\s*(\d+)', line.strip()):
                current_vlan = {
                    'vlan_id': match.group(1),
                    'name': 'N/A',
                    'status': 'active',
                    'ports': []
                    }
                vlans.append(current_vlan)
            elif current_vlan:
                if match := re.match(r'^VLAN Name\s*:\s*(.*)', line.strip()):
                    current_vlan['name'] = match.group(1).strip()
                elif match := re.match(r'^Tagged Ports\s*:\s*(.*)', line.strip()):
                    current_vlan['ports'].extend(
                        re.findall(r'(?:GE |XGE|Ethernet|GigabitEthernet|XGigabitEthernet)[0-9/]+', match.group(1))
                        )
                elif match := re.match(r'^Untagged Ports\s*:\s*(.*)', line.strip()):
                    current_vlan['ports'].extend(
                        re.findall(r'(?:GE|XGE|Ethernet|GigabitEthernet|XGigabitEthernet)[0-9/]+', match.group(1))
                        )

        return vlans

    def get_mac_address_table(self) -> Dict[str, str]:
        """获取MAC地址表"""
        command = 'display mac-address'
        output = self.send_safe_command(command)

        if self.verbose:
            self.logger.info(f"{command}  output: {output}")

        # 3. 构建MAC地址字典 {接口名: MAC地址}
        mac_dict = {}
        for line in output.split('\n'):
            if match := re.match(
                    r'^([0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4})\s+\d+/-/-\s+([A-Za-z0-9]+[GE]\d+(?:/\d+)+(?:\([a-zA-Z0-9\-_]+\))?)',
                    line.strip()
                    ):
                mac, interface = match.groups()
                mac_dict[interface] = mac.upper().replace('-', ':')  # 统一格式为00:E0:FC:12:34:56
        return mac_dict


    def _get_interface_mac(self, interface: str) -> str:
        """
        获取并标准化接口MAC地址
        # 00e0-fc12-3456 (华为标准)
        # 00e0.fc12.3456  (常见格式)
        # 00e0fc-123456 (H3C某些型号)
        # 00e0fc123456 (无分隔符)
        re ==>
        1.  # 00e0-fc12-3456 或 00e0.fc12.3456
        2.  # 00e0fc-123456
        3.  # 00e0fc123456
        """
        command = f'display interface {interface}'
        output = self.send_safe_command(command)

        # 增强MAC地址识别模式
        mac_pattern = r"""
            (?:Hardware[^\n]*?address|MAC[^\n]*?address)[^\n]*?
            (
                (?:[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4})
                |
                (?:[0-9a-fA-F]{6}-[0-9a-fA-F]{6})
                |
                (?:[0-9a-fA-F]{2}(?:[:-][0-9a-fA-F]{2}){5})
                |
                (?:[0-9a-fA-F]{12})
            )
        """
        match = re.search(mac_pattern, output, re.VERBOSE | re.IGNORECASE)
        return self._standardize_mac(match.group(1)) if match else 'N/A'

    def _get_trunk_members(self, trunk: str) -> List[str]:
        trunk_id = trunk.replace('Eth-Trunk', '')
        command = f'display eth-trunk {trunk_id}'
        output = self.send_safe_command(command)

        # 优化后的正则表达式：
        # 1. ^\s* 匹配行开头和可能的空格
        # 2. ([A-Za-z0-9]+[GE]\d+(?:/\d+)+) 匹配接口前缀和路径
        # 3. (?:\([a-z0-9]+\))? 可选括号标记，但包含在捕获组中
        # 4. .* 忽略行剩余部分
        raw_members = re.findall(
            r'^\s*([A-Za-z0-9]+[GE]\d+(?:/\d+)+(?:\([a-zA-Z0-9\-_]+\))?)',
            output,
            re.MULTILINE
            )

        # 去重并过滤空结果
        members = list(set([m.strip() for m in raw_members if m]))

        return members


if __name__ == '__main__':
    # 测试示例
    import os

    try:
        with HuaweiSwitchReader(
                ip=os.environ.get('HUAWEI_SWITCH_IP', "1"),
                username=os.environ.get('HUAWEI_SWITCH_USER', "1"),
                password=os.environ.get('HUAWEI_SWITCH_PASS', "1"),
                port=int(os.environ.get('HUAWEI_SWITCH_PORT', 1)),
                device_type=os.environ.get('DEVICE_TYPE', 'huawei'),
                verbose=True,
                is_development=True,
                ) as mgr:
            # 测试基本连接
            # print("设备提示符:", mgr.net_connect.find_prompt())

            # 测试命令执行
            print("版本信息:", mgr.get_version())
            print("\n=== 物理接口 ===")
            for iface in mgr.get_physical_interfaces():
                print(
                    f"{iface['name']:20} {iface['status']:10} {iface['protocol']:10} {iface['speed']:8} {iface['mac']}"
                    )

            print("\n=== Eth-Trunk ===")
            for trunk in mgr.get_eth_trunks():
                print(f"{trunk['name']:20} {trunk['status']:10} 成员: {', '.join(trunk['members'])}")

    except Exception as e:
        print(e)
