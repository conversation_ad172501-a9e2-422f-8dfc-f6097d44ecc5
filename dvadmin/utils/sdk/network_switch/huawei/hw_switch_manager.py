from .hw_switch_read import HuaweiSwitchReader


class HuaweiSwitchManager(HuaweiSwitchReader):
    def __init__(self, ip: str, username: str, password: str, port: int = 22, timeout: int = 10,
                 verbose: bool = False, max_retries: int = 3, device_type="huawei"):
        super().__init__(ip, username, password, port, timeout, verbose, max_retries, device_type)

    def create_trunk(self, trunk_name: str, trunk_members: list) -> bool:
        """
        创建 E-trunk 聚合端口
        """
        pass
