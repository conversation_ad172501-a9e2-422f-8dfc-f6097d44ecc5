from datetime import datetime
from uuid import uuid4
from typing import List, Union, Optional, Any, Dict, Tuple, Callable
import xlsxwriter
from django import apps
from django.db import models
from django.db.models import QuerySet, Field, ForeignKey
from decimal import Decimal
import json
from collections import defaultdict


class ExcelWriter:
    """支持 ForeignKey 字段优化的高性能 Excel 导出工具"""

    def __init__(self, file_name: Optional[str] = None):
        """
        初始化 Excel 写入器

        :param file_name: 输出文件名，如果为 None 则自动生成
        """
        self.file_name = file_name or f'export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        self.workbook = xlsxwriter.Workbook(self.file_name)
        self.worksheet = None
        self.current_row = 0
        self.foreign_key_field_config = {}

        # 预定义格式属性
        self.format_properties = {
            'header': {
                'bold': True, 'font_size': 12, 'align': 'center',
                'valign': 'vcenter', 'border': 1, 'bg_color': '#D3D3D3',
                'text_wrap': True, 'font_name': 'Arial'
                },
            'default': {
                'align': 'left', 'valign': 'vcenter', 'border': 1,
                'text_wrap': True
                },
            'date': {
                'num_format': 'yyyy-mm-dd', 'align': 'left',
                'valign': 'vcenter', 'border': 1
                },
            'datetime': {
                'num_format': 'yyyy-mm-dd hh:mm:ss', 'align': 'left',
                'valign': 'vcenter', 'border': 1
                },
            'money': {
                'num_format': '¥#,##0.00', 'align': 'right',
                'valign': 'vcenter', 'border': 1
                },
            'integer': {
                'num_format': '#,##0', 'align': 'right',
                'valign': 'vcenter', 'border': 1
                },
            'boolean': {
                'align': 'center', 'valign': 'vcenter', 'border': 1
                }
            }

        # 预创建格式对象
        self.formats = {
            name: self._create_format(props)
            for name, props in self.format_properties.items()
            }

        # 字段类型映射到格式
        self.field_type_format_map = {
            'AutoField': 'integer',
            'BigAutoField': 'integer',
            'IntegerField': 'integer',
            'BigIntegerField': 'integer',
            'SmallIntegerField': 'integer',
            'PositiveIntegerField': 'integer',
            'PositiveSmallIntegerField': 'integer',
            'DecimalField': 'money',
            'FloatField': 'money',
            'DateField': 'date',
            'DateTimeField': 'datetime',
            'BooleanField': 'boolean',
            'NullBooleanField': 'boolean',
            'ForeignKey': 'default'
            }

        # 列宽自动调整
        self._column_widths = {}

    def _create_format(self, properties: Dict) -> Any:
        """创建单元格格式"""
        return self.workbook.add_format(properties)

    def _merge_formats(self, base_format_name: str, extra_props: Dict) -> Any:
        """合并格式属性并创建新格式"""
        base_props = self.format_properties.get(base_format_name, {}).copy()
        base_props.update(extra_props)
        return self._create_format(base_props)

    @staticmethod
    def _get_field_verbose_name(field: Field) -> str:
        """获取字段的显示名称"""
        return field.verbose_name if hasattr(field, 'verbose_name') else field.name

    def _get_field_format(self, field: Field) -> Dict:
        """根据字段类型获取默认格式配置"""
        field_type = field.get_internal_type()
        format_type = self.field_type_format_map.get(field_type, 'default')
        return self.format_properties.get(format_type, {})

    @staticmethod
    def _get_foreign_key_display(obj: Any, field_name: str) -> str:
        """
        获取 ForeignKey 字段的显示值
        默认使用 __str__() 方法
        """
        related_obj = getattr(obj, field_name)
        if related_obj is None:
            return 'unknown'
        return str(related_obj)

    def generate_field_map(
            self,
            model_class,
            custom_config: Dict[str, Union[Dict, Callable]] = None,
            exclude_fields: List[str] = None,
            foreign_key_config: Dict[str, Union[str, Callable]] = None
            ) -> Dict[str, Tuple[str, Dict]]:
        """
        自动生成 field_map 配置（修复关联字段导出问题）
        """
        field_map = {}
        exclude_fields = exclude_fields or []
        foreign_key_config = foreign_key_config or {}

        for field in model_class._meta.get_fields():
            if field.name in exclude_fields:
                continue

                # 获取显示名称（关键修改：不再跳过关联字段）
            display_name = self._get_field_verbose_name(field)
            format_config = self._get_field_format(field)

            # 处理 ForeignKey 字段（确保外键配置生效）
            if isinstance(field, ForeignKey):
                if field.name in foreign_key_config:
                    if callable(foreign_key_config[field.name]):
                        # 处理函数直接添加到 custom_config
                        if custom_config is None:
                            custom_config = {}
                        custom_config[field.name] = foreign_key_config[field.name]
                    else:
                        # 字符串配置修改显示名称
                        display_name = f"{display_name}({foreign_key_config[field.name]})"
                else:
                    # 默认使用外键对象的 __str__ 方法
                    if custom_config is None:
                        custom_config = {}
                    custom_config[field.name] = lambda obj, fn=field.name: self._get_foreign_key_display(obj, fn)

            # 应用自定义配置
            if custom_config and field.name in custom_config:
                if not callable(custom_config[field.name]):
                    format_config.update(custom_config[field.name])

            field_map[field.name] = (display_name, format_config)
        return field_map

    def add_worksheet(self, sheet_name: Optional[str] = None) -> None:
        """添加新的工作表"""
        self.worksheet = self.workbook.add_worksheet(sheet_name)
        self.current_row = 0
        self._column_widths = {}

    def write_header(self, headers: List[Tuple[str, str, Optional[Dict]]]) -> None:
        """写入表头"""
        if not self.worksheet:
            self.add_worksheet()

        for col, header_info in enumerate(headers):
            if len(header_info) == 3 and header_info[2]:
                fmt = self._merge_formats('header', header_info[2])
            else:
                fmt = self.formats['header']

            self.worksheet.write(self.current_row, col, header_info[1], fmt)
            self._column_widths[col] = len(header_info[1]) * 1.2

        self.current_row += 1

    def _auto_detect_format(self, value: Any, col_type: str = None) -> Any:
        """根据值类型自动选择格式"""
        if value is None:
            return self.formats['default']
        elif isinstance(value, datetime):
            return self.formats['datetime']
        elif isinstance(value, Decimal):
            return self.formats['money']
        elif isinstance(value, float):
            return self.formats['money']
        elif isinstance(value, bool):
            return self.formats['boolean']
        elif isinstance(value, int):
            if col_type and 'int' in col_type.lower():
                return self.formats['integer']
            return self.formats['default']
        elif isinstance(value, (dict, list)):
            return self.formats['default']
        return self.formats['default']

    @staticmethod
    def _format_value(value: Any, col_type: str = None, foreign_key_field_name: str = None) -> Any:
        """格式化值以便写入Excel"""
        if value is None:
            return ""
        elif isinstance(value, bool):
            return "是" if value else "否"
        elif isinstance(value, (dict, list)):
            return json.dumps(value, ensure_ascii=False)
        elif isinstance(value, Decimal):
            return float(value)
        elif isinstance(value, datetime):
            return value.strftime("%Y-%m-%d %H:%M:%S")

        elif isinstance(value, models.Model):
            if hasattr(value, '__str__'):
                value = str(value)
            else:
                value = str(value)
            return value
        elif isinstance(value, int) and col_type and 'int' in col_type.lower():
            return value
        return value

    def write_row(
            self,
            row_data: List[Any],
            col_types: List[str] = None,
            custom_handlers: Dict[str, Callable] = None
            ) -> None:
        """
        写入单行数据

        :param row_data: 行数据列表
        :param col_types: 字段类型列表
        :param custom_handlers: 自定义字段处理函数 {字段名: 处理函数}
        """
        if not self.worksheet:
            self.add_worksheet()
        if not custom_handlers:
            custom_handlers = {}

        for col, data in enumerate(row_data):
            col_type = col_types[col] if col_types else None
            field_name = col_types[col] if col_types else None  # 这里用col_types传递字段名

            # 应用自定义处理函数
            if custom_handlers and field_name in custom_handlers:
                data = custom_handlers[field_name](data)

            fmt = self._auto_detect_format(data, col_type)
            foreign_key_field_names = custom_handlers.keys()
            formatted_value = self._format_value(data, col_type, field_name)
            self.worksheet.write(self.current_row, col, formatted_value, fmt)

            # 更新列宽
            cell_width = len(str(formatted_value)) * 1.1
            if cell_width > self._column_widths.get(col, 0):
                self._column_widths[col] = cell_width

        self.current_row += 1

    def write_model_data(
            self,
            queryset: QuerySet,
            field_map: Dict[str, Tuple[str, Dict]] = None,
            custom_config: Dict[str, Union[Dict, Callable]] = None,
            exclude_fields: List[str] = None,
            foreign_key_config: Dict[str, Union[str, Callable]] = None
            ) -> None:
        """
        从Django模型QuerySet导出数据

        :param queryset: Django QuerySet
        :param field_map: 字段映射 {模型字段名: (显示名称, 格式配置)}
        :param custom_config: 自定义字段配置 {字段名: 格式配置或处理函数}
        :param exclude_fields: 要排除的字段列表
        :param foreign_key_config: ForeignKey 字段配置 {字段名: '显示字段名' 或处理函数}
        """
        model = queryset.model

        # 如果没有提供 field_map，则自动生成
        if field_map is None:
            field_map = self.generate_field_map(
                model,
                custom_config=custom_config,
                exclude_fields=exclude_fields,
                foreign_key_config=foreign_key_config
                )

        # 准备表头
        headers = [(field, *info) for field, info in field_map.items()]
        self.write_header(headers)

        # 获取字段类型信息和自定义处理函数
        col_types = []
        custom_handlers = {}

        for field_name, _ in field_map.items():
            field = model._meta.get_field(field_name)
            col_types.append(field.get_internal_type())

            # 收集自定义处理函数
            if custom_config and field_name in custom_config and callable(custom_config[field_name]):
                custom_handlers[field_name] = custom_config[field_name]
            elif isinstance(field, ForeignKey):
                # 默认 ForeignKey 处理
                custom_handlers[field_name] = lambda obj, fn=field_name: self._get_foreign_key_display(obj, fn)
        # 写入数据
        for obj in queryset:
            row_data = []
            for field_name, _ in field_map.items():
                value = getattr(obj, field_name)
                row_data.append(value)

            self.write_row(row_data, col_types, custom_handlers)

        # 应用自动列宽
        self._apply_column_widths()

    def write_data(
            self,
            data: Union[QuerySet, List[Dict]],
            expand_column: Optional[str] = None,
            field_map: Dict[str, Tuple[str, Dict]] = None,
            custom_config: Dict[str, Union[Dict, Callable]] = None,
            exclude_fields: List[str] = None,
            foreign_key_config: Dict[str, Union[str, Callable]] = None,
            keep_columns: Optional[List[str]] = None,
            custom_formatters: Optional[Dict[str, Callable]] = None,
            header_mapping: Optional[Dict[str, str]] = None,
            use_model_verbose_name=True,
            expand_prefix: str = "[明细]",
            ) -> None:
        """
        最终修复版：确保在所有情况下外键字段正确导出
        """
        self.foreign_key_field_config = foreign_key_config or {}

        if isinstance(data, QuerySet):
            if expand_column:
                # 处理带展开列的情况（修复点：传递exclude_fields）
                self._handle_query_set_with_expand(
                    data, expand_column, keep_columns,
                    custom_formatters, header_mapping,
                    expand_prefix, foreign_key_config,
                    exclude_fields  # 新增参数传递
                    )
            else:
                # 处理普通QuerySet情况
                self._handle_simple_query_set(
                    data, field_map, custom_config,
                    exclude_fields, foreign_key_config
                    )
        elif isinstance(data, list) and all(isinstance(x, dict) for x in data):
            if expand_column:
                # 处理带展开列的字典数据
                self._write_expanded_dict_data(
                    data, expand_column, keep_columns,
                    custom_formatters, header_mapping,
                    expand_prefix, foreign_key_config
                    )
            else:
                # 处理普通字典数据
                self._write_simple_dict_data(
                    data, custom_formatters,
                    foreign_key_config, header_mapping
                    )
        else:
            raise ValueError("Unsupported data type")

    def _handle_query_set_with_expand(
            self,
            queryset: QuerySet,
            expand_column: str,
            keep_columns: Optional[List[str]] = None,
            custom_formatters: Optional[Dict[str, Callable]] = None,
            header_mapping: Optional[Dict[str, str]] = None,
            expand_prefix: str = "[明细]",
            foreign_key_config: Optional[Dict[str, Union[str, Callable]]] = None,
            exclude_fields: Optional[List[str]] = None,  # 添加这个参数
            use_model_verbose_name: bool = True,
            ) -> None:
        """处理需要展开明细的QuerySet数据"""
        model = queryset.model

        # 1. 获取所有外键字段映射
        fk_fields = {
            f.name: f"{f.name}_id"
            for f in model._meta.get_fields()
            if isinstance(f, ForeignKey)
            }

        # 2. 转换数据时保留外键对象
        dict_data = []
        for obj in queryset:
            item = obj.to_dict
            # 恢复外键原始对象
            for fk_name in (foreign_key_config or {}).keys():
                if fk_name in fk_fields:
                    item[fk_name] = getattr(obj, fk_name)  # 获取完整外键对象
                    item.pop(fk_fields[fk_name], None)  # 移除_id字段

            # 添加展开列数据
            item[expand_column] = getattr(obj, expand_column)
            dict_data.append(item)

            # 3. 处理字段名映射
        if header_mapping:
            new_mapping = {}
            for field, display in header_mapping.items():
                # 处理外键字段映射
                if field in fk_fields:
                    new_mapping[field] = display  # 原始字段名
                    new_mapping[fk_fields[field]] = display  # _id字段名
                else:
                    new_mapping[field] = display
            header_mapping = new_mapping

            # 4. 自动生成表头映射
        if use_model_verbose_name:
            auto_mapping = self._get_model_header_mapping(model, expand_column)
            if header_mapping:
                auto_mapping.update(header_mapping)
            header_mapping = auto_mapping

            # 5. 确保保留所有需要的列（修复点：传递exclude_fields）
        keep_cols = keep_columns or self._get_all_model_fields(model, exclude_fields)

        self._write_expanded_dict_data(
            dict_data, expand_column,
            keep_cols,
            custom_formatters,
            header_mapping,
            expand_prefix,
            foreign_key_config
            )

    def _get_model_header_mapping(
            self,
            model: models.Model,
            expand_column: str
            ) -> Dict[str, str]:
        """
        获取模型的字段verbose_name映射
        """
        header_mapping = {}

        # 主表字段
        for field in model._meta.get_fields():
            verbose_name = getattr(field, 'verbose_name', None)
            header_mapping[field.name] = str(verbose_name) if verbose_name else field.name

            # 展开表字段
        try:
            first_obj = model.objects.first()
            if first_obj:
                expand_data = getattr(first_obj, expand_column)
                expand_items = json.loads(expand_data) if isinstance(expand_data, str) else expand_data
                if expand_items and isinstance(expand_items, list) and len(expand_items) > 0:
                    for field in expand_items[0].keys():
                        header_mapping[field] = field
        except Exception as e:
            print(f"Warning: Could not parse expand column fields - {str(e)}")

        return header_mapping

    def _get_all_model_fields(
            self,
            model: models.Model,
            exclude_fields: List[str] = None
            ) -> List[str]:
        """获取模型所有字段（包括外键）"""
        exclude_fields = exclude_fields or []
        return [
            f.name for f in model._meta.get_fields()
            if f.name not in exclude_fields and not f.many_to_many
            ]

    def _handle_simple_query_set(
            self,
            queryset: QuerySet,
            field_map: Dict[str, Tuple[str, Dict]] = None,
            custom_config: Dict[str, Union[Dict, Callable]] = None,
            exclude_fields: List[str] = None,
            foreign_key_config: Dict[str, Union[str, Callable]] = None
            ) -> None:
        """处理不展开的QuerySet数据"""
        model = queryset.model

        if field_map is None:
            field_map = self.generate_field_map(
                model,
                custom_config=custom_config,
                exclude_fields=exclude_fields,
                foreign_key_config=foreign_key_config
                )

        # 准备表头
        headers = [(field, *info) for field, info in field_map.items()]
        self.write_header(headers)

        # 准备列类型和处理器
        col_types = []
        custom_handlers = {}

        for field_name, _ in field_map.items():
            field = model._meta.get_field(field_name)
            col_types.append(field.get_internal_type())

            # 收集自定义处理函数
            if custom_config and field_name in custom_config and callable(custom_config[field_name]):
                custom_handlers[field_name] = custom_config[field_name]
            elif isinstance(field, ForeignKey):
                # 应用外键配置
                if foreign_key_config and field_name in foreign_key_config:
                    handler = foreign_key_config[field_name]
                    if callable(handler):
                        custom_handlers[field_name] = handler
                    else:
                        # 字符串配置处理
                        def get_attr(value, attr_name=handler):
                            if value and hasattr(value, attr_name):
                                return getattr(value, attr_name)
                            return str(value)

                        custom_handlers[field_name] = get_attr
                else:
                    # 默认处理
                    custom_handlers[field_name] = lambda obj, fn=field_name: self._get_foreign_key_display(obj, fn)

        # 写入数据
        for obj in queryset:
            row_data = []
            for field_name, _ in field_map.items():
                value = getattr(obj, field_name)
                if field_name in custom_handlers:
                    value = custom_handlers[field_name](value)
                row_data.append(value)

            self.write_row(row_data, col_types)

        self._apply_column_widths()

    def _write_simple_dict_data(
            self,
            data: List[Dict],
            custom_formatters: Optional[Dict[str, Callable]] = None,
            foreign_key_config: Optional[Dict[str, Union[str, Callable]]] = None,
            header_mapping: Optional[Dict[str, str]] = None
            ) -> None:
        """处理普通字典数据（不展开）"""
        if not data:
            return

            # 准备表头
        sample = data[0]
        headers = []
        custom_handlers = custom_formatters or {}

        # 处理外键配置
        if foreign_key_config:
            for fk_name, handler in foreign_key_config.items():
                if callable(handler):
                    custom_handlers[fk_name] = handler
                elif isinstance(handler, str):
                    def get_attr(value, attr_name=handler):
                        if value and hasattr(value, attr_name):
                            return getattr(value, attr_name)
                        return str(value)

                    custom_handlers[fk_name] = get_attr

                    # 构建表头
        for col in sample.keys():
            display_name = header_mapping.get(col, col) if header_mapping else col
            headers.append((col, display_name, {}))

        self.write_header(headers)

        # 写入数据
        for item in data:
            row_data = []
            for col, _ in headers:
                value = item.get(col)
                if col in custom_handlers:
                    value = custom_handlers[col](value)
                row_data.append(value)

            self.write_row(row_data)

        self._apply_column_widths()

    def _write_expanded_dict_data(
            self,
            data: List[Dict],
            expand_column: str,
            keep_columns: Optional[List[str]] = None,
            custom_formatters: Optional[Dict[str, Callable]] = None,
            header_mapping: Optional[Dict[str, str]] = None,
            expand_prefix: str = "[明细]",
            foreign_key_config: Optional[Dict[str, Union[str, Callable]]] = None
            ) -> None:
        """修复版：正确处理外键字段值"""
        if not data:
            return

            # 1. 准备字段列表
        keep_columns = self._get_keep_columns(data[0], expand_column, keep_columns)
        expand_fields = self._get_expand_fields(data[0][expand_column])

        # 2. 准备表头
        headers = []
        for col in keep_columns:
            display_name = header_mapping.get(col, col) if header_mapping else col
            headers.append((col, display_name, {}))

        for field in expand_fields:
            prefixed_field = f"{expand_prefix}{field}"
            display_name = (
                header_mapping.get(prefixed_field, header_mapping.get(field, field))
                if header_mapping else field
            )
            if field in keep_columns:
                display_name = f"{expand_prefix}{display_name}"
            headers.append((field, display_name, {}))

        # 3. 写入表头
        self.write_header(headers)

        # 4. 准备自定义处理函数（关键修复点）
        custom_handlers = custom_formatters or {}
        if foreign_key_config:
            for fk_name, handler in foreign_key_config.items():
                # 处理函数直接应用
                if callable(handler):
                    if fk_name in keep_columns:
                        custom_handlers[fk_name] = handler
                    elif f"{fk_name}_id" in keep_columns:
                        custom_handlers[f"{fk_name}_id"] = lambda val, h=handler: h(val) if val else None
                        # 字符串配置处理（修复点）
                elif isinstance(handler, str):
                    if fk_name in keep_columns:
                        def get_attr(value, attr_name=handler):
                            if value and hasattr(value, attr_name):
                                return getattr(value, attr_name)
                            return str(value)

                        custom_handlers[fk_name] = get_attr
                    elif f"{fk_name}_id" in keep_columns:
                        def get_attr_from_id(id_value, attr_name=handler):
                            if not id_value:
                                return ""
                            # 这里需要根据实际情况获取对象
                            # 示例：假设可以通过模型类获取
                            try:
                                model = apps.get_model('contract', 'User')  # 替换为实际模型
                                obj = model.objects.get(pk=id_value)
                                return getattr(obj, attr_name, str(obj))
                            except Exception:
                                return str(id_value)

                        custom_handlers[f"{fk_name}_id"] = get_attr_from_id

                        # 5. 写入数据
        merge_ranges = defaultdict(list)
        cell_values = {}

        for row_idx, item in enumerate(data):
            try:
                expand_data = item[expand_column]
                expand_items = json.loads(expand_data) if isinstance(expand_data, str) else expand_data
            except Exception as e:
                print(f"Error parsing expand column: {str(e)}")
                expand_items = []

            start_row = self.current_row

            for expand_item in expand_items:
                row_data = []

                # 写入保持列
                for col_idx, (col, _, _) in enumerate(headers[:len(keep_columns)]):
                    value = item.get(col)
                    if col in custom_handlers:
                        try:
                            value = custom_handlers[col](value)
                        except Exception as e:
                            print(f"Error processing column {col}: {str(e)}")
                            value = str(value)
                    row_data.append(value)
                    cell_values[(self.current_row, col_idx)] = value

                    # 写入展开列
                for field in expand_fields:
                    value = expand_item.get(field)
                    if field in custom_handlers:
                        try:
                            value = custom_handlers[field](value)
                        except Exception as e:
                            print(f"Error processing field {field}: {str(e)}")
                            value = str(value)
                    row_data.append(value)

                self.write_row(row_data)

                # 记录合并范围
            if self.current_row - 1 > start_row:
                for col_idx in range(len(keep_columns)):
                    merge_ranges[col_idx].append((start_row, self.current_row - 1))

        # 合并单元格
        for col_idx, ranges in merge_ranges.items():
            for start_row, end_row in ranges:
                value = cell_values.get((start_row, col_idx), "")
                self.worksheet.merge_range(
                    start_row, col_idx, end_row, col_idx,
                    value,
                    self.formats['default']
                    )

        self._apply_column_widths()

    @staticmethod
    def _get_keep_columns(sample_item: Dict, expand_column: str, keep_columns: Optional[List[str]]) -> List[str]:
        """获取需要保留的列（排除展开列）"""
        if keep_columns is None:
            return [col for col in sample_item.keys() if col != expand_column]
        return [col for col in keep_columns if col != expand_column and col in sample_item]

    @staticmethod
    def _get_display_name(field: str, header_mapping: Optional[Dict[str, str]]) -> str:
        """获取字段显示名称（优先使用header_mapping）"""
        if header_mapping and field in header_mapping:
            return header_mapping[field]
        return field

    @staticmethod
    def _get_expand_fields(expand_data: Any) -> List[str]:
        """从展开数据中提取字段名"""
        if isinstance(expand_data, str):
            expand_items = json.loads(expand_data)
        else:
            expand_items = expand_data
        return list(expand_items[0].keys()) if expand_items else []

    @staticmethod
    def _get_default_keep_columns(
            model: models.Model,
            exclude_fields: Optional[List[str]] = None
            ) -> List[str]:
        """获取模型默认保留字段（排除expand_column和指定排除字段）"""
        exclude_fields = exclude_fields or []
        return [
            f.name for f in model._meta.get_fields()
            if f.name not in exclude_fields and not f.is_relation
            ]

    def _apply_column_widths(self) -> None:
        """应用自动调整的列宽"""
        for col, width in self._column_widths.items():
            self.worksheet.set_column(col, col, min(width, 200))

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """确保工作簿正确关闭"""
        if exc_type is not None:
            print(f"Error occurred: {exc_val}")
        self.workbook.close()
        return False


if __name__ == '__main__':
    import os

    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
    import django

    django.setup()
    from contract.models import Contract, Payment

    export_data = Contract.objects.filter(is_deleted=False).all()

    # 自定义配置
    custom_config_data = {
        'amount': {'bold': True, 'color': "#ffffff"},  # 金额加粗
        # 'status': lambda obj: f"状态: {obj.status.upper()}"  # 自定义状态显示
        }

    # ForeignKey 配置
    foreign_key_config_data = {
        # 'creator': 'name',  # 显示 creator 的 name 字段
        # 或使用函数
        'customer': 'name',
        'creator': lambda obj: obj.name  if obj.name  else '无',
        # 'customer': lambda obj: obj.name  if obj.name  else None,
        }

    # 使用示例
    with ExcelWriter("contract_payments.xlsx") as writer:
        # 方法1: 自动生成 field_map
        writer.write_data(
            data=export_data,
            custom_config=custom_config_data,
            foreign_key_config=foreign_key_config_data,
            exclude_fields=['seq', 'id', 'modifier', 'dept_belong_id', 'is_deleted'],
            expand_column='sku_list',
            header_mapping={
                "name": "【合同】名称",
                "customer": "关联客户名称",
                "creator":"创建人",
                "description": "合同备注",
                "[产品]name": "名称",
                "[产品]unit": "单位",
                "[产品]count": "数量",
                "[产品]price": "价格",
                "[产品]attributes": "规格",
                "[产品]description": "备注",
                "[产品]label":  "标签",
                "[产品]total_price": "总价",
                },
            expand_prefix="[产品]",
            use_model_verbose_name=True,
            )
        pay_data = Payment.objects.filter(
            is_deleted=False
            ).all()
        # 方法2: 使用专用工作表
        writer.add_worksheet("回款记录")
        writer.write_data(
            data=pay_data,
            exclude_fields=['seq', 'id', 'modifier', 'dept_belong_id', 'is_deleted',
                            'payment_period_history'],
            header_mapping={
                "creator":  "创建人",
                },
            use_model_verbose_name=True,
            foreign_key_config={
        "creator": "name"
        },
            )
