INTERVAL_RESOURCE_VALUES_CONFIG = {
    'xresource.MachineRoom': {
        'key': 'id',
        'value': 'name',
        # 'path_key': 'resource',
        'path_key': 'resource/machine_room',
    },
    'xresource.PrivateRoom': {
        'key': 'id',
        'value': 'name',
        # 'path_key': 'resource',
        'path_key': 'resource/private_room',
    },
    'xresource.IDCRackMachine': {
        'key': 'id',
        'value': 'rack_sn',
        # 'path_key': 'resource',
        'path_key': 'resource/idc_rack_machine',
    },
    'xresource.NetworkHardware': {
        'key': 'id',
        'value': 'physical_machine_sn',
        # 'path_key': 'resource',
        'path_key': 'resource/physical_asset/network_hardware',
    },
    'xresource.PhysicalServerMachine': {
        'key': 'id',
        'value': 'physical_machine_sn',
        # 'path_key': 'resource',
        'path_key': 'resource/physical_asset/physical_server_machine',
    },
    'xresource.GeneralConsumable': {
        'key': 'id',
        'value': 'name',
        # 'path_key': 'resource'
        'path_key': 'resource/physical_asset/general_consumable',
    },
    'xresource.HolisticAccessory': {
        'key': 'id',
        'value': 'physical_machine_sn',
        # 'path_key': 'resource'
        'path_key': 'resource/physical_asset/holistic_accessory',
    },
    'operatorcmdb.Host': {
            'key': 'id',
            'value': 'name',
            # 'path_key': 'resource'
            'path_key': 'operatorcmdb/host',
        }
}

TEMP_INTERVAL_RESOURCE_PUBLIC_PATH_KEY_CONFIG = 'resource'
