# -*- coding: utf-8 -*-

"""
@author: 猿小天
@contact: QQ:1638245306
@Created on: 2021/5/31 031 22:08
@Remark: 公共基础model类
"""
from datetime import datetime, date
from importlib import import_module
from uuid import uuid4
from django.apps import apps
from django.db import models

from application.settings import base

table_prefix = base.TABLE_PREFIX  # 数据库表名前缀


class SoftDeleteQuerySet(models.QuerySet):
    pass


class SoftDeleteManager(models.Manager):
    """支持软删除"""

    def __init__(self, *args, **kwargs):
        self.__add_is_del_filter = False
        super(SoftDeleteManager, self).__init__(*args, **kwargs)

    def filter(self, *args, **kwargs):
        # 考虑是否主动传入is_deleted
        if not kwargs.get('is_deleted') is None:
            self.__add_is_del_filter = True
        return super(SoftDeleteManager, self).filter(*args, **kwargs)

    def get_queryset(self):
        if self.__add_is_del_filter:
            return SoftDeleteQuerySet(self.model, using=self._db).exclude(is_deleted=False)
        return SoftDeleteQuerySet(self.model).exclude(is_deleted=True)

    def get_by_natural_key(self, name):
        return SoftDeleteQuerySet(self.model).get(username=name)


class SoftDeleteModel(models.Model):
    """
    软删除模型
    一旦继承,就将开启软删除
    """
    is_deleted = models.BooleanField(verbose_name="是否软删除", help_text='是否软删除', default=False, db_index=True)
    objects = SoftDeleteManager()

    class Meta:
        abstract = True
        verbose_name = '软删除模型'
        verbose_name_plural = verbose_name

    def delete(self, using=None, soft_delete=True, *args, **kwargs):
        """
        重写删除方法,直接开启软删除
        """
        self.is_deleted = True
        self.save(using=using)


class CoreModel(models.Model):
    """
    核心标准抽象模型模型,可直接继承使用
    增加审计字段, 覆盖字段时, 字段名称请勿修改, 必须统一审计字段名称
    """
    id = models.BigAutoField(primary_key=True, help_text="Id", verbose_name="Id")
    description = models.CharField(max_length=255, verbose_name="描述", null=True, blank=True, help_text="描述")
    creator = models.ForeignKey(to=base.AUTH_USER_MODEL, related_query_name='creator_query', null=True,
                                verbose_name='创建人', help_text="创建人", on_delete=models.SET_NULL,
                                db_constraint=False)
    modifier = models.CharField(max_length=255, null=True, blank=True, help_text="修改人", verbose_name="修改人")
    dept_belong_id = models.IntegerField(help_text="数据归属部门", null=True, blank=True,
                                      verbose_name="数据归属部门")
    update_datetime = models.DateTimeField(auto_now=True, null=True, blank=True, help_text="修改时间",
                                           verbose_name="修改时间")
    create_datetime = models.DateTimeField(auto_now_add=True, null=True, blank=True, help_text="创建时间",
                                           verbose_name="创建时间")

    @staticmethod
    def __field_to_value__(value):
        if not value:
            return ''
        elif isinstance(value, datetime):
            current_value = value.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(value, date):
            current_value = value.strftime('%Y-%m-%d')
        else:
            current_value = value
        return current_value

    def _to_dict(self):
        data = {}
        for field in self._meta.fields:
            field_name = field.attname
            value = getattr(self, field_name, None)
            value = self.__field_to_value__(value)
            data[field_name] = value
        return data

    class Meta:
        abstract = True
        verbose_name = '核心模型'
        verbose_name_plural = verbose_name


class ChaosCoreModel(models.Model):
    """
    核心标准抽象模型模型,可直接继承使用
    增加审计字段, 覆盖字段时, 字段名称请勿修改, 必须统一审计字段名称
    """
    seq = models.BigAutoField(
        primary_key=True, help_text="seq", verbose_name="seq", db_comment='主键Seq')
    id = models.CharField(
        max_length=63, default='', unique=True, db_index=True, help_text="Id", verbose_name="Id", db_comment='Id')
    description = models.CharField(
        max_length=255, verbose_name="描述", null=True, blank=True, help_text="描述", db_comment='描述')
    creator = models.ForeignKey(
        to=base.AUTH_USER_MODEL, to_field='id', related_query_name='creator_query', null=True,
        verbose_name='创建人', help_text="创建人", on_delete=models.SET_NULL,
        db_constraint=False, db_comment='创建人')
    modifier = models.CharField(
        max_length=255, null=True, blank=True, help_text="修改人", verbose_name="修改人", db_comment='修改人')
    dept_belong_id = models.CharField(
        max_length=255, help_text="数据归属部门", null=True, blank=True, verbose_name="数据归属部门", db_comment='数据归属部门')
    update_datetime = models.DateTimeField(
        auto_now=True, null=True, blank=True, help_text="修改时间", verbose_name="修改时间", db_comment='修改时间')
    create_datetime = models.DateTimeField(
        auto_now_add=True, null=True, blank=True, help_text="创建时间", verbose_name="创建时间", db_comment='创建时间')
    is_deleted = models.BooleanField(
        verbose_name="是否软删除", help_text='是否软删除', default=False, db_index=True, db_comment='是否软删除')

    @classmethod
    def get_db_table(cls):
        return cls._meta.db_table

    def save(self, *args, **kwargs):
        if not self.id:
            t_db_table = self.get_db_table().split('_', 1)[1]
            if t_db_table[-1] == 's':
                t_db_table = t_db_table[:-1]
            self.id = '{table_name}-{uuid_str}'.format(table_name=t_db_table, uuid_str=str(uuid4().hex))[:63]
        super(ChaosCoreModel, self).save(*args, **kwargs)

    def delete(self, using=None, keep_parents=False):
        # Override the default delete method to perform a soft delete
        self.is_deleted = True
        self.save(using=using)

    class Meta:
        abstract = True
        verbose_name = '核心模型'
        verbose_name_plural = verbose_name


def get_all_models_objects(model_name=None):
    """
    获取所有 models 对象
    :return: {}
    """
    base.ALL_MODELS_OBJECTS = {}
    if not base.ALL_MODELS_OBJECTS:
        all_models = apps.get_models()
        for item in list(all_models):
            table = {
                "tableName": item._meta.verbose_name,
                "table": item.__name__,
                "tableFields": []
            }
            for field in item._meta.fields:
                fields = {
                    "title": field.verbose_name,
                    "field": field.name
                }
                table['tableFields'].append(fields)
            base.ALL_MODELS_OBJECTS.setdefault(item.__name__, {"table": table, "object": item})
    if model_name:
        return base.ALL_MODELS_OBJECTS[model_name] or {}
    return base.ALL_MODELS_OBJECTS or {}


def get_model_from_app(app_name):
    """获取模型里的字段"""
    model_module = import_module(app_name + '.models')
    filter_model = [
        getattr(model_module, item) for item in dir(model_module)
        if item != 'CoreModel' and item != 'ChaosModel' and issubclass(getattr(model_module, item).__class__, models.base.ModelBase)
    ]
    model_list = []
    for model in filter_model:
        if model.__name__ == 'AbstractUser':
            continue
        fields = [
            {'title': field.verbose_name, 'name': field.name, 'object': field}
            for field in model._meta.fields
        ]
        model_list.append({
            'app': app_name,
            'verbose': model._meta.verbose_name,
            'model': model.__name__,
            'object': model,
            'fields': fields
        })
    return model_list


def get_custom_app_models(app_name=None):
    """
    获取所有项目下的app里的models
    """
    if app_name:
        return get_model_from_app(app_name)
    all_apps = apps.get_app_configs()
    res = []
    for app in all_apps:
        if app.name.startswith('django'):
            continue
        if app.name in base.COLUMN_EXCLUDE_APPS:
            continue
        try:
            all_models = get_model_from_app(app.name)
            if all_models:
                for model in all_models:
                    res.append(model)
        except Exception as e:
            pass
    return res
