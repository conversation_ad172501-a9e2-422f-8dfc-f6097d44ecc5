import re
from django.db.models import F

from rest_framework.filters import BaseFilterBackend

from dvadmin.system.models import ApiWhite<PERSON>ist, RowTargetRole, Users, RowOriginRole
from dvadmin.utils.json_response import ErrorResponse
from dvadmin.utils.resource_permissions.interval_resource_values_config import INTERVAL_RESOURCE_VALUES_CONFIG, TEMP_INTERVAL_RESOURCE_PUBLIC_PATH_KEY_CONFIG

from django.core.cache import cache
from dvadmin.utils.redis_keys.row_permission_filter import USER_API_ROW_PERMISSION


class RowPermissionsFilter(BaseFilterBackend):
    """
    数据 级权限过滤器
    0. 获取用户的部门id，没有部门则返回空
    1. 判断过滤的数据是否有创建人所在部门 "creator" 字段,没有则返回全部
    2. 如果用户没有关联角色则返回本部门数据
    3. 根据角色的最大权限进行数据过滤(会有多个角色，进行去重取最大权限)
    3.1 判断用户是否为超级管理员角色/如果有1(所有数据) 则返回所有数据

    4. 只为仅本人数据权限时只返回过滤本人数据，并且部门为自己本部门(考虑到用户会变部门，只能看当前用户所在的部门数据)
    5. 自定数据权限 获取部门，根据部门过滤
    """

    def filter_queryset(self, request, queryset, view):
        """
        接口白名单是否认证数据权限
        """
        api = request.path  # 当前请求接口
        method = request.method  # 当前请求方法

        """
        判断是否为超级管理员:
        如果不是超级管理员,则进入下一步权限判断
        """
        if request.user.is_superuser == 1:
            return queryset
        methodList = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        method = methodList.index(method)

        queryset = self._filter_permission_rows(request, queryset, api, method)
        return queryset

    @staticmethod
    def _filter_permission_rows(request, queryset, api, method):
        user_id = getattr(request.user, 'id')
        role_ids = Users.objects.get(id=user_id).role.values_list('id', flat=True)
        if not role_ids:
            return ErrorResponse(msg="未获取到角色信息")
        user_api_row_perm = cache.get(
            USER_API_ROW_PERMISSION.format(
                USER_ID=request.user.id,
                API_PATH=api,
                API_METHOD=method
            ), False)
        if not user_api_row_perm:
            # 未配置资源权限的API 则 直接返回
            interval_resource_config = {}
            for key, value in INTERVAL_RESOURCE_VALUES_CONFIG.items():
                if value['path_key'] in api:
                    interval_resource_config = value
                    interval_resource_config['row_origin'] = key
                    break
            if not interval_resource_config:
                return queryset.none()
            # 获取已开启数据权限授权的资源类型
            row_origins = RowOriginRole.objects.filter(
                role__in=list(role_ids),
                is_external=False,
                is_open_row_permission=True
            ).values_list('row_origin', flat=True)
            if not row_origins:
                return queryset.none()
            if interval_resource_config['row_origin'] in row_origins:
                user_api_row_perm = True
                cache.set(USER_API_ROW_PERMISSION.format(
                    USER_ID=user_id,
                    API_PATH=api,
                    API_METHOD=method
                ), True)
        if user_api_row_perm:
            row_ids = RowTargetRole.objects.filter(
                role__in=list(role_ids)
            ).values_list('row_id', flat=True)
            row_ids = set(list(row_ids))
            queryset = queryset.filter(
                is_deleted=False,
                id__in=row_ids
            )
            return queryset
        return queryset.none()
