"""
<AUTHOR>
@Date    ：2024/11/8
"""
import pytz
import arrow
from django.utils import timezone


def get_nested_value(data, known_keys):
    """
    从多级嵌套字典中获取指定键的值，其中某一级的键是未知的，且某些层级包含列表。

    :param data: 多级嵌套字典
    :param known_keys: 已知键的列表，其中某一级的键可以用 '*' 表示未知键
    :return: 键对应的值，如果找不到则返回None
    """
    if not isinstance(data, (dict, list)) or not known_keys:
        return None

    current_data = data
    for key in known_keys:
        if key == '*':
            # 处理未知键的情况
            if isinstance(current_data, dict):
                for sub_key, sub_value in current_data.items():
                    if isinstance(sub_value, (dict, list)):
                        result = get_nested_value(sub_value, known_keys[known_keys.index(key) + 1:])
                        if result is not None:
                            return result
            elif isinstance(current_data, list):
                for item in current_data:
                    if isinstance(item, (dict, list)):
                        result = get_nested_value(item, known_keys[known_keys.index(key) + 1:])
                        if result is not None:
                            return result
            return None
        else:
            if isinstance(current_data, dict) and key in current_data:
                current_data = current_data[key]
            elif isinstance(current_data, list):
                for item in current_data:
                    if isinstance(item, (dict, list)):
                        result = get_nested_value(item, [key] + known_keys[known_keys.index(key) + 1:])
                        if result is not None:
                            return result
            else:
                return None

    return current_data


def str_utc_to_shanghai_date(str_utc_time: str):
    """
    字符串 UTC 转化为上海时区的指定格式的日期

    :param str_utc_time: 字符串 UTC 时间
    :return: 格式化的上海时区的日期
    """
    if not str_utc_time:
        return None
    # 将字符串解析为 UTC 时间
    utc_time = arrow.get(str_utc_time)

    # 转换为上海时区
    shanghai_time = utc_time.to('Asia/Shanghai')

    # 格式化输出
    formatted_shanghai_time = shanghai_time.format('YYYY-MM-DD HH:mm:ss')

    return formatted_shanghai_time


def get_shanghai_str_time():
    # 获取当前的 UTC 时间（aware）
    now_utc = timezone.now()

    # 定义上海时区
    shanghai_tz = pytz.timezone('Asia/Shanghai')

    # 将 UTC 时间转换为上海时间
    now_shanghai = now_utc.astimezone(shanghai_tz)

    # 格式化时间为字符串
    now_localtime_str = now_shanghai.strftime('%Y-%m-%d %H:%M:%S')

    return now_localtime_str


def get_utc_str_time():
    # 获取当前的 UTC 时间（aware）
    now_utc = timezone.now()
    # 格式化时间为字符串
    now_localtime_str = now_utc.strftime('%Y-%m-%d %H:%M:%S')

    return now_localtime_str


def mask_string(s, head_chars=3, tail_chars=3, max_length=16):
    """
    遮掩字符串，首尾显示指定字符数，中间部分用 '*' 替换。输出为固定长度

    :param s: 输入字符串
    :param head_chars: 首部显示的字符数
    :param tail_chars: 尾部显示的字符数
    :param max_length: 最大显示长度
    :return: 遮掩后的字符串
    """
    total_chars = head_chars + tail_chars
    if len(s) <= max_length:
        return s  # 如果字符串长度不超过最大长度，直接返回

    if len(s) <= total_chars:
        return s  # 如果字符串长度不超过首尾显示的总字符数，直接返回

    masked_part_length = len(s) - total_chars
    masked_part = '*' * masked_part_length

    result = s[:head_chars] + masked_part + s[-tail_chars:]

    if len(result) > max_length:
        # 如果结果超过最大长度，截断
        result = result[:max_length]

    return result


def mask_constant_length_string(s, head_chars=3, tail_chars=3, max_length=16):
    """
    遮掩字符串，首尾显示指定字符数，中间部分用 '*' 替换，并填充到最大显示长度。

    :param s: 输入字符串
    :param head_chars: 首部显示的字符数
    :param tail_chars: 尾部显示的字符数
    :param max_length: 最大显示长度
    :return: 遮掩后的字符串
    """
    total_chars = head_chars + tail_chars
    if len(s) <= max_length:
        # 如果字符串长度不超过最大长度，直接返回并填充到最大长度
        return s.ljust(max_length, '*')

    if len(s) <= total_chars:
        # 如果字符串长度不超过首尾显示的总字符数，直接返回并填充到最大长度
        return s.ljust(max_length, '*')

    masked_part_length = len(s) - total_chars
    masked_part = '*' * masked_part_length

    result = s[:head_chars] + masked_part + s[-tail_chars:]

    if len(result) < max_length:
        # 如果结果长度小于最大长度，填充到最大长度
        result = result.ljust(max_length, '*')

    return result
