"""
<AUTHOR>
@Date    ：2024/12/24
"""

import os
import mimetypes
from django.core.files import File as DjangoFile
from dvadmin.system.views.file_list import FileSerializer


def get_mime_type(file_path):
    # 确保 .docx 文件的 MIME 类型被正确注册
    # 注册常见的 Office 文件 MIME 类型
    mimetypes.add_type('application/vnd.openxmlformats-officedocument.wordprocessingml.document', '.docx')
    mimetypes.add_type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '.xlsx')
    mimetypes.add_type('application/vnd.openxmlformats-officedocument.presentationml.presentation', '.pptx')

    # 推断 MIME 类型
    mime_type, _ = mimetypes.guess_type(file_path)
    if not mime_type:
        mime_type = 'application/octet-stream'  # 默认为二进制流
    return mime_type


def upload_to_file_system(file_path, request=None):
    """
    后端文件上传到前端文件管理中
    :param file_path: 本地文件的绝对路径
    :param request: Request 对象，在 serializer 中使用
    :return: 上传成功后返回的 file_url 或 url
    :raises Exception: 上传失败时抛出异常并返回上传失败的原因
    :rtype: dict
    :usage: upload_to_file_system(file_path, request)  # 示例：将本地 Word 文档上传到前端文件管理中并返回 file_url 或 url
    """
    with open(file_path, 'rb') as fp:
        django_file = DjangoFile(fp, name=os.path.basename(file_path))
        # 获取 MIME 类型
        mime_type = get_mime_type(django_file.name)
        data = {
            'file': django_file,
            'mime_type': mime_type,
        }
        context = {
            'request': request  # 将请求对象传递给 serializer
        }
        serializer = FileSerializer(data=data, context=context)
        if serializer.is_valid():
            serializer.save()
            return serializer.data or serializer.data
        else:
            raise Exception("文件上传失败: " + str(serializer.errors))
