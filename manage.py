#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys


def main():
    """Run administrative tasks."""
    # Custom settings
    # 解析命令行参数
    settings_module = 'application.settings.interval_admin'  # 默认配置文件
    if len(sys.argv) > 1 and sys.argv[1] == 'runserver':
        for (index, args) in enumerate(sys.argv):
            if '--settings' in args:
                t_args = args.split('=')
                if len(t_args) <= 1:
                    raise SystemExit('Please use `--settings=example` argv, example is settings file.')
                t_settings = args.split('=')[1]
                settings_module = f'application.settings.{t_settings}'
                del sys.argv[index]
    # End Custom settings

    # 设置 DJANGO_SETTINGS_MODULE 环境变量
    print('******************************')
    print(settings_module)
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', settings_module)
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
