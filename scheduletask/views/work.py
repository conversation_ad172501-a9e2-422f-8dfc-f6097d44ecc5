import json
from django.forms.models import model_to_dict
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

from operatorcmdb.models import Host
from application.logger import logger
from application.settings.base import WORKER_ENV
from scheduletask.utils.ansible_runner_handler import Env<PERSON><PERSON>atorFactory, AnsibleRunnerWrapper
from scheduletask.models import Work, AnsTemplate
from scheduletask.serializers.work import WorkSerializer, WorkImportSerializer
from dvadmin.system.models import Users
from dvadmin.celery_workers import sync_scheduletask_worker
from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.middleware import RequestMiddleware
from dvadmin.utils.json_response import DetailResponse, ErrorResponse, SuccessResponse


class WorkModelViewSet(ChaosCustomModelViewSet):
    queryset = Work.objects.order_by('-create_datetime')
    serializer_class = WorkSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
    }
    # 导入
    import_serializer_class = WorkImportSerializer


    def list(self, request, *args, **kwargs):
        try:
            logger.info("进入任务列表接口...")
            queryset = self.filter_queryset(self.get_queryset())
            query_params = dict(request.query_params)
            if "creator_name" in query_params.keys():
                user_ids = list(Users.objects.filter(name__icontains=query_params.get("creator_name")[0]).values_list("id"))
                queryset = queryset.filter(creator_id__in=user_ids)
            elif "template_name" in query_params.keys():
                queryset = queryset.filter(template__name__icontains=query_params.get("template_name")[0])
            
            page = self.paginate_queryset(queryset)
            serializer = None
            if page is not None:
                serializer = self.get_serializer(page, many=True, request=request)
            else:
                serializer = self.get_serializer(queryset, many=True, request=request)

            for work_info in serializer.data:
                temp_name = AnsTemplate.objects.get(id=work_info.get('template')).name if work_info.get(
                    'template') else ""
                work_info['template_name'] = temp_name
                host_list = list(
                    Host.objects.filter(id__in=work_info.get("host_ids")).values_list('ip_private')) if work_info.get(
                    'host_ids') else []
                work_info['host_list'] = [item[0] for item in host_list] if host_list else []

            if page is not None:
                return self.get_paginated_response(serializer.data)
        except Exception as e:
            return ErrorResponse(msg=f"获取任务列表失败 ERROR: {str(e)}")
        return SuccessResponse(data=serializer.data, msg="获取成功")


    @staticmethod
    def create_exec_work(project_vars,template_id,host_ids,description):
        try:
            request = RequestMiddleware.get_current_request()
            inventory = {
                "all": {
                    "hosts": {},
                    "vars": project_vars
                },
            }
            # 1. 拼接inventory 根据host操作系统实例化不同的item 最终添加到inventory中
            # 2. 执行ansible-runner异步任务
            project_path = AnsTemplate.objects.get(id=template_id).project_path

            for host_id in host_ids:
                host_obj = model_to_dict(Host.objects.get(id=host_id))
                env_generator = EnvGeneratorFactory.get_envgenerator(host_obj['os'])
                host, host_vers = env_generator.get_env_dict(host_obj,WORKER_ENV)
                inventory["all"]["hosts"][host] = host_vers

            work_data = {
                'params': project_vars,
                'host_ids': host_ids,
                'template': template_id,
                'description':description
            }

            logger.info(f"开始执行任务 任务参数: {work_data}")
            serializer = WorkSerializer(data=work_data,request=request)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            work_id = serializer.data['id']
            data = {
                "id": work_id,
                "inventory": inventory,
                "private_data_dir": project_path
            }
            #queue = "dev_queue"
            sync_scheduletask_worker.run.apply_async(args=[data],)

        except Exception as e:
            logger.error(f"执行任务失败,<Detail: {str(e)}>")
            return False,{},f"执行任务失败,<Detail: {str(e)}>"
        return True, data, "任务开始执行"


    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def exec(self, request):
        request_data = request.data
        logger.info("进入任务执行接口...")
        project_vars = request_data.get('params', {})
        template_id = request_data.get('template_id')
        request_data['template'] = template_id
        host_ids = request_data.get('host_ids')
        description = request_data.get("description")
        code, data, msg = self.create_exec_work(project_vars, template_id, host_ids, description)
        if not code:
            return ErrorResponse(msg=f"执行任务失败,<Detail: {str(msg)}>")
        return DetailResponse(msg=msg, data=data['id'])


    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def apply(self, request):
        req_data = request.data

        try:
            template_info = model_to_dict(AnsTemplate.objects.get(id=req_data.get('template')))
            host_info_list = []
            for host_id in req_data.get('host_ids'):
                host_info_list.append(model_to_dict(Host.objects.get(id=host_id)))

            item = {
                "template_info": template_info,
                "host_info_list": host_info_list
            }

        except Exception as e:
            logger.error(f"应用失败,<Detail: {str(e)}>")
            return ErrorResponse(msg="应用失败")
        return SuccessResponse(data=item)
