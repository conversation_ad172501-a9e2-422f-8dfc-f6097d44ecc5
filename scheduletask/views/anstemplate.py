import os, zipfile, time, uuid, shutil
from rest_framework.decorators import action
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from application.logger import logger
from scheduletask.utils.file_processor import FileProcessor, ExtractorFactory
from scheduletask.config.const import *
from dvadmin.utils.json_response import DetailResponse, ErrorResponse, SuccessResponse
from scheduletask.models import AnsTemplate
from scheduletask.serializers.anstemplate import AnsTemplateImportSerializer, AnsTemplateSerializer


class AnsTemplateModelViewSet(ChaosCustomModelViewSet):
    queryset = AnsTemplate.objects.order_by('-create_datetime')
    serializer_class = AnsTemplateSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = AnsTemplateImportSerializer

    def create(self, request, *args, **kwargs):
        """
        创建模板：
        1. 将上传的压缩文件解压缩
        2. 解压之后将解压的目录copy到CHAOS_NFS_DIR下 (如果有同名文件 添加时间戳后缀)
        3. 将项目路径、其他参数入库
        """
        project_dirs = request.FILES.getlist('project_package')
        try:
            for project_dir in project_dirs:
                template_dirs = []
                # 定义解压路径和目标复制路径
                decompression_dir = os.path.join('/tmp', str(uuid.uuid4()))

                # 使用工厂获取适当的解压器
                extractor = ExtractorFactory.get_extractor(project_dir.name)
                extractor.file = project_dir

                # 创建 FileProcessor 实例
                processor = FileProcessor(extractor, decompression_dir, CHAOS_NFS_DIR)

                # 处理文件并获取结果
                template_dirs, success = processor.process()

                if not success:
                    return ErrorResponse(msg=f"添加模板失败 ERROR: {template_dirs[0]}")

                for project_path in template_dirs:
                    name = os.path.basename(project_path)
                    data = {
                        "name": name,
                        "project_path": project_path
                    }
                    serializer = AnsTemplateSerializer(data=data, request=request)
                    serializer.is_valid(raise_exception=True)
                    serializer.save()
        except Exception as e:
            return ErrorResponse(msg=f"添加模板失败 ERROR: {str(e)}")
        return SuccessResponse(msg="添加成功")
