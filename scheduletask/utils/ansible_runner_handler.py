import time
import ansible_runner
from scheduletask.config.const import *


class Authenticator:
    def get_env_dict(self):
        raise NotImplementedError("This method should be overridden.")


class PassWDEnvGenerator(Authenticator):
    def get_env_dict(self,meta_data,runtime_env="development"):
        host = meta_data.get('ip_private')
        print(f"用户密码认证： {meta_data}")
        ansible_ssh_user = meta_data.get('ssh_user',CHAOS_OS_DEBIAN_DEFAULT_USER) if meta_data.get("ssh_user") else CHAOS_OS_DEBIAN_DEFAULT_USER
        ansible_ssh_password = meta_data.get('ssh_passwd', CHAOS_OS_DEBIAN_DEFAULT_PASSWD) if meta_data.get("ssh_passwd") else CHAOS_OS_DEBIAN_DEFAULT_PASSWD
        
        vars = {
            "ansible_ssh_password":ansible_ssh_password,
            "ansible_ssh_user": ansible_ssh_user,
            "ansible_ssh_timeout":60,
        }
        return host,vars


class SecretEnvGenerator(Authenticator):
    def get_env_dict(self,meta_data,runtime_env="development"):
        host = meta_data.get('ip_private')
        print(f"密钥认证： {meta_data}")
        ansible_ssh_user = CHAOS_OS_REDHAT_DEFAULT_USER
        ansible_ssh_private_key_file = ""
        if runtime_env == "production":
          ansible_ssh_private_key_file = CHAOS_OS_REDHAT_PRO_SECRET
        else:
          ansible_ssh_private_key_file = CHAOS_OS_REDHAT_DEFAULT_SECRET

        vars = {
            "ansible_ssh_private_key_file":ansible_ssh_private_key_file,
            "ansible_ssh_user": ansible_ssh_user,
            "ansible_ssh_timeout":60,
        }
        return host,vars


class EnvGeneratorFactory:
    @staticmethod
    def get_envgenerator(distribution):
        if any(distribution.lower().startswith(host_os) for host_os in CHAOS_OS_PASSWD_AUTH_LIST):
            return PassWDEnvGenerator()
        elif any(distribution.lower().startswith(host_os) for host_os in CHAOS_OS_KEY_AUTH_LIST):
            return SecretEnvGenerator() 
        else:
            raise ValueError("Unsupported os.")


class AnsibleRunnerWrapper:
    def __init__(
        self,
        inventory,
        private_data_dir,
        playbook="playbook.yml",
        timeout=CHAOS_ANSIBLE_TIMEOUT
    ):
        self.inventory = inventory
        self.playbook = playbook
        self.timeout = timeout
        self.private_data_dir=private_data_dir
        self.runner = None
        self.status = None
        self.stdout_path = None

    def run_async(self):
        # 后台运行
        self.runner = ansible_runner.run_async(
            private_data_dir=self.private_data_dir,
            inventory=self.inventory,
            playbook=self.playbook,
            timeout=self.timeout,
            status_handler=self.status_handler
        )

    def run(self):
        # 前台运行
        self.runner = ansible_runner.run(
            private_data_dir=self.private_data_dir,
            inventory=self.inventory,
            playbook=self.playbook,
            timeout=self.timeout,
            status_handler=self.status_handler
        )

    def is_running(self):
        # 检查 Ansible 运行是否仍在进行中
        if self.runner:
            return self.runner.status == 'running'
        return False

    def read_lines(self):
        # 读取 Ansible 的输出，流式读取
        if self.runner:
            for line in self.runner.stdout:
                yield line

    @property
    def code(self):
        """
        ansible-runner 同步运行时返回状态码
        """
        return self.runner.rc if self.runner else None

    def status_handler(self,data,runner_config):
        """
        ansible-runner 状态回调：starting -> running -> canceled | timeout | failed | successful
        """
        runner_ident = data.get("runner_ident")
        if not runner_ident:
            raise RuntimeError("The runner_ident is None...")
        self.status = data.get("status")
        self.stdout_path = os.path.join(
            self.private_data_dir, 'artifacts', runner_ident, 'stdout')


if __name__ == "__main__":
    inventory = {'all': {'hosts': {'************': {'ansible_ssh_private_key_file': '/datadisk/chaos_templates/secret_keys/centos.key', 'ansible_ssh_user': 'centos'}}, 'vars': {'docker_version': '26.00', 'cuda_version': '12.4'}}}
    project_path = "/datadisk/chaos_templates/demo_autoexectemplate"
    # project_path = "/datadisk/chaos_templates/demo_autoexectemplate2_20250126153227"
    ansible_runner_obj = AnsibleRunnerWrapper(
        inventory=inventory,
        playbook='playbook.yml',
        private_data_dir=project_path)
    ansible_runner_obj.run()
