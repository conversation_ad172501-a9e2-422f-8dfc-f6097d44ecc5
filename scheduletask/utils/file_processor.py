import os, shutil, time
import zipfile
import tarfile


class Extractor:
    def extract(self, file_path, output_dir):
        raise NotImplementedError("This method should be overridden.")


class ZipExtractor(Extractor):
    def extract(self, file_path, output_dir):
        with zipfile.ZipFile(file_path, 'r') as zip_ref:
            zip_ref.extractall(output_dir)


class TarExtractor(Extractor):
    def extract(self, file_path, output_dir):
        with tarfile.open(file_path, 'r') as tar_ref:
            tar_ref.extractall(output_dir)


class TarGzExtractor(Extractor):
    def extract(self, file_path, output_dir):
        with tarfile.open(file_path, 'r:gz') as tar_ref:
            tar_ref.extractall(output_dir)


class ExtractorFactory:
    @staticmethod
    def get_extractor(file_path):
        if file_path.endswith('.zip'):
            return ZipExtractor()
        elif file_path.endswith('.tar'):
            return TarExtractor()
        elif file_path.endswith('.tar.gz'):
            return TarGzExtractor()
        else:
            raise ValueError("Unsupported file format.")


class FileProcessor:
    def __init__(self, extractor, decompression_dir, target_dir):
        self.extractor = extractor
        self.decompression_dir = decompression_dir
        self.target_dir = target_dir

    def process(self):
        # 创建解压目录（如果不存在）
        if not os.path.exists(self.decompression_dir):
            os.makedirs(self.decompression_dir)

        # 解压缩文件
        try:
            self.extractor.extract(self.extractor.file, self.decompression_dir)
        except Exception as e:
            shutil.rmtree(self.decompression_dir)
            return [str(e)], False

            # 目录列表
        template_dirs = []

        # 将解压后的文件复制到目标目录
        for item in os.listdir(self.decompression_dir):
            source_path = os.path.join(self.decompression_dir, item)
            target_path = os.path.join(self.target_dir, item)

            # 如果目标路径已存在，添加时间戳后缀
            if os.path.exists(target_path):
                timestamp = time.strftime("%Y%m%d%H%M%S")
                name, extension = os.path.splitext(item)
                target_path = os.path.join(self.target_dir, f"{name}_{timestamp}{extension}")

            # 复制文件或目录 将成功的目录添加到模板目录列表中
            try:
                if os.path.isdir(source_path):
                    shutil.copytree(source_path, target_path)
                else:
                    shutil.copy2(source_path, target_path)
                template_dirs.append(target_path)
            except Exception as e:
                continue

        shutil.rmtree(self.decompression_dir)

        return template_dirs, True
