import os,time
import json
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from websockets.exceptions import ConnectionClosedOK
from scheduletask.models import Work


class LogConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.work_id = self.scope['url_route']['kwargs']['work_id']
        self.log_file_path,self.work_status = await self.get_log_file_path(self.work_id)
        self.log_file = None  # 初始化文件对象
        self.tail_task = None  # 用于存储 tail 任务的引用

        # 接受 WebSocket 连接
        await self.accept()
        await self.send(text_data="Wait for connection ... \n")

    async def disconnect(self, close_code):
        # 断开连接时的清理
        if self.tail_task:
            self.tail_task.cancel()  # 取消 tail 任务
            try:
                await self.tail_task  # 等待任务完成（可能会抛出 CancelledError）
            except asyncio.CancelledError:
                pass
        if self.log_file:
            self.log_file.close()  # 关闭文件对象
            self.log_file = None  # 清空文件对象

    async def receive(self, text_data):
        # 处理客户端发来的消息
        if text_data == "ping":
            await self.send(text_data="pong")
        else:
            try:
                # 如果获取文件失败 尝试重新获取；如果三次获取还是失败则主动关闭websocket
                for i in range(3):
                    self.log_file_path, self.work_status = await self.get_log_file_path(self.work_id)
                    if self.log_file_path and os.path.exists(self.log_file_path):
                        await self.send(text_data="The connection success log information is as follows: \n")
                        # 先读取文件的所有内容，然后开始实时读取新内容
                        await self.read_all_lines(self.log_file_path)
                        # 启动 tail 任务
                        self.tail_task = asyncio.create_task(self.tail_log_file(self.log_file_path))
                        break
                    else:
                        # 如果文件不存在 并且任务状态是异常的 那么可能是celery运行任务的时候出现异常
                        if self.work_status == 3:
                            await self.send(text_data="The connection fail: the celery processing task may be abnormal ... \n")
                            break

                        # 文件不存在时拒绝连接
                        await self.send(text_data=f"The connection fail：log file does not exist: {self.log_file_path}, try to connect again. \n")
                        if i == 2:
                            await self.send(text_data=f"Try to connect again fail,the connection is closed, please open it again later. \n")
                            await self.close()
                        await asyncio.sleep(1)
            except ConnectionClosedOK:
                pass

    async def read_all_lines(self, file_path):
        """
        读取日志文件的所有现有内容（不跳过任何行）
        """
        try:
            with open(file_path, 'r') as log_file:
                # 读取文件中的所有行
                lines = log_file.readlines()
                for line in lines:
                    await self.send(text_data=line)  # 将每行发送给客户端
        except Exception as e:
            print(f"Error reading log file: {e}")

    async def tail_log_file(self, file_path):
        """
        实时读取日志文件内容并通过 WebSocket 发送给客户端（类似 `tail -f`）
        """
        self.log_file = open(file_path, 'r')  # 打开文件并保存到实例变量
        self.log_file.seek(0, os.SEEK_END)  # 将文件指针移动到文件末尾，准备读取新的内容

        try:
            while True:
                line = self.log_file.readline()
                if not line:
                    await asyncio.sleep(0.1)
                    continue
                # 发送新读取的日志行
                await self.send(text_data=line)
        except asyncio.CancelledError:
            # 任务被取消时的处理
            pass
        except Exception as e:
            print(f"Error reading log file: {e}")
        finally:
            if self.log_file:
                self.log_file.close()  # 确保在退出时关闭文件

    @database_sync_to_async
    def get_log_file_path(self, work_id):
        """
        根据任务 ID 获取日志文件路径
        """
        work_obj = Work.objects.get(id=work_id)
        stdout_path = work_obj.stdout_path
        status = work_obj.status
        return stdout_path,status
