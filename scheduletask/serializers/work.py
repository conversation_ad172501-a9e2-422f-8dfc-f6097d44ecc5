from scheduletask.models import Work
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer


class WorkImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = Work
        exclude = ()


# 这里是创建/更新时的列化器
class WorkSerializer(ChaosCustomModelSerializer):
    class Meta:
        model = Work
        fields = '__all__'
