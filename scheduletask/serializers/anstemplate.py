from scheduletask.models import AnsTemplate
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer


class AnsTemplateImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = AnsTemplate
        exclude = ()


# 这里是创建/更新时的列化器
class AnsTemplateSerializer(ChaosCustomModelSerializer):
    class Meta:
        model = AnsTemplate
        fields = '__all__'
