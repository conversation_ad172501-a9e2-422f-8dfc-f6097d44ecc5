from django.db import models
from dvadmin.utils.chaos_models import ChaosCoreModel

TABLE_PREFIX = 'scheduletask_'


class AnsTemplate(ChaosCoreModel):
    name = models.CharField(max_length=255, unique=True)
    project_path = models.CharField(max_length=1024, verbose_name="ansible项目路径")
    params = models.JSONField(default=list, null=True, verbose_name="模板参数 用于执行的时候使用")

    class Meta:
        db_table = TABLE_PREFIX + "templates"
        verbose_name = "模板表"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)


class Work(ChaosCoreModel):
    WORK_STATUS = {
        0: '待执行',
        1: '执行中',
        2: '成功',
        3: '失败',
    }
    params = models.JSONField(default=dict, verbose_name="任务执行时候的参数列表", null=True)
    host_ids = models.JSONField(default=list, verbose_name="执行主机列表", null=True)
    stdout_path = models.CharField(max_length=1024, verbose_name="日志文件路径", null=True)
    status = models.SmallIntegerField(choices=WORK_STATUS, default=0, verbose_name='任务状态', null=True)
    ticket_id = models.CharField(
        max_length=63,  null=True, blank=True, db_index=True,
        help_text="工单ID", db_comment='工单ID', verbose_name='工单ID'
    )
    template = models.ForeignKey(
        'AnsTemplate', verbose_name='模板ID',
        on_delete=models.CASCADE, null=True, to_field='id')

    class Meta:
        db_table = TABLE_PREFIX + "works"
        verbose_name = "任务表"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)
