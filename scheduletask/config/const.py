# coding=utf-8
import os 
from application.settings.base import BASE_DIR
from application.settings.base import MEDIA_ROOT

# 存储相关
CHAOS_NFS_DIR = os.getenv("CHAOS_NFS_DIR", os.path.join(BASE_DIR, MEDIA_ROOT, "ansible_templates"))

# 认证相关
CHAOS_OS_REDHAT_DEFAULT_USER = os.getenv("CHAOS_OS_REDHAT_DEFAULT_USER", "centos")
CHAOS_OS_REDHAT_DEFAULT_SECRET = os.getenv("CHAOS_OS_REDHAT_DEFAULT_SECRET", os.path.join(CHAOS_NFS_DIR, "secret_keys/test.key"))
CHAOS_OS_REDHAT_DEV_SECRET = os.getenv("CHAOS_OS_REDHAT_DEV_SECRET", os.path.join(CHAOS_NFS_DIR, "secret_keys/test.key"))
CHAOS_OS_REDHAT_PRO_SECRET = os.getenv("CHAOS_OS_REDHAT_PRO_SECRET", os.path.join(CHAOS_NFS_DIR, "secret_keys/pro.key"))
CHAOS_OS_DEBIAN_DEFAULT_USER = os.getenv("CHAOS_OS_DEBIAN_DEFAULT_USER", "xingzai")
CHAOS_OS_DEBIAN_DEFAULT_PASSWD = os.getenv("CHAOS_OS_DEBIAN_DEFAULT_PASSWD", "xingzai!@#456")
CHAOS_OS_KEY_AUTH_LIST = ["centos"]
CHAOS_OS_PASSWD_AUTH_LIST = ["ubuntu", "debian", "rocky"]
CHAOS_DEV_PATH_KEY=os.getenv("CHAOS_DEV_PATH_KEY", "chaos.dev.hzxingzai.cn")
CHAOS_PRO_PATH_KEY = os.getenv("CHAOS_PRO_PATH_KEY", "chaos.hzxingzai.cn")

# Ansible相关
CHAOS_ANSIBLE_TIMEOUT = 3600
CHAOS_ANSIBLE_TASK_RUNNING_STATU_LIST = ["starting", "running"]
