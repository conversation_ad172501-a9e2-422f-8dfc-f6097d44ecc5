from rest_framework import routers
from django.urls import path
from scheduletask.views.work import WorkModelViewSet
from scheduletask.views.anstemplate import AnsTemplateModelViewSet

scheduletask_url = routers.SimpleRouter()
scheduletask_url.register(r'work', WorkModelViewSet)
scheduletask_url.register(r'template', AnsTemplateModelViewSet)

app_name = 'scheduletask'

urlpatterns = [
    # path('create_template/', AnsTemplateViewSet.as_view(), name='create_template'),
]
urlpatterns += scheduletask_url.urls
