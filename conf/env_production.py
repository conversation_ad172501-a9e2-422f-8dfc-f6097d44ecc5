"""
<AUTHOR>
@Date    ：2024/10/29
"""
import os


# ########################### DATABASE CONFIGURATION #######################
DATABASE_NAME = os.environ.get('DATABASE_NAME', '')  # mysql 时使用
# 数据库地址
DATABASE_HOST = os.environ.get('DATABASE_HOST', '')
# # 数据库端口
DATABASE_PORT = int(os.environ.get('DATABASE_PORT', 3306))
# # 数据库用户名
DATABASE_USER = os.environ.get('DATABASE_USER', '')
# # 数据库密码
DATABASE_PASSWORD = os.environ.get('DATABASE_PASSWORD', '')

# ########################### TENANT CONFIGURATION #######################
TENANT_DATABASE_NAME = os.environ.get('TENANT_DATABASE_NAME', '')  # mysql 时使用
# 数据库地址
TENANT_DATABASE_HOST = os.environ.get('TENANT_DATABASE_HOST', '')
# # 数据库端口
TENANT_DATABASE_PORT = int(os.environ.get('TENANT_DATABASE_PORT', 3306))
# # 数据库用户名
TENANT_DATABASE_USER = os.environ.get('TENANT_DATABASE_USER', '')
# # 数据库密码
TENANT_DATABASE_PASSWORD = os.environ.get('TENANT_DATABASE_PASSWORD', '')
# ########################### TENANT CONFIGURATION END #######################

SECRET_KEY = os.environ.get('SECRET_KEY', '"django-insecure--z8%exyzt7e_%i@1+#1mm=%lb5=^fx_57=1@a+_y7bg5-w%)sm"')

# ================================================= #
# ******** redis配置，无redis 可不进行配置  ******** #
# ================================================= #
REDIS_DB = int(os.environ.get('REDIS_DB', 1))
CELERY_BROKER_DB = int(os.environ.get('CELERY_BROKER_DB', 2))
CHAOS_CELERY_BROKER_DB = int(os.environ.get('CHAOS_CELERY_BROKER_DB', CELERY_BROKER_DB + 1))
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', '')
REDIS_HOST = os.environ.get('REDIS_HOST', '127.0.0.1')
REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
REDIS_URL = f'redis://:{REDIS_PASSWORD or ""}@{REDIS_HOST}:{REDIS_PORT}'
# ================================================= #
# ****************** 功能 启停  ******************* #
# ================================================= #
DEBUG = os.environ.get('DEBUG', False)
# API文档是否显示, 生产环境不显示，默认为JSON
DEFAULT_RENDERER_CLASS = 'rest_framework.renderers.JSONRenderer'


# ======================================================== #
# ****************** 数据库字段 加密配置  ******************* #
# ======================================================== #
ENCRYPTED_FIELDS_KEY = os.environ.get(
    "ENCRYPTED_FIELDS_KEY",
    "PQYn2FlA9nxzJy7C7z2Zm-Usjy-uunxKYZ72eovbIUE="
)

# 开启 OpenTelemetry 日志
IS_OPEN_OTLP = True
