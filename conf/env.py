import os
import pymysql
# 读取 WORKER_ENV 环境变量，默认为 development
WORKER_ENV = os.environ.get('WORKER_ENV', 'development')
# 公共配置项

# ================================================= #
# *************** mysql数据库 配置  *************** #
# ================================================= #
pymysql.install_as_MySQLdb()
# 表前缀
TABLE_PREFIX = "chaos_"
DATABASE_ENGINE = "django.db.backends.mysql"


# ================================================= #
# ****************** 其他 配置  ******************* #
# ================================================= #

ALLOWED_HOSTS = ["*"]
# 列权限中排除App应用
COLUMN_EXCLUDE_APPS = []

# 动态配置项
if WORKER_ENV == 'development':
    # 此调用不可消除，否则会出现配置无法加载的问题
    from conf.env_development import *
    # 此为动态生成
    REDIS_URL = f'redis://:{REDIS_PASSWORD or ""}@{REDIS_HOST}:{REDIS_PORT}'
else:
    from conf.env_production import *
