import requests
from requests.auth import HTTPBasic<PERSON>uth

def get_nat_server():
    url = 'http://10.10.200.9:10008/restconf/data/huawei-nat-server:nat-server/'
    headers = {
        'Content-Type': 'application/yang.data+xml',
        'Accept': '*/*',
    }
    response = requests.get(
        url, headers=headers,
        auth=HTTPBasicAuth('chaos_nat', '131231312')
        )
    print(response.headers)
    print(response.text)
    return response.json()


if __name__ == '__main__':
    print(get_nat_server())


