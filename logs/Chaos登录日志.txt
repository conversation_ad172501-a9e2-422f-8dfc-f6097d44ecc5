(.venv) PS D:\星临办公\code-projects\projects\nova-django-admin\nova-admin> python .\manage.py runserver
******************************
application.settings.interval_admin
******************************
application.settings.interval_admin
[2025-01-07 17:32:04][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
Performing system checks...

System check identified no issues (0 silenced).

You have 1 unapplied migration(s). Your project may not work properly until you apply the migrations for app(s): resource.
Run 'python manage.py migrate' to apply them.
January 07, 2025 - 17:32:05
Django version 5.0.6, using settings 'application.settings.interval_admin'
Starting ASGI/Channels version 3.0.5 development server at http://127.0.0.1:8000/
Quit the server with CTRL-BREAK.
[2025-01-07 17:32:05][daphne.server.run():113] [INFO] HTTP/2 support not enabled (install the http2 and tls Twisted extras)
[2025-01-07 17:32:05][daphne.server.run():122] [INFO] Configuring endpoint tcp:port=8000:interface=127.0.0.1
[2025-01-07 17:32:05][daphne.server.listen_success():153] [INFO] Listening on TCP address 127.0.0.1:8000
[2025-01-07 17:32:05][django.channels.server.log_action():168] [INFO] WebSocket HANDSHAKING /ws/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2t
lbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM2MzI4MzYxLCJpYXQiOjE3MzYyNDE5NjEsImp0aSI6IjM5MzgyYjcxOTdjMzQxYjBiNDlmYjFjNjA0MmQ5MDU5IiwidXNlcl9pZCI6MX0.-gGug_YnJc3j5q3eUUDs7OPhpaeSeRJ8EbL7nBUInkQ/ [127.0.0.1:25578]
[2025-01-07 17:32:05][django.channels.server.log_action():164] [INFO] WebSocket CONNECT /ws/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl9
0eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM2MzI4MzYxLCJpYXQiOjE3MzYyNDE5NjEsImp0aSI6IjM5MzgyYjcxOTdjMzQxYjBiNDlmYjFjNjA0MmQ5MDU5IiwidXNlcl9pZCI6MX0.-gGug_YnJc3j5q3eUUDs7OPhpaeSeRJ8EbL7nBUInkQ/ [127.0.0.1:25578]
[2025-01-07 17:32:18][dvadmin.utils.backends.authenticate():22] [INFO] caoxiangpeng 正在使用本地登录...
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP POST /api/login/ 200 [0.62, 127.0.0.1:25646]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/user/user_info/ 200 [0.05, 127.0.0.1:25646]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/init/dictionary/?dictionary_key=all 200 [0.06, 127.0.0.1:25650]
caoxiangpeng
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/init/dictionary/?dictionary_key=all 200 [0.09, 127.0.0.1:25650]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/init/settings/ 200 [0.10, 127.0.0.1:25656]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/user/user_info/ 200 [0.13, 127.0.0.1:25655]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/menu/web_router/ 200 [0.12, 127.0.0.1:25658]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/menu_button/menu_button_all_permission/ 200 [0.14, 127.0.0.1:25646]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/dept/all_dept/ 200 [0.15, 127.0.0.1:25657]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/user/user_info/ 200 [0.05, 127.0.0.1:25656]
caoxiangpeng
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/init/dictionary/?dictionary_key=all 200 [0.07, 127.0.0.1:25658]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/init/settings/ 200 [0.10, 127.0.0.1:25657]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/dept/all_dept/ 200 [0.10, 127.0.0.1:25646]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/menu/web_router/ 200 [0.11, 127.0.0.1:25655]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/menu_button/menu_button_all_permission/ 200 [0.11, 127.0.0.1:25656]
[2025-01-07 17:32:19][django.channels.server.log_action():168] [INFO] WebSocket HANDSHAKING /ws/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2t
lbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM2MzI4NzM4LCJpYXQiOjE3MzYyNDIzMzgsImp0aSI6IjdjYTgyM2U0NGE4NzRhNjliOGQxNTYyZGQ1MmZmMDE1IiwidXNlcl9pZCI6Mn0.z4fBSJBqsPT9vqCDUTWFHTADeL-BbmsHcATVJuBjKqQ/ [127.0.0.1:25691]
[2025-01-07 17:32:19][django.channels.server.log_action():164] [INFO] WebSocket CONNECT /ws/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl9
0eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM2MzI4NzM4LCJpYXQiOjE3MzYyNDIzMzgsImp0aSI6IjdjYTgyM2U0NGE4NzRhNjliOGQxNTYyZGQ1MmZmMDE1IiwidXNlcl9pZCI6Mn0.z4fBSJBqsPT9vqCDUTWFHTADeL-BbmsHcATVJuBjKqQ/ [127.0.0.1:25691]


################################
需要重构的接口
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP POST /api/login/ 200 [0.62, 127.0.0.1:25646]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/dept/all_dept/ 200 [0.15, 127.0.0.1:25657]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/menu/web_router/ 200 [0.11, 127.0.0.1:25655]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/init/dictionary/?dictionary_key=all 200 [0.07, 127.0.0.1:25658]
[2025-01-07 17:32:19][django.channels.server.log_action():147] [INFO] HTTP GET /api/system/menu_button/menu_button_all_permission/ 200 [0.11, 127.0.0.1:25656]

