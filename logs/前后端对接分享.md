# 前后端对接分享
> 1. 前端浏览器的本质：携带格式及优化查看的 txt 文件。

> 2. 我是谁，我在哪，我要做什么？

## 前提条件
1. 作为一个运维开发人员，我需要对接一个`完整的后端系统`，需要对接全新的前端系统，我该如何去选择。

## 第一步：明确后端系统的作用及应对用户场景
> 常见场景如下：

1. PC 端 
    - `常见的运维开发场景`
    - 是否需要直接暴漏到公网(安全要求等级不一样`等保`，核心数据、权限、用户架构要求不一样)
2. PC 端 + 移动端 `大企业的大应用场景`
3. 各类小程序端 
    - 涉及的业务及用户主要为各大小程序端的业务场景。
4. chat 实时对话（对接:个人微信、企业微信、钉钉） 
   - 应用于业务内容无法暴漏到公网但又必须应用的场景。 如：某政务企业一些内部请求查询及创建工单。
   - 内部知识库对接，暴漏给售前机架构师查询（不方便登录PC 端）。

## 第二步：分析后端系统权限架构（核心部分）
1. 后端权限精细度
2. 后端用户架构
    - 是否需要支持多租户
    - 是否需要前台用户和后台用户模块分离
    - 是否需要支持多语言(类似于插件管理，给一个偏好设置->身上别一个胸牌)
    - 是否需要支持单一sso登录（多个电脑只允许同一个账号不允许同时登录，一般需要后台服务端支持）
    - 是否需要支持手机号登录
    - 验证码
3. 后端按钮、菜单、路由权限在哪里控制，还是通过统一控制


## 第二步：确认前端语言/框架
1. Vue + TS/JS/X：目前国内最常见
2. Bootstrap:早期前端框架的模版语言
3. Html+Css+JS：纯手搓
4. EJS: 模版语言：常用用于服务器DOC渲染、文档生成、报告生成
5. React：React 是一个用于构建用户界面（UI）的 JavaScript 库，用户界面由按钮、文本和图像等小单元内容构建而成。React 帮助你把它们组合成可重用、可嵌套的 组件。从 web 端网站到移动端应用，屏幕上的所有内容都可以被分解成组件。在本章节中，你将学习如何创建、定制以及有条件地显示 React 组件。
6. Kotlin: Compose Multiplatform在安卓、iOS、Web的共享UI逻辑。
7. Unit-App：多个小程序前端框架
8. 前后端非分离项目可直接调用后端系统的web-template框架生成语言
9. Naive-UI: vue3.0前端框架
10. Element-UI: vue2.0/vue3.0前端框架
11. AntdV: vue3.0前端框架
12. 其他还有很多

## 第三步：选型已有开源的系统框架（核心部分）
1. [Vben-Admin](https://doc.vben.pro/)
2. [Ant Design Pro](https://preview.pro.ant.design/profile/advanced)
3. [Naive-Admin](https://v1.naiveadmin.com/#/login?redirect=/dashboard)
4. [Django-Admin](https://docs.djangoproject.com/en/4.1/ref/contrib/admin/) 
5. [Django-Admin-美化](https://pythondjango.cn/django/applications/4-django-simple-ui-configuration/)
6. [FastCurd](http://fast-crud.docmirror.cn/)
7. [TinyVue](https://opentiny.design/vue-pro/docs/start)
8. 开源Admin框架前后端分离的还有很多


## 基础内容理解
- [路由守卫（保安、门禁）](https://router.vuejs.org/zh/guide/advanced/navigation-guards.html)
- [HTTP请求详解](https://www.runoob.com/http/http-messages.html)
- [拓展-WS/WSS协议](https://websocket.p2hp.com/)
