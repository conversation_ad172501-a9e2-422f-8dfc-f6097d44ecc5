{"code": 2000, "data": [{"id": 1, "name": "系统管理", "menus": [{"id": 2, "name": "菜单管理", "isCheck": false, "btns": [{"id": 4, "name": "路由", "value": "menu:router", "isCheck": false, "data_range": null}, {"id": 2, "name": "详情", "value": "menu:Retrieve", "isCheck": false, "data_range": null}, {"id": 12, "name": "自动匹配列权限", "value": "column:Match", "isCheck": false, "data_range": null}, {"id": 15, "name": "编辑列权限", "value": "column:Update", "isCheck": false, "data_range": null}, {"id": 13, "name": "编辑", "value": "menu:Update", "isCheck": false, "data_range": null}, {"id": 5, "name": "查询按钮权限", "value": "btn:Search", "isCheck": false, "data_range": null}, {"id": 3, "name": "查询所有", "value": "menu:SearchAll", "isCheck": false, "data_range": null}, {"id": 6, "name": "查询列权限", "value": "column:Search", "isCheck": false, "data_range": null}, {"id": 1, "name": "查询", "value": "menu:Search", "isCheck": false, "data_range": null}, {"id": 10, "name": "新增按钮权限", "value": "btn:Create", "isCheck": false, "data_range": null}, {"id": 11, "name": "新增列权限", "value": "column:Create", "isCheck": false, "data_range": null}, {"id": 7, "name": "新增", "value": "menu:Create", "isCheck": false, "data_range": null}, {"id": 17, "name": "删除按钮权限", "value": "btn:Delete", "isCheck": false, "data_range": null}, {"id": 18, "name": "删除列权限", "value": "column:Delete", "isCheck": false, "data_range": null}, {"id": 16, "name": "删除", "value": "menu:Delete", "isCheck": false, "data_range": null}, {"id": 14, "name": "修改按钮权限", "value": "btn:Update", "isCheck": false, "data_range": null}, {"id": 9, "name": "下移", "value": "menu:MoveDown", "isCheck": false, "data_range": null}, {"id": 8, "name": "上移", "value": "menu:MoveUp", "isCheck": false, "data_range": null}], "columns": []}, {"id": 3, "name": "部门管理", "isCheck": false, "btns": [{"id": 20, "name": "详情", "value": "dept:Retrieve", "isCheck": false, "data_range": null}, {"id": 27, "name": "编辑", "value": "dept:Update", "isCheck": false, "data_range": null}, {"id": 21, "name": "查询所有", "value": "dept:SearchAll", "isCheck": false, "data_range": null}, {"id": 19, "name": "查询", "value": "dept:Search", "isCheck": false, "data_range": null}, {"id": 24, "name": "新增", "value": "dept:Create", "isCheck": false, "data_range": null}, {"id": 22, "name": "懒加载查询所有", "value": "dept:LazySearchAll", "isCheck": false, "data_range": null}, {"id": 23, "name": "头信息", "value": "dept:HeaderInfo", "isCheck": false, "data_range": null}, {"id": 28, "name": "删除", "value": "dept:Delete", "isCheck": false, "data_range": null}, {"id": 26, "name": "下移", "value": "dept:MoveDown", "isCheck": false, "data_range": null}, {"id": 25, "name": "上移", "value": "dept:MoveUp", "isCheck": false, "data_range": null}], "columns": []}, {"id": 4, "name": "角色管理", "isCheck": false, "btns": [{"id": 30, "name": "详情", "value": "role:Retrieve", "isCheck": false, "data_range": null}, {"id": 33, "name": "编辑", "value": "role:Update", "isCheck": false, "data_range": null}, {"id": 29, "name": "查询", "value": "role:Search", "isCheck": false, "data_range": null}, {"id": 31, "name": "权限配置", "value": "role:Permission", "isCheck": false, "data_range": null}, {"id": 32, "name": "新增", "value": "role:Create", "isCheck": false, "data_range": null}, {"id": 35, "name": "删除", "value": "role:Delete", "isCheck": false, "data_range": null}, {"id": 34, "name": "保存", "value": "role:Save", "isCheck": false, "data_range": null}], "columns": [{"id": 1, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 2, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 3, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 4, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 5, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 6, "field_name": "key", "title": "权限字符", "is_query": false, "is_create": false, "is_update": false}, {"id": 7, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 8, "field_name": "name", "title": "角色名称", "is_query": false, "is_create": false, "is_update": false}, {"id": 9, "field_name": "sort", "title": "角色顺序", "is_query": false, "is_create": false, "is_update": false}, {"id": 10, "field_name": "status", "title": "角色状态", "is_query": false, "is_create": false, "is_update": false}, {"id": 11, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}]}, {"id": 5, "name": "用户管理", "isCheck": false, "btns": [{"id": 42, "name": "重设密码", "value": "user:ResetPassword", "isCheck": false, "data_range": null}, {"id": 43, "name": "重置密码", "value": "user:De<PERSON><PERSON><PERSON>ass<PERSON>", "isCheck": false, "data_range": null}, {"id": 37, "name": "详情", "value": "user:Retrieve", "isCheck": false, "data_range": null}, {"id": 41, "name": "编辑", "value": "user:Update", "isCheck": false, "data_range": null}, {"id": 36, "name": "查询", "value": "user:Search", "isCheck": false, "data_range": null}, {"id": 38, "name": "新增", "value": "user:Create", "isCheck": false, "data_range": null}, {"id": 39, "name": "导出", "value": "user:Export", "isCheck": false, "data_range": null}, {"id": 40, "name": "导入", "value": "user:Import", "isCheck": false, "data_range": null}, {"id": 44, "name": "删除", "value": "user:Delete", "isCheck": false, "data_range": null}], "columns": [{"id": 12, "field_name": "avatar", "title": "头像", "is_query": false, "is_create": false, "is_update": false}, {"id": 13, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 14, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 15, "field_name": "dept", "title": "所属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 16, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 17, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 18, "field_name": "email", "title": "邮箱", "is_query": false, "is_create": false, "is_update": false}, {"id": 19, "field_name": "gender", "title": "性别", "is_query": false, "is_create": false, "is_update": false}, {"id": 20, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 21, "field_name": "mobile", "title": "电话", "is_query": false, "is_create": false, "is_update": false}, {"id": 22, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 23, "field_name": "name", "title": "姓名", "is_query": false, "is_create": false, "is_update": false}, {"id": 24, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 25, "field_name": "username", "title": "用户账号", "is_query": false, "is_create": false, "is_update": false}, {"id": 26, "field_name": "user_type", "title": "用户类型", "is_query": false, "is_create": false, "is_update": false}]}, {"id": 6, "name": "岗位管理", "isCheck": false, "btns": [{"id": 48, "name": "编辑", "value": "system:post:Update", "isCheck": false, "data_range": null}, {"id": 47, "name": "添加", "value": "system:post:Create", "isCheck": false, "data_range": null}, {"id": 45, "name": "查询", "value": "system:post:Search", "isCheck": false, "data_range": null}, {"id": 46, "name": "查看", "value": "system:post:Retrieve", "isCheck": false, "data_range": null}, {"id": 49, "name": "删除", "value": "system:post:Delete", "isCheck": false, "data_range": null}], "columns": [{"id": 27, "field_name": "content", "title": "内容", "is_query": false, "is_create": false, "is_update": false}, {"id": 28, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 29, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 30, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 31, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 32, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 33, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 34, "field_name": "target_type", "title": "目标类型", "is_query": false, "is_create": false, "is_update": false}, {"id": 35, "field_name": "title", "title": "标题", "is_query": false, "is_create": false, "is_update": false}, {"id": 36, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}]}, {"id": 7, "name": "行权限管理", "isCheck": false, "btns": [], "columns": [{"id": 37, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 38, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 39, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 40, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 41, "field_name": "enable_datasource", "title": "激活数据权限", "is_query": false, "is_create": false, "is_update": false}, {"id": 42, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 43, "field_name": "method", "title": "接口请求方法", "is_query": false, "is_create": false, "is_update": false}, {"id": 44, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 45, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 46, "field_name": "url", "title": "url", "is_query": false, "is_create": false, "is_update": false}]}, {"id": 10, "name": "消息中心", "isCheck": false, "btns": [{"id": 51, "name": "详情", "value": "messageCenter:Retrieve", "isCheck": false, "data_range": null}, {"id": 53, "name": "编辑", "value": "messageCenter:Update", "isCheck": false, "data_range": null}, {"id": 50, "name": "查询", "value": "messageCenter:Search", "isCheck": false, "data_range": null}, {"id": 52, "name": "新增", "value": "messageCenter:Create", "isCheck": false, "data_range": null}, {"id": 54, "name": "删除", "value": "messageCenter:Delete", "isCheck": false, "data_range": null}], "columns": [{"id": 47, "field_name": "color", "title": "颜色", "is_query": false, "is_create": false, "is_update": false}, {"id": 48, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 49, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 50, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 51, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 52, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 53, "field_name": "is_value", "title": "是否为value值", "is_query": false, "is_create": false, "is_update": false}, {"id": 54, "field_name": "label", "title": "字典名称", "is_query": false, "is_create": false, "is_update": false}, {"id": 55, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 56, "field_name": "parent", "title": "父级", "is_query": false, "is_create": false, "is_update": false}, {"id": 57, "field_name": "remark", "title": "备注", "is_query": false, "is_create": false, "is_update": false}, {"id": 58, "field_name": "sort", "title": "显示排序", "is_query": false, "is_create": false, "is_update": false}, {"id": 59, "field_name": "status", "title": "状态", "is_query": false, "is_create": false, "is_update": false}, {"id": 60, "field_name": "type", "title": "数据值类型", "is_query": false, "is_create": false, "is_update": false}, {"id": 61, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 62, "field_name": "value", "title": "字典编号", "is_query": false, "is_create": false, "is_update": false}, {"id": 143, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 144, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 145, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 146, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 147, "field_name": "enable_datasource", "title": "激活数据权限", "is_query": false, "is_create": false, "is_update": false}, {"id": 148, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 149, "field_name": "method", "title": "接口请求方法", "is_query": false, "is_create": false, "is_update": false}, {"id": 150, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 151, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 152, "field_name": "url", "title": "url", "is_query": false, "is_create": false, "is_update": false}, {"id": 335, "field_name": "content", "title": "内容", "is_query": false, "is_create": false, "is_update": false}, {"id": 336, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 337, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 338, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 339, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 340, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 341, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 342, "field_name": "target_type", "title": "目标类型", "is_query": false, "is_create": false, "is_update": false}, {"id": 343, "field_name": "title", "title": "标题", "is_query": false, "is_create": false, "is_update": false}, {"id": 344, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}]}, {"id": 11, "name": "接口白名单", "isCheck": false, "btns": [{"id": 56, "name": "详情", "value": "api_white_list:Retrieve", "isCheck": false, "data_range": null}, {"id": 58, "name": "编辑", "value": "api_white_list:Update", "isCheck": false, "data_range": null}, {"id": 55, "name": "查询", "value": "api_white_list:Search", "isCheck": false, "data_range": null}, {"id": 57, "name": "新增", "value": "api_white_list:Create", "isCheck": false, "data_range": null}, {"id": 59, "name": "删除", "value": "api_white_list:Delete", "isCheck": false, "data_range": null}], "columns": [{"id": 63, "field_name": "code", "title": "地区编码", "is_query": false, "is_create": false, "is_update": false}, {"id": 64, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 65, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 66, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 67, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 68, "field_name": "enable", "title": "是否启用", "is_query": false, "is_create": false, "is_update": false}, {"id": 69, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 70, "field_name": "initials", "title": "首字母", "is_query": false, "is_create": false, "is_update": false}, {"id": 71, "field_name": "level", "title": "地区层级(1省份 2城市 3区县 4乡级)", "is_query": false, "is_create": false, "is_update": false}, {"id": 72, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 73, "field_name": "name", "title": "名称", "is_query": false, "is_create": false, "is_update": false}, {"id": 74, "field_name": "pcode", "title": "父地区编码", "is_query": false, "is_create": false, "is_update": false}, {"id": 75, "field_name": "pinyin", "title": "拼音", "is_query": false, "is_create": false, "is_update": false}, {"id": 76, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 239, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 240, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 241, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 242, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 243, "field_name": "enable_datasource", "title": "激活数据权限", "is_query": false, "is_create": false, "is_update": false}, {"id": 244, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 245, "field_name": "method", "title": "接口请求方法", "is_query": false, "is_create": false, "is_update": false}, {"id": 246, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 247, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 248, "field_name": "url", "title": "url", "is_query": false, "is_create": false, "is_update": false}]}]}, {"id": 12, "name": "常规配置", "menus": [{"id": 13, "name": "系统配置", "isCheck": false, "btns": [{"id": 61, "name": "详情", "value": "system_config:Retrieve", "isCheck": false, "data_range": null}, {"id": 63, "name": "编辑", "value": "system_config:Update", "isCheck": false, "data_range": null}, {"id": 60, "name": "查询", "value": "system_config:Search", "isCheck": false, "data_range": null}, {"id": 62, "name": "新增", "value": "system_config:Create", "isCheck": false, "data_range": null}, {"id": 64, "name": "删除", "value": "system_config:Delete", "isCheck": false, "data_range": null}], "columns": [{"id": 153, "field_name": "color", "title": "颜色", "is_query": false, "is_create": false, "is_update": false}, {"id": 154, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 155, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 156, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 157, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 158, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 159, "field_name": "is_value", "title": "是否为value值", "is_query": false, "is_create": false, "is_update": false}, {"id": 160, "field_name": "label", "title": "字典名称", "is_query": false, "is_create": false, "is_update": false}, {"id": 161, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 162, "field_name": "parent", "title": "父级", "is_query": false, "is_create": false, "is_update": false}, {"id": 163, "field_name": "remark", "title": "备注", "is_query": false, "is_create": false, "is_update": false}, {"id": 164, "field_name": "sort", "title": "显示排序", "is_query": false, "is_create": false, "is_update": false}, {"id": 165, "field_name": "status", "title": "状态", "is_query": false, "is_create": false, "is_update": false}, {"id": 166, "field_name": "type", "title": "数据值类型", "is_query": false, "is_create": false, "is_update": false}, {"id": 167, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 168, "field_name": "value", "title": "字典编号", "is_query": false, "is_create": false, "is_update": false}]}, {"id": 14, "name": "字典管理", "isCheck": false, "btns": [{"id": 66, "name": "详情", "value": "dictionary:Retrieve", "isCheck": false, "data_range": null}, {"id": 68, "name": "编辑", "value": "dictionary:Update", "isCheck": false, "data_range": null}, {"id": 65, "name": "查询", "value": "dictionary:Search", "isCheck": false, "data_range": null}, {"id": 67, "name": "新增", "value": "dictionary:Create", "isCheck": false, "data_range": null}, {"id": 69, "name": "删除", "value": "dictionary:Delete", "isCheck": false, "data_range": null}], "columns": [{"id": 91, "field_name": "agent", "title": "agent信息", "is_query": false, "is_create": false, "is_update": false}, {"id": 92, "field_name": "area_code", "title": "区域代码", "is_query": false, "is_create": false, "is_update": false}, {"id": 93, "field_name": "browser", "title": "浏览器名", "is_query": false, "is_create": false, "is_update": false}, {"id": 94, "field_name": "city", "title": "城市", "is_query": false, "is_create": false, "is_update": false}, {"id": 95, "field_name": "continent", "title": "州", "is_query": false, "is_create": false, "is_update": false}, {"id": 96, "field_name": "country", "title": "国家", "is_query": false, "is_create": false, "is_update": false}, {"id": 97, "field_name": "country_code", "title": "简称", "is_query": false, "is_create": false, "is_update": false}, {"id": 98, "field_name": "country_english", "title": "英文全称", "is_query": false, "is_create": false, "is_update": false}, {"id": 99, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 100, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 101, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 102, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 103, "field_name": "district", "title": "县区", "is_query": false, "is_create": false, "is_update": false}, {"id": 104, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 105, "field_name": "ip", "title": "登录ip", "is_query": false, "is_create": false, "is_update": false}, {"id": 106, "field_name": "isp", "title": "运营商", "is_query": false, "is_create": false, "is_update": false}, {"id": 107, "field_name": "latitude", "title": "纬度", "is_query": false, "is_create": false, "is_update": false}, {"id": 108, "field_name": "login_type", "title": "登录类型", "is_query": false, "is_create": false, "is_update": false}, {"id": 109, "field_name": "longitude", "title": "经度", "is_query": false, "is_create": false, "is_update": false}, {"id": 110, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 111, "field_name": "os", "title": "操作系统", "is_query": false, "is_create": false, "is_update": false}, {"id": 112, "field_name": "province", "title": "省份", "is_query": false, "is_create": false, "is_update": false}, {"id": 113, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 114, "field_name": "username", "title": "登录用户名", "is_query": false, "is_create": false, "is_update": false}, {"id": 169, "field_name": "code", "title": "地区编码", "is_query": false, "is_create": false, "is_update": false}, {"id": 170, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 171, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 172, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 173, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 174, "field_name": "enable", "title": "是否启用", "is_query": false, "is_create": false, "is_update": false}, {"id": 175, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 176, "field_name": "initials", "title": "首字母", "is_query": false, "is_create": false, "is_update": false}, {"id": 177, "field_name": "level", "title": "地区层级(1省份 2城市 3区县 4乡级)", "is_query": false, "is_create": false, "is_update": false}, {"id": 178, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 179, "field_name": "name", "title": "名称", "is_query": false, "is_create": false, "is_update": false}, {"id": 180, "field_name": "pcode", "title": "父地区编码", "is_query": false, "is_create": false, "is_update": false}, {"id": 181, "field_name": "pinyin", "title": "拼音", "is_query": false, "is_create": false, "is_update": false}, {"id": 182, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 249, "field_name": "color", "title": "颜色", "is_query": false, "is_create": false, "is_update": false}, {"id": 250, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 251, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 252, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 253, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 254, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 255, "field_name": "is_value", "title": "是否为value值", "is_query": false, "is_create": false, "is_update": false}, {"id": 256, "field_name": "label", "title": "字典名称", "is_query": false, "is_create": false, "is_update": false}, {"id": 257, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 258, "field_name": "parent", "title": "父级", "is_query": false, "is_create": false, "is_update": false}, {"id": 259, "field_name": "remark", "title": "备注", "is_query": false, "is_create": false, "is_update": false}, {"id": 260, "field_name": "sort", "title": "显示排序", "is_query": false, "is_create": false, "is_update": false}, {"id": 261, "field_name": "status", "title": "状态", "is_query": false, "is_create": false, "is_update": false}, {"id": 262, "field_name": "type", "title": "数据值类型", "is_query": false, "is_create": false, "is_update": false}, {"id": 263, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 264, "field_name": "value", "title": "字典编号", "is_query": false, "is_create": false, "is_update": false}]}, {"id": 15, "name": "地区管理", "isCheck": false, "btns": [{"id": 71, "name": "详情", "value": "area:Retrieve", "isCheck": false, "data_range": null}, {"id": 73, "name": "编辑", "value": "area:Update", "isCheck": false, "data_range": null}, {"id": 70, "name": "查询", "value": "area:Search", "isCheck": false, "data_range": null}, {"id": 72, "name": "新增", "value": "area:Create", "isCheck": false, "data_range": null}, {"id": 74, "name": "删除", "value": "area:Delete", "isCheck": false, "data_range": null}], "columns": [{"id": 115, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 116, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 117, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 118, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 119, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 120, "field_name": "json_result", "title": "返回信息", "is_query": false, "is_create": false, "is_update": false}, {"id": 121, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 122, "field_name": "request_body", "title": "请求参数", "is_query": false, "is_create": false, "is_update": false}, {"id": 123, "field_name": "request_browser", "title": "请求浏览器", "is_query": false, "is_create": false, "is_update": false}, {"id": 124, "field_name": "request_ip", "title": "请求ip地址", "is_query": false, "is_create": false, "is_update": false}, {"id": 125, "field_name": "request_method", "title": "请求方式", "is_query": false, "is_create": false, "is_update": false}, {"id": 126, "field_name": "request_modular", "title": "请求模块", "is_query": false, "is_create": false, "is_update": false}, {"id": 127, "field_name": "request_msg", "title": "操作说明", "is_query": false, "is_create": false, "is_update": false}, {"id": 128, "field_name": "request_os", "title": "操作系统", "is_query": false, "is_create": false, "is_update": false}, {"id": 129, "field_name": "request_path", "title": "请求地址", "is_query": false, "is_create": false, "is_update": false}, {"id": 130, "field_name": "response_code", "title": "响应状态码", "is_query": false, "is_create": false, "is_update": false}, {"id": 131, "field_name": "status", "title": "响应状态", "is_query": false, "is_create": false, "is_update": false}, {"id": 132, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 183, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 184, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 185, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 186, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 187, "field_name": "engine", "title": "引擎", "is_query": false, "is_create": false, "is_update": false}, {"id": 188, "field_name": "file_url", "title": "文件地址", "is_query": false, "is_create": false, "is_update": false}, {"id": 189, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 190, "field_name": "md5sum", "title": "文件md5", "is_query": false, "is_create": false, "is_update": false}, {"id": 191, "field_name": "mime_type", "title": "Mime类型", "is_query": false, "is_create": false, "is_update": false}, {"id": 192, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 193, "field_name": "name", "title": "名称", "is_query": false, "is_create": false, "is_update": false}, {"id": 194, "field_name": "size", "title": "文件大小", "is_query": false, "is_create": false, "is_update": false}, {"id": 195, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 196, "field_name": "url", "title": "url", "is_query": false, "is_create": false, "is_update": false}, {"id": 265, "field_name": "code", "title": "地区编码", "is_query": false, "is_create": false, "is_update": false}, {"id": 266, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 267, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 268, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 269, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 270, "field_name": "enable", "title": "是否启用", "is_query": false, "is_create": false, "is_update": false}, {"id": 271, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 272, "field_name": "initials", "title": "首字母", "is_query": false, "is_create": false, "is_update": false}, {"id": 273, "field_name": "level", "title": "地区层级(1省份 2城市 3区县 4乡级)", "is_query": false, "is_create": false, "is_update": false}, {"id": 274, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 275, "field_name": "name", "title": "名称", "is_query": false, "is_create": false, "is_update": false}, {"id": 276, "field_name": "pcode", "title": "父地区编码", "is_query": false, "is_create": false, "is_update": false}, {"id": 277, "field_name": "pinyin", "title": "拼音", "is_query": false, "is_create": false, "is_update": false}, {"id": 278, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}]}, {"id": 16, "name": "附件管理", "isCheck": false, "btns": [{"id": 75, "name": "详情", "value": "file:Retrieve", "isCheck": false, "data_range": null}, {"id": 77, "name": "编辑", "value": "file:Update", "isCheck": false, "data_range": null}, {"id": 76, "name": "查询", "value": "file:Search", "isCheck": false, "data_range": null}, {"id": 78, "name": "删除", "value": "file:Delete", "isCheck": false, "data_range": null}], "columns": [{"id": 279, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 280, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 281, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 282, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 283, "field_name": "engine", "title": "引擎", "is_query": false, "is_create": false, "is_update": false}, {"id": 284, "field_name": "file_url", "title": "文件地址", "is_query": false, "is_create": false, "is_update": false}, {"id": 285, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 286, "field_name": "md5sum", "title": "文件md5", "is_query": false, "is_create": false, "is_update": false}, {"id": 287, "field_name": "mime_type", "title": "Mime类型", "is_query": false, "is_create": false, "is_update": false}, {"id": 288, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 289, "field_name": "name", "title": "名称", "is_query": false, "is_create": false, "is_update": false}, {"id": 290, "field_name": "size", "title": "文件大小", "is_query": false, "is_create": false, "is_update": false}, {"id": 291, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 292, "field_name": "url", "title": "url", "is_query": false, "is_create": false, "is_update": false}]}]}, {"id": 24, "name": "资产管理", "menus": [{"id": 25, "name": "服务器管理", "isCheck": false, "btns": [{"id": 104, "name": "编辑", "value": "resource:physicalAsset:physicalServerMachine:Update", "isCheck": false, "data_range": null}, {"id": 103, "name": "添加", "value": "resource:physicalAsset:physicalServerMachine:Create", "isCheck": false, "data_range": null}, {"id": 101, "name": "查询", "value": "resource:physicalAsset:physicalServerMachine:Search", "isCheck": false, "data_range": null}, {"id": 102, "name": "查看", "value": "resource:physicalAsset:physicalServerMachine:Retrieve", "isCheck": false, "data_range": null}, {"id": 106, "name": "导入", "value": "resource:physicalAsset:physicalServerMachine:Import", "isCheck": false, "data_range": null}, {"id": 105, "name": "删除", "value": "resource:physicalAsset:physicalServerMachine:Delete", "isCheck": false, "data_range": null}], "columns": []}, {"id": 26, "name": "网络设备管理", "isCheck": false, "btns": [{"id": 110, "name": "编辑", "value": "resource:physicalAsset:networkHardware:Update", "isCheck": false, "data_range": null}, {"id": 109, "name": "添加", "value": "resource:physicalAsset:networkHardware:Create", "isCheck": false, "data_range": null}, {"id": 107, "name": "查询", "value": "resource:physicalAsset:networkHardware:Search", "isCheck": false, "data_range": null}, {"id": 108, "name": "查看", "value": "resource:physicalAsset:networkHardware:Retrieve", "isCheck": false, "data_range": null}, {"id": 112, "name": "导入", "value": "resource:physicalAsset:networkHardware:Import", "isCheck": false, "data_range": null}, {"id": 111, "name": "删除", "value": "resource:physicalAsset:networkHardware:Delete", "isCheck": false, "data_range": null}], "columns": []}, {"id": 27, "name": "配件管理", "isCheck": false, "btns": [{"id": 116, "name": "编辑", "value": "resource:physicalAsset:holisticAccessory:Update", "isCheck": false, "data_range": null}, {"id": 115, "name": "添加", "value": "resource:physicalAsset:holisticAccessory:Create", "isCheck": false, "data_range": null}, {"id": 113, "name": "查询", "value": "resource:physicalAsset:holisticAccessory:Search", "isCheck": false, "data_range": null}, {"id": 114, "name": "查看", "value": "resource:physicalAsset:holisticAccessory:Retrieve", "isCheck": false, "data_range": null}, {"id": 118, "name": "导入", "value": "resource:physicalAsset:holisticAccessory:Import", "isCheck": false, "data_range": null}, {"id": 117, "name": "删除", "value": "resource:physicalAsset:holisticAccessory:Delete", "isCheck": false, "data_range": null}], "columns": []}, {"id": 28, "name": "耗材管理", "isCheck": false, "btns": [{"id": 122, "name": "编辑", "value": "resource:physicalAsset:generalConsumable:Update", "isCheck": false, "data_range": null}, {"id": 121, "name": "添加", "value": "resource:physicalAsset:generalConsumable:Create", "isCheck": false, "data_range": null}, {"id": 119, "name": "查询", "value": "resource:physicalAsset:generalConsumable:Search", "isCheck": false, "data_range": null}, {"id": 120, "name": "查看", "value": "resource:physicalAsset:generalConsumable:Retrieve", "isCheck": false, "data_range": null}, {"id": 124, "name": "导入", "value": "resource:physicalAsset:generalConsumable:Import", "isCheck": false, "data_range": null}, {"id": 123, "name": "删除", "value": "resource:physicalAsset:generalConsumable:Delete", "isCheck": false, "data_range": null}], "columns": []}]}, {"id": 17, "name": "日志管理", "menus": [{"id": 18, "name": "登录日志", "isCheck": false, "btns": [{"id": 80, "name": "详情", "value": "login_log:Retrieve", "isCheck": false, "data_range": null}, {"id": 79, "name": "查询", "value": "login_log:Search", "isCheck": false, "data_range": null}], "columns": [{"id": 221, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 222, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 223, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 224, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 225, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 226, "field_name": "json_result", "title": "返回信息", "is_query": false, "is_create": false, "is_update": false}, {"id": 227, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 228, "field_name": "request_body", "title": "请求参数", "is_query": false, "is_create": false, "is_update": false}, {"id": 229, "field_name": "request_browser", "title": "请求浏览器", "is_query": false, "is_create": false, "is_update": false}, {"id": 230, "field_name": "request_ip", "title": "请求ip地址", "is_query": false, "is_create": false, "is_update": false}, {"id": 231, "field_name": "request_method", "title": "请求方式", "is_query": false, "is_create": false, "is_update": false}, {"id": 232, "field_name": "request_modular", "title": "请求模块", "is_query": false, "is_create": false, "is_update": false}, {"id": 233, "field_name": "request_msg", "title": "操作说明", "is_query": false, "is_create": false, "is_update": false}, {"id": 234, "field_name": "request_os", "title": "操作系统", "is_query": false, "is_create": false, "is_update": false}, {"id": 235, "field_name": "request_path", "title": "请求地址", "is_query": false, "is_create": false, "is_update": false}, {"id": 236, "field_name": "response_code", "title": "响应状态码", "is_query": false, "is_create": false, "is_update": false}, {"id": 237, "field_name": "status", "title": "响应状态", "is_query": false, "is_create": false, "is_update": false}, {"id": 238, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 293, "field_name": "agent", "title": "agent信息", "is_query": false, "is_create": false, "is_update": false}, {"id": 294, "field_name": "area_code", "title": "区域代码", "is_query": false, "is_create": false, "is_update": false}, {"id": 295, "field_name": "browser", "title": "浏览器名", "is_query": false, "is_create": false, "is_update": false}, {"id": 296, "field_name": "city", "title": "城市", "is_query": false, "is_create": false, "is_update": false}, {"id": 297, "field_name": "continent", "title": "州", "is_query": false, "is_create": false, "is_update": false}, {"id": 298, "field_name": "country", "title": "国家", "is_query": false, "is_create": false, "is_update": false}, {"id": 299, "field_name": "country_code", "title": "简称", "is_query": false, "is_create": false, "is_update": false}, {"id": 300, "field_name": "country_english", "title": "英文全称", "is_query": false, "is_create": false, "is_update": false}, {"id": 301, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 302, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 303, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 304, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 305, "field_name": "district", "title": "县区", "is_query": false, "is_create": false, "is_update": false}, {"id": 306, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 307, "field_name": "ip", "title": "登录ip", "is_query": false, "is_create": false, "is_update": false}, {"id": 308, "field_name": "isp", "title": "运营商", "is_query": false, "is_create": false, "is_update": false}, {"id": 309, "field_name": "latitude", "title": "纬度", "is_query": false, "is_create": false, "is_update": false}, {"id": 310, "field_name": "login_type", "title": "登录类型", "is_query": false, "is_create": false, "is_update": false}, {"id": 311, "field_name": "longitude", "title": "经度", "is_query": false, "is_create": false, "is_update": false}, {"id": 312, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 313, "field_name": "os", "title": "操作系统", "is_query": false, "is_create": false, "is_update": false}, {"id": 314, "field_name": "province", "title": "省份", "is_query": false, "is_create": false, "is_update": false}, {"id": 315, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 316, "field_name": "username", "title": "登录用户名", "is_query": false, "is_create": false, "is_update": false}]}, {"id": 19, "name": "操作日志", "isCheck": false, "btns": [{"id": 81, "name": "详情", "value": "operation_log:Retrieve", "isCheck": false, "data_range": null}, {"id": 82, "name": "查询", "value": "operation_log:Search", "isCheck": false, "data_range": null}], "columns": [{"id": 317, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 318, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 319, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 320, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 321, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 322, "field_name": "json_result", "title": "返回信息", "is_query": false, "is_create": false, "is_update": false}, {"id": 323, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 324, "field_name": "request_body", "title": "请求参数", "is_query": false, "is_create": false, "is_update": false}, {"id": 325, "field_name": "request_browser", "title": "请求浏览器", "is_query": false, "is_create": false, "is_update": false}, {"id": 326, "field_name": "request_ip", "title": "请求ip地址", "is_query": false, "is_create": false, "is_update": false}, {"id": 327, "field_name": "request_method", "title": "请求方式", "is_query": false, "is_create": false, "is_update": false}, {"id": 328, "field_name": "request_modular", "title": "请求模块", "is_query": false, "is_create": false, "is_update": false}, {"id": 329, "field_name": "request_msg", "title": "操作说明", "is_query": false, "is_create": false, "is_update": false}, {"id": 330, "field_name": "request_os", "title": "操作系统", "is_query": false, "is_create": false, "is_update": false}, {"id": 331, "field_name": "request_path", "title": "请求地址", "is_query": false, "is_create": false, "is_update": false}, {"id": 332, "field_name": "response_code", "title": "响应状态码", "is_query": false, "is_create": false, "is_update": false}, {"id": 333, "field_name": "status", "title": "响应状态", "is_query": false, "is_create": false, "is_update": false}, {"id": 334, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}]}]}, {"id": 20, "name": "资源管理", "menus": [{"id": 21, "name": "机房管理", "isCheck": false, "btns": [{"id": 86, "name": "编辑", "value": "resource:machineRoom:Update", "isCheck": false, "data_range": null}, {"id": 85, "name": "添加", "value": "resource:machineRoom:Create", "isCheck": false, "data_range": null}, {"id": 83, "name": "查询", "value": "resource:machineRoom:Search", "isCheck": false, "data_range": null}, {"id": 84, "name": "查看", "value": "resource:machineRoom:Retrieve", "isCheck": false, "data_range": null}, {"id": 88, "name": "导入", "value": "resource:machineRoom:Import", "isCheck": false, "data_range": null}, {"id": 87, "name": "删除", "value": "resource:machineRoom:Delete", "isCheck": false, "data_range": null}], "columns": []}, {"id": 22, "name": "包间管理", "isCheck": false, "btns": [{"id": 92, "name": "编辑", "value": "resource:privateRoom:Update", "isCheck": false, "data_range": null}, {"id": 91, "name": "添加", "value": "resource:privateRoom:Create", "isCheck": false, "data_range": null}, {"id": 89, "name": "查询", "value": "resource:privateRoom:Search", "isCheck": false, "data_range": null}, {"id": 90, "name": "查看", "value": "resource:privateRoom:Retrieve", "isCheck": false, "data_range": null}, {"id": 94, "name": "导入", "value": "resource:privateRoom:Import", "isCheck": false, "data_range": null}, {"id": 93, "name": "删除", "value": "resource:privateRoom:Delete", "isCheck": false, "data_range": null}], "columns": []}, {"id": 23, "name": "机柜管理", "isCheck": false, "btns": [{"id": 98, "name": "编辑", "value": "resource:idcRackMachine:Update", "isCheck": false, "data_range": null}, {"id": 97, "name": "添加", "value": "resource:idcRackMachine:Create", "isCheck": false, "data_range": null}, {"id": 95, "name": "查询", "value": "resource:idcRackMachine:Search", "isCheck": false, "data_range": null}, {"id": 96, "name": "查看", "value": "resource:idcRackMachine:Retrieve", "isCheck": false, "data_range": null}, {"id": 100, "name": "导入", "value": "resource:idcRackMachine:Import", "isCheck": false, "data_range": null}, {"id": 99, "name": "删除", "value": "resource:idcRackMachine:Delete", "isCheck": false, "data_range": null}], "columns": []}, {"id": 24, "name": "资产管理", "isCheck": false, "btns": [], "columns": []}]}, {"id": 7, "name": "行权限管理", "menus": [{"id": 8, "name": "行角色权限", "isCheck": false, "btns": [], "columns": []}, {"id": 9, "name": "行用户权限", "isCheck": false, "btns": [], "columns": [{"id": 133, "field_name": "content", "title": "内容", "is_query": false, "is_create": false, "is_update": false}, {"id": 134, "field_name": "create_datetime", "title": "创建时间", "is_query": false, "is_create": false, "is_update": false}, {"id": 135, "field_name": "creator", "title": "创建人", "is_query": false, "is_create": false, "is_update": false}, {"id": 136, "field_name": "dept_belong_id", "title": "数据归属部门", "is_query": false, "is_create": false, "is_update": false}, {"id": 137, "field_name": "description", "title": "描述", "is_query": false, "is_create": false, "is_update": false}, {"id": 138, "field_name": "id", "title": "Id", "is_query": false, "is_create": false, "is_update": false}, {"id": 139, "field_name": "modifier", "title": "修改人", "is_query": false, "is_create": false, "is_update": false}, {"id": 140, "field_name": "target_type", "title": "目标类型", "is_query": false, "is_create": false, "is_update": false}, {"id": 141, "field_name": "title", "title": "标题", "is_query": false, "is_create": false, "is_update": false}, {"id": 142, "field_name": "update_datetime", "title": "修改时间", "is_query": false, "is_create": false, "is_update": false}]}]}], "msg": "success"}