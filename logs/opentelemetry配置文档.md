# python3 django+requests 适配 opentelemetry 链式追踪
在 Django 项目中配置 OpenTelemetry 可以帮助你进行分布式追踪和监控。以下是一个详细的步骤指南，介绍如何在 Django 项目中配置 OpenTelemetry。

## 步骤 1：安装依赖
首先，你需要安装 OpenTelemetry 的相关依赖包。你可以使用 pip 来安装这些包：
```shell
pip install opentelemetry-api opentelemetry-sdk opentelemetry-instrumentation-django opentelemetry-instrumentation-requests opentelemetry-exporter-otlp

```
## 步骤 2：配置 OpenTelemetry
在你的 Django 项目的 settings.py 文件中，添加 OpenTelemetry 的初始化代码。这通常放在 settings.py 文件的顶部。

```python
# settings.py

import os
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.instrumentation.django import DjangoInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter

# 初始化 TracerProvider
trace.set_tracer_provider(TracerProvider())

# 设置 OTLP 导出器
otlp_exporter = OTLPSpanExporter(
    endpoint=os.getenv('OTEL_EXPORTER_OTLP_ENDPOINT', 'http://localhost:4317'),  # 替换为你的 OpenTelemetry Collector 地址
    headers={
        "key1": os.getenv('OTEL_EXPORTER_OTLP_HEADERS_KEY1', 'value1'),
        "key2": os.getenv('OTEL_EXPORTER_OTLP_HEADERS_KEY2', 'value2')
    }
)

# 添加 BatchSpanProcessor
span_processor = BatchSpanProcessor(otlp_exporter)
trace.get_tracer_provider().add_span_processor(span_processor)

# 启用 Django 仪器化
DjangoInstrumentor().instrument()

# 启用 requests 仪器化
RequestsInstrumentor().instrument()

# 其他 Django 设置
DEBUG = False
ALLOWED_HOSTS = os.getenv('DJANGO_ALLOWED_HOSTS', '').split(',')
# 其他设置...
```

## 步骤 3：配置 ASGI 应用
在 ASGI 应用入口文件中（通常是 asgi.py），确保导入并使用 OpenTelemetry 的 ASGI 中间件。

```python
# asgi.py

import os
import django
from channels.routing import get_default_application
from opentelemetry.instrumentation.asgi import OpenTelemetryMiddleware

# 设置 DJANGO_SETTINGS_MODULE 环境变量
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "your_project.settings")

# 加载 Django 设置
django.setup()

# 获取默认的 ASGI 应用
asgi_application = get_default_application()

# 包裹 ASGI 应用，使其支持 OpenTelemetry
application = OpenTelemetryMiddleware(asgi_application)


```
## 步骤 4：配置 WSGI 应用（可选）
如果你也在使用 WSGI 应用，可以在 wsgi.py 文件中添加类似的配置。
```python
# wsgi.py

import os
from django.core.wsgi import get_wsgi_application
from opentelemetry.instrumentation.wsgi import OpenTelemetryMiddleware

# 设置 DJANGO_SETTINGS_MODULE 环境变量
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "your_project.settings")

# 获取默认的 WSGI 应用
wsgi_application = get_wsgi_application()

# 包裹 WSGI 应用，使其支持 OpenTelemetry
application = OpenTelemetryMiddleware(wsgi_application)
```


## 步骤 5：配置环境变量
确保在你的环境变量中设置必要的配置项。你可以在 .env 文件中设置这些变量，或者在 Kubernetes 的 ConfigMap 中设置。
```
# 确保程序可以加载此环境变量
export DJANGO_SETTINGS_MODULE=your_project.settings
export OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4317/v1/traces
```
