import os
from openstack import connection

# 创建连接
auth_args = {
    'auth_url': 'https://************:5000/v3',
    'project_name': 'admin',
    'username': 'admin',
    'password': 'your_admin_password',
    'user_domain_name': 'default',
    'project_domain_name': 'default'
}

conn = connection.Connection(**auth_args)

# 创建项目
project_name = 'baidu-testing005'
project_description = '百度测试'
domain_id = 'default'
project = conn.identity.create_project(
    name=project_name,
    description=project_description,
    domain_id=domain_id,
    is_enabled=True
)

print(f"Project created: {project.name} ({project.id})")

# 设置计算配额
compute_quotas = {
    'cores': 20,
    'instances': 10,
    'ram': 20480  # 单位为 MB
}
conn.compute.update_quota_set(project_id=project.id, **compute_quotas)
print(f"Compute quotas set for project {project.name}")

# 设置块存储配额
block_storage_quotas = {
    'volumes': 10,
    'gigabytes': 1000  # 单位为 GB
}
conn.block_storage.update_quota_set(project_id=project.id, **block_storage_quotas)
print(f"Block storage quotas set for project {project.name}")

# 设置对象存储配额
object_storage_quotas = {
    'container_count': 100,
    'object_count': 1000,
    'bytes': ***********  # 单位为字节
}
conn.object_store.update_account_metadata(project_id=project.id, **object_storage_quotas)
print(f"Object storage quotas set for project {project.name}")

# 设置网络配额
network_quotas = {
    'network': 10,
    'subnet': 20,
    'port': 50,
    'router': 5,
    'floatingip': 20,
    'security_group': 20,
    'security_group_rule': 100
}
conn.network.update_quota_set(project_id=project.id, **network_quotas)
print(f"Network quotas set for project {project.name}")
