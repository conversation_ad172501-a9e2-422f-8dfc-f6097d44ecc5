# values.yaml

```text
volumes:
  dataVolumeName: chaos-django-data
pvc:
  name: chaos-django-data
  accessModes:
    - ReadWriteOnce
  mountPath: /code/media/ 

```

```text
development.yaml


   spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chaos-web.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      volumes:
        - name: {{ .Values.volumes.dataVolumeName }}
          persistentVolumeClaim:
            claimName: {{ .Values.pvc.name }}
            accessModes:
              - {{ .Values.pvc.accessModes | first }}

      containers:
        - name: {{ .Chart.Name }}
          volumeMounts:
           - name: {{ .Values.volumes.dataVolumeName }}
             mountPath: {{ .Values.pvc.mountPath }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.configmap.enabled }}
          envFrom:
          - configMapRef:
              name: {{ .Values.configmap.name }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}


           volumeMounts:
           - name: {{ .Values.volumes.dataVolumeName }}
             mountPath: {{ .Values.pvc.mountPath }}
          envFrom:
          - configMapRef:
              name: {{ .Values.configmap.name }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
         - name: {{ .Values.volumes.dataVolumeName }}
           persistentVolumeClaim:
             claimName: {{ .Values.pvc.name }}

```

```text
development.yaml



apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "chaos-django.fullname" . }}
  labels:
    {{- include "chaos-django.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chaos-django.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chaos-django.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chaos-django.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.configmap.enabled }}
          volumeMounts:
           - name: {{ .Values.volumes.dataVolumeName }}
             mountPath: {{ .Values.pvc.mountPath }}
          envFrom:
          - configMapRef:
              name: {{ .Values.configmap.name }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
         - name: {{ .Values.volumes.dataVolumeName }}
           persistentVolumeClaim:
             claimName: {{ .Values.pvc.name }}

      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}



apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "chaos-django.fullname" . }}
  labels:
    {{- include "chaos-django.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chaos-django.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chaos-django.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chaos-django.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.configmap.enabled }}
          volumeMounts:
           - name: {{ .Values.volumes.dataVolumeName }}
             mountPath: {{ .Values.pvc.mountPath }}
          envFrom:
          - configMapRef:
              name: {{ .Values.configmap.name }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
         - name: {{ .Values.volumes.dataVolumeName }}
           persistentVolumeClaim:
             claimName: {{ .Values.pvc.name }}

      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
```
