"""
<AUTHOR>
@Date    ：2024/12/16
"""


import time
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from notice.models import (
    Notification
)


class NotificationImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = Notification
        exclude = ()


class NotificationSerializer(ChaosCustomModelSerializer):
    """
    工单-序列化器
    """

    class Meta:
        model = Notification
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]
