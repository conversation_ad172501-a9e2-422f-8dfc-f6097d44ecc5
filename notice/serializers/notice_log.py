"""
<AUTHOR>
@Date    ：2024/12/16
"""


import time
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from notice.models import (
    NoticeLog
)


class NoticeLogImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = NoticeLog
        exclude = ()


class NoticeLogSerializer(ChaosCustomModelSerializer):
    """
    工单-序列化器
    """

    class Meta:
        model = NoticeLog
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]
