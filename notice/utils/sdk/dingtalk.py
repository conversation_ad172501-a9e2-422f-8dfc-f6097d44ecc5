"""
<AUTHOR>
@Date    ：2024/12/16
"""
import json
import time
import hmac
import hashlib
import base64
import urllib.parse
import requests

from notice.models import Notification
from application.logger import logger


class DingTalkSDK(object):
    # 默认的测试URL和密钥
    DEFAULT_TESTING_URL = 'https://oapi.dingtalk.com/robot/send'
    DEFAULT_TESTING_ACCESS_TOKEN = '41179480ebf6ebc32248e51640cc4f38ec940a9663c1136fd33610ba3d864227'
    DEFAULT_TESTING_SECRET = 'SECb98b4a3381dedb09f58bf620704d7cdfc692841d220918e161876e567ce9c12b'

    def __init__(self, notification_key=None, is_testing=False):
        self.headers = {
            'Content-Type': 'application/json'
        }
        self.dingtalk_url = ''
        self.access_token = ''
        self.secret = ''
        self.base_url = f'{self.dingtalk_url}?access_token={self.access_token}'
        self.load_configuration(notification_key, is_testing)

    def load_configuration(self, notification_key, is_testing):
        try:
            if notification_key:
                # 从数据库中获取配置信息
                notification = Notification.objects.filter(
                    key=notification_key,
                    is_enabled=True,
                    is_deleted=False,
                ).first()
                if not notification:
                    raise ValueError("Notification with key '{}' does not exist.".format(notification_key))
                self.dingtalk_url = notification.webhook_url
                self.access_token = notification.access_id
                self.secret = notification.access_secret
                self.base_url = f'{self.dingtalk_url}?access_token={self.access_token}'
            else:
                # 使用默认的测试配置
                if is_testing:
                    self.dingtalk_url = self.DEFAULT_TESTING_URL
                    self.secret = self.DEFAULT_TESTING_SECRET
                    self.access_token = self.DEFAULT_TESTING_ACCESS_TOKEN
                    self.base_url = f'{self.dingtalk_url}?access_token={self.access_token}'
            if not self.dingtalk_url or not self.secret:
                raise ValueError("DingTalk URL and secret must be provided.")
        except Exception as e:
            logger.error(f"Failed to load configuration: {str(e)}")
            raise Exception("Failed to load configuration")

    def get_sign_timestamp(self):
        # 参考文档： https://open.dingtalk.com/document/robots/customize-robot-security-settings
        timestamp = str(int(time.time() * 1000))
        secret = self.secret
        secret_enc = secret.encode('utf-8')
        string_to_sign = '{}\n{}'.format(timestamp, secret)
        string_to_sign_enc = string_to_sign.encode('utf-8')
        hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign, timestamp

    def get_url(self):
        sign, timestamp = self.get_sign_timestamp()
        url = f'{self.dingtalk_url}?access_token={self.access_token}&sign={sign}&timestamp={timestamp}'
        return url

    def send_text(self, msg, is_at_all=False):
        # 钉钉文本类通知
        body = {
            "at": {
                "isAtAll": is_at_all
            },
            "text": {
                "content": msg
            },
            "msgtype": "text"
        }
        url = self.get_url()
        res = requests.post(url=url, headers=self.headers, data=json.dumps(body))
        logger.info(f'msg:{msg}, res:{res.text}')
        return res.json()

    def send_markdown(self, title, msg, at_mobiles=None, is_at_all=False):
        """
        钉钉文本类通知
        # 备注： 不支持span标签
        :param title: 通知标题
        :param msg: 通知内容
        :param at_mobiles: @的手机号列表，如 ['156xxxx8888', '156xxxx9999']
        :param is_at_all: 是否@所有人
        :return: dict
        """
        if at_mobiles is None:
            at_mobiles = []
        if is_at_all:
            body = {
                "msgtype": "markdown",
                "markdown": {
                    "title": title,
                    "text": msg
                },
                "at": {
                    "isAtAll": True,
                }
            }
        else:
            body = {
                "msgtype": "markdown",
                "markdown": {
                    "title": title,
                    "text": msg
                },
                "at": {
                    "atMobiles": at_mobiles,
                    "isAtAll": False,
                }
            }
        res = requests.post(url=self.get_url(), headers=self.headers, data=json.dumps(body))
        logger.info(f'dingtalk:{self.base_url} msg:{msg}, res:{res.text}')
        return res.json()


if __name__ == "__main__":
    import os
    import django

    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
    django.setup()
    # 示例
    dingtalk = DingTalkSDK(notification_key='dingtalk_test', is_testing=True)
    # dingtalk.send_text('Hello, DingTalk!')
    dingtalk.send_markdown('Test', '# 示例\n* 这是一个示例')
