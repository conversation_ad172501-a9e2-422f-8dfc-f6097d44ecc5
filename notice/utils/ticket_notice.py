"""
<AUTHOR>
@Date    ：2024/12/16
"""
from application.logger import logger
from dvadmin.system.models import Users
from notice.utils.sdk.dingtalk import DingTalkSDK
from notice.models import NoticeLog


class ManagerTicketNotice(object):
    def __init__(self):
        self.notice_robot_key = 'interval_ticket_notice'
        self.dt_sdk = DingTalkSDK(self.notice_robot_key)

    def send_ticket_notice(self, title, ticket_data, extr_status=''):
        try:
            logger.info(f'开始推送【{title}】【ticket_id】')
            obj_log = NoticeLog.objects.filter(
                resource_id=ticket_data['id'],
                type='钉钉',
                description=title,
                is_success=True,
            ).first()
            if obj_log:
                return
            at_mobiles = []
            creator_name = ''
            operator_name = ''
            # 获取@人 通知内容
            at_notice_message = '请您及时查看并处理。'
            at_member = int(ticket_data['assign_to'])
            if at_member:
                operator_info = Users.objects.filter(
                    id=at_member,
                ).first()
                if operator_info:
                    at_mobiles.append(operator_info.mobile)
                    operator_name = operator_info.name
            if int(ticket_data['creator_id']):
                creator_info = Users.objects.filter(
                    id=int(ticket_data['creator_id']),
                ).first()
                if creator_info:
                    creator_name = creator_info.name
            if extr_status in ['软件安装开始', '软件安装成功', '软件安装失败', '软件安装部分成功', '分配公网']:
                current_status_str = f'当前状态: <font color="blue">{extr_status}</font>'
                operator_name = creator_name
                if extr_status == '软件安装失败':
                    at_notice_message = '软件安装失败，请排查问题后，重新安装。'
                elif extr_status == '软件安装开始':
                    at_notice_message = '请您耐心等待，正在安装中，安装过程中请勿操作。'
                elif extr_status == '软件安装部分成功':
                    at_notice_message = '软件安装部分成功，请排查问题后，重新安装。'
                elif extr_status == '分配公网':
                    if '失败' in title:
                        at_notice_message = title + '@冀武 请您排查问题后，手动分配公网及策略。'
                    else:
                        at_notice_message = title
            elif '失败' in ticket_data["current_status"]:
                current_status_str = f'当前状态: <font color="red">{ticket_data["current_status"]}</font>'
            elif '完成' in ticket_data["current_status"]:
                current_status_str = f'当前状态: <font color="green">{ticket_data["current_status"]}</font>'
                operator_name = creator_name
                at_notice_message = '请等待交付报告【额外安装需求（如:cuda安装、驱动安装等），请等待线下操作完成后另行通知】。'
            else:
                current_status_str = f'当前状态: <font color="blue">{ticket_data["current_status"]}</font>'
            content = f"""### {title}
订单ID: {ticket_data['ticket_id']}

订单标题: {ticket_data['name']}

{current_status_str}

创建人: {creator_name}

创建时间: {ticket_data['create_datetime']}

@{operator_name} {at_notice_message}
"""
            logger.info(content)
            res = self.dt_sdk.send_markdown(
                title=title,
                msg=content,
                at_mobiles=at_mobiles
            )
            if res.get('errcode') == 0:
                updated_metadata_data = {
                    'resource_id': ticket_data['id'],
                    'type': '钉钉',
                    'description': title,
                    'is_success': True,
                    'message': content,
                    'webhook_to': self.dt_sdk.base_url
                }
                log_obj = NoticeLog.objects.create(**updated_metadata_data)
                log_obj.save()
        except Exception as e:
            logger.error(f'Error sending, Details: {str(e)}')






