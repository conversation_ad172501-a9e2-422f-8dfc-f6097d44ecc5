"""
<AUTHOR>
@Date    ：2025/05/13
"""
from application.logger import logger
from application.settings.base import DING_CONTRACT_FINANCE_PHONE,DING_CONTRACT_ROBOT_KEY
from notice.utils.sdk.dingtalk import DingTalkSDK
from dvadmin.system.models import Role,Users


class ContactPayService:
    def __init__(self):
        self.sale_notice_map = self.get_sales_mobiles()

    def get_sales_mobiles(self):
        users = Users.objects.all()
        return {
            user.name: user.mobile
            for user in users if user.name and user.mobile
        }

    def get_finance_mobiles(self):
        return [DING_CONTRACT_FINANCE_PHONE]

    def get_mobile_by_name(self, name):
        return self.sale_notice_map.get(name)


class ContractPayNoticeBuilder:
    def __init__(self, contact_pay_service: ContactPayService):
        self.contact_pay_service = contact_pay_service

    def get_contract_expired_mainbody(self, item):
        return (f"- 合同: {item['name']}（{item['code']}）"
                f"已于 {str(item['end_time']).split(' ')[0]} 过期\n")

    def get_contract_sign_mainbody(self, item):
        return f"- 合同: {item['name']}（{item['code']}）\n"

    def build_wait_approval_notice(self,title,item):
        content = f"### {title}\n"
        content += f"""
合同编号: {item['code']}

合同名称: {item['name']}

客户名称: {item['customer_name']}

当前状态: <font color="blue">{item['status']}</font>

合同申请人: {item['applicant']}
          """
        at_mobiles = self.contact_pay_service.get_finance_mobiles()
        # 添加@人信息
        content += "\n" + " ".join([f"@{m}" for m in at_mobiles]) + "\n"
        content += f'请您及时查看并处理。\n'
        return content, list(at_mobiles)

    def build_payment_notice(self,title,items):
        content = f"### {title}\n"
        at_mobiles = set()
        for item in items:
            if item['status'] == "未回款":
                content += (f"""
- 合同编号: {item['payment']['contract_code']}

  合同名称: {item['contract']['name']}

  收款状态: <font color="red">{item['status']}</font>

  周期: {str(item['payment_start_time']).split(" ")[0]} - {str(item['payment_end_time']).split(" ")[0]}
            """)

            else:
                content += (f"""
- 合同编号: {item['payment']['contract_code']}

  合同名称: {item['contract']['name']}

  收款状态: <font color="#FFA500">{item['status']}</font>

  周期: {str(item['payment_start_time']).split(" ")[0]} - {str(item['payment_end_time']).split(" ")[0]}
                """)

            # 收集签订人的手机号
            sign_name = item['contract'].get("sign_name")
            mobile = self.contact_pay_service.get_mobile_by_name(sign_name)
            if mobile:
                at_mobiles.add(mobile)
        
        for _ in self.contact_pay_service.get_finance_mobiles():
            at_mobiles.add(_)

        # 添加@人信息
        content += "\n" + " ".join([f"@{m}" for m in at_mobiles]) + "\n"
        content += f'请您及时查看并处理。\n'
        return content, list(at_mobiles)

    def build_contract_notice(self, title, items, notice_type='expired'):
        content = f"### {title}\n"
        at_mobiles = set()

        for item in items:
            if notice_type == "expired":
                content += self.get_contract_expired_mainbody(item)
            else:
                content += self.get_contract_sign_mainbody(item)

            # 收集签订人的手机号
            sign_name = item.get("sign_name")
            mobile = self.contact_pay_service.get_mobile_by_name(sign_name)
            if mobile:
                at_mobiles.add(mobile)

        # 添加@人信息
        content += "\n" + " ".join([f"@{m}" for m in at_mobiles]) + "\n"
        content += "请您知晓。\n" if notice_type == "expired" else "请您及时查看并处理。\n"
        return content, list(at_mobiles)


class ManagerContractPaymentNotice(object):
    def __init__(self):
        self.notice_robot_key = DING_CONTRACT_ROBOT_KEY
        self.dt_sdk = DingTalkSDK(self.notice_robot_key)
        
        # 初始化联系人服务和内容构建器
        self.contact_pay_service = ContactPayService()
        self.notice_builder = ContractPayNoticeBuilder(self.contact_pay_service)

    def send_contract_common_notice(self,title:str,items:list,notice_type:str='expired'):
        """
        推送合同过期 | 未双签消息提示
        :param title: 告警标题
        :param items: 合同列表
        :return: None
        """
        try:
            logger.info(f'开始推送【{title}】......')
            content, at_mobiles = self.notice_builder.build_contract_notice(title, items, notice_type)

            self.dt_sdk.send_markdown(
                title=title,
                msg=content,
                at_mobiles=at_mobiles
            )
        except Exception as e:
            logger.error(f'推送【{title}】失败，详情: {str(e)}', exc_info=True)

    def send_contract_wait_approval_notice(self,title:str,item:dict):
        """
        推送合同待审批消息提示
        :param title: 告警标题
        :param item:  合同信息
        :return: None
        """
        try:
            logger.info(f'开始推送【{title}】......')
            content, at_mobiles = self.notice_builder.build_wait_approval_notice(title, item)
  
            self.dt_sdk.send_markdown(
                title=title,
                msg=content,
                at_mobiles=at_mobiles
            )
        except Exception as e:
            logger.error(f'推送【{title}】失败，详情: {str(e)}', exc_info=True)

    def send_payment_common_notice(self,title:str,items:list):
        """
        推送回款账期逾期 | 当前时间未回款消息提示
        :param title: 告警标题
        :param items: 回款列表
        :return: None
        """
        try:
            logger.info(f'开始推送【{title}】......')
            content, at_mobiles = self.notice_builder.build_payment_notice(title, items)
            self.dt_sdk.send_markdown(
                title=title,
                msg=content,
                at_mobiles=at_mobiles
            )
        except Exception as e:
            logger.error(f'推送【{title}】失败，详情: {str(e)}', exc_info=True)


