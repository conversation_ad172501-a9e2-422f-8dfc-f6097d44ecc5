"""
<AUTHOR>
@Date    ：2024/12/16
"""
from application.logger import logger
from notice.utils.sdk.dingtalk import DingTalkSDK


class ManagerHostStatusNotice(object):
    def __init__(self):
        self.notice_robot_key = 'interval_host_expire_notice'
        self.dt_sdk = DingTalkSDK(self.notice_robot_key)
        self.at_mobiles = ['18637535839', '15869122013']

    def send_operator_cmdb_host_expire_notice(self, title, expiring_hosts, expired_hosts):
        """
        推送到期主机提醒,此法应用于 operator_cmdb_host 表中的过期通知，因为字段不一致
        仅做推送，无需额外记录通知日志
        :param title: 告警标题
        :param expiring_hosts: 即将到期的主机
        :param expired_hosts: 已到期的主机
        :return: None
        """
        if not expiring_hosts and not expired_hosts:
            logger.info('无需通知......')
            return
        try:
            logger.info(f'开始推送【{title}】......')
            # 获取@人 通知内容
            content = f"### {title}\n"
            if expired_hosts:
                content += '#### 已过期主机:\n'
                for expired_host in expired_hosts:
                    content += (f"- 主机{expired_host['ip_bmc']}（{expired_host['area']}）"
                                f"已于 {expired_host['expire_time']} 到期\n")
            if expiring_hosts:
                content += '#### 即将过期主机:\n'
                for expiring_host in expiring_hosts:
                    content += (f"- 主机{expiring_host['ip_bmc']}（{expiring_host['area']}）"
                                f"将于 {expiring_host['expire_time']} 即将到期\n")
            # 添加@人信息
            if self.at_mobiles:
                at_mobiles_msg = ' '.join([f'@{i}' for i in self.at_mobiles])
                content += f'\n{at_mobiles_msg}\n'

            content += f'请您及时查看并处理。\n'
            self.dt_sdk.send_markdown(
                title=title,
                msg=content,
                at_mobiles=self.at_mobiles
            )
        except Exception as e:
            logger.error(f'推送【{title}】失败，详情: {str(e)}', exc_info=True)

    def send_operator_cmdb_host_quite_notice(self, title, quite_hosts):
        """
        推送到期主机提醒,此法应用于 operator_cmdb_host 表中的过期通知，因为字段不一致
        仅做推送，无需额外记录通知日志
        :param title: 告警标题
        :param quite_hosts: 静默的主机
        :return: None
        """
        if not quite_hosts:
            logger.info('无需通知......')
            return
        try:
            logger.info(f'开始推送【{title}】......')
            # 获取@人 通知内容
            content = f"#### {title}\n"
            if quite_hosts:
                for quite_host in quite_hosts:
                    content += (f"- 主机{quite_host['ip_bmc']}"
                                f"（{quite_host['area']}）静默期结束进入Buffer池\n")
            # 添加@人信息
            if self.at_mobiles:
                at_mobiles_msg = ' '.join([f'@{i}' for i in self.at_mobiles])
                content += f'\n{at_mobiles_msg}\n'

            content += f'请您知悉，有异常请尽快处理。\n'
            self.dt_sdk.send_markdown(
                title=title,
                msg=content,
                at_mobiles=self.at_mobiles
            )
        except Exception as e:
            logger.error(f'推送【{title}】失败，详情: {str(e)}', exc_info=True)

    def send_openstack_server_expire_notice(self, title, expiring_hosts, expired_hosts):
        """
        推送到期主机提醒, 此法应用于 openstack 实例表中的过期通知
        仅做推送，无需额外记录通知日志
        :param title: 告警标题
        :param expiring_hosts: 即将到期的主机
        :param expired_hosts: 已到期的主机
        :return: None
        """
        if not expiring_hosts and not expired_hosts:
            logger.info('无需通知......')
            return
        try:
            logger.info(f'开始推送【{title}】......')
            # 获取@人 通知内容
            content = f"### {title}\n"
            if expired_hosts:
                content += '#### 已过期主机:\n'
                for expired_host in expired_hosts:
                    content += (f"- 主机{expired_host['baremetal_node_bmc_ip']}（{expired_host['node']}）"
                                f"已于 {expired_host['expire_at']} 到期\n")
            if expiring_hosts:
                content += '#### 即将过期主机:\n'
                for expiring_host in expiring_hosts:
                    content += (f"- 主机{expiring_host['baremetal_node_bmc_ip']}（{expiring_host['node']}）"
                                f"将于 {expiring_host['expire_at']} 即将到期\n")
            # 添加@人信息
            if self.at_mobiles:
                at_mobiles_msg = ' '.join([f'@{i}' for i in self.at_mobiles])
                content += f'\n{at_mobiles_msg}\n'

            content += f'请您及时查看并处理。\n'
            self.dt_sdk.send_markdown(
                title=title,
                msg=content,
                at_mobiles=self.at_mobiles
            )
        except Exception as e:
            logger.error(f'推送【{title}】失败，详情: {str(e)}', exc_info=True)
