from django.db import models

# Create your models here.

from dvadmin.utils.chaos_models import ChaosCoreModel


TABLE_PREFIX = 'notice_'


class NoticeLog(ChaosCoreModel):
    """
    通知日志
    """
    resource_id = models.CharField(
        max_length=63, blank=True, null=True, db_index=True,
        verbose_name='资源ID', help_text='资源ID', db_comment='资源ID'
    )
    type = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='通知类型', help_text='通知类型', db_comment='通知类型'
    )
    message = models.JSONField(
        blank=True, null=True,
        verbose_name='通知消息', help_text='通知消息', db_comment='通知消息'
    )
    is_success = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='是否成功', help_text='是否成功', db_comment='是否成功'
    )
    webhook_to = models.CharField(
        max_length=1023, blank=True, null=True,
        verbose_name='请求地址', help_text='请求地址', db_comment='请求地址'
    )

    class Meta:
        db_table = TABLE_PREFIX + "logs"
        verbose_name = "通知日志表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class Notification(ChaosCoreModel):
    """
    通知配置
    """
    name = models.CharField(
        max_length=255, blank=False, null=True,
        verbose_name='通知名称', help_text='通知名称', db_comment='通知名称'
    )
    key = models.CharField(
        max_length=63, blank=False, null=True,
        verbose_name='key名称', help_text='key名称', db_comment='key名称'
    )
    type = models.CharField(
        max_length=63, default='钉钉', blank=False, null=True,
        verbose_name='通知类型', help_text='通知类型', db_comment='通知类型'
    )
    webhook_url = models.CharField(
        max_length=1023, blank=True, null=True,
        verbose_name='Webhook URL', help_text='Webhook URL', db_comment='Webhook URL'
    )
    access_id = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='Access ID', help_text='Access ID', db_comment='Access ID'
    )
    access_secret = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='Access Secret', help_text='Access Secret', db_comment='Access Secret'
    )
    is_enabled = models.BooleanField(
        default=True, blank=True, null=True,
        verbose_name='是否启用', help_text='是否启用', db_comment='是否启用'
    )

    class Meta:
        db_table = TABLE_PREFIX + "notifications"
        verbose_name = "通知配置表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)
