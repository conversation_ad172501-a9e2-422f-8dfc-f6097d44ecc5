"""
<AUTHOR>
@Date    ：2024/12/16
通知模块 路由
"""

from rest_framework import routers

from notice.views.notification import NotificationViewSet
from notice.views.notice_log import NoticeLogViewSet

# 特殊自定义 API

resource_url = routers.SimpleRouter()
resource_url.register(r'notice_log', NoticeLogViewSet)
resource_url.register(r'notification', NotificationViewSet)


app_name = 'notice'


urlpatterns = [
    # path('create_baremetal_server/', CreateBaremetalServerView.as_view(), name='create_baremetal_server'),
    # path('resource/machine_room/', MachineRoomViewSet.as_view('get', 'list')),
    # path('resource/machine_room/<id>/', MachineRoomViewSet.as_view()),
]
urlpatterns += resource_url.urls
