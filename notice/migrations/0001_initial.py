# Generated by Django 5.0.6 on 2024-12-16 13:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NoticeLog',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('resource_id', models.CharField(blank=True, db_comment='资源ID', help_text='资源ID', max_length=63, null=True, verbose_name='资源ID')),
                ('message', models.JSONField(blank=True, db_comment='通知消息', help_text='通知消息', null=True, verbose_name='通知消息')),
                ('is_success', models.BooleanField(blank=True, db_comment='是否成功', default=False, help_text='是否成功', null=True, verbose_name='是否成功')),
                ('webhook_to', models.CharField(blank=True, db_comment='请求地址', help_text='请求地址', max_length=1023, null=True, verbose_name='请求地址')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '通知日志表',
                'verbose_name_plural': '通知日志表',
                'db_table': 'notice_logs',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(db_comment='通知名称', help_text='通知名称', max_length=255, null=True, verbose_name='通知名称')),
                ('key', models.CharField(db_comment='key名称', help_text='key名称', max_length=63, null=True, verbose_name='key名称')),
                ('type', models.CharField(db_comment='通知类型', default='钉钉', help_text='通知类型', max_length=63, null=True, verbose_name='通知类型')),
                ('webhook_url', models.CharField(blank=True, db_comment='Webhook URL', help_text='Webhook URL', max_length=1023, null=True, verbose_name='Webhook URL')),
                ('access_id', models.CharField(blank=True, db_comment='Access ID', help_text='Access ID', max_length=255, null=True, verbose_name='Access ID')),
                ('access_secret', models.CharField(blank=True, db_comment='Access Secret', help_text='Access Secret', max_length=255, null=True, verbose_name='Access Secret')),
                ('is_enabled', models.BooleanField(blank=True, db_comment='是否启用', default=True, help_text='是否启用', null=True, verbose_name='是否启用')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '通知配置表',
                'verbose_name_plural': '通知配置表',
                'db_table': 'notice_notifications',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
