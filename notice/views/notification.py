"""
<AUTHOR>
@Date    ：2024/12/16
"""
from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from notice.models import Notification
from notice.serializers.notification import (
    NotificationSerializer,
    NotificationImportSerializer
)


class NotificationViewSet(ChaosCustomModelViewSet):
    """
    任务接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Notification.objects.order_by('-create_datetime')
    serializer_class = NotificationSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = NotificationImportSerializer
    import_field_dict = {
    }
    permission_classes = []
