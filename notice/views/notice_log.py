"""
<AUTHOR>
@Date    ：2024/12/16
"""
from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from notice.models import NoticeLog
from notice.serializers.notice_log import (
    NoticeLogSerializer,
    NoticeLogImportSerializer
)


class NoticeLogViewSet(ChaosCustomModelViewSet):
    """
    任务接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = NoticeLog.objects.order_by('-create_datetime')
    serializer_class = NoticeLogSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'webhook_to',
    }
    # 导入
    import_serializer_class = NoticeLogImportSerializer
    import_field_dict = {
    }
    permission_classes = []
