from django.urls import path
from rest_framework.routers import SimpleRouter

from .views.cruddemo import CrudDemoModelViewSet
from .views.projects import ProjectsModelViewSet
from .views.applications import ApplicationsModelViewSet
from .views.server import OPServerViewSet, GpuInstanceViewSet, BaremetalViewSet
from .views.image import OPImageViewSet, OPImageListViewSet
from .views.flavor import OPFlavorViewSet, OPFlavorListViewSet
# from .views.se import OPSecurityGroupViewSet, OPSecurityGroupListViewSet
from .views.network import OPNetworkViewSet, OPNetworkListViewSet
from .views.subnet import OPSubnetViewSet, OPSubnetListViewSet
from .views.keypair import OPKeypairViewSet, OPKeypairListViewSet

router = SimpleRouter()
# 这里进行注册路径，并把视图关联上，这里的api地址以视图名称为后缀，这样方便记忆api/CrudDemoModelViewSet
router.register("api/CrudDemoModelViewSet", CrudDemoModelViewSet)
router.register("api/ProjectsModelViewSet", ProjectsModelViewSet)
router.register("api/ApplicationsModelViewSet", ApplicationsModelViewSet)
router.register("api/op/server", OPServerViewSet)
router.register("api/op/gpu-instance", GpuInstanceViewSet)
# router.register("api/op/baremetal", BaremetalViewSet)
# router.register("api/op/image", OPImageViewSet)
# router.register("api/op/flavor", OPFlavorViewSet)
# # router.register("api/op/sg", OPSecurityGroupViewSet)
# router.register("api/op/network", OPNetworkViewSet)
# router.register("api/op/subnet", OPSubnetViewSet)
# router.register("api/op/keypair", OPKeypairViewSet)


urlpatterns = [
    path('api/op/all_projects/', ProjectsModelViewSet.as_view({'get': 'all_projects'})),
    path('api/op/image/list/', OPImageListViewSet.as_view({'get': 'list'})),
    # path('api/op/flavor/list/', OPFlavorListViewSet.as_view({'get': 'list'})),
    # # path('api/op/sg/list/', OPSecurityGroupListViewSet.as_view({'get': 'list'})),
    # path('api/op/network/list/', OPNetworkListViewSet.as_view({'get': 'list'})),
    # path('api/op/subnet/list/', OPSubnetListViewSet.as_view({'get': 'list'})),
    # path('api/op/keypair/list/', OPKeypairListViewSet.as_view({'get': 'list'})),
]
urlpatterns += router.urls
