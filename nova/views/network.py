from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.viewsets import ReadOnlyModelViewSet

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from nova.models import OPNetwork
from nova.serializers import network


class LargeLimitOffsetPagination(LimitOffsetPagination):
    default_limit = 1000000  # 设置一个非常大的默认限制


class OPNetworkViewSet(ChaosCustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    haha
    """
    queryset = OPNetwork.objects.all()
    serializer_class = network.OPNetworkModelSerializer
    create_serializer_class = network.OPNetworkModelCreateUpdateSerializer
    update_serializer_class = network.OPNetworkModelCreateUpdateSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ['project_name', ]
    search_fields = ('net_id', 'name')
    ordering_fields = ('id',)


class OPNetworkListViewSet(ReadOnlyModelViewSet):
    queryset = OPNetwork.objects.all()
    serializer_class = network.OPNetworkListSerializer
    pagination_class = LargeLimitOffsetPagination
    filter_fields = ['project_name', ]
    http_method_names = ['get']  # 只允许GET请求
