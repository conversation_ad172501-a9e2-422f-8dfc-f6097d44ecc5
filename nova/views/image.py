from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.pagination import LimitOffsetPagination

from rest_framework.filters import SearchFilter, OrderingFilter

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from rest_framework.viewsets import ReadOnlyModelViewSet
from nova.models import OPImage
from nova.serializers import image


class LargeLimitOffsetPagination(LimitOffsetPagination):
    default_limit = 1000000  # 设置一个非常大的默认限制


class OPImageViewSet(ChaosCustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    haha
    """

    queryset = OPImage.objects.all()
    serializer_class = image.OPImageModelSerializer
    create_serializer_class = image.OPImageModelCreateUpdateSerializer
    update_serializer_class = image.OPImageModelCreateUpdateSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ['status',]
    search_fields = ('image_id', 'name')
    ordering_fields = ('id',)


class OPImageListViewSet(ReadOnlyModelViewSet):
    queryset = OPImage.objects.filter(image_type="image", disk_format="qcow2")
    serializer_class = image.OPImageListSerializer
    pagination_class = LargeLimitOffsetPagination
    http_method_names = ['get']  # 只允许GET请求

