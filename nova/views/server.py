from django_filters.rest_framework import Django<PERSON>ilterBackend
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON>, OrderingFilter

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from nova.models import OPServer
from nova.serializers.server import OPServerModelSerializer, OPServerModelCreateUpdateSerializer

from nova.cloud_api.op import create_instance


class OPServerViewSet(ChaosCustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    haha
    """

    queryset = OPServer.objects.filter(instance_type='instance').all()
    serializer_class = OPServerModelSerializer
    create_serializer_class = OPServerModelCreateUpdateSerializer
    update_serializer_class = OPServerModelCreateUpdateSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ('insid', 'name', 'ipaddr')
    ordering_fields = ('id',)

    # 重写post方法（调用create-perform_create）
    def perform_create(self, serializer):
        print('perform_create')
        data = serializer.validated_data
        # 修改字段值，或者一些自己的逻辑
        kwargs = {
            'vm_name': data['name'],
            'image_name': data['image_name'],
            'flavor_name': data['flavor_name'],
            'net_name': data['netname'],
        }
        if data.get('key_name'):
            kwargs['key_name'] = data.get('key_name')
        if data['volume_size']:
            kwargs['volume_size'] = data['volume_size']

        # 调用opestack api创建实例
        server_id = create_instance(**kwargs)
        data['insid'] = server_id
        data['instance_type'] = 'instance'
        data['status'] = '创建中'
        # data['vm_state'] = server.vm_state
        data['project_id'] = '491b74a318724e988d09bbe90f076a46'

        # 更新序列化器的数据
        serializer.instance = None
        serializer._validated_data = data        # 调用 save() 来保存带有修改字段的新实例
        instance = serializer.save()
        # 在这里，你可以添加保存实例后的自定义操作


class GpuInstanceViewSet(ChaosCustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    haha
    """

    queryset = OPServer.objects.filter(instance_type='gpu-instance').all()
    serializer_class = OPServerModelSerializer
    create_serializer_class = OPServerModelCreateUpdateSerializer
    update_serializer_class = OPServerModelCreateUpdateSerializer


class BaremetalViewSet(ChaosCustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    haha
    """

    queryset = OPServer.objects.filter(instance_type='baremetal').all()
    serializer_class = OPServerModelSerializer
    create_serializer_class = OPServerModelCreateUpdateSerializer
    update_serializer_class = OPServerModelCreateUpdateSerializer
