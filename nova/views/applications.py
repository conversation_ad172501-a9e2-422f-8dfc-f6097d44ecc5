from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from nova.models import ApplicationsModel
from nova.serializers.applications import ApplicationsModelSerializer


class ApplicationsModelViewSet(ChaosCustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = ApplicationsModel.objects.all()
    serializer_class = ApplicationsModelSerializer
    filter_fields = '__all__'

    # _get_list_by_ids configuration
    # api_list_by_ids_dict = {
    #     'is_opened': True,
    #     'id_field': 'id',
    #     'name_field': 'name',
    # }
    # 导入
    # import_serializer_class = HostImportSerializer
    # import_field_dict = {
    #     "name": "登客户名称",
    #     "description": "公司描述",
    #     "locale_state": {
    #         "title": "国家",
    #         "choices": {"queryset": Area.objects.filter(
    #             level=1
    #         ), "values_name": "name", "key_name": "code"}
    #     }
    # }
    # permission_classes = []

