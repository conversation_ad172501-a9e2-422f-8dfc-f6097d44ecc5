from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON>, OrderingFilter
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.viewsets import ReadOnlyModelViewSet

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from nova.models import OPKeypair
from nova.serializers import keypair


class LargeLimitOffsetPagination(LimitOffsetPagination):
    default_limit = 1000000  # 设置一个非常大的默认限制


class OPKeypairViewSet(ChaosCustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    haha
    """
    queryset = OPKeypair.objects.all()
    serializer_class = keypair.OPKeypairModelSerializer
    create_serializer_class = keypair.OPKeypairModelCreateUpdateSerializer
    update_serializer_class = keypair.OPKeypairModelCreateUpdateSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ['project_name', ]
    search_fields = ('keypair_id', 'name')
    ordering_fields = ('id',)


class OPKeypairListViewSet(ReadOnlyModelViewSet):
    queryset = OPKeypair.objects.all()
    serializer_class = keypair.OPKeypairListSerializer
    pagination_class = LargeLimitOffsetPagination
    filter_fields = ['project_name', 'user_id']
    http_method_names = ['get']  # 只允许GET请求
