from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import DetailResponse
from nova.models import ProjectsModel
from nova.serializers.projects import ProjectsModelSerializer


class ProjectsModelViewSet(ChaosCustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = ProjectsModel.objects.all()
    serializer_class = ProjectsModelSerializer
    filter_fields = '__all__'

    @action(methods=["GET"], detail=False, permission_classes=[IsAuthenticated], extra_filter_class=[])
    def all_projects(self, request, *args, **kwargs):
        """增加一个额外的方法，获取所有project"""
        queryset = ProjectsModel.objects.filter(is_deleted=False).values('id', 'name', 'sign')
        return DetailResponse(data=queryset, msg="获取成功")
