from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.viewsets import ReadOnlyModelViewSet

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from nova.models import OPSubnet
from nova.serializers import subnet


class LargeLimitOffsetPagination(LimitOffsetPagination):
    default_limit = 1000000  # 设置一个非常大的默认限制


class OPSubnetViewSet(ChaosCustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    haha
    """
    queryset = OPSubnet.objects.all()
    serializer_class = subnet.OPSubnetModelSerializer
    create_serializer_class = subnet.OPSubnetModelCreateUpdateSerializer
    update_serializer_class = subnet.OPSubnetModelCreateUpdateSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ['project_name', ]
    search_fields = ('subnet_id', 'name')
    ordering_fields = ('id',)


class OPSubnetListViewSet(ReadOnlyModelViewSet):
    queryset = OPSubnet.objects.all()
    serializer_class = subnet.OPSubnetListSerializer
    pagination_class = LargeLimitOffsetPagination
    filter_fields = ['project_name', ]
    http_method_names = ['get']  # 只允许GET请求
