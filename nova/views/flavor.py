from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.viewsets import ReadOnlyModelViewSet

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from nova.models import OPFlavor
from nova.serializers import flavor


class LargeLimitOffsetPagination(LimitOffsetPagination):
    default_limit = 1000000  # 设置一个非常大的默认限制


class OPFlavorViewSet(ChaosCustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    haha
    """
    queryset = OPFlavor.objects.all()
    serializer_class = flavor.OPFlavorModelSerializer
    create_serializer_class = flavor.OPFlavorModelCreateUpdateSerializer
    update_serializer_class = flavor.OPFlavorModelCreateUpdateSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ['vcpus', ]
    search_fields = ('flavor_id', 'name')
    ordering_fields = ('id',)


# 普通实例规格
class OPFlavorListViewSet(ReadOnlyModelViewSet):
    queryset = OPFlavor.objects.filter(flavor_type="instance")
    serializer_class = flavor.OPFlavorListSerializer
    pagination_class = LargeLimitOffsetPagination
    http_method_names = ['get']  # 只允许GET请求


# gpu实例规格
class OPFlavorGPUInstanceListViewSet(ReadOnlyModelViewSet):
    queryset = OPFlavor.objects.filter(flavor_type="gpu-instance")
    serializer_class = flavor.OPFlavorListSerializer
    pagination_class = LargeLimitOffsetPagination
    http_method_names = ['get']  # 只允许GET请求


# baremetal实例规格
class OPFlavorBaremetalListViewSet(ReadOnlyModelViewSet):
    queryset = OPFlavor.objects.filter(flavor_type="baremetal")
    serializer_class = flavor.OPFlavorListSerializer
    pagination_class = LargeLimitOffsetPagination
    http_method_names = ['get']  # 只允许GET请求
