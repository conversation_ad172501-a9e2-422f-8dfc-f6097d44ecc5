# backend/crud_demo/serializers.py

from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from nova.models import CrudDemoModel


class CrudDemoModelSerializer(ChaosCustomModelSerializer):
    """
    序列化器
    """

    # 这里是进行了序列化模型及所有的字段
    class Meta:
        model = CrudDemoModel
        fields = "__all__"


# 这里是创建/更新时的列化器
class CrudDemoModelCreateUpdateSerializer(ChaosCustomModelSerializer):
    """
    创建/更新时的列化器
    """

    class Meta:
        model = CrudDemoModel
        fields = '__all__'


# 导入序列化器
class CrudDemoModelImportSerializer(ChaosCustomModelSerializer):
    """
    DemoModel导入时的列化器
    """

    class Meta:
        model = CrudDemoModel
        fields = '__all__'
