from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from nova.models import ApplicationsModel


class ApplicationsModelImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = ApplicationsModel
        exclude = ()


class ApplicationsModelSerializer(ChaosCustomModelSerializer):
    """
    主机-序列化器
    """

    class Meta:
        model = ApplicationsModel
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]
