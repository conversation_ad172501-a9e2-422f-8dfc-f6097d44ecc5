# backend/crud_demo/serializers.py

from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from nova.models import OPFlavor


class OPFlavorModelSerializer(ChaosCustomModelSerializer):
    """
    序列化器
    """

    # 这里是进行了序列化模型及所有的字段
    class Meta:
        model = OPFlavor
        fields = "__all__"


# 这里是创建/更新时的列化器
class OPFlavorModelCreateUpdateSerializer(ChaosCustomModelSerializer):
    """
    创建/更新时的列化器
    """

    class Meta:
        model = OPFlavor
        fields = '__all__'


class OPFlavorListSerializer(ChaosCustomModelSerializer):
    """
    序列化器
    """

    # 这里是进行了序列化模型及所有的字段
    class Meta:
        model = OPFlavor
        fields = ['flavor_id', 'name', 'vcpus', 'ram', 'disk', 'ephemeral', 'is_public']
