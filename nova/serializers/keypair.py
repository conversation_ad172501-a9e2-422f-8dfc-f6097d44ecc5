# backend/crud_demo/serializers.py

from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from nova.models import OPKeypair


class OPKeypairModelSerializer(ChaosCustomModelSerializer):
    """
    序列化器
    """

    # 这里是进行了序列化模型及所有的字段
    class Meta:
        model = OPKeypair
        fields = "__all__"


# 这里是创建/更新时的列化器
class OPKeypairModelCreateUpdateSerializer(ChaosCustomModelSerializer):
    """
    创建/更新时的列化器
    """

    class Meta:
        model = OPKeypair
        fields = '__all__'


class OPKeypairListSerializer(ChaosCustomModelSerializer):
    """
    list所有序列化器
    """

    # 这里是进行了序列化模型及所有的字段
    class Meta:
        model = OPKeypair
        fields = ['keypair_id', 'name']
