from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from nova.models import ProjectsModel


class ProjectsModelImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = ProjectsModel
        exclude = ()


class ProjectsModelSerializer(ChaosCustomModelSerializer):
    """
    主机-序列化器
    """

    class Meta:
        model = ProjectsModel
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]
