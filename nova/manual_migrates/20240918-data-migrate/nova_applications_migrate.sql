# 导出结果时，删除默认的 id 列
SELECT *, CONVERT(id, CHAR) AS id, 0 AS is_deleted FROM nova_projects;


# 导出结果时，删除默认的 id 列
SELECT *, CONVERT(id, CHAR) AS id, 0 AS is_deleted FROM nova_applications;


# 导出结果时，删除默认的 id 列
SELECT *, CONVERT(id, CHAR) AS id, 0 AS is_deleted FROM nova_op_flavor;


# 导出结果时，删除默认的 id 列
SELECT *, CONVERT(id, CHAR) AS id, 0 AS is_deleted FROM nova_op_image;


# 导出结果时，删除默认的 id 列
SELECT *, CONVERT(id, CHAR) AS id, 0 AS is_deleted FROM nova_op_server;
