# 生产环境更新项目关系ID为uuid-->project_id
UPDATE nova_applications SET project_id='project-2dc1fc40e89d4d9f8b7d8d4d26d3b900' WHERE project_id='7';
UPDATE nova_applications SET project_id='project-6a464f4581454172959e019eefe8e474' WHERE project_id='1';
UPDATE nova_applications SET project_id='project-473b392dd28940c7abd4bb5e0de67a93' WHERE project_id='2';
UPDATE nova_applications SET project_id='project-44d6deef6e1944c7aa5a4e9eb4b67305' WHERE project_id='3';
UPDATE nova_applications SET project_id='project-d23bc7a297c84e92b80cc21c140129db' WHERE project_id='4';
UPDATE nova_applications SET project_id='project-1186b848abe44bf886ad0ae938118d91' WHERE project_id='5';
UPDATE nova_applications SET project_id='project-5aa4ba1027db4630a7c02d0217698756' WHERE project_id='6';


# 更新nova_applications 中的id为uuid
#####################################################################################################
-- 创建临时存储表
CREATE TEMPORARY TABLE IF NOT EXISTS temp_uuids (
    seq INT PRIMARY KEY,
    new_uuid VARCHAR(64) NOT NULL,
    UNIQUE INDEX `new_uuid` (`new_uuid`) USING BTREE
) ENGINE=InnoDB;

-- 清空原有数据
TRUNCATE TABLE temp_uuids;

-- 插入并关联 New-UUID
INSERT INTO temp_uuids (seq, new_uuid)
SELECT seq, CONCAT('application-', REPLACE(UUID(), '-', ''))
FROM nova_applications;

-- 更新关联 New-UUID
UPDATE nova_applications yt
JOIN temp_uuids tu ON yt.seq = tu.seq
SET yt.id = tu.new_uuid;

-- 清理临时表
DROP TEMPORARY TABLE temp_uuids;

#####################################################################################################