# 文件backend/crud_demo/models.py

from django.db import models

# Create your models here.
from dvadmin.utils.chaos_models import ChaosCoreModel


class CrudDemoModel(ChaosCoreModel):
    """
    DEMO：商品测试
    """
    goods = models.CharField(max_length=255, verbose_name="商品")
    inventory = models.IntegerField(verbose_name="库存量")
    goods_price = models.FloatField(verbose_name="商品定价")
    purchase_goods_date = models.DateField(verbose_name="进货时间")

    class Meta:
        db_table = "goods"
        verbose_name = '商品表'
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)


class ProjectsModel(ChaosCoreModel):
    """
    项目
    """
    name = models.CharField(
        max_length=32, unique=True,
        verbose_name='项目名', db_comment='项目名', help_text='项目名')
    sign = models.CharField(
        max_length=32, unique=True,
        verbose_name="项目标识", db_comment='项目标识', help_text='项目标识')

    class Meta:
        db_table = 'nova_projects'
        verbose_name = '项目'
        verbose_name_plural = verbose_name
        ordering = ['id']


class ApplicationsModel(ChaosCoreModel):
    """
    应用
    """

    name = models.CharField(
        max_length=128, unique=True,
        verbose_name='应用名称', db_comment='应用名称', help_text='应用名称')
    url = models.CharField(
        max_length=128, blank=True,
        verbose_name='应用地址', db_comment='应用地址', help_text='应用地址')
    port = models.CharField(
        max_length=128, blank=True,
        verbose_name='端口号', db_comment='端口号', help_text='端口号')
    user = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='管理账号', db_comment='管理账号', help_text='管理账号')
    passwd = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='管理密码', db_comment='管理密码', help_text='管理密码')
    app_type = models.CharField(
        max_length=128, blank=True,
        verbose_name='应用类型', db_comment='应用类型', help_text='应用类型')
    deploy_dir = models.CharField(
        max_length=128, blank=True,
        verbose_name='部署主目录', db_comment='部署主目录', help_text='部署主目录')
    deploy_sub_dir = models.CharField(
        max_length=128, blank=True,
        verbose_name='部署子目录', db_comment='部署子目录', help_text='部署子目录')
    project = models.ForeignKey(
        'ProjectsModel', to_field='id', on_delete=models.SET_NULL,
        related_name='project_application', null=True, blank=True,
        verbose_name='所属项目', db_comment='所属项目', help_text='所属项目')
    # hosts = models.ManyToManyField('Hosts', db_table='cmdb_applications_to_cmdb_hosts', blank=True, verbose_name='关联主机')
    decompress_dir = models.CharField(
        max_length=128, blank=True,
        verbose_name='解压目录', db_comment='解压目录', help_text='包解压目录名称')
    memo = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='备注', db_comment='备注', help_text='备注')

    class Meta:
        db_table = 'nova_applications'
        verbose_name = '应用'
        verbose_name_plural = verbose_name
        ordering = ['id']


# class Hosts(CoreModel):
#     """
#     主机设备
#     """
#     instance_id = models.CharField(max_length=255, verbose_name='实例ID')
#     sn = models.CharField(max_length=255, blank=True, null=True)
#     type = models.CharField(max_length=255, blank=True, null=True)
#     ip_private = models.CharField(max_length=255, blank=True, null=True)
#     ip_public = models.CharField(max_length=255, blank=True, null=True)
#     ip_bmc = models.CharField(max_length=255, blank=True, null=True)
#     ip_kvm_host = models.CharField(max_length=255, blank=True, null=True)
#     bmc_user = models.CharField(max_length=255, blank=True, null=True)
#     bmc_passwd = models.CharField(max_length=255, blank=True, null=True)
#     area = models.CharField(max_length=255, blank=True, null=True)
#     idc = models.CharField(max_length=255, blank=True, null=True)
#     room = models.CharField(max_length=255, blank=True, null=True)
#     cabinet = models.CharField(max_length=255, blank=True, null=True)
#     comment = models.CharField(max_length=255, blank=True, null=True)
#     status = models.CharField(max_length=255, blank=True, null=True)
#     os = models.CharField(max_length=255, blank=True, null=True)
#     cpu = models.CharField(max_length=255, blank=True, null=True)
#     mem = models.CharField(max_length=255, blank=True, null=True)
#     sys_disk = models.CharField(max_length=255, blank=True, null=True)
#     data_disk = models.CharField(max_length=255, blank=True, null=True)
#     gpu_model = models.CharField(max_length=255, blank=True, null=True)
#     gpu_count = models.CharField(max_length=255, blank=True, null=True)
#     ssh_port = models.CharField(max_length=255, blank=True, null=True)
#     ssh_user = models.CharField(max_length=255, blank=True, null=True)
#     ssh_passwd = models.CharField(max_length=255, blank=True, null=True)
#     start_time = models.CharField(max_length=255, blank=True, null=True)
#     expire_time = models.CharField(max_length=255, blank=True, null=True)
#     is_buffer = models.IntegerField(default=0)
#     ipmi_mon = models.IntegerField(blank=True, null=True, default=0)
#     node_mon = models.IntegerField(blank=True, null=True, default=0)
#     owner = models.CharField(max_length=255, blank=True, null=True, verbose_name='客户、管理员')
#
#     class Meta:
#         db_table = 'nova_hosts'
#         verbose_name = '主机'
#         verbose_name_plural = verbose_name
#         ordering = ['id']


class OPServer(ChaosCoreModel):
    """
    openstack server instance
    """
    insid = models.CharField(
        max_length=128, unique=True, null=True,
        verbose_name='openstack instance id，全局唯一', db_comment='openstack instance id，全局唯一',
        help_text='openstack instance id，全局唯一')
    instance_type = models.CharField(
        max_length=64, blank=True, null=True,
        verbose_name='实例类型', db_comment='实例类型', help_text='实例类型')
    name = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='server名', db_comment='server名', help_text='server名')
    project_id = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='项目id', db_comment='项目id', help_text='项目id')
    compute_host = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='宿主机', db_comment='宿主机', help_text='宿主机')
    ipaddr = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='IP address', db_comment='IP address', help_text='IP address')
    image_name = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='镜像名', db_comment='镜像名', help_text='镜像名')
    key_name = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='key pair name', db_comment='key pair name', help_text='key pair name')
    flavor_name = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='flavor_name', db_comment='flavor name', help_text='flavor name')
    volume_size = models.IntegerField(
        blank=True, null=True,
        verbose_name='创建时传入的volume大小', db_comment='创建时传入的volume大小', help_text='创建时传入的volume大小')
    vcpus = models.IntegerField(
        blank=True, null=True,
        verbose_name="cpu核心", db_comment='cpu核心', help_text='cpu核心')
    disk = models.IntegerField(
        blank=True, null=True,
        verbose_name="系统盘", db_comment='系统盘', help_text='系统盘')
    ram = models.IntegerField(
        blank=True, null=True,
        verbose_name="内存", db_comment='内存', help_text='内存')
    security_groups = models.JSONField(
        blank=True, verbose_name='安全组', db_comment='安全组', help_text='安全组')
    status = models.CharField(
        max_length=64, blank=True, null=True,
        verbose_name='状态', db_comment='状态', help_text='状态')
    vm_state = models.CharField(
        max_length=64, blank=True, null=True,
        verbose_name='vm状态', db_comment='vm状态', help_text='vm状态')
    netname = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='网络名', db_comment='网络名', help_text='网络名')
    meta = models.JSONField(
        blank=True, null=True,
        verbose_name='metadata', db_comment='metadata', help_text='metadata')
    hostname = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='主机名', db_comment='主机名', help_text='主机名')
    created_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    launched_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='启动时间', db_comment='启动时间', help_text='启动时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    # 下面是公共字段
    sync_time = models.DateTimeField(
        blank=True, null=True,
        verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')

    class Meta:
        db_table = 'nova_op_server'
        verbose_name = '服务器'
        verbose_name_plural = verbose_name
        ordering = ['-seq']


class OPImage(ChaosCoreModel):
    """
    openstack server instance
    """
    image_id = models.CharField(
        max_length=255, unique=True,
        verbose_name='镜像ID', db_comment='镜像ID', help_text='镜像ID')
    name = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='镜像名称', db_comment='镜像名称', help_text='镜像名称')
    visibility = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='可见性', db_comment='可见性', help_text='可见性')
    size = models.BigIntegerField(
        blank=True, null=True,
        verbose_name="大小", db_comment='大小', help_text='大小')
    status = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='状态', db_comment='状态', help_text='状态')
    min_ram = models.IntegerField(
        blank=True, null=True,
        verbose_name="最小内存", db_comment='最小内存', help_text='最小内存')
    min_disk = models.IntegerField(
        blank=True, null=True,
        verbose_name="最小磁盘", db_comment='最小磁盘', help_text='最小磁盘')
    image_type = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='镜像类型', db_comment='镜像类型', help_text='镜像类型')
    disk_format = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='磁盘格式', db_comment='磁盘格式', help_text='磁盘格式')
    protected = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='受保护的', db_comment='受保护的', help_text='受保护的')
    project_id = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='项目id', db_comment='项目id', help_text='项目id')
    project_name = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='项目名称', db_comment='项目名称', help_text='项目名称')
    owner_id = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='owner id', db_comment='owner id', help_text='owner id')
    owner_name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='owner名称', db_comment='owner名称', help_text='owner名称')
    created_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    # 下面是公共字段
    sync_time = models.DateTimeField(
        blank=True, null=True,
        verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')

    class Meta:
        db_table = 'nova_op_image'
        verbose_name = '镜像'
        verbose_name_plural = verbose_name
        ordering = ['id']


class OPFlavor(ChaosCoreModel):
    """
    openstack flavor 实例规格
    """
    flavor_id = models.CharField(
        max_length=255, unique=True,
        verbose_name='实例规格ID', db_comment='实例规格ID', help_text='实例规格ID')
    flavor_type = models.CharField(
        max_length=64, blank=True, null=True,
        verbose_name='规格类型', db_comment='规格类型', help_text='规格类型')
    name = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='规格名称', db_comment='规格名称', help_text='规格名称')
    ram = models.IntegerField(
        blank=True, null=True,
        verbose_name="内存", db_comment='内存', help_text='内存')
    vcpus = models.IntegerField(
        blank=True, null=True,
        verbose_name="cpu", db_comment='cpu', help_text='cpu')
    disk = models.IntegerField(
        blank=True, null=True,
        verbose_name="系统盘", db_comment='系统盘', help_text='系统盘')
    ephemeral = models.IntegerField(
        blank=True, null=True,
        verbose_name="临时存储", db_comment='临时存储', help_text='临时存储')
    swap = models.IntegerField(
        blank=True, null=True,
        verbose_name="swap", db_comment='swap', help_text='swap')
    rxtx_factor = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='RX/TX factor', db_comment='RX/TX factor', help_text='RX/TX factor')
    is_public = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='公开', db_comment='公开', help_text='公开')
    extra_specs = models.JSONField(
        blank=True, null=True,
        verbose_name='metadata', db_comment='metadata', help_text='metadata')
    project_id = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='项目id', db_comment='项目id', help_text='项目id')
    project_name = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='项目名称', db_comment='项目名称', help_text='项目名称')
    # 下面是公共字段
    sync_time = models.DateTimeField(
        blank=True, null=True,
        verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')

    class Meta:
        db_table = 'nova_op_flavor'
        verbose_name = '实例规格'
        verbose_name_plural = verbose_name
        ordering = ['id']


class OPSecurityGroup(ChaosCoreModel):
    """
    openstack SecurityGroup 安全组
    """
    sg_id = models.CharField(
        max_length=255, unique=True,
        verbose_name='安全组ID', db_comment='安全组ID', help_text='安全组ID')
    name = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='规格名称', db_comment='规格名称', help_text='规格名称')
    description = models.TextField(
        blank=True, null=True,
        verbose_name='描述', db_comment='描述', help_text='描述')
    security_group_rules = models.JSONField(
        blank=True, verbose_name='安全组规则', db_comment='安全组规则', help_text='安全组规则')
    tenant_id = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='租户id', db_comment='租户id', help_text='租户id')
    project_id = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='项目id', db_comment='项目id', help_text='项目id')
    project_name = models.CharField(
        max_length=128, blank=True, null=True,
        verbose_name='项目名称', db_comment='项目名称', help_text='项目名称')
    is_shared = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='共享的', db_comment='共享的', help_text='共享的')
    created_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    # 下面是公共字段
    sync_time = models.DateTimeField(
        blank=True, null=True,
        verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')

    class Meta:
        db_table = 'nova_op_security_group'
        verbose_name = '安全组'
        verbose_name_plural = verbose_name
        ordering = ['id']


class OPNetwork(ChaosCoreModel):
    """
    openstack Network 网络
    """
    net_id = models.CharField(
        max_length=255, unique=True, verbose_name='网络ID', db_comment='网络ID', help_text='网络ID')
    name = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='网络名称', db_comment='网络名称', help_text='网络名称')
    subnet_ids = models.JSONField(
        blank=True, null=True, verbose_name='子网ID', db_comment='子网ID', help_text='子网ID')
    project_id = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='项目id', db_comment='项目id', help_text='项目id')
    project_name = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='项目名称', db_comment='项目名称', help_text='项目名称')
    tenant_id = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='租户id', db_comment='租户id', help_text='租户id')
    mtu = models.IntegerField(
        blank=True, null=True, verbose_name='mtu', db_comment='mtu', help_text='mtu')
    status = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='状态', db_comment='状态', help_text='状态')
    availability_zones = models.JSONField(
        blank=True, null=True, verbose_name='可用区', db_comment='可用区', help_text='可用区')
    is_admin_state_up = models.BooleanField(
        default=False, blank=True, null=True, verbose_name='可管理', db_comment='可管理', help_text='可管理')
    is_router_external = models.BooleanField(
        default=False, blank=True, null=True, verbose_name='外部的', db_comment='外部的', help_text='外部的')
    is_shared = models.BooleanField(
        default=False, blank=True, null=True, verbose_name='共享的', db_comment='共享的', help_text='共享的')
    is_default = models.BooleanField(
        default=False, blank=True, null=True, verbose_name='默认的', db_comment='共享的', help_text='共享的')
    provider_network_type = models.CharField(
        max_length=64, blank=True, null=True, verbose_name='网络类型', db_comment='网络类型', help_text='网络类型')
    provider_physical_network = models.CharField(
        max_length=64, blank=True, null=True, verbose_name='物理网络', db_comment='物理网络', help_text='物理网络')
    provider_segmentation_id = models.IntegerField(
        blank=True, null=True, verbose_name='段ID', db_comment='段ID', help_text='段ID')
    created_at = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    # 下面是公共字段
    sync_time = models.DateTimeField(
        blank=True, null=True, verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')

    class Meta:
        db_table = 'nova_op_network'
        verbose_name = '网络'
        verbose_name_plural = verbose_name
        ordering = ['id']


class OPSubnet(ChaosCoreModel):
    """
    openstack Subnet 子网(隶属于网络下)
    """
    subnet_id = models.CharField(
        max_length=255, unique=True, verbose_name='ID', db_comment='安全组ID', help_text='安全组ID')
    name = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='安全组名称', db_comment='安全组名称', help_text='安全组名称')
    project_id = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='项目id', db_comment='项目id', help_text='项目id')
    project_name = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='项目名称', db_comment='项目名称', help_text='项目名称')
    network_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='网络id', db_comment='网络id', help_text='网络id')
    ip_version = models.IntegerField(
        blank=True, null=True, verbose_name='ip_version', db_comment='ip_version', help_text='ip_version')
    is_dhcp_enabled = models.BooleanField(
        default=False, blank=True, null=True, verbose_name='DHCP开启', db_comment='DHCP开启', help_text='DHCP开启')
    gateway_ip = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='GW', db_comment='GW', help_text='GW')
    cidr = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='cidr', db_comment='cidr', help_text='cidr')
    allocation_pools = models.JSONField(
        blank=True, null=True, verbose_name='网络分配池', db_comment='网络分配池', help_text='网络分配池')
    host_routes = models.JSONField(
        blank=True, null=True, verbose_name='主机路由', db_comment='主机路由', help_text='主机路由')
    dns_nameservers = models.JSONField(
        blank=True, null=True, verbose_name='DNS服务器', db_comment='DNS服务器', help_text='DNS服务器')
    created_at = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='创建时间', db_comment='创建时间', help_text='创建时间')
    updated_at = models.CharField(
        max_length=255, blank=True, null=True, verbose_name='更新时间', db_comment='更新时间', help_text='更新时间')
    # 下面是公共字段
    sync_time = models.DateTimeField(
        blank=True, null=True, verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')

    class Meta:
        db_table = 'nova_op_subnet'
        verbose_name = '子网'
        verbose_name_plural = verbose_name
        ordering = ['seq']


class OPKeypair(ChaosCoreModel):
    """
    openstack Subnet 子网(隶属于网络下)
    """
    keypair_id = models.CharField(
        max_length=255, unique=True, verbose_name='keypair ID', db_comment='keypair ID', help_text='keypair ID')
    name = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='安全组名称', db_comment='安全组名称', help_text='安全组名称')
    public_key = models.TextField(
        blank=True, null=True, verbose_name='公钥', db_comment='公钥', help_text='公钥')
    fingerprint = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='指纹', db_comment='指纹', help_text='指纹')
    type = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='类型', db_comment='类型', help_text='类型')
    project_id = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='项目id', db_comment='项目id', help_text='项目id')
    project_name = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='项目名称', db_comment='项目名称', help_text='项目名称')
    user_id = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='用户id', db_comment='用户id', help_text='用户id')
    user_name = models.CharField(
        max_length=128, blank=True, null=True, verbose_name='用户名称', db_comment='用户名称', help_text='用户名称')
    # 下面是公共字段
    sync_time = models.DateTimeField(
        blank=True, null=True, verbose_name='sync同步时间', db_comment='sync同步时间', help_text='sync同步时间')

    class Meta:
        db_table = 'nova_op_keypair'
        verbose_name = '秘钥对'
        verbose_name_plural = verbose_name
        ordering = ['seq']