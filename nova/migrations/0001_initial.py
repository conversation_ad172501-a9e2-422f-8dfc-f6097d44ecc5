# Generated by Django 5.0.6 on 2024-09-18 09:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CrudDemoModel',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.Char<PERSON>ield(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('goods', models.CharField(max_length=255, verbose_name='商品')),
                ('inventory', models.IntegerField(verbose_name='库存量')),
                ('goods_price', models.FloatField(verbose_name='商品定价')),
                ('purchase_goods_date', models.DateField(verbose_name='进货时间')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '商品表',
                'verbose_name_plural': '商品表',
                'db_table': 'goods',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='OPFlavor',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('flavor_id', models.CharField(db_comment='实例规格ID', help_text='实例规格ID', max_length=255, unique=True, verbose_name='实例规格ID')),
                ('flavor_type', models.CharField(blank=True, db_comment='规格类型', help_text='规格类型', max_length=64, null=True, verbose_name='规格类型')),
                ('name', models.CharField(blank=True, db_comment='规格名称', help_text='规格名称', max_length=128, null=True, verbose_name='规格名称')),
                ('ram', models.IntegerField(blank=True, db_comment='内存', help_text='内存', null=True, verbose_name='内存')),
                ('vcpus', models.IntegerField(blank=True, db_comment='cpu', help_text='cpu', null=True, verbose_name='cpu')),
                ('disk', models.IntegerField(blank=True, db_comment='系统盘', help_text='系统盘', null=True, verbose_name='系统盘')),
                ('ephemeral', models.IntegerField(blank=True, db_comment='临时存储', help_text='临时存储', null=True, verbose_name='临时存储')),
                ('swap', models.IntegerField(blank=True, null=True, verbose_name='swap')),
                ('rxtx_factor', models.CharField(blank=True, max_length=128, null=True, verbose_name='RX/TX factor')),
                ('is_public', models.BooleanField(blank=True, default=False, null=True, verbose_name='公开')),
                ('extra_specs', models.JSONField(blank=True, null=True, verbose_name='metadata')),
                ('project_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='项目id')),
                ('project_name', models.CharField(blank=True, max_length=128, null=True, verbose_name='项目名称')),
                ('sync_time', models.DateTimeField(blank=True, null=True, verbose_name='sync同步时间')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '实例规格',
                'verbose_name_plural': '实例规格',
                'db_table': 'nova_op_flavor',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='OPImage',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('image_id', models.CharField(max_length=255, unique=True, verbose_name='镜像ID')),
                ('name', models.CharField(blank=True, max_length=128, null=True, verbose_name='镜像名称')),
                ('visibility', models.CharField(blank=True, max_length=128, null=True, verbose_name='可见性')),
                ('size', models.BigIntegerField(blank=True, null=True, verbose_name='大小')),
                ('status', models.CharField(blank=True, max_length=128, null=True, verbose_name='状态')),
                ('min_ram', models.IntegerField(blank=True, null=True, verbose_name='最小内存')),
                ('min_disk', models.IntegerField(blank=True, null=True, verbose_name='最小磁盘')),
                ('image_type', models.CharField(blank=True, max_length=128, null=True, verbose_name='镜像类型')),
                ('disk_format', models.CharField(blank=True, max_length=128, null=True, verbose_name='磁盘格式')),
                ('protected', models.BooleanField(blank=True, default=False, null=True, verbose_name='收保护的')),
                ('project_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='项目id')),
                ('project_name', models.CharField(blank=True, max_length=128, null=True, verbose_name='项目名称')),
                ('owner_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='owner id')),
                ('owner_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='owner名称')),
                ('created_at', models.CharField(blank=True, max_length=255, null=True, verbose_name='创建时间')),
                ('updated_at', models.CharField(blank=True, max_length=255, null=True, verbose_name='更新时间')),
                ('sync_time', models.DateTimeField(blank=True, null=True, verbose_name='sync同步时间')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '镜像',
                'verbose_name_plural': '镜像',
                'db_table': 'nova_op_image',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='OPServer',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('insid', models.CharField(max_length=128, null=True, unique=True, verbose_name='openstack instance id，全局唯一')),
                ('instance_type', models.CharField(blank=True, max_length=64, null=True, verbose_name='实例类型')),
                ('name', models.CharField(blank=True, max_length=128, null=True, verbose_name='server名')),
                ('project_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='项目id')),
                ('compute_host', models.CharField(blank=True, max_length=128, null=True, verbose_name='宿主机')),
                ('ipaddr', models.CharField(blank=True, max_length=128, null=True, verbose_name='IP address')),
                ('image_name', models.CharField(blank=True, max_length=128, null=True, verbose_name='镜像名')),
                ('key_name', models.CharField(blank=True, max_length=128, null=True, verbose_name='key pair name')),
                ('flavor_name', models.CharField(blank=True, max_length=128, null=True, verbose_name='flavor_name')),
                ('volume_size', models.IntegerField(blank=True, null=True, verbose_name='创建时传入的volume大小')),
                ('vcpus', models.IntegerField(blank=True, null=True, verbose_name='cpu核心')),
                ('disk', models.IntegerField(blank=True, null=True, verbose_name='系统盘')),
                ('ram', models.IntegerField(blank=True, null=True, verbose_name='内存')),
                ('security_groups', models.JSONField(blank=True, verbose_name='安全组')),
                ('status', models.CharField(blank=True, max_length=64, null=True, verbose_name='状态')),
                ('vm_state', models.CharField(blank=True, max_length=64, null=True, verbose_name='vm状态')),
                ('netname', models.CharField(blank=True, max_length=128, null=True, verbose_name='网络名')),
                ('meta', models.JSONField(blank=True, null=True, verbose_name='metadata')),
                ('hostname', models.CharField(blank=True, max_length=128, null=True, verbose_name='主机名')),
                ('created_at', models.CharField(blank=True, max_length=255, null=True, verbose_name='创建时间')),
                ('launched_at', models.CharField(blank=True, max_length=255, null=True, verbose_name='启动时间')),
                ('updated_at', models.CharField(blank=True, max_length=255, null=True, verbose_name='更新时间')),
                ('sync_time', models.DateTimeField(blank=True, null=True, verbose_name='sync同步时间')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '服务器',
                'verbose_name_plural': '服务器',
                'db_table': 'nova_op_server',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='ProjectsModel',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('name', models.CharField(max_length=32, unique=True, verbose_name='项目名')),
                ('sign', models.CharField(max_length=32, unique=True, verbose_name='项目标识')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '项目',
                'verbose_name_plural': '项目',
                'db_table': 'nova_projects',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='ApplicationsModel',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('name', models.CharField(max_length=128, unique=True, verbose_name='应用名称')),
                ('url', models.CharField(blank=True, max_length=128, verbose_name='应用地址')),
                ('port', models.CharField(blank=True, max_length=128, verbose_name='端口号')),
                ('user', models.CharField(blank=True, max_length=128, null=True, verbose_name='管理账号')),
                ('passwd', models.CharField(blank=True, max_length=128, null=True, verbose_name='管理密码')),
                ('app_type', models.CharField(blank=True, max_length=128, verbose_name='应用类型')),
                ('deploy_dir', models.CharField(blank=True, max_length=128, verbose_name='部署主目录')),
                ('deploy_sub_dir', models.CharField(blank=True, max_length=128, verbose_name='部署子目录')),
                ('decompress_dir', models.CharField(blank=True, help_text='包解压目录名称', max_length=128, verbose_name='解压目录')),
                ('memo', models.CharField(blank=True, max_length=128, null=True, verbose_name='备注')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='nova.projectsmodel', verbose_name='所属项目')),
            ],
            options={
                'verbose_name': '应用',
                'verbose_name_plural': '应用',
                'db_table': 'nova_applications',
                'ordering': ['id'],
            },
        ),
    ]
