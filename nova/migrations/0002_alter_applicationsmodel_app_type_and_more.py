# Generated by Django 5.0.6 on 2024-09-18 15:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('nova', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='applicationsmodel',
            name='app_type',
            field=models.CharField(blank=True, db_comment='应用类型', help_text='应用类型', max_length=128, verbose_name='应用类型'),
        ),
        migrations.AlterField(
            model_name='applicationsmodel',
            name='decompress_dir',
            field=models.CharField(blank=True, db_comment='解压目录', help_text='包解压目录名称', max_length=128, verbose_name='解压目录'),
        ),
        migrations.AlterField(
            model_name='applicationsmodel',
            name='deploy_dir',
            field=models.CharField(blank=True, db_comment='部署主目录', help_text='部署主目录', max_length=128, verbose_name='部署主目录'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='applicationsmodel',
            name='deploy_sub_dir',
            field=models.Char<PERSON>ield(blank=True, db_comment='部署子目录', help_text='部署子目录', max_length=128, verbose_name='部署子目录'),
        ),
        migrations.AlterField(
            model_name='applicationsmodel',
            name='memo',
            field=models.CharField(blank=True, db_comment='备注', help_text='备注', max_length=128, null=True, verbose_name='备注'),
        ),
        migrations.AlterField(
            model_name='applicationsmodel',
            name='name',
            field=models.CharField(db_comment='应用名称', help_text='应用名称', max_length=128, unique=True, verbose_name='应用名称'),
        ),
        migrations.AlterField(
            model_name='applicationsmodel',
            name='passwd',
            field=models.CharField(blank=True, db_comment='管理密码', help_text='管理密码', max_length=128, null=True, verbose_name='管理密码'),
        ),
        migrations.AlterField(
            model_name='applicationsmodel',
            name='port',
            field=models.CharField(blank=True, db_comment='端口号', help_text='端口号', max_length=128, verbose_name='端口号'),
        ),
        migrations.AlterField(
            model_name='applicationsmodel',
            name='project',
            field=models.ForeignKey(blank=True, db_comment='所属项目', help_text='所属项目', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='project_application', to='nova.projectsmodel', to_field='id', verbose_name='所属项目'),
        ),
        migrations.AlterField(
            model_name='applicationsmodel',
            name='url',
            field=models.CharField(blank=True, db_comment='应用地址', help_text='应用地址', max_length=128, verbose_name='应用地址'),
        ),
        migrations.AlterField(
            model_name='applicationsmodel',
            name='user',
            field=models.CharField(blank=True, db_comment='管理账号', help_text='管理账号', max_length=128, null=True, verbose_name='管理账号'),
        ),
        migrations.AlterField(
            model_name='opflavor',
            name='extra_specs',
            field=models.JSONField(blank=True, db_comment='metadata', help_text='metadata', null=True, verbose_name='metadata'),
        ),
        migrations.AlterField(
            model_name='opflavor',
            name='is_public',
            field=models.BooleanField(blank=True, db_comment='公开', default=False, help_text='公开', null=True, verbose_name='公开'),
        ),
        migrations.AlterField(
            model_name='opflavor',
            name='project_id',
            field=models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=255, null=True, verbose_name='项目id'),
        ),
        migrations.AlterField(
            model_name='opflavor',
            name='project_name',
            field=models.CharField(blank=True, db_comment='项目名称', help_text='项目名称', max_length=128, null=True, verbose_name='项目名称'),
        ),
        migrations.AlterField(
            model_name='opflavor',
            name='rxtx_factor',
            field=models.CharField(blank=True, db_comment='RX/TX factor', help_text='RX/TX factor', max_length=128, null=True, verbose_name='RX/TX factor'),
        ),
        migrations.AlterField(
            model_name='opflavor',
            name='swap',
            field=models.IntegerField(blank=True, db_comment='swap', help_text='swap', null=True, verbose_name='swap'),
        ),
        migrations.AlterField(
            model_name='opflavor',
            name='sync_time',
            field=models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='created_at',
            field=models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='disk_format',
            field=models.CharField(blank=True, db_comment='磁盘格式', help_text='磁盘格式', max_length=128, null=True, verbose_name='磁盘格式'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='image_id',
            field=models.CharField(db_comment='镜像ID', help_text='镜像ID', max_length=255, unique=True, verbose_name='镜像ID'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='image_type',
            field=models.CharField(blank=True, db_comment='镜像类型', help_text='镜像类型', max_length=128, null=True, verbose_name='镜像类型'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='min_disk',
            field=models.IntegerField(blank=True, db_comment='最小磁盘', help_text='最小磁盘', null=True, verbose_name='最小磁盘'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='min_ram',
            field=models.IntegerField(blank=True, db_comment='最小内存', help_text='最小内存', null=True, verbose_name='最小内存'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='name',
            field=models.CharField(blank=True, db_comment='镜像名称', help_text='镜像名称', max_length=128, null=True, verbose_name='镜像名称'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='owner_id',
            field=models.CharField(blank=True, db_comment='owner id', help_text='owner id', max_length=255, null=True, verbose_name='owner id'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='owner_name',
            field=models.CharField(blank=True, db_comment='owner名称', help_text='owner名称', max_length=255, null=True, verbose_name='owner名称'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='project_id',
            field=models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=255, null=True, verbose_name='项目id'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='project_name',
            field=models.CharField(blank=True, db_comment='项目名称', help_text='项目名称', max_length=128, null=True, verbose_name='项目名称'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='protected',
            field=models.BooleanField(blank=True, db_comment='受保护的', default=False, help_text='受保护的', null=True, verbose_name='受保护的'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='size',
            field=models.BigIntegerField(blank=True, db_comment='大小', help_text='大小', null=True, verbose_name='大小'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='status',
            field=models.CharField(blank=True, db_comment='状态', help_text='状态', max_length=128, null=True, verbose_name='状态'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='sync_time',
            field=models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='updated_at',
            field=models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间'),
        ),
        migrations.AlterField(
            model_name='opimage',
            name='visibility',
            field=models.CharField(blank=True, db_comment='可见性', help_text='可见性', max_length=128, null=True, verbose_name='可见性'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='compute_host',
            field=models.CharField(blank=True, db_comment='宿主机', help_text='宿主机', max_length=128, null=True, verbose_name='宿主机'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='created_at',
            field=models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='disk',
            field=models.IntegerField(blank=True, db_comment='系统盘', help_text='系统盘', null=True, verbose_name='系统盘'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='flavor_name',
            field=models.CharField(blank=True, db_comment='flavor name', help_text='flavor name', max_length=128, null=True, verbose_name='flavor_name'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='hostname',
            field=models.CharField(blank=True, db_comment='主机名', help_text='主机名', max_length=128, null=True, verbose_name='主机名'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='image_name',
            field=models.CharField(blank=True, db_comment='镜像名', help_text='镜像名', max_length=128, null=True, verbose_name='镜像名'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='insid',
            field=models.CharField(db_comment='openstack instance id，全局唯一', help_text='openstack instance id，全局唯一', max_length=128, null=True, unique=True, verbose_name='openstack instance id，全局唯一'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='instance_type',
            field=models.CharField(blank=True, db_comment='实例类型', help_text='实例类型', max_length=64, null=True, verbose_name='实例类型'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='ipaddr',
            field=models.CharField(blank=True, db_comment='IP address', help_text='IP address', max_length=128, null=True, verbose_name='IP address'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='key_name',
            field=models.CharField(blank=True, db_comment='key pair name', help_text='key pair name', max_length=128, null=True, verbose_name='key pair name'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='launched_at',
            field=models.CharField(blank=True, db_comment='启动时间', help_text='启动时间', max_length=255, null=True, verbose_name='启动时间'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='meta',
            field=models.JSONField(blank=True, db_comment='metadata', help_text='metadata', null=True, verbose_name='metadata'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='name',
            field=models.CharField(blank=True, db_comment='server名', help_text='server名', max_length=128, null=True, verbose_name='server名'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='netname',
            field=models.CharField(blank=True, db_comment='网络名', help_text='网络名', max_length=128, null=True, verbose_name='网络名'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='project_id',
            field=models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=255, null=True, verbose_name='项目id'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='ram',
            field=models.IntegerField(blank=True, db_comment='内存', help_text='内存', null=True, verbose_name='内存'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='security_groups',
            field=models.JSONField(blank=True, db_comment='安全组', help_text='安全组', verbose_name='安全组'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='status',
            field=models.CharField(blank=True, db_comment='状态', help_text='状态', max_length=64, null=True, verbose_name='状态'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='sync_time',
            field=models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='updated_at',
            field=models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='vcpus',
            field=models.IntegerField(blank=True, db_comment='cpu核心', help_text='cpu核心', null=True, verbose_name='cpu核心'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='vm_state',
            field=models.CharField(blank=True, db_comment='vm状态', help_text='vm状态', max_length=64, null=True, verbose_name='vm状态'),
        ),
        migrations.AlterField(
            model_name='opserver',
            name='volume_size',
            field=models.IntegerField(blank=True, db_comment='创建时传入的volume大小', help_text='创建时传入的volume大小', null=True, verbose_name='创建时传入的volume大小'),
        ),
        migrations.AlterField(
            model_name='projectsmodel',
            name='name',
            field=models.CharField(db_comment='项目名', help_text='项目名', max_length=32, unique=True, verbose_name='项目名'),
        ),
        migrations.AlterField(
            model_name='projectsmodel',
            name='sign',
            field=models.CharField(db_comment='项目标识', help_text='项目标识', max_length=32, unique=True, verbose_name='项目标识'),
        ),
    ]
