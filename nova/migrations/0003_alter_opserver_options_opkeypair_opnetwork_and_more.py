# Generated by Django 5.0.6 on 2024-11-24 14:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('nova', '0002_alter_applicationsmodel_app_type_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='opserver',
            options={'ordering': ['-seq'], 'verbose_name': '服务器', 'verbose_name_plural': '服务器'},
        ),
        migrations.CreateModel(
            name='OPKeypair',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('keypair_id', models.CharField(db_comment='keypair ID', help_text='keypair ID', max_length=255, unique=True, verbose_name='keypair ID')),
                ('name', models.CharField(blank=True, db_comment='安全组名称', help_text='安全组名称', max_length=128, null=True, verbose_name='安全组名称')),
                ('public_key', models.TextField(blank=True, db_comment='公钥', help_text='公钥', null=True, verbose_name='公钥')),
                ('fingerprint', models.CharField(blank=True, db_comment='指纹', help_text='指纹', max_length=128, null=True, verbose_name='指纹')),
                ('type', models.CharField(blank=True, db_comment='类型', help_text='类型', max_length=128, null=True, verbose_name='类型')),
                ('project_id', models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=128, null=True, verbose_name='项目id')),
                ('project_name', models.CharField(blank=True, db_comment='项目名称', help_text='项目名称', max_length=128, null=True, verbose_name='项目名称')),
                ('user_id', models.CharField(blank=True, db_comment='用户id', help_text='用户id', max_length=128, null=True, verbose_name='用户id')),
                ('user_name', models.CharField(blank=True, db_comment='用户名称', help_text='用户名称', max_length=128, null=True, verbose_name='用户名称')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '秘钥对',
                'verbose_name_plural': '秘钥对',
                'db_table': 'nova_op_keypair',
                'ordering': ['seq'],
            },
        ),
        migrations.CreateModel(
            name='OPNetwork',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('net_id', models.CharField(db_comment='网络ID', help_text='网络ID', max_length=255, unique=True, verbose_name='网络ID')),
                ('name', models.CharField(blank=True, db_comment='网络名称', help_text='网络名称', max_length=128, null=True, verbose_name='网络名称')),
                ('subnet_ids', models.JSONField(blank=True, db_comment='子网ID', help_text='子网ID', null=True, verbose_name='子网ID')),
                ('project_id', models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=128, null=True, verbose_name='项目id')),
                ('project_name', models.CharField(blank=True, db_comment='项目名称', help_text='项目名称', max_length=128, null=True, verbose_name='项目名称')),
                ('tenant_id', models.CharField(blank=True, db_comment='租户id', help_text='租户id', max_length=128, null=True, verbose_name='租户id')),
                ('mtu', models.IntegerField(blank=True, db_comment='mtu', help_text='mtu', null=True, verbose_name='mtu')),
                ('status', models.CharField(blank=True, db_comment='状态', help_text='状态', max_length=128, null=True, verbose_name='状态')),
                ('availability_zones', models.JSONField(blank=True, db_comment='可用区', help_text='可用区', null=True, verbose_name='可用区')),
                ('is_admin_state_up', models.BooleanField(blank=True, db_comment='可管理', default=False, help_text='可管理', null=True, verbose_name='可管理')),
                ('is_router_external', models.BooleanField(blank=True, db_comment='外部的', default=False, help_text='外部的', null=True, verbose_name='外部的')),
                ('is_shared', models.BooleanField(blank=True, db_comment='共享的', default=False, help_text='共享的', null=True, verbose_name='共享的')),
                ('is_default', models.BooleanField(blank=True, db_comment='共享的', default=False, help_text='共享的', null=True, verbose_name='默认的')),
                ('provider_network_type', models.CharField(blank=True, db_comment='网络类型', help_text='网络类型', max_length=64, null=True, verbose_name='网络类型')),
                ('provider_physical_network', models.CharField(blank=True, db_comment='物理网络', help_text='物理网络', max_length=64, null=True, verbose_name='物理网络')),
                ('provider_segmentation_id', models.IntegerField(blank=True, db_comment='段ID', help_text='段ID', null=True, verbose_name='段ID')),
                ('created_at', models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间')),
                ('updated_at', models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '网络',
                'verbose_name_plural': '网络',
                'db_table': 'nova_op_network',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='OPSecurityGroup',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('sg_id', models.CharField(db_comment='安全组ID', help_text='安全组ID', max_length=255, unique=True, verbose_name='安全组ID')),
                ('name', models.CharField(blank=True, db_comment='规格名称', help_text='规格名称', max_length=128, null=True, verbose_name='规格名称')),
                ('description', models.TextField(blank=True, db_comment='描述', help_text='描述', null=True, verbose_name='描述')),
                ('security_group_rules', models.JSONField(blank=True, db_comment='安全组规则', help_text='安全组规则', verbose_name='安全组规则')),
                ('tenant_id', models.CharField(blank=True, db_comment='租户id', help_text='租户id', max_length=128, null=True, verbose_name='租户id')),
                ('project_id', models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=128, null=True, verbose_name='项目id')),
                ('project_name', models.CharField(blank=True, db_comment='项目名称', help_text='项目名称', max_length=128, null=True, verbose_name='项目名称')),
                ('is_shared', models.BooleanField(blank=True, db_comment='共享的', default=False, help_text='共享的', null=True, verbose_name='共享的')),
                ('created_at', models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间')),
                ('updated_at', models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '安全组',
                'verbose_name_plural': '安全组',
                'db_table': 'nova_op_security_group',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='OPSubnet',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('subnet_id', models.CharField(db_comment='安全组ID', help_text='安全组ID', max_length=255, unique=True, verbose_name='ID')),
                ('name', models.CharField(blank=True, db_comment='安全组名称', help_text='安全组名称', max_length=128, null=True, verbose_name='安全组名称')),
                ('project_id', models.CharField(blank=True, db_comment='项目id', help_text='项目id', max_length=128, null=True, verbose_name='项目id')),
                ('project_name', models.CharField(blank=True, db_comment='项目名称', help_text='项目名称', max_length=128, null=True, verbose_name='项目名称')),
                ('network_id', models.CharField(blank=True, db_comment='网络id', help_text='网络id', max_length=255, null=True, verbose_name='网络id')),
                ('ip_version', models.IntegerField(blank=True, db_comment='ip_version', help_text='ip_version', null=True, verbose_name='ip_version')),
                ('is_dhcp_enabled', models.BooleanField(blank=True, db_comment='DHCP开启', default=False, help_text='DHCP开启', null=True, verbose_name='DHCP开启')),
                ('gateway_ip', models.CharField(blank=True, db_comment='GW', help_text='GW', max_length=128, null=True, verbose_name='GW')),
                ('cidr', models.CharField(blank=True, db_comment='cidr', help_text='cidr', max_length=128, null=True, verbose_name='cidr')),
                ('allocation_pools', models.JSONField(blank=True, db_comment='网络分配池', help_text='网络分配池', null=True, verbose_name='网络分配池')),
                ('host_routes', models.JSONField(blank=True, db_comment='主机路由', help_text='主机路由', null=True, verbose_name='主机路由')),
                ('dns_nameservers', models.JSONField(blank=True, db_comment='DNS服务器', help_text='DNS服务器', null=True, verbose_name='DNS服务器')),
                ('created_at', models.CharField(blank=True, db_comment='创建时间', help_text='创建时间', max_length=255, null=True, verbose_name='创建时间')),
                ('updated_at', models.CharField(blank=True, db_comment='更新时间', help_text='更新时间', max_length=255, null=True, verbose_name='更新时间')),
                ('sync_time', models.DateTimeField(blank=True, db_comment='sync同步时间', help_text='sync同步时间', null=True, verbose_name='sync同步时间')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '子网',
                'verbose_name_plural': '子网',
                'db_table': 'nova_op_subnet',
                'ordering': ['seq'],
            },
        ),
    ]
