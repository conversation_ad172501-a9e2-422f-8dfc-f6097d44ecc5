import json

import openstack
from openstack import connection

openstack.enable_logging(debug=False)


def get_openstack_conn():
    # Initialize connection
    # conn = openstack.connect(cloud='jinhua')

    # 创建连接到OpenStack的连接对象
    # 替换以下变量为你的OpenStack环境的具体信息
    auth_url = 'https://10.10.70.188:5000/v3'
    project_name = 'xingzai'
    username = 'admin'
    password = 'f5170ef8b7b4a93d2a1e8ae4b84bd0c6c8aaa235d9de'
    user_domain_name = 'Default'  # 或者你的用户域名称
    project_domain_name = 'default'  # 或者你的项目域名称

    conn = connection.Connection(
        region_name='RegionOne',
        auth={
            'auth_url': auth_url,
            'project_name': project_name,
            'username': username,
            'password': password,
            'user_domain_name': user_domain_name,
            'project_domain_name': project_domain_name
        },
        verify=False
    )
    return conn


conn = get_openstack_conn()


def get_instances():
    # 查询vm
    i = 0
    myli = []
    for server in conn.compute.servers():
        # print(server)
        dic = {}
        myserver = server.to_dict()
        print(json.dumps(myserver))
        for netname in myserver.get('addresses'):
            ipaddr = myserver.get('addresses').get(netname)[0].get('addr')
        dic['insid'] = myserver.get('id')
        dic['name'] = myserver.get('name')
        dic["project_id"] = myserver.get('project_id')
        dic["compute_host"] = myserver.get('compute_host')
        dic["ipaddr"] = ipaddr
        dic["key_name"] = myserver.get('key_name')
        dic["flavor_name"] = myserver.get('flavor').get('name')
        dic["netname"] = netname
        dic['status'] = myserver.get('status')
        dic['vm_state'] = myserver.get('vm_state')
        dic['vcpus'] = myserver.get('flavor').get('vcpus')
        dic['disk'] = myserver.get('flavor').get('disk')
        dic['ram'] = myserver.get('flavor').get('ram')
        dic['security_groups'] = json.dumps(myserver.get('security_groups'))  # 存储为json字符串
        dic['status'] = myserver.get('status')
        dic['vm_state'] = myserver.get('vm_state')
        dic['meta'] = json.dumps(myserver.get('metadata'))  # 存储为json字符串
        dic['hostname'] = myserver.get('hostname')
        dic['created_at'] = myserver.get('created_at')
        dic['launched_at'] = myserver.get('launched_at')
        dic['updated_at'] = myserver.get('updated_at')
        # todo: add image name
        dic['image_id'] = myserver.get('image').get('id')
        # if not dic['image_id'] and myserver.get('attached_volumes'):
        #     for x in myserver.get('attached_volumes'):
        #         if x.get('id'):
        #             dic['image_id'] = x.get('id')
        #             continue
        dic['image_name'] = myserver.get('image_name')
        myli.append(dic)
    # print(myli)
    # for i in myli:
    #     print(i)
    return myli


# # 获取所有宿主机列表
# hypervisors = conn.list_hypervisors()
# # 遍历宿主机列表并输出信息
# for hypervisor in hypervisors:
#     print(hypervisor)
#     print("\n")


def create_instance(vm_name, image_name, flavor_name, net_name, key_name='', volume_size=50, instance_type='instance'):
    # 创建vm
    # 查找镜像和 flavor、网络
    image = conn.compute.find_image(image_name)
    flavor = conn.compute.find_flavor(flavor_name)
    network = conn.network.find_network(net_name)

    block_device_mapping_v2 = [{
        'uuid': image.id,
        'source_type': 'image',
        'destination_type': 'volume',
        'boot_index': 0,
        'delete_on_termination': False,
        'volume_size': volume_size,
        # 'device_name': '/dev/vda',
    }]

    metadata = {
        'instance_type': instance_type,  # instance/gpu-instance/baremetal
    }

    create_kwargs = {
        'name': vm_name,
        'image_id': image.id,
        'flavor_id': flavor.id,
        'networks': [{"uuid": network.id}],  # 替换为您的网络UUID
        'block_device_mapping_v2': block_device_mapping_v2,
        'security_groups': [{'name': 'default'}],
        'metadata': metadata,
        'adminPass': "abcd@1234",
    }
    if key_name:
        create_kwargs['key_name'] = key_name

    # 创建虚拟机
    server = conn.compute.create_server(**create_kwargs)
    print(f"虚拟机 {vm_name} 开始创建，ID: {server.id}, 状态：{server.status}")
    # 等待虚拟机创建完成
    # conn.compute.wait_for_server(server)
    # # 等待服务器创建完成
    # while True:
    #     # 查询服务器状态
    #     server = conn.compute.get_server(server_id)
    #     status = server.status
    #     print(f"Current server status: {status}")
    #
    #     # 如果服务器状态变为活跃，则执行回调函数
    #     if status == 'ACTIVE':
    #         server_create_callback(server_id)
    #         break
    #
    #     # 等待一段时间再次检查状态
    #     time.sleep(5)
    # print(f"虚拟机 {vm_name} 创建完成，ID: {server.id}, 状态：{server.status}")
    # wait_server_created(server)
    return server.id


# 异步创建server
# def wait_server_created(server):
#     # 虚拟机创建完成后，执行回调函数
#     print(f'虚拟机 {server.name} 异步等待创建完成')
#     await conn.compute.wait_for_server(server)
#     print(f"虚拟机 {server.name} 创建完成，ID: {server.id}, 状态：{server.status}")



def change_password(vm_id, new_password):
    # 修改虚拟机的密码
    conn.compute.change_server_password(vm_id, new_password)
    print(f'虚拟机 {vm_id} 已创建并修改密码。')


# 关闭虚拟机
def stop_instance(instance_id):
    server = conn.compute.find_server(instance_id)
    conn.compute.stop_server(server)


# 启动虚拟机
def start_instance(instance_id):
    server = conn.compute.find_server(instance_id)
    conn.compute.start_server(server)


# 查看网络
def get_networks():
    # 列出所有网络
    networks = list(conn.network.networks())
    # 打印每个网络的详细信息
    for network in networks:
        print(f'ID: {network.id}, 名称: {network.name}, 子网: {network.subnet_ids}')


# 查看子网
def get_subnets():
    # 列出所有子网
    subnets = list(conn.network.subnets())
    # 打印每个子网的详细信息
    for subnet in subnets:
        # print(subnet)
        print(
            f'ID: {subnet.id}, 名称: {subnet.name}, 网络ID: {subnet.network_id}, 网段: {subnet.cidr}, GW: {subnet.gateway_ip}')


# 查看镜像
def get_images_dict():
    img_dict = {}
    for image in conn.image.images():
        print(image)
        image_type = "image"
        if image.properties.get('image_type'):
            image_type = image.properties['image_type']
        if image.properties.get('block_device_mapping'):
            image_type = json.loads(image.properties['block_device_mapping'])[0].get('source_type')

        tmp_dict = {
            'image_id': image.id,
            'name': image.name,
            'visibility': image.visibility,
            'size': image.size,
            'status': image.status,
            'min_ram': image.min_ram,
            'min_disk': image.min_disk,
            'image_type': image_type,
            'disk_format': image.disk_format,
            'protected': image.is_protected,
            'project_id': image.location.project.id,
            'project_name': image.location.project.name,
            'owner_id': image.owner,
            'owner_name': image.properties.get('owner_user_name'),
            'created_at': image.created_at,
            'updated_at': image.updated_at,
        }
        # print(f'ID: {tmp_dict}')
        img_dict[image.id] = tmp_dict
    return img_dict


# 查看flavor
def get_flavors_dict():
    dic = {}
    # 列出所有flavor
    flavors = list(conn.compute.flavors())
    # 打印每个flavor的详细信息
    for flavor in flavors:
        # print(flavor)
        tmp_flavor = {
            'flavor_id': flavor.id,
            'name': flavor.name,
            'ram': flavor.ram,
            'vcpus': flavor.vcpus,
            'disk': flavor.disk,
            'ephemeral': flavor.ephemeral,
            'swap': flavor.swap,
            'rxtx_factor': flavor.rxtx_factor,
            'is_public': flavor.is_public,
            'extra_specs': json.dumps(flavor.extra_specs),
            'project_id': flavor.location.project.id,
            'project_name': flavor.location.project.name
        }
        dic[flavor.id] = tmp_flavor
    return dic


def list_keypairs():
    # 如果不指定user_id，则只返回自己名下的keypair
    print("List Keypairs:")
    keypairs = conn.compute.keypairs(user_id='12df83337a5e4ce88ee186e36505247f')
    for keypair in keypairs:
        print(keypair)


def create_and_attach_volume(server_id):
    # 创建一个新的卷作为数据盘
    volume = conn.block_storage.create_volume(
        # name='my-data-volume',
        size=10,  # 卷大小，单位为GB
        # volume_type=''  # 选择合适的卷类型
    )

    # 等待卷创建完成
    volume = conn.block_storage.wait_for_status(volume, status='available', failures=['error'], interval=2, wait=120)

    # 附加卷到服务器
    attachment = conn.compute.create_volume_attachment(
        server_id,
        volume_id=volume.id
    )
    print(f"Attached Volume: {volume.name} (ID: {volume.id})")


# 列出所有安全组
def get_security_groups_dict():
    dic = {}
    # 列出所有security_groups
    security_groups = list(conn.network.security_groups())
    # 打印每个sg的详细信息
    for sg in security_groups:
        # print(sg)
        # print(f'ID: {sg.id}, 名称: {sg.name}, 描述: {sg.description}, 租户ID： {sg.tenant_id}, project_id:{sg.project_id}, '
        #       f'updated_at: {sg.updated_at}, ')
        tmp_sg = {
            'sg_id': sg.id,
            'name': sg.name,
            'description': sg.description,
            'security_group_rules': json.dumps(sg.security_group_rules),
            'tenant_id': sg.tenant_id,
            'project_id': sg.location.project.id,
            'project_name': sg.location.project.name,
            'is_shared': sg.shared,
            'created_at': sg.created_at,
            'updated_at': sg.updated_at,
        }
        dic[sg.id] = tmp_sg
    return dic


if __name__ == '__main__':
    # ins_id = '856b1ecb-ec6c-4f1b-8d12-6ec3e24d2dd6'
    # start_instance(ins_id)
    # create_instance(vm_name='sdk_vm_t1', image_name='centos-7.9', flavor_name='ecs.e-1c1m40g', net_name='vlan', key_name='xingzai1')
    # change_password("7271465f-78c2-4569-9ccd-c5474f7c654c", "abcd@1234")
    # get_networks()
    #
    # get_subnets()

    # print(get_images_dict())
    #
    # print(get_flavors_dict())
    # get_instances()
    # list_keypairs()
    # create_instance()
    # create_and_attach_volume('854532c1-5c5f-49ef-a2b2-7d0a10125982')
    print(get_security_groups_dict())

