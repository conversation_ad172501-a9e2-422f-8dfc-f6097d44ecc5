#!/usr/bin/env python
"""
G6数据格式测试脚本
"""

import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from operatorcmdb.models import VirtualSwitch
from operatorcmdb.services.g6_data_service import g6_data_service
from xresource.models import Machine<PERSON>oom


def test_switch_g6_data():
    """测试单个交换机的G6数据格式"""
    print("🔍 测试单个交换机G6数据格式...")
    
    # 查找第一个交换机
    switch = VirtualSwitch.objects.filter(is_deleted=False).first()
    if not switch:
        print("❌ 未找到可用的交换机")
        return
    
    print(f"  交换机: {switch.name} (ID: {switch.id})")
    
    try:
        g6_data = g6_data_service.get_switch_g6_data(switch.id)
        
        if 'error' in g6_data:
            print(f"❌ 获取G6数据失败: {g6_data['error']}")
            return
        
        print("✅ G6数据获取成功")
        
        # 验证数据结构
        required_fields = ['id', 'name', 'count', 'label', 'currency', 'rate', 'status', 'variableName', 'variableValue', 'variableUp', 'children']
        
        for field in required_fields:
            if field not in g6_data:
                print(f"❌ 缺少必需字段: {field}")
                return
        
        print("✅ 数据结构验证通过")
        
        # 显示数据摘要
        print(f"📊 数据摘要:")
        print(f"  ID: {g6_data['id']}")
        print(f"  名称: {g6_data['name']}")
        print(f"  计数: {g6_data['count']}")
        print(f"  标签: {g6_data['label']}")
        print(f"  状态: {g6_data['status']}")
        print(f"  子节点数量: {len(g6_data['children'])}")
        
        # 显示子节点信息
        for i, child in enumerate(g6_data['children']):
            print(f"    子节点{i+1}: {child['name']} ({len(child.get('children', []))} 个子项)")
        
        # 保存示例数据到文件
        with open('switch_g6_data_example.json', 'w', encoding='utf-8') as f:
            json.dump(g6_data, f, ensure_ascii=False, indent=2)
        print("📁 示例数据已保存到 switch_g6_data_example.json")
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()


def test_room_g6_data():
    """测试机房G6数据格式"""
    print("\n🏢 测试机房G6数据格式...")
    
    # 查找有交换机的机房
    room = MachineRoom.objects.filter(
        is_deleted=False,
        virtualswitch__isnull=False
    ).distinct().first()
    
    if not room:
        print("❌ 未找到有交换机的机房")
        return
    
    print(f"  机房: {room.name} (ID: {room.id})")
    
    try:
        g6_data = g6_data_service.get_room_switches_g6_data(room.id)
        
        if 'error' in g6_data:
            print(f"❌ 获取机房G6数据失败: {g6_data['error']}")
            return
        
        print("✅ 机房G6数据获取成功")
        
        # 显示数据摘要
        print(f"📊 机房数据摘要:")
        print(f"  ID: {g6_data['id']}")
        print(f"  名称: {g6_data['name']}")
        print(f"  交换机数量: {g6_data['count']}")
        print(f"  状态: {g6_data['status']}")
        print(f"  分组数量: {len(g6_data['children'])}")
        
        # 显示分组信息
        for i, group in enumerate(g6_data['children']):
            print(f"    分组{i+1}: {group['name']} ({group['count']} 个交换机)")
        
        # 保存示例数据到文件
        with open('room_g6_data_example.json', 'w', encoding='utf-8') as f:
            json.dump(g6_data, f, ensure_ascii=False, indent=2)
        print("📁 机房示例数据已保存到 room_g6_data_example.json")
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()


def validate_g6_format(data, level=0):
    """递归验证G6数据格式"""
    indent = "  " * level
    required_fields = ['id', 'name', 'count', 'label', 'currency', 'rate', 'status', 'variableName', 'variableValue', 'variableUp']
    
    # 检查必需字段
    for field in required_fields:
        if field not in data:
            print(f"{indent}❌ 缺少字段: {field}")
            return False
    
    # 检查字段类型
    if not isinstance(data['id'], str):
        print(f"{indent}❌ id 应为字符串类型")
        return False
    
    if not isinstance(data['name'], str):
        print(f"{indent}❌ name 应为字符串类型")
        return False
    
    if not isinstance(data['count'], int):
        print(f"{indent}❌ count 应为整数类型")
        return False
    
    if not isinstance(data['rate'], (int, float)):
        print(f"{indent}❌ rate 应为数字类型")
        return False
    
    if not isinstance(data['variableValue'], (int, float)):
        print(f"{indent}❌ variableValue 应为数字类型")
        return False
    
    if not isinstance(data['variableUp'], bool):
        print(f"{indent}❌ variableUp 应为布尔类型")
        return False
    
    # 检查状态值
    valid_statuses = ['G', 'B', 'R', 'DI']
    if data['status'] not in valid_statuses:
        print(f"{indent}❌ status 应为 {valid_statuses} 中的一个")
        return False
    
    # 检查rate范围
    if not (0 <= data['rate'] <= 1):
        print(f"{indent}❌ rate 应在 0-1 范围内")
        return False
    
    # 检查variableValue范围
    if not (0 <= data['variableValue'] <= 1):
        print(f"{indent}❌ variableValue 应在 0-1 范围内")
        return False
    
    print(f"{indent}✅ 节点 {data['name']} 格式验证通过")
    
    # 递归检查子节点
    if 'children' in data and data['children']:
        for child in data['children']:
            if not validate_g6_format(child, level + 1):
                return False
    
    return True


def test_g6_format_validation():
    """测试G6格式验证"""
    print("\n🔍 测试G6格式验证...")
    
    # 获取测试数据
    switch = VirtualSwitch.objects.filter(is_deleted=False).first()
    if not switch:
        print("❌ 未找到可用的交换机")
        return
    
    try:
        g6_data = g6_data_service.get_switch_g6_data(switch.id)
        
        if 'error' in g6_data:
            print(f"❌ 获取G6数据失败: {g6_data['error']}")
            return
        
        print("开始格式验证...")
        if validate_g6_format(g6_data):
            print("✅ 所有节点格式验证通过")
        else:
            print("❌ 格式验证失败")
        
    except Exception as e:
        print(f"❌ 验证异常: {str(e)}")


def show_api_usage():
    """显示API使用示例"""
    print("\n📚 API使用示例:")
    print("""
# 1. 获取单个交换机的G6数据
GET /api/virtual-switches/{switch_id}/get_g6_data/

# 2. 获取机房交换机的G6数据
GET /api/virtual-switches/get_room_g6_data/?machine_room_id={room_id}

# 3. 获取交换机接口信息（兼容接口）
GET /api/virtual-switches/interfaces/?switch_id={switch_id}

# 响应格式示例:
{
  "code": 2000,
  "msg": "获取G6数据成功",
  "data": {
    "id": "switch-123",
    "name": "Core-Switch-01",
    "count": 48,
    "label": "42/48",
    "currency": "Interfaces",
    "rate": 1,
    "status": "G",
    "variableName": "Uptime",
    "variableValue": 0.95,
    "variableUp": true,
    "children": [
      {
        "id": "physical-interfaces-1234",
        "name": "Physical Interfaces",
        "count": 48,
        "label": "42/48",
        "currency": "Ports",
        "rate": 0.875,
        "status": "G",
        "variableName": "Active",
        "variableValue": 0.875,
        "variableUp": true,
        "children": [...]
      }
    ]
  }
}
""")


def show_g6_integration_guide():
    """显示G6集成指南"""
    print("\n🎨 G6集成指南:")
    print("""
# 前端G6使用示例:

import G6 from '@antv/g6';

// 1. 获取数据
const response = await fetch('/api/virtual-switches/1/get_g6_data/');
const result = await response.json();
const data = result.data;

// 2. 创建G6图实例
const graph = new G6.TreeGraph({
  container: 'container',
  width: 800,
  height: 600,
  modes: {
    default: ['collapse-expand', 'drag-canvas', 'zoom-canvas']
  },
  defaultNode: {
    size: 26,
    anchorPoints: [
      [0, 0.5],
      [1, 0.5]
    ]
  },
  defaultEdge: {
    type: 'cubic-horizontal'
  },
  layout: {
    type: 'compactBox',
    direction: 'LR',
    getId: function getId(d) {
      return d.id;
    },
    getHeight: function getHeight() {
      return 16;
    },
    getWidth: function getWidth() {
      return 16;
    },
    getVGap: function getVGap() {
      return 10;
    },
    getHGap: function getHGap() {
      return 100;
    }
  }
});

// 3. 渲染数据
graph.data(data);
graph.render();

// 4. 自定义节点样式
graph.node(function(node) {
  return {
    label: node.name,
    labelCfg: {
      offset: 10,
      position: node.children && node.children.length > 0 ? 'left' : 'right'
    }
  };
});
""")


if __name__ == '__main__':
    print("🧪 G6数据格式测试")
    print("=" * 60)
    
    try:
        # 测试单个交换机G6数据
        test_switch_g6_data()
        
        # 测试机房G6数据
        test_room_g6_data()
        
        # 测试格式验证
        test_g6_format_validation()
        
        # 显示API使用示例
        show_api_usage()
        
        # 显示G6集成指南
        show_g6_integration_guide()
        
        print("\n✨ 测试完成!")
        print("您现在可以使用这些API接口获取G6格式的数据了。")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
