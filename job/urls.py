from django.urls import path
from rest_framework.routers import SimpleRouter

from .views.template import TemplateModelViewSet
from. views.job import JobModelViewSet
from. views.history import HistoryModelViewSet

router = SimpleRouter()
# 这里进行注册路径，并把视图关联上，这里的api地址以视图名称为后缀，这样方便记忆api/CrudDemoModelViewSet
router.register("api/job/TemplateModelViewSet", TemplateModelViewSet)
router.register("api/job/JobModelViewSet", JobModelViewSet)
router.register("api/job/HistoryModelViewSet", HistoryModelViewSet)

urlpatterns = [
]
urlpatterns += router.urls
