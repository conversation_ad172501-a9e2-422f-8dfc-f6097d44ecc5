from dvadmin.utils.pagination import CustomPagination
from dvadmin.utils.viewset import CustomModelViewSet
from job.models import Template
from job.serializers.template import TemplateModelSerializer, TemplateModelCreateUpdateSerializer


class StandardResultsSetPagination(CustomPagination):
    page_size = 1000


class TemplateModelViewSet(CustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Template.objects.all()
    serializer_class = TemplateModelSerializer
    create_serializer_class = TemplateModelCreateUpdateSerializer
    update_serializer_class = TemplateModelCreateUpdateSerializer
    # 未设置page和limit时默认的limit大小
    pagination_class = StandardResultsSetPagination
