from dvadmin.utils.viewset import CustomModelViewSet
from job.models import Job
from job.serializers.job import JobModelSerializer, JobModelCreateUpdateSerializer


class JobModelViewSet(CustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Job.objects.all()
    serializer_class = JobModelSerializer
    create_serializer_class = JobModelCreateUpdateSerializer
    update_serializer_class = JobModelCreateUpdateSerializer
