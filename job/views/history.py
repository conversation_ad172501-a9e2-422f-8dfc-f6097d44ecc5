from dvadmin.utils.viewset import CustomModelViewSet

from job.models import History
from job.serializers.history import HistoryModelSerializer, HistoryModelCreateUpdateSerializer


class HistoryModelViewSet(CustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = History.objects.all()
    serializer_class = HistoryModelSerializer
    create_serializer_class = HistoryModelCreateUpdateSerializer
    update_serializer_class = HistoryModelCreateUpdateSerializer

