from dvadmin.utils.serializers import CustomModelSerializer
from rest_framework import serializers
from job.models import Job


class JobModelSerializer(CustomModelSerializer):
    """
    序列化器
    """
    template_name = serializers.ReadOnlyField(source='template.name')
    template_type = serializers.ReadOnlyField(source='template.type')
    template_folder = serializers.ReadOnlyField(source='template.folder')
    template_yml_path = serializers.ReadOnlyField(source='template.yml_path')

    # 这里是进行了序列化模型及所有的字段
    class Meta:
        model = Job
        fields = "__all__"

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        # 添加choices中文显示
        ret['ip_field_display'] = instance.get_ip_field_display()
        return ret


# 这里是创建/更新时的列化器
class JobModelCreateUpdateSerializer(CustomModelSerializer):
    """
    创建/更新时的列化器
    """

    class Meta:
        model = Job
        fields = '__all__'
