from dvadmin.utils.serializers import CustomModelSerializer
from job.models import History


class HistoryModelSerializer(CustomModelSerializer):
    # 这里是进行了序列化模型及所有的字段
    class Meta:
        model = History
        fields = "__all__"


# 这里是创建/更新时的列化器
class HistoryModelCreateUpdateSerializer(CustomModelSerializer):
    """
    创建/更新时的列化器
    """

    class Meta:
        model = History
        fields = '__all__'
