from dvadmin.utils.serializers import CustomModelSerializer
from job.models import Template


class TemplateModelSerializer(CustomModelSerializer):
    """
    序列化器
    """

    # 这里是进行了序列化模型及所有的字段
    class Meta:
        model = Template
        fields = "__all__"

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        # 添加choices显示
        ret['template_type_display'] = instance.get_type_display()
        return ret


# 这里是创建/更新时的列化器
class TemplateModelCreateUpdateSerializer(CustomModelSerializer):
    """
    创建/更新时的列化器
    """

    class Meta:
        model = Template
        fields = '__all__'
