from django.db import models
from dvadmin.utils.models import CoreModel

TYPE_CHOICES = (
    ('M', 'module'),
    ('P', 'playbook'),
)

IP_TYPE_CHOICES = (
    ('ip_private', '内网IP'),
    ('ip_bmc', 'BMC IP'),
)


class Job(CoreModel):
    """
    job 作业
    """
    name = models.CharField(max_length=128, blank=True, null=True, unique=True, verbose_name="名称")
    template = models.ForeignKey('job.Template', null=True, on_delete=models.SET_NULL,verbose_name='作业模板')
    params = models.CharField(max_length=255, blank=True, null=True, verbose_name="参数")
    desc = models.CharField(max_length=255, blank=True, null=True, verbose_name="描述")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    execute_hosts = models.ManyToManyField('operatorcmdb.Host', db_table='job_exec_hosts', blank=True, verbose_name='关联执行主机')
    ip_field = models.CharField(max_length=64, choices=IP_TYPE_CHOICES, verbose_name="IP字段选取")

    class Meta:
        db_table = 'job_job'
        verbose_name = '作业'
        verbose_name_plural = verbose_name
        ordering = ['id']


class Template(CoreModel):
    """
    template 作业模板
    """
    name = models.CharField(max_length=64, blank=True, null=True, unique=True, verbose_name="名称")
    type = models.CharField(max_length=10, null=True, choices=TYPE_CHOICES, verbose_name="作业类型")
    module = models.CharField(max_length=64, blank=True, null=True, verbose_name="模块名")
    desc = models.CharField(max_length=255, blank=True, null=True, verbose_name="描述")
    folder = models.CharField(max_length=255, blank=True, null=True, verbose_name="playbook所在目录")
    yml_path = models.CharField(max_length=255, blank=True, null=True, verbose_name="yml文件在playbook目录中的相对路径")
    params = models.CharField(max_length=255, blank=True, null=True, verbose_name="参数")

    class Meta:
        db_table = 'job_template'
        verbose_name = '作业模板'
        verbose_name_plural = verbose_name
        ordering = ['id']


class History(CoreModel):
    """
    history 历史作业
    """
    execute_id = models.CharField(max_length=255, null=True, blank=True, verbose_name="本次执行ID")
    job_name = models.CharField(max_length=255, verbose_name="Job外部名称")
    job_code = models.CharField(max_length=255, verbose_name="Job内部代号")
    ip_field = models.CharField(max_length=64, choices=IP_TYPE_CHOICES, verbose_name="IP字段选取")
    type = models.CharField(max_length=10, choices=TYPE_CHOICES, verbose_name="作业类型")
    module = models.CharField(max_length=64, blank=True, null=True, verbose_name="模块名")
    folder = models.CharField(max_length=255, blank=True, null=True, verbose_name="playbook所在目录")
    yml_path = models.CharField(max_length=255, blank=True, null=True, verbose_name="yaml文件相对路径")
    params = models.CharField(max_length=255, blank=True, null=True, verbose_name="参数")
    detail = models.CharField(max_length=255, null=True, blank=True, verbose_name="结果详情")
    status = models.CharField(max_length=16, null=True, blank=True, verbose_name="状态")
    rc = models.IntegerField(blank=True, null=True, verbose_name="状态码")
    execute_host_ips = models.CharField(max_length=255, blank=True, null=True, verbose_name="执行主机ips")
    execute_host_ids = models.CharField(max_length=255, verbose_name="执行主机ids")
    execute_user = models.CharField(max_length=16, null=True, blank=True, verbose_name='操作人')
    start_time = models.DateTimeField(auto_now_add=True, null=True, blank=True)

    class Meta:
        db_table = 'job_history'
        verbose_name = '历史作业'
        verbose_name_plural = verbose_name
        ordering = ['-id']
