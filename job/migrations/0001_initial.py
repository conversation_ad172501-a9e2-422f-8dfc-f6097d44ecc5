# Generated by Django 5.0.6 on 2024-08-19 15:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('operatorcmdb', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='History',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('execute_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='本次执行ID')),
                ('job_name', models.CharField(max_length=255, verbose_name='Job外部名称')),
                ('job_code', models.CharField(max_length=255, verbose_name='Job内部代号')),
                ('ip_field', models.CharField(choices=[('ip_private', '内网IP'), ('ip_bmc', 'BMC IP')], max_length=64, verbose_name='IP字段选取')),
                ('type', models.CharField(choices=[('M', 'module'), ('P', 'playbook')], max_length=10, verbose_name='作业类型')),
                ('module', models.CharField(blank=True, max_length=64, null=True, verbose_name='模块名')),
                ('folder', models.CharField(blank=True, max_length=255, null=True, verbose_name='playbook所在目录')),
                ('yml_path', models.CharField(blank=True, max_length=255, null=True, verbose_name='yaml文件相对路径')),
                ('params', models.CharField(blank=True, max_length=255, null=True, verbose_name='参数')),
                ('detail', models.CharField(blank=True, max_length=255, null=True, verbose_name='结果详情')),
                ('status', models.CharField(blank=True, max_length=16, null=True, verbose_name='状态')),
                ('rc', models.IntegerField(blank=True, null=True, verbose_name='状态码')),
                ('execute_host_ips', models.CharField(blank=True, max_length=255, null=True, verbose_name='执行主机ips')),
                ('execute_host_ids', models.CharField(max_length=255, verbose_name='执行主机ids')),
                ('execute_user', models.CharField(blank=True, max_length=16, null=True, verbose_name='操作人')),
                ('start_time', models.DateTimeField(auto_now_add=True, null=True)),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '历史作业',
                'verbose_name_plural': '历史作业',
                'db_table': 'job_history',
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Template',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('name', models.CharField(blank=True, max_length=64, null=True, unique=True, verbose_name='名称')),
                ('type', models.CharField(choices=[('M', 'module'), ('P', 'playbook')], max_length=10, null=True, verbose_name='作业类型')),
                ('module', models.CharField(blank=True, max_length=64, null=True, verbose_name='模块名')),
                ('desc', models.CharField(blank=True, max_length=255, null=True, verbose_name='描述')),
                ('folder', models.CharField(blank=True, max_length=255, null=True, verbose_name='playbook所在目录')),
                ('yml_path', models.CharField(blank=True, max_length=255, null=True, verbose_name='yml文件在playbook目录中的相对路径')),
                ('params', models.CharField(blank=True, max_length=255, null=True, verbose_name='参数')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '作业模板',
                'verbose_name_plural': '作业模板',
                'db_table': 'job_template',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='Job',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('name', models.CharField(blank=True, max_length=128, null=True, unique=True, verbose_name='名称')),
                ('params', models.CharField(blank=True, max_length=255, null=True, verbose_name='参数')),
                ('desc', models.CharField(blank=True, max_length=255, null=True, verbose_name='描述')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('ip_field', models.CharField(choices=[('ip_private', '内网IP'), ('ip_bmc', 'BMC IP')], max_length=64, verbose_name='IP字段选取')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('execute_hosts', models.ManyToManyField(blank=True, db_table='job_exec_hosts', to='operatorcmdb.host', verbose_name='关联执行主机')),
                ('template', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='job.template', verbose_name='作业模板')),
            ],
            options={
                'verbose_name': '作业',
                'verbose_name_plural': '作业',
                'db_table': 'job_job',
                'ordering': ['id'],
            },
        ),
    ]
