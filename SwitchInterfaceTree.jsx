import React, { useState, useEffect } from 'react';
import {
  Tree,
  Card,
  Space,
  Tag,
  Typography,
  Spin,
  Alert,
  Descriptions,
  Drawer,
  Button,
  Input,
  Row,
  Col,
  Statistic,
  Progress
} from 'antd';
import {
  ClusterOutlined,
  ApiOutlined,
  ThunderboltOutlined,
  LinkOutlined,
  DisconnectOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  MacCommandOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Search } = Input;

const SwitchInterfaceTree = ({ switchId, onNodeSelect }) => {
  const [treeData, setTreeData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedNodeData, setSelectedNodeData] = useState(null);
  const [summary, setSummary] = useState({});

  // 图标映射
  const getIcon = (type, state) => {
    const iconProps = { style: { marginRight: 4 } };
    
    switch (type) {
      case 'switch':
        return <ClusterOutlined {...iconProps} style={{ ...iconProps.style, color: '#1890ff' }} />;
      case 'interface_group':
        return <ApiOutlined {...iconProps} style={{ ...iconProps.style, color: '#52c41a' }} />;
      case 'speed_group':
        return <ThunderboltOutlined {...iconProps} style={{ ...iconProps.style, color: '#fa8c16' }} />;
      case 'physical_interface':
        return state === 'up' 
          ? <ApiOutlined {...iconProps} style={{ ...iconProps.style, color: '#52c41a' }} />
          : <DisconnectOutlined {...iconProps} style={{ ...iconProps.style, color: '#ff4d4f' }} />;
      case 'trunk_interface':
        return state === 'up'
          ? <ClusterOutlined {...iconProps} style={{ ...iconProps.style, color: '#52c41a' }} />
          : <DisconnectOutlined {...iconProps} style={{ ...iconProps.style, color: '#ff4d4f' }} />;
      case 'member_interface':
        return <LinkOutlined {...iconProps} style={{ ...iconProps.style, color: '#722ed1' }} />;
      case 'mac_address':
        return <MacCommandOutlined {...iconProps} style={{ ...iconProps.style, color: '#13c2c2' }} />;
      default:
        return <ApiOutlined {...iconProps} />;
    }
  };

  // 获取状态标签
  const getStatusTag = (state) => {
    if (!state) return <Tag color="default">未知</Tag>;
    
    const colorMap = {
      'up': 'success',
      'down': 'error',
      'admin down': 'warning'
    };
    
    return <Tag color={colorMap[state] || 'default'}>{state}</Tag>;
  };

  // 加载数据
  const loadData = async () => {
    if (!switchId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/virtual-switches/${switchId}/get_interface_tree/`);
      const result = await response.json();
      
      if (result.code === 2000) {
        const { tree_data, summary } = result.data;
        setTreeData(tree_data);
        setSummary(summary);
        
        // 默认展开第一层
        if (tree_data.length > 0) {
          const firstLevelKeys = [tree_data[0].key];
          if (tree_data[0].children) {
            firstLevelKeys.push(...tree_data[0].children.map(child => child.key));
          }
          setExpandedKeys(firstLevelKeys);
        }
      } else {
        throw new Error(result.msg || '获取数据失败');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 搜索功能
  const onSearch = (value) => {
    setSearchValue(value);
    if (value) {
      // 查找匹配的节点并展开其父节点
      const expandedKeys = getExpandedKeysForSearch(treeData, value);
      setExpandedKeys(expandedKeys);
      setAutoExpandParent(true);
    }
  };

  // 获取搜索匹配的展开键
  const getExpandedKeysForSearch = (data, searchValue) => {
    const expandedKeys = [];
    
    const findMatchingNodes = (nodes, parentKey = null) => {
      nodes.forEach(node => {
        const title = node.title.toLowerCase();
        const search = searchValue.toLowerCase();
        
        if (title.includes(search)) {
          if (parentKey) {
            expandedKeys.push(parentKey);
          }
          expandedKeys.push(node.key);
        }
        
        if (node.children) {
          findMatchingNodes(node.children, node.key);
        }
      });
    };
    
    findMatchingNodes(data);
    return [...new Set(expandedKeys)];
  };

  // 渲染树节点标题
  const renderTreeTitle = (nodeData) => {
    const { title, type, data } = nodeData;
    const searchIndex = searchValue ? title.toLowerCase().indexOf(searchValue.toLowerCase()) : -1;
    
    let displayTitle = title;
    if (searchIndex > -1 && searchValue) {
      const beforeStr = title.substring(0, searchIndex);
      const matchStr = title.substring(searchIndex, searchIndex + searchValue.length);
      const afterStr = title.substring(searchIndex + searchValue.length);
      
      displayTitle = (
        <span>
          {beforeStr}
          <span style={{ backgroundColor: '#ffc069', padding: '0 2px' }}>
            {matchStr}
          </span>
          {afterStr}
        </span>
      );
    }
    
    return (
      <Space size="small">
        {getIcon(type, data?.state)}
        <span>{displayTitle}</span>
        {data?.state && <span>{getStatusTag(data.state)}</span>}
        {data?.mac_count > 0 && (
          <Tag size="small" color="cyan">
            {data.mac_count} MAC
          </Tag>
        )}
        {data?.member_count > 0 && (
          <Tag size="small" color="purple">
            {data.member_count} 成员
          </Tag>
        )}
      </Space>
    );
  };

  // 节点选择
  const onSelect = (selectedKeys, info) => {
    setSelectedKeys(selectedKeys);
    if (selectedKeys.length > 0) {
      const nodeData = info.node;
      setSelectedNodeData(nodeData);
      setDrawerVisible(true);
      
      if (onNodeSelect) {
        onNodeSelect(nodeData);
      }
    }
  };

  // 渲染节点详情
  const renderNodeDetails = () => {
    if (!selectedNodeData) return null;
    
    const { type, data, title } = selectedNodeData;
    
    return (
      <Descriptions column={1} size="small" bordered>
        <Descriptions.Item label="节点类型">
          <Tag color="blue">{getTypeLabel(type)}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="名称">{title}</Descriptions.Item>
        
        {data?.state && (
          <Descriptions.Item label="状态">
            {getStatusTag(data.state)}
          </Descriptions.Item>
        )}
        
        {data?.speed && (
          <Descriptions.Item label="速度">{data.speed} Mbps</Descriptions.Item>
        )}
        
        {data?.mac_address && (
          <Descriptions.Item label="MAC地址">
            <Text code>{data.mac_address}</Text>
          </Descriptions.Item>
        )}
        
        {data?.management_ip && (
          <Descriptions.Item label="管理IP">
            <Text code>{data.management_ip}</Text>
          </Descriptions.Item>
        )}
        
        {data?.vendor && (
          <Descriptions.Item label="厂商">{data.vendor}</Descriptions.Item>
        )}
        
        {data?.sn && (
          <Descriptions.Item label="序列号">{data.sn}</Descriptions.Item>
        )}
        
        {data?.line_state && (
          <Descriptions.Item label="协议状态">{data.line_state}</Descriptions.Item>
        )}
        
        {data?.members && data.members.length > 0 && (
          <Descriptions.Item label="成员接口">
            <Space wrap>
              {data.members.map((member, index) => (
                <Tag key={index} size="small">{member}</Tag>
              ))}
            </Space>
          </Descriptions.Item>
        )}
        
        {data?.parent_trunk && (
          <Descriptions.Item label="所属聚合接口">
            <Tag color="purple">{data.parent_trunk}</Tag>
          </Descriptions.Item>
        )}
        
        {data?.interface && (
          <Descriptions.Item label="关联接口">{data.interface}</Descriptions.Item>
        )}
        
        {data?.node && (
          <Descriptions.Item label="节点">{data.node}</Descriptions.Item>
        )}
        
        {data?.last_sync_at && (
          <Descriptions.Item label="最后同步时间">
            {new Date(data.last_sync_at).toLocaleString()}
          </Descriptions.Item>
        )}
        
        {/* 统计信息 */}
        {data?.total_count && (
          <Descriptions.Item label="总数">{data.total_count}</Descriptions.Item>
        )}
        
        {data?.active_count !== undefined && (
          <Descriptions.Item label="活跃数">{data.active_count}</Descriptions.Item>
        )}
        
        {data?.mac_count && (
          <Descriptions.Item label="MAC地址数">{data.mac_count}</Descriptions.Item>
        )}
      </Descriptions>
    );
  };

  // 获取类型标签
  const getTypeLabel = (type) => {
    const labels = {
      'switch': '交换机',
      'interface_group': '接口分组',
      'speed_group': '速度分组',
      'physical_interface': '物理接口',
      'trunk_interface': '聚合接口',
      'member_interface': '成员接口',
      'mac_address': 'MAC地址'
    };
    return labels[type] || type;
  };

  useEffect(() => {
    loadData();
  }, [switchId]);

  return (
    <div style={{ height: '100%' }}>
      <Row gutter={[16, 16]} style={{ height: '100%' }}>
        <Col span={16}>
          <Card
            title={
              <Space>
                <ClusterOutlined />
                交换机接口树
                {summary.switch_name && (
                  <Text type="secondary">- {summary.switch_name}</Text>
                )}
              </Space>
            }
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={loadData}
                loading={loading}
                size="small"
              >
                刷新
              </Button>
            }
            style={{ height: '100%' }}
            bodyStyle={{ height: 'calc(100% - 57px)', overflow: 'auto' }}
          >
            <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
              <Search
                placeholder="搜索接口名称..."
                onSearch={onSearch}
                onChange={(e) => onSearch(e.target.value)}
                style={{ width: '100%' }}
                allowClear
              />
            </Space>

            {error && (
              <Alert
                message="加载失败"
                description={error}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
                action={
                  <Button size="small" onClick={loadData}>
                    重试
                  </Button>
                }
              />
            )}

            <Spin spinning={loading}>
              <Tree
                treeData={treeData}
                onExpand={setExpandedKeys}
                expandedKeys={expandedKeys}
                autoExpandParent={autoExpandParent}
                onSelect={onSelect}
                selectedKeys={selectedKeys}
                titleRender={renderTreeTitle}
                showIcon={false}
                style={{ minHeight: 400 }}
              />
            </Spin>
          </Card>
        </Col>

        <Col span={8}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* 统计信息 */}
            <Card title="统计信息" size="small">
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="物理接口"
                    value={summary.physical_interfaces || 0}
                    suffix={`/ ${summary.active_physical || 0} 活跃`}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="聚合接口"
                    value={summary.trunk_interfaces || 0}
                    suffix={`/ ${summary.active_trunk || 0} 活跃`}
                  />
                </Col>
                <Col span={24} style={{ marginTop: 16 }}>
                  <Statistic
                    title="MAC地址"
                    value={summary.mac_addresses || 0}
                  />
                </Col>
              </Row>
              
              {summary.physical_interfaces > 0 && (
                <div style={{ marginTop: 16 }}>
                  <Text strong>物理接口活跃率</Text>
                  <Progress
                    percent={Math.round((summary.active_physical || 0) / summary.physical_interfaces * 100)}
                    size="small"
                    status={summary.active_physical / summary.physical_interfaces > 0.8 ? 'success' : 'normal'}
                  />
                </div>
              )}
            </Card>

            {/* 使用说明 */}
            <Card title="使用说明" size="small">
              <Space direction="vertical" size={4}>
                <Text style={{ fontSize: 12 }}>• 点击节点查看详细信息</Text>
                <Text style={{ fontSize: 12 }}>• 使用搜索框快速定位接口</Text>
                <Text style={{ fontSize: 12 }}>• 展开节点查看子项目</Text>
                <Text style={{ fontSize: 12 }}>• 绿色表示活跃，红色表示异常</Text>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>

      {/* 详情抽屉 */}
      <Drawer
        title={
          <Space>
            <InfoCircleOutlined />
            节点详情
          </Space>
        }
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={500}
      >
        {renderNodeDetails()}
      </Drawer>
    </div>
  );
};

export default SwitchInterfaceTree;
