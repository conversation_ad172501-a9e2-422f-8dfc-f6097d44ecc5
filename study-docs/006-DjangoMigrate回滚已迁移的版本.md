# DjangoMigrate回滚已迁移的版本
在 Django 中，如果你需要回滚一个或多个迁移，可以使用 `migrate` 命令结合应用名称和目标迁移文件名。这里有一些常见的场景和对应的命令：

### 回滚到上一个迁移

如果你想回滚到最后一次迁移之前的版本，你可以指定应用名称和 `one-before-last-migration` 的名称。但是，Django 提供了一个更简便的方法，即指定零迁移（`zero migration`），这将完全移除该应用的所有迁移，或者你可以指定具体的一个迁移文件名。

```bash
python manage.py migrate your_app_name one_migration_name_before_last
```

但实际上，更常用的是直接指定要回滚到的特定迁移文件名。例如，如果最近的迁移是 `0005_auto_20250108_1043.py`，而你想回滚到前一个迁移 `0004_auto_20250107_1042.py`，你应该运行：

```bash
python manage.py migrate your_app_name 0004_auto_20250107_1042
```

### 完全回滚所有迁移

如果你想完全回滚某个应用的所有迁移，你可以使用 `zero` 关键字：

```bash
python manage.py migrate your_app_name zero
```

这将会删除该应用中所有的数据库表（前提是这些表是由 Django 的迁移创建的），并使所有迁移标记为未应用。

### 回滚多个迁移

如果你想回滚多个迁移但不是全部，你可以指定一个早于当前最新迁移的目标迁移文件名：

```bash
python manage.py migrate your_app_name target_migration_name
```

### 查看当前应用的迁移状态

在进行任何回滚操作之前，最好先查看当前应用的迁移状态，以确定你想要回滚到哪个迁移。你可以使用 `showmigrations` 命令来列出所有应用的迁移，并且会标明哪些已经应用了：

```bash
python manage.py showmigrations
```

### 注意事项

- **数据丢失风险**：回滚迁移可能会导致数据丢失，特别是当你回滚涉及到删除字段或模型的迁移时。确保在执行回滚之前备份你的数据库。
- **依赖关系**：如果你的应用有依赖其他应用的迁移，那么你需要小心处理这些依赖关系，以免破坏数据库的一致性。
- **测试环境优先**：总是先在测试环境中尝试回滚操作，确认无误后再应用到生产环境。

### 回滚用例
```shell
# 复制时注意
python .\manage.py migrate tenant 0008_alter_tenantopenstackserver_ipaddr  --database=tenant

# 执行日志
(.venv) PS D:\星临办公\code-projects\projects\nova-django-admin\nova-admin> python .\manage.py migrate tenant 0008_alter_tenantopenstackserver_ipaddr  --database=tenant                 
******************************
application.settings.interval_admin
Operations to perform:
  Target specific migration: 0008_alter_tenantopenstackserver_ipaddr, from tenant
Running migrations:
  Rendering model states... DONE
  Unapplying tenant.0010_tenantaccountgroup_project_id_and_more... OK
  Unapplying tenant.0009_tenantaccount_project_id_and_more... OK
```
