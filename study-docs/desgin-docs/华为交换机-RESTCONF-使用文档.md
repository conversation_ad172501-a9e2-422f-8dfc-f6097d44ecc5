# 华为交换机-RESTCONF-使用文档
## 简介
> 华为交换机支持RESTCONF协议, 即常见的RESTful API协议，此法可以做到授予权限最小化，避免使用高权限的用户操作设备。
### 定义
RESTCONF是一种基于HTTP的协议，提供RESTful风格的编程接口，支持对网络设备的数据进行增、删、改、查操作。

### 目的
随着网络规模的增大、复杂性的增加，自动化运维的需求日益增加，NETCONF提供基于RPC机制的应用编程接口。但是NETCONF已无法满足网络发展中对设备编程接口提出的新要求，当前网络管理者希望设备能够提供支持Web应用访问和操作网络设备的标准化接口。

RESTCONF是在融合NETCONF和HTTP协议的基础上发展而来的。RESTCONF以HTTP协议的方法提供了NETCONF协议的核心功能，编程接口符合IT业界流行的RESTful风格，为用户提供高效开发Web化运维工具的能力。

关于RESTCONF协议与NETCONF协议基于YANG数据模型进行的比较，如表1所示。

### 表1 RESTCONF与NETCONF比较
|项目 | NETCONF+YANG                                           | RESTCONF+YANG                                                                              | 
|---|--------------------------------------------------------|--------------------------------------------------------------------------------------------|
|传输通道（协议）| NETCONF传输层首选推荐SSH（Secure Shell）协议，XML信息通过SSH协议承载。      | RESTCONF是基于HTTP协议访问设备资源。RESTCONF提供的编程接口符合IT业界流行的RESTful风格。                                 |
|报文格式 | 采用XML编码。                                               | 采用XML。                                                                                     |
|操作特点 | NETCONF的操作复杂，例如： 1. NETCONF支持增、删、改、查，支持多个配置数据库，也支持回滚等。 | RESTCONF的操作简单，例如：1. RESTCONF支持增、删、改、查操作，仅支持<running/>配置数据库。 2. RESTCONF操作方法无需两阶段提交，操作直接生效。 |
### 受益
- RESTCONF提供RESTful风格的编程接口，支持Web开发。
- 标准化的接口，兼容多家厂商的设备，可以降低开发和维护成本。
- 扩展性好，不同制造商设备可以定义自己的协议操作。
- 无需网管工具。

## 背景
- Chaos 系统需要对接 华为交换机 的公网策略管理接口。
- 需要用到 NAT Server 的增删改查接口。

## 参考链接
- [配置通过RESTCONF管理设备](https://support.huawei.com/hedex/hdx.do?docid=EDOC1100386427&id=ZH-CN_TASK_0000001563771133)
- [NAT Server 策略管理API](https://support.huawei.com/hedex/hdx.do?docid=EDOC1100386427&id=ZH-CN_TOPIC_0000001563751625)
- [RESTCONF 报文格式](https://support.huawei.com/hedex/hdx.do?docid=EDOC1100386427&id=ZH-CN_CONCEPT_0000001563771113)
- [RESTCONF 错误码定义](https://support.huawei.com/hedex/hdx.do?docid=EDOC1100386427&id=ZH-CN_CONCEPT_0000001512691626)

## 认证方式
> 目前仅支持 `HTTP Basic Auth` 认证方式
- HTTP Basic Auth

## 使用接口说明

### 查询所有 NAT Server 策略
> GET /restconf/data/huawei-nat-server:nat-server
### 查询 NAT Server 策略
#### 命令行
> 查询名为test的NAT Server配置
```shell
display nat server name test
```

#### RESTCONF YANG API
|功能 |XPATH|
|---|---|
|查询NAT Server配置|hw-nat-server:nat-server/hw-nat-server:server-mappings/hw-nat-server:server-mapping|

#### 数据需求
|元素 |描述 |值|
|---|---|---|
|hw-nat-server:name|NAT Server名称|test|

#### 操作示例

```shell
# URL格式
GET /restconf/data/huawei-nat-server:nat-server/server-mappings/server-mapping=test
```

#### 响应示例
```shell
<?xml version="1.0" encoding="UTF-8"?>
<server-mapping
    xmlns="urn:huawei:yang:huawei-nat-server">
    <name>test</name>
    <global-vpn-name>vpn1</global-vpn-name>
    <global-zone>trust</global-zone>
    <protocol>tcp</protocol>
    <reverse-enable>true</reverse-enable>
    <inside-vpn-name>vpn2</inside-vpn-name>
    <route-enable>true</route-enable>
    <global>
        <start-ip>*******</start-ip>
    </global>
    <global-port>
        <start-port>3000</start-port>
        <end-port>4000</end-port>
    </global-port>
    <inside>
        <start-ip>***********</start-ip>
    </inside>
    <inside-port>
        <start-port>5000</start-port>
        <end-port>6000</end-port>
    </inside-port>
</server-mapping>
```

### 创建 NAT Server 策略
- [HiSecEngine USG6000F 产品文档创建](https://support.huawei.com/hedex/hdx.do?docid=EDOC1100386427&id=ZH-CN_TOPIC_0000001563751569)
### 修改 NAT Server 策略
- [HiSecEngine USG6000F 产品文档修改](https://support.huawei.com/hedex/hdx.do?docid=EDOC1100386427&id=ZH-CN_TOPIC_0000001563871261)
### 删除 NAT Server 策略
- [HiSecEngine USG6000F 产品文档删除](https://support.huawei.com/hedex/hdx.do?docid=EDOC1100386427&id=ZH-CN_TOPIC_0000001563871317)

## 脚本示例 `hisec_engine_client.py`
```python
import requests
import xmltodict

from requests.auth import HTTPBasicAuth

from application.logger import logger


class HisecEngineClient(object):
    def __init__(
            self,
            host=None,
            port=None,
            username=None,
            password=None,
            protocol='http'
    ):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.protocol = protocol
        self.base_path = '/restconf/data'
        self.base_rest_url = f'{self.protocol}://{self.host}:{self.port}/{self.base_path}'
        self.headers = {
            "Cache-Control": "no-cache,no-store",
            "Connection": "Keep-Alive",
            "Accept": "application/yang-data+xml",
            "Content-Type": "application/yang-data+xml",
        }
        self.auth = None
        self.product_code_path = {
            'nat_server': 'huawei-nat-server:nat-server'
        }
        self.init_client()

    def init_client(self, env='development'):
        if env == 'development':
            self.host = '*********'
            self.port = 10008
            self.username = 'chaos_nat'
            self.password = '1111111111111'
            self.auth = HTTPBasicAuth(self.username, self.password)
            self.base_rest_url = f'{self.protocol}://{self.host}:{self.port}{self.base_path}'
        else:
            self.auth = HTTPBasicAuth(self.username, self.password)

    @staticmethod
    def get_nat_server_payload_data(project_name=None, public_ip=None, public_port=None, inside_ip=None,
                                    inside_port=None):
        if not public_ip or not inside_ip:
            raise Exception('public_ip and inside_ip is needed')
        if not public_port:
            payload_data = {
                'server-mapping': {
                    'name': f'chaos_{project_name}_{inside_ip}',
                    'route-enable': True,
                    'inside-vpn-name': 'public',
                    'global': {
                        'start-ip': public_ip
                    },
                    'inside': {
                        'start-ip': inside_ip
                    }
                }
            }
        else:
            if not inside_port:
                raise Exception('inside_port needed')
            payload_data = {
                'server-mapping': {
                    'name': f'chaos_{project_name}_{inside_ip}_public{public_port}_interval{inside_port}',
                    'route-enable': True,
                    'inside-vpn-name': 'public',
                    'protocol': 'tcp',
                    'global': {
                        'start-ip': public_ip
                    },
                    'global-port': {
                        'start-port': public_port
                    },
                    'inside': {
                        'start-ip': inside_ip
                    },
                    'inside-port': {
                        'start-port': inside_port
                    }
                }
            }
        logger.info(f'<nat_server_payload_data>: {payload_data}')
        return payload_data

    def get_all_nat_servers(self):
        url = f'{self.base_rest_url}/{self.product_code_path["nat_server"]}'
        logger.info(f'<get_all_nat_servers><{url}>')
        response = requests.get(
            url, headers=self.headers,
            auth=self.auth
        )
        logger.info(f'<get_all_nat_servers><{url}>: {response.status_code}')
        dict_response = xmltodict.parse(response.text)
        logger.info(f'<get_all_nat_servers_response>: {dict_response}')
        return response.status_code, dict_response

    def create_nat_server(self, project_name, public_ip, inside_ip, public_port=None, inside_port=None):
        url = f'{self.base_rest_url}/{self.product_code_path["nat_server"]}/server-mappings'
        payload_data = self.get_nat_server_payload_data(
            project_name=project_name,
            public_ip=public_ip,
            inside_ip=inside_ip,
            public_port=public_port,
            inside_port=inside_port
        )
        data = xmltodict.unparse(payload_data)
        response = requests.post(
            url, headers=self.headers, data=data,
            auth=self.auth,
        )
        if int(response.status_code) != 201:
            logger.error(f'<create_nat_server><{url}>: {response.text}')
            re_data = xmltodict.parse(response.text)
            return 400, re_data
        else:
            return 200, {}

    def edit_nat_server(self, source_nat_server_name, project_name, public_ip, inside_ip, public_port=None, inside_port=None):
        if not source_nat_server_name:
            return 400, {}
        payload_data = self.get_nat_server_payload_data(
            project_name=project_name,
            public_ip=public_ip,
            public_port=public_port,
            inside_ip=inside_ip,
            inside_port=inside_port
        )
        logger.info(f'<edit_nat_server_payload_data>: {payload_data}')
        # 【Important】修改 nat server 策略时，需要保证 新name 和 原始name 一致
        payload_data['server-mapping']['name'] = source_nat_server_name
        url = f'{self.base_rest_url}/{self.product_code_path["nat_server"]}/server-mappings/server-mapping={source_nat_server_name}'
        logger.info(f'<edit_nat_server_url><{url}>')
        data = xmltodict.unparse(payload_data)
        response = requests.put(
            url, headers=self.headers, data=data,
            auth=self.auth,
        )
        logger.info(f'<edit_nat_server><{url}>: {response.status_code}')
        if int(response.status_code) != 204:
            logger.error(f'<edit_nat_server><{url}>: {response.text}')
            re_data = xmltodict.parse(response.text)
            return 400, re_data
        else:
            return 200, {}

    def get_nat_server(self, name):
        url = f'{self.base_rest_url}/{self.product_code_path["nat_server"]}/server-mappings/server-mapping={name}'
        logger.info(f'<get_nat_server><{url}>')
        response = requests.get(
            url, headers=self.headers,
            auth=self.auth
        )
        logger.info(f'<get_nat_server><{url}>: {response.status_code}')
        dict_response = xmltodict.parse(response.text)
        return response.status_code, dict_response

    def delete_nat_server(self, name):
        if not name:
            return 400, {}
        url = f'{self.base_rest_url}/{self.product_code_path["nat_server"]}/server-mappings/server-mapping={name}'
        logger.info(f'<delete_nat_server><{url}>')
        response = requests.delete(
            url, headers=self.headers,
            auth=self.auth
        )
        logger.info(f'<delete_nat_server><{url}>: {response.status_code}')
        if int(response.status_code) != 204:
            logger.error(f'<delete_nat_server><{url}>: {response.text}')
            re_data = xmltodict.parse(response.text)
            return 400, re_data
        else:
            return 200, {}


if __name__ == '__main__':
    import os
    import django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
    django.setup()
    admin_client = HisecEngineClient(node="金华")
    # res = admin_client.get_all_nat_servers()
    # print(res)
    # res = admin_client.get_nat_server('caoxiangpeng-testing_***********')
    # print(res)
    # res = admin_client.create_nat_server(
    #     project_name='caoxiangpeng-testing',
    #     public_ip='***************',
    #     inside_ip='***********',
    #     public_port=8000,
    #     inside_port=8000
    # )
    # print(res)
    # res = admin_client.edit_nat_server(
    #     source_nat_server_name='chaos_caoxiangpeng-testing_***********',
    #     project_name='caoxiangpeng-testing',
    #     public_ip='***************',
    #     inside_ip='***********',
    #     public_port=8001,
    #     inside_port=8002
    # )
    # print(res)
    # res = admin_client.delete_nat_server('chaos_caoxiangpeng-testing_***********11111111111111111111111111')
    # print(res)

```

## 枚举字段
### 协议`tcp`
- [参考链接](https://support.huawei.com/hedex/hdx.do?docid=EDOC1100386427&id=ahuawei-nat-server_leaf_660272758594fd73922383042d5b1f42)


| Key   | Value |
|-------|-------|
| tcp   | TCP   |
| udp   | UDP   |
| icmp  | ICMP  |
| ipv6  | IPv6  |
| gre   | GRE   |
| esp   | ESP   |
| ah    | AH    |
| sctp  | SCTP  |