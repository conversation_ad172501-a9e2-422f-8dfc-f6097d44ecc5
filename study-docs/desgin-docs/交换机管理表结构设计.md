# 虚拟交换机系统数据结构表

## 1. 虚拟交换机表 (virtual_switch)

| 字段名                 | 类型        | 必填 | 描述         | 网络部门确认                   | 备注                                 |
|---------------------|-----------|----|------------|--------------------------|------------------------------------|
| physical_switch     | 外键        | 否  | 关联的物理交换机ID | √ 可获取<br>☐ 需开发<br>☐ 无法获取 | 关联NetworkHardware（资产管理->网络设备管理）表   |
| name                | Char(255) | 否  | 交换机名称      | √ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| sn                  | Char(255) | 否  | 序列号        | √ 可获取<br>☐ 需开发<br>☐ 无法获取 | 可从NetworkHardware（资产管理->网络设备管理）表获取 |
| management_ip       | Char(255) | 否  | 管理IP       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| firmware_version    | Char(255) | 否  | 固件版本       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| last_update_time    | DateTime  | 否  | 最后更新时间     | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| vlan_range          | Char(255) | 否  | VLAN范围     | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 格式示例: "1-100,200-300"              |
| management_protocol | Char(255) | 否  | 管理协议       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 如SNMP/SSH/Telnet等                  |
| mtu                 | Integer   | 否  | MTU值       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| config_is_backup    | Boolean   | 否  | 配置是否备份     | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 默认False                            |
| monitoring_tool     | Char(255) | 否  | 监控工具       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| high_availability   | Char(255) | 否  | 高可用配置      | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| security_policy     | Char(255) | 否  | 安全策略       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |

## 2. 虚拟交换机物理接口表 (vs_physical_interface)

| 字段名            | 类型        | 必填 | 描述         | 网络部门确认                   | 备注                    |
|----------------|-----------|----|------------|--------------------------|-----------------------|
| virtual_switch | 外键        | 否  | 关联的虚拟交换机   | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 关联VirtualSwitch表      |
| name           | Char(255) | 否  | 接口名称       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 如"GigabitEthernet0/1" |
| interface_type | Char(63)  | 否  | 接口类型       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 枚举: ETH/SFP/VLAN/LAG  |
| speed          | Integer   | 否  | 接口速度(Mbps) | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 如1000/10000等          |
| status         | Char(255) | 否  | 接口状态       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 如up/down等             |
| mtu            | Integer   | 否  | 接口MTU值     | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                       |

## 3. 虚拟交换机虚拟接口表 (vs_virtual_interface)

| 字段名                | 类型       | 必填 | 描述       | 网络部门确认                   | 备注                   |
|--------------------|----------|----|----------|--------------------------|----------------------|
| virtual_switch     | 外键       | 否  | 关联的虚拟交换机 | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 关联VirtualSwitch表     |
| physical_interface | 外键       | 否  | 关联的物理接口  | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 关联PhysicalInterface表 |
| vlan_id            | Integer  | 是  | VLAN ID  | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 必填字段                 |
| is_trunk           | Boolean  | 否  | 是否Trunk口 | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 默认False              |
| mac_address1       | Char(32) | 否  | 主MAC地址   | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                      |
| mac_address2       | Char(32) | 否  | 备MAC地址   | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                      |

