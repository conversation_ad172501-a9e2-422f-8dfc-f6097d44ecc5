# git 常用操作
## 拉取dev分支，并合并到个人分支内
1. **确保你在个人分支上**：
   首先，切换到你的个人分支。

   ```sh
   git checkout feature/my-feature
   ```

2. **从远程仓库获取最新的更改**：
   获取所有远程分支的最新状态，但不自动合并或切换分支。

   ```sh
   git fetch origin
   ```

3. **合并 `dev` 分支到你的个人分支**：
   将 `origin/dev` 分支的内容合并到当前分支（即你的个人分支）。

   ```sh
   git merge origin/dev
   ```

   或者，如果你想要直接从远程 `dev` 分支合并：

   ```sh
   git merge dev
   ```

4. **解决可能的冲突**：
   如果在合并过程中出现冲突，Git 会提示你哪些文件存在冲突。你需要手动编辑这些文件来解决冲突。

   - 打开冲突文件，找到类似以下内容的部分：
     ```
     <<<<<<< HEAD
     // 当前分支的内容
     =======
     // dev 分支的内容
     >>>>>>> dev
     ```

   - 根据需要编辑文件以解决冲突，然后删除冲突标记。

5. **提交合并结果**：
   解决所有冲突后，添加修改的文件并提交合并。

   ```sh
   git add .
   git commit -m "Merge dev into feature/my-feature"
   ```

6. **推送更改到远程仓库**：
   最后，将合并后的更改推送到远程仓库中的个人分支。

   ```sh
   git push origin feature/my-feature
   ```

### 示例脚本

以下是一个完整的示例脚本，展示了如何执行上述步骤：

```sh
# 切换到个人分支
git checkout feature/my-feature

# 从远程仓库获取最新的更改
git fetch origin

# 合并 dev 分支到当前分支
git merge origin/dev

# 如果有冲突，解决冲突后继续
# 编辑冲突文件并保存
# 添加已解决冲突的文件
git add .

# 提交合并
git commit -m "Merge dev into feature/my-feature"

# 推送更改到远程仓库
git push origin feature/my-feature
```
请注意，在 CI/CD 环境中处理合并冲突可能会比较复杂，通常建议手动解决冲突，或者在 CI/CD 中仅用于自动化非冲突情况下的合并。如果有冲突，最好通知开发人员手动解决。

