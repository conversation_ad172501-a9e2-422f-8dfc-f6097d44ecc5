# OpenTelemetry 配置适配`Chaos`文档
> 因为本项目采用 ASGI启动项目，无法采用简单的配置适配。
## 项目安装依赖文件
```shell
opentelemetry-api==1.28.2
opentelemetry-sdk==1.28.2
opentelemetry-instrumentation-django==0.49b2
opentelemetry-instrumentation-asgi==0.49b2
opentelemetry-instrumentation-requests==0.49b2
opentelemetry-exporter-otlp==1.28.2
```
## 问题排查总结
1. k8s在加载 `opentelemetry` 时，由于启动的`PYTHONPATH`路径为`declare -x PYTHONPATH="/otel-auto-instrumentation-python/opentelemetry/instrumentation/auto_instrumentation:/otel-auto-instrumentati
on-python"`,此项目的`PYTHONPATH`为`/code`。项目在启动时`依赖冲突`及`环境冲突(PYTHONPATH)`。
2. 本项目采用 `uvicorn`命令启动，`多进程`+`异步`+`websocket`启动,而非单纯的`gunicorn`启动（gunicorn暂未适配，尚未尝试）。
3. `k8s`生产环境中无`OpenTelemetry`无法启动如何解决？在配置文件中加载是否启动`OpenTelemetry`标识。


## 项目中配置文件修改
> 默认配置文件：settings
>
> Chaos 系统配置文件：application.settings.base  # 此为基础配置文件


```shell
# 引入 Django 的 OTLPSpanExporter
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.instrumentation.django import DjangoInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter


# ############################### open telemetry 配置 ##################################
if IS_OPEN_OTLP:
    # 初始化 TracerProvider
    trace.set_tracer_provider(TracerProvider())

    # 设置 OTLP 导出器
    otlp_exporter = OTLPSpanExporter(
        endpoint=os.environ.get('OTEL_EXPORTER_OTLP_ENDPOINT1', 'http://opentelemetry-collector.monitoring:4318/v1/traces'),  # 替换为你的 OpenTelemetry Collector 地址
        headers={
            "application_name": "nova-admin-backend",
        }
    )

    # 添加 BatchSpanProcessor
    span_processor = BatchSpanProcessor(otlp_exporter)
    trace.get_tracer_provider().add_span_processor(span_processor)

    # 启用 Django 仪器化
    DjangoInstrumentor().instrument()
    # 启用 requests 仪器化
    RequestsInstrumentor().instrument(service_name='nova-admin-backend-requests')

# ############################### open telemetry 配置结束 ##################################
```

## 启动配置文件适配
> django中一般采用 asgi.py 或 wsgi.py 或 gunicorn.conf，作为启动配置文件，根据需求自定义配置。
> 
> Chaos系统中采用的为unicorn+asgi的启动方式，故配置application.asgi 下的文件。
```python
"""
ASGI config for application project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/howto/deployment/asgi/
"""

import os
from channels.auth import AuthMiddlewareStack
from channels.security.websocket import AllowedHostsOriginValidator
from channels.routing import ProtocolTypeRouter, URLRouter
from django.core.asgi import get_asgi_application
from opentelemetry.instrumentation.asgi import OpenTelemetryMiddleware

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
# 配置 不安全的异步任务，因为其他应用程序要`付着`在主进程中，开启已知的可以忽略。
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

http_application = get_asgi_application()

from application.routing import websocket_urlpatterns

application = ProtocolTypeRouter({
    "http": http_application,
    'websocket': AllowedHostsOriginValidator(
        AuthMiddlewareStack(
            URLRouter(
                websocket_urlpatterns  # 指明路由文件是devops/routing.py
            )
        )
    ),
})

application = OpenTelemetryMiddleware(application)

```

## `Charts` 配置
> 启动`Charts`中的 `OpenTelemetry`,在k8s中才会额外加载 `OpenTelemetry`。


```yaml
# 记得修改后重新发布呦
annotations:
    instrumentation.opentelemetry.io/inject-python: 'true'
```




