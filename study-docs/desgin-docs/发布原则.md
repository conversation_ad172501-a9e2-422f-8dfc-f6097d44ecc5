### 数据库相关 
- 设计表结构修改 更新数据库之前一定要进行备份 备份规范：本地一份、服务器一份
- 更新数据库分为两种方式：
```python
1. sql更新：通过sql方式更新(需要进行详细的备注说明 可参考manual_migrate_database_sql/mysql) 
2. migrate: 不要整体迁移；单模块迁移(如本次数据库表结构更改只涉及scheduletask模块 那么只执行 ... migrate scheduletask)
```

### 环境相关
- 如环境中新增python包，需要写入`requirements.txt`中
- 新增业务需要考虑在容器内运行环境的情况，如新增业务使用到了`ansible-runner`，那么需要考虑基础镜像是否要更新一些工具：如 openssh sshpass(基础镜像位于：django-admin-backend/release/base-dockerfile/dockerfile) 

### 测试
- 核心功能测试：如订单业务
- 新功能测试
- 新功能影响业务测试：如新功能调用了其他业务相关的功能或者对其他业务有修改操作，则其他业务也要进行功能测试