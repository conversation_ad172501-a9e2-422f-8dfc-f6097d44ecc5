# 安全开发及注意事项
## 一、写在最前
- **发布前做好备份**。
- **发布前做好备份**。
- **发布前做好备份**。

1. 高危操作(更新、删除)，头脑清醒，谨慎操作，做好记录。
2. 代码禁止上传到非内部网络存储平台（github、gitee等）。
3. 密码禁止使用简单密码。
4. 关联业务做好备注及高危提醒。
5. 生产发布代码前一定做好审阅与检查，`未经过同意前禁止私自点击发布到生产环境`。


## 二、安全开发生命周期（SDL）
- 需求分析阶段
  - 明确软件安全要求，包括数据保护、用户认证与授权等方面。
  - 进行威胁建模，识别潜在的安全风险。
- 设计阶段
  - 设计系统架构时考虑安全性，如使用加密技术保护敏感数据。
  - 确保第三方库和组件的安全性，定期更新以修复已知漏洞。
- 编码阶段
  - 遵循安全编码准则，避免常见漏洞如SQL注入、XSS攻击等。
  - 使用静态代码分析工具检测潜在的安全问题。
- 测试阶段
  - 实施安全测试，包括但不限于单元测试、集成测试、渗透测试等。
  - 对发现的安全漏洞及时修复，并验证修复效果。
- 部署阶段
  - 确保生产环境配置的安全性，比如防火墙设置、日志记录等。
  - 迁移数据前做好`**备份**`、`**备份**`、`**备份**`！！！
  - 手动更新数据做好记录。
  - 制定应急响应计划，以便快速应对安全事件。
- 维护阶段
  - 持续监控系统运行状态，及时发现并处理异常情况。
  - 定期评估软件安全性，根据最新安全标准更新防护措施。
## 三、安全开发注意事项
- 输入验证：所有外部输入都应被视为不可信，需严格校验，防止注入攻击。
- 最小权限原则：应用程序及其组件只赋予完成任务所需的最小权限。
- 错误处理：合理处理程序错误，避免泄露过多信息给攻击者。
- 密码管理：采用强密码策略，存储密码时使用加盐哈希算法。
- 会话管理：妥善管理用户会话，使用HTTPS保证传输安全。
- 依赖管理：保持依赖库版本更新，关注官方发布的安全公告。

## 其他注意事项
### 1. 应用规范

#### 1.1 应用命名规范

- 一般前后端分离架构，示例：

| 功能 | 命名                |
| ---- | ------------------- |
| 前端 | application1-frontend |
| 后端 | application1-backend  |

- 微服务架构下，按实际业务模块命名，建议采用项目名-模块的方式，格式示例如下：

| 功能     | 命名           |
| -------- | -------------- |
| 前端     | application-frontend |
| 工具模块 | application-tools    |
| 权限模块 | application-admin    |
| 网关     | application-gateway  |

#### 1.2 业务用中间件规范

- 业务系统Redis按环境（测试、预发、生产等）划分实例，各项目模块使用时一定不要复用DB号，以免冲突。
- 线上环境中间件连接地址均使用域名连接

### 2. 数据库规范

#### 2.1 账户密码安全【强制】

- 密码复杂度不少于8位，包含数字、字母、字符
- 密码不允许跟用户名一致
- 删除数据库默认空用户账户和初始test数据库

#### 2.2  账户安全管理【强制】

- 线上环境禁止直接使用root用户连接业务数据库
- 数据库名称和账号名称的分隔符使用中划线
- 为应用数据库创建独立的业务账号，管理权限和只读权限分离
- 若使用RDS，则需要写清楚账号和数据库的用途

#### 2.3 连接池使用规范

- 建议在性能测试阶段进行连接池最优设置
