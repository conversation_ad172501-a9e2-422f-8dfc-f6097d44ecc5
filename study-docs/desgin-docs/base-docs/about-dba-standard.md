# 关于 DBA 深切的建议
### 数据库设计原则
> 总是在灾难发生后，才想起容灾的重要性；
总是在吃过亏以后，才记得曾有人提醒过。备份！备份！备份，优化！优化！优化。
>
>每种数据库有其自身优势，譬如：`NoSQL` 更适合做缓存大型文本类数据，`sql` 更适合处理数据之间的关系，尽量不要让其做它做它不擅长的事情。
#### 核心原则
1. 尽量不在数据库做运算
   - 尽量不在数据库做运算
   - 复杂运算移到程序端 CPU
   - 尽可能简单应用 MySQL
2. 尽量控制单表数据量
    - 纯 INT 不超过 1000W
    - 含 CHAR 不超过 500W
    - 单列数据不宜存放太大的数据，能提出来尽量单独提出来
    - 能分区则分区
3. 尽量控制表字段数量
    - 顺序读 1G 文件需N秒
    - 单行不超过 200Byte
    - 单表不超 50 个纯 INT 字段
    - 单表不超 20 个 CHAR(10) 字段
    - <font color=#FF0000>单表字段数上限控制在 20~50 个</font>
4. 平衡范式与冗余
    - 单个字段不可再分，唯一性
    - 不存在非主属性只依赖部分主键，消除不完全依赖
    - 消除传递依赖
5. 拒绝 3B
> 数据库的并发就像城市交通，呈非线性增长
    
    - 大SQL（BIG SQL）
    - 大事务（BIG Transaction）
    - 大批量（BIG Batch）

#### 字段类原则
1. 用好数值字段类型
- 整型：TINYINT(1Byte)、TINYINT(1Byte)、SMALLINT(2B)、MEDIUMINT(3B)、INT(4B)、BIGINT(8B)
- 浮点型：FLOAT(4B)、DOUBLE(8B)
- DECIMAL(M,D)
    - `int(1)`和`int(11)` 没啥区别，只是显示的位数不同而已。
    - `BIGINT AUTO_INCREMENT` 自增主键使用int unsigned类型，但不建议使用bigint。
    - `DECIMAL(N, 0)` 当采用DECIMAL数据类型的时候，一般小数位数不会是 0，如果小数位数设置为 0，使用 INT 类型。
2. 将字符转化为数字
> 数字查询优于字符串
- `INT UNSIGNED` 代替 `char(15)` INET_ATON()和INET_NTOA() 实现数据转换
3. 优先使用 ENUM 或 SET
- ENUM 占用1字节，转为数值运算
- SET 视节点定，最多占用8字节
- 比较时需要加‘单引号（即使是数值）
4. 避免使用 NULL 字段
    - 字符串尽量都加上 `NOT NULL DEFAULT ''`
    - 数字尽量加上 `NOT NULL DEFAULT 0`
5. 少用并拆分 TEXT/BLOB
- TEXT 类型处理性能远低于VARCHAR
    - 弊端
        - 强制生成硬盘临时表
        - 浪费更多空间
        - VARCHAR(65535)==>64K(注意UTF-8)
6. 不在数据库里存图片
> 数据太大，查询太慢，数据库做好存储路径即可。
#### 索引类原则
1. 谨慎合理添加索引
2. 字符字段必须建前缀索引
3.不在索引列做运算
4.自增列或全局ID做INNODB主键
5.尽量不用外键
    - 外键可节省开发量
    - 有额外开销
    - 逐行操作
    - 可“到达”其他表，意味着锁
    - 高并发时容易死锁
#### SQL类原则
1. SQL语句尽可能简单
2. 保持事务（连接）短小
3. 尽可能避免使用SP/TRIG/FUNC
4. 尽量不用SELECT *
5. 改写OR为IN()
6. 改写OR为UNION
7. 避免负向查询和%前缀模糊查询
8. 减少COUNT(*)
9. LIMIT高效分页
10. 用UNION ALL 而非UNION
11. 分解联接保证高并发
12. GROUP BY 去除排序
13. 同数据类型的列值比较
14. Load data 导数据
15. 打散大批量更新
16. Know Every SQL

#### 约定类原则
1. 隔离线上线下
2. 禁止未经DBA确认的子查询
3. 永远不在程序端显式加锁
4. 统一字符集为UTF8
5. 统一命名规范
    - 库表等名称统一用小写
    - 索引命名默认为“idx_字段名"
    - 库名用缩写，尽量在2~7个字母 `DataSharing ==> ds`
    - 注意避免用保留字命名

