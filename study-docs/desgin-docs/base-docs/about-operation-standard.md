# 关于 运维 深切的建议
[TOC]

## 1. 应用规范

### 1.1 应用命名规范

- 一般前后端分离架构，示例：

| 功能 | 命名                |
| ---- | ------------------- |
| 前端 | application1-frontend |
| 后端 | application1-backend  |

- 微服务架构下，按实际业务模块命名，建议采用项目名-模块的方式，格式示例如下：

| 功能     | 命名           |
| -------- | -------------- |
| 前端     | application-frontend |
| 工具模块 | application-tools    |
| 权限模块 | application-admin    |
| 网关     | application-gateway  |

### 1.2 业务用中间件规范

- 业务系统Redis按环境（测试、预发、生产等）划分实例，各项目模块使用时一定不要复用DB号，以免冲突。
- 线上环境中间件连接地址均使用域名连接

## 2. 数据库规范

### 2.1 账户密码安全【强制】

- 密码复杂度不少于8位，包含数字、字母、字符
- 密码不允许跟用户名一致
- 删除数据库默认空用户账户和初始test数据库

### 2.2  账户安全管理【强制】

- 线上环境禁止直接使用root用户连接业务数据库
- 数据库名称和账号名称的分隔符使用中划线
- 为应用数据库创建独立的业务账号，管理权限和只读权限分离
- 若使用RDS，则需要写清楚账号和数据库的用途

### 2.3 连接池使用规范

- 建议在性能测试阶段进行连接池最优设置

### 2.4 其他

- 其他规范具体参考DBA业务标准文档

## 3. 日志规范

### 3.1 日志格式

- 严格以JSON格式输出，日志打印到文件，每行为一个完整json打印，必须的公共字段：

| 参数名    | 解释                                          | 示例                                       |
| :-------- | :-------------------------------------------- | :----------------------------------------- |
| app       | 应用名称，用于区分不同应用                    | usermaster                                 |
| level     | 日志等级，可选值：DEBUG, INFO, WARNING, ERROR | ERROR                                      |
| timestamp | UNIX时间戳，精确到毫秒                        | 1593307830000                              |
| message   | 日志内容                                      | Creating order, order no. 1234567890       |
| traceId   | 跟踪ID                                        | TRACE-a5d2ff32-4612-4f15-bf8d-774e231f0aa7 |

除必须的公共字段，其余各应用系统可自定义按需输出。

**示例：**

```json
{
    "app": "usermaster",
    "traceId": "TRACE-a5d2ff32-4612-4f15-bf8d-774e231f0aa7",
    "timestamp": "1593307830000",
    "level": "ERROR",
    "message": "Creating order, order no. 1234567890"
}
```

注意：上述示例为了方便阅读进行了格式化，实际输出为一整行JSON字符串

### 3.2 输出位置

输出到容器内 /logdata 目录，日志统一使用 app.log 文件名，示例： /logdata/app.log 。

### 3.3 日志级别

根据环境合理使用日志级别，生产环境一般不允许开debug模式。

### 关于每个模块内数据库权限的配置说明
> 每个模块用户名和数据库名一致，密码目前采用同一微服务框架下同一个密码，密码要求一定的复杂度。
> 权限仅限本库的增删改改查，目前禁用存储过程和视图。

```
## 数据库授权
### flask-micro-demo
GRANT ALL ON `flask-micro-demo`.* TO 'flask-micro-demo'@'%' IDENTIFIED BY
 'zjbMMrkYZHSTDFO0';
FLUSH PRIVILEGES;
```

### 关于基础镜像构建
> `alpine` + `python3.8.3`

#### 为什么使用 `alpine`？
1. 小巧：基于Musl libc和busybox，和busybox一样小巧，最小的Docker镜像只有5MB；
2. 安全：面向安全的轻量发行版。
3. 简单：提供APK包管理工具，软件的搜索、安装、删除、升级都非常方便。
4. 适合容器使用：由于小巧、功能完备，非常适合作为容器的基础镜像。

#### 为什么使用 那么高的 `python` 版本？
> python2 已停止维护，python 的更新迭代还是比较快的，对于新版本一些功能目前可能用不到，为了以后代码的可扩展性和适用性，
>采用目前( `2020-7-10` )相对比较稳定版本( `python3.7.8` )作为基础系统的 python 版本。

##### pip.conf
```
[distutils]
index-servers =
    pypi
    pypitest
    python
    pythontest

[pypi]
repository:https://mirrors.aliyun.com/pypi

[pypitest]
repository:https://testmirrors.aliyun.com/pypi

[chaos]
repository=http://ip/repository/python/
username=username
password=password

[chaostest]
repository=http://ip/repository/python/
username=username
password=password

[global]
index-url = http://mirrors.aliyun.com/pypi/simple/
extra-index-url = http://ip/repository/python/simple

[install]
trusted-host = mirrors.aliyun.com
```

##### requirements.txt
````
alembic==1.4.2
arrow==0.15.7
bleach==3.1.5
blinker==1.4
certifi==2020.6.20
cffi==1.14.0
chardet==3.0.4
click==7.1.2
colorama==0.4.3
cryptography==2.9.2
docutils==0.16
elasticsearch==7.8.0
Flask==1.1.2
Flask-Mail==0.9.1
Flask-Migrate==2.5.3
flask-redis==0.4.0
Flask-Script==2.0.6
Flask-SQLAlchemy==2.4.3
idna==2.9
importlib-metadata==1.7.0
itsdangerous==1.1.0
Jinja2==2.11.2
jwt==1.0.0
keyring==21.2.1
Mako==1.1.3
Markdown==3.2.2
MarkupSafe==1.1.1
Naked==0.1.31
packaging==20.4
pkginfo==*******
pyaml==20.4.0
pyasn1==0.4.8
pyasn1-modules==0.2.8
pycparser==2.20
Pygments==2.6.1
PyMySQL==0.9.3
pyparsing==2.4.7
python-dateutil==2.8.1
python-editor==1.0.4
PyYAML==5.3.1
readme-renderer==26.0
redis==3.5.3
requests==2.24.0
requests-toolbelt==0.9.1
rfc3986==1.4.0
shellescape==3.8.1
six==1.15.0
SQLAlchemy==1.3.18
tqdm==4.46.1
twine==3.2.0
urllib3==1.25.9
webencodings==0.5.1
Werkzeug==1.0.1
zipp==3.1.0
````
