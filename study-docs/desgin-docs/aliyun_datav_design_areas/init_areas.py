import json
import operator


class AreaManager(object):
    def __init__(self, filename):
        self.filename = filename
        self.zhi_xia_shi_area = [
            '重庆市',
            '北京市',
            '天津市',
            '上海市',
        ]
        self.all_areas = {}
        self.source_areas = []
        self.temp_formated_areas = {}
        self.data = []

    def read_file(self):
        with open(self.filename, 'r', encoding='utf-8') as fp:
            areas = fp.read()
            dict_areas = json.loads(areas)
            return dict_areas

    def set_all_areas(self):
        list_areas = self.read_file()
        tt  = 0
        for i in list_areas:
            temp_level = i['treeID'].split('-')
            if len(temp_level) >= 2:
                # 获取国家数据
                level1_area = temp_level[0]
                level2_area = '-'.join(temp_level[:2])
                if len(temp_level) == 2:
                    if i['name'] in self.zhi_xia_shi_area:
                        self.all_areas[i['treeID']] = i
                        j = {
                            'treeID': level2_area + '-1',
                            'name': '市辖区',
                            'treeName': level2_area + '-市辖区',
                            'parent': level2_area
                        }
                        self.all_areas[j['treeID']] = j
                        continue

                if len(temp_level) == 3:
                    if self.all_areas.get(level2_area + '-1', {}).get('name') == '市辖区':
                        temp_level.insert(2, '1')
                        level3_area = '-'.join(temp_level[:3])
                        level4_area = '-'.join(temp_level[:4])
                        i = {
                            'treeID': level4_area,
                            'name': i['name'],
                            'treeName': level4_area + '-' + i['name'],
                            'parent': level3_area
                        }
                        self.all_areas[i['treeID']] = i
                        continue
                    else:
                        self.all_areas[i['treeID']] = i
                        continue
                self.all_areas[i['treeID']] = i
            else:
                self.all_areas[i['treeID']] = i
        for i in self.all_areas.values():
            self.source_areas.append(i)
        with open('all_areas.json', 'w', encoding='utf-8') as fp:
            fp.write(json.dumps(self.all_areas, ensure_ascii=False, indent=2))

    def set_level1_areas(self):
        # 国数据
        for i in self.source_areas:
            if not i['parent']:
                self.temp_formated_areas = {
                    'code': i['treeID'],
                    'name': i['name'],
                    'children': [],
                }

    def set_level2_areas(self):
        # 省数据
        temp_level2_data = []
        for i in self.source_areas:
            if not i['parent']:
                continue
            temp_level = i['treeID'].split('-')
            if len(temp_level) == 2:
                temp_level2_data.append(i)
        for i in temp_level2_data:
            self.temp_formated_areas['children'].append({
                'code': i['treeID'],
                'name': i['name'],
                'children': [],
            })

    def set_level3_areas(self):
        # 市、区县数据
        temp_level3_data = []
        for i in self.source_areas:
            if not i['parent']:
                continue
            temp_level = i['treeID'].split('-')
            if len(temp_level) == 3:
                temp_level3_data.append(i)
        for i in temp_level3_data:

            temp_level = i['treeID'].split('-')
            # 获取国家数据
            level1_area = temp_level[0]
            level2_area = '-'.join(temp_level[:2])
            level3_area = '-'.join(temp_level[:3])
            for index, j in enumerate(self.temp_formated_areas['children']):
                if j['code'] == level2_area:
                    self.temp_formated_areas['children'][index]['children'].append({
                        'code': i['treeID'],
                        'name': i['name'],
                        'children': [],
                    })

    def set_level4_areas(self):
        # 市、区县数据
        temp_level4_data = []
        for i in self.source_areas:
            if not i['parent']:
                continue
            temp_level = i['treeID'].split('-')
            if len(temp_level) == 4:
                temp_level4_data.append(i)
        for i in temp_level4_data:
            temp_level = i['treeID'].split('-')
            # 获取国家数据
            level1_area = temp_level[0]
            level2_area = '-'.join(temp_level[:2])
            level3_area = '-'.join(temp_level[:3])
            for index, j in enumerate(self.temp_formated_areas['children']):
                if j['code'] == level2_area:
                    for index_k, k in enumerate(self.temp_formated_areas['children'][index]['children']):
                        if k['code'] == level3_area:
                            self.temp_formated_areas['children'][index]['children'][index_k]['children'].append({
                                'code': i['treeID'],
                                'name': i['name'],
                                'children': [],
                            })

    def write_areas(self):
        with open('pca-design-areas.json', 'w', encoding='utf-8') as f:
            f.write(json.dumps(self.temp_formated_areas['children'], indent=2, ensure_ascii=False))
            print('areas 已写入 pca-design-areas.json')

    def get_areas(self):
        self.set_all_areas()
        self.set_level1_areas()
        self.set_level2_areas()
        self.set_level3_areas()
        self.set_level4_areas()
        self.write_areas()
        # print(json.dumps(self.temp_formated_areas, indent=2))


if __name__ == '__main__':
    AreaManager('./design-areas.json').get_areas()

