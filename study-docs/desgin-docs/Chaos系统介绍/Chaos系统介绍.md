# 算力资源管理调度平台核心功能总览

**系统定位：** 一站式数据中心智能运维平台  
**最后更新于：** 2025年6月3日

## 核心闭环能力

- ✅ 物理资源 → 云资源 → 业务流程全链路纳管
- ✅ 自动化（订单/部署/网络） + 实时监控 + 审计护航
- ✅ 报表管理 + 报表导出
- ✅ 采销管理：客户管理 + 合同管理 + 回款管理 + 产品管理
## Chaos 系统架构
![Chaos系统架构图](./images/Chaos系统架构图.png)

## Chaos 选型

### 后端
- 基础选型： `Python 3.10` + `RestFramework 3.14` + `Django 5.0.6` + `WebSocket` + `MySQL 8.0` + `Redis 7.0`
- 核心工具
  - cryptography `隐私数据`、`重要数据`，数据库内的数据对称加密
  - python-docx docx类文档 `加载` `读取` ` 修改` `保存`
  - openpyxl execl类文档 `加载` `读取` ` 修改` `保存`
  - openstacksdk openstack管理SDK
  - paramiko `ssh` 协议操作
- 异步任务及定时任务: `celery 5.4` `django-celery-beta`

### 前端
- 基础选型: `Vue3` + `pinia` + [`ElementPlus` + `FastCrud`] + `TinyVue`

### 主要功能介绍
#### 1. **✅基础资源**

- **基础资源** 模块用于管理数据中心的物理资源，如机房、机柜等。
  - **IDC机房**
    - **机房管理**：管理数据中心的机房信息，包括位置、负责人等信息。
    - **包间管理**：管理机房中心的包间信息。
    - **机柜管理**：管理机房内的机柜，记录每个机柜的设备分布和使用情况。
  - **资产管理**
    - **服务器管理**：记录和管理所有物理服务器的信息。
    - **网络设备管理**：管理路由器、交换机等网络设备。
    - **配件管理**：管理服务器和网络设备的配件。
    - **耗材管理**：管理日常使用的耗材，如线缆、标签等。

#### 2. **✅运维管理**
- **运维管理**模块帮助管理员监控和管理系统的运行状态，确保应用程序和服务的正常运作。
  - **主机管理**：管理主机（服务器），配置监控、物理服务器、Buffer池等需求的标识信息。
  - **应用管理**：运维侧使用 ==> 应用台账信息<span style="color: red"> 有隐秘数据，暂不进行展示</span>。
  - **项目管理**：运维侧使用 ==> 应用台账分组信息<span style="color: red"> 有隐秘数据，暂不进行展示</span>。
  - **租户账号**：管理系统内的租户，未来给客户使用的租户账号信息，用于关联Chaos系统内的用户和openstack云平台。
  - **自动化运维**： 自动化运维模块，用于实现自动化运维快速安装已验证的软件包，并完成软件包的安装及配置，如（cuda，docker）。
    - **适配软件**：管理已适配软件包的信息，如（cuda，docker）。 
    - **模版管理**：管理自动化运维的已适配的模版信息。
    - **任务管理**：管理自动化运维的自动任务的信息，启动任务、查看任务详情等。


#### 3. ** ✅采销管理**
- **采销管理** 模块用于管理客户信息和销售流程，帮助销售人员更好地跟踪客户需求和合同进度。
  - **客户管理**：当前尚未正式启用，记录信息较为简单。
  - **合同管理**：记录客户订单信息，用于跟踪订单进度和交付情况。
  - **回款管理**：管理客户订单的回款信息，确保客户付款及时。
  - **产品管理**：管理产品信息，如裸金属、内存、带宽等，用于合同管理的产品信息快速输入。

#### 4. **✅监控大屏**
- **监控大屏** 模块提供实时的可视化监控界面，帮助管理员快速了解系统的运行状态和资源使用情况。
  - **服务器总览**：展示所有服务器的整体运行情况，包括 CPU、内存、磁盘等资源的使用率。
  - **服务器IPMI**：通过 IPMI 协议监控服务器的硬件状态，如温度、风扇转速等。
  - **黑盒监控**：监控服务器的内部健康状况，及时发现潜在问题。
  - **机柜功率**：监控机柜的电力消耗情况，确保电力供应稳定。
  - **服务器单台**：详细展示单台服务器的运行状态，包括性能指标、告警信息等。

#### 5. **✅云资源池**
- **云资源池** 模块用于纳管openstack云平台集群及租户管理。
  - **网络配置**
    - **Vlan配置**：管理虚拟局域网（VLAN）与物理网络关联配置，自动创建物理网络vlan及其子网。
    - **公网IP**：管理公网IP分配与策略。
      - **公网IP池**：已配置的公网IP池，管理公网IP的分配和策略，自动分配与回收。
      - **公网策略**：管理公网IP的策略，如全端口开放、指定端口开放等。
  - **基础配置**
    - **OpenStack配置**：多云平台 OpenStack 管理节点的基础配置。
    - **OpenStack项目（租户）**：管理 OpenStack 中项目，自动配置默认优化后的配额（默认的配额太小，裸金属服务器最多支持两台）、自动授权此项目给admin用户用于管理（默认admin用户无法直接管理，需添加此用户）。
#### 6. **✅云产品及服务**
- **云产品及服务** 模块提供云资源的管理功能。
  - **弹性计算**
    - **裸金属**：裸金属或云主机管理，目前已完成了主机的创建、编辑、开机、关机、删除、重启等主机相关的操作。
    - **云主机**：云主机管理，目前已实现云主机的创建、编辑、开机、关机、删除、重启等主机相关的操作。
    
#### 7. **✅订单管理**
- **订单管理** 模块帮助用户管理和跟踪裸金属或云主机自动创建的订单，订单关联任务通知到钉钉群，并生成交付报告。
  - **订单列表**：管理员管理所有订单。
  - **我的待办**：谁需要处理的订单。
  - **我的订单**：我提交的订单。
  - **申请主机**：自动化创建主机的申请单。已适配软件包、自动匹配网络信息、自动关联公网IP池等。

#### 8. **✅报表管理**
- **报表大屏** 模块用于生成记录各种报表，帮助管理员了解系统的使用情况、资源使用情况等。
  - **报表管理**：管理员可以生成各种报表历史记录，如用户访问统计、资源使用情况、租户的资源情况等，支持导出PDF。
  - **核心资源概览**：以机房为维度，展示已上线的核心资源（如服务器数量、机柜数量、Buffer池机器数量、Buffer池GPU、网络分类图表、裸机监控TOP10等）。
  - **租户资源概览**：以租户维度，展示已上线的租户资源情况（如总服务器数量、配额、近期创建数量、近期即将过期等）。

#### 8. **✅系统管理**
- **系统管理** 模块帮助管理员维护和管理平台的基本设置和用户权限。
  - **系统配置**
    - **菜单管理**：管理员可以添加、删除或修改系统中的菜单项，控制不同用户能看到的功能。
    - **部门管理**：管理公司内部的各个部门，分配资源和权限。
    - **角色管理**：定义不同角色（如管理员、普通用户等），并为每个角色分配不同的权限。
    - **用户管理**：添加、删除或修改用户信息，管理用户的登录和访问权限。
    - **数据权限**：控制用户可以访问哪些数据，确保敏感信息的安全。
    - **岗位管理**：定义和管理不同岗位，分配相应的职责和权限。
    - **消息中心**：用于发送和接收系统通知、公告等信息，确保用户及时了解重要事项。
    - **接口白名单**：设置允许访问系统 API 的 IP 地址或域名，增强安全性。

  - **常规配置**：
    - **系统配置**：调整系统的全局设置，如时间格式、默认语言等。
    - **字典管理**：管理系统中常用的分类和选项，如状态、类型等，方便统一管理和使用。
    - **地区管理**：维护国家、省份、城市等地理信息，用于地址选择和其他相关功能。
    - **附件管理**：已上传的文件管理，如图片、文档等。

  - **审计日志**：
    - **登录日志**：记录用户的登录时间和地点，帮助管理员追踪谁在何时登录了系统。
    - **操作日志**：记录用户在系统中的所有操作，如添加、修改或删除数据，便于追溯和审计。
    - **重要资源变更日志**：记录关键资源（如服务器、网络设备等）的变化情况，确保系统的重要资产得到妥善管理。

#### 9. **❓工单管理**
> 比如，服务器已过期，创建一个工单用于关机服务器，工单流程走完后（确认信息后，审批通过）调度任务自动关机相关主机。
- **工单管理** 模块帮助用户规范流程操作，自动关联需要变更的数据。
  - **工作台**
    - **我的工单** 我提交的
    - **我的代办** 我需要处理的
    - **我的审批** 我需要审批的
    - **新建工单** 创建工单
    - **工单统计** 用于运营或管理员的报表
  - **工单设计**  管理员或流程需求者 ==> 设计工单流程，类似于工单设计器
    - **工单分类**
    - **表单设计**
    - **关联数据**
    - **工单模版** 
    - **任务管理** 工单某一任务完成状态执行的操作。
    - **工单通知** 通知方式管理，比如系统内通知、邮件通知、钉钉通知，关联到工单模版内
#### **10. ❓超级工作表**
> 上传一个execl模版表，生成适配Chaos系统的前端、后端、数据库代码，确认无误后，手动发布。
---

### 总结
目前已基本完成了资源的全流程纳管:
- ==> 机房、包间、机柜信息纳管（方便资源统筹管理）。
- ==> 基础物理相关资源纳管（有利于资源审计及明确资源（物理网络Vlan、公网IP池等）信息） 。
- ==> 云服务平台纳管(替代常见的需要登录到openstack的操作) 。
- ==> 订单管理（用户自助申请、流程规范化及自动化操作）。
- ==> 采销管理（客户信息、合同信息、回款信息、产品信息）规范化。

--------------------------------
### 备注
#### 订单创建主要分为两部, 1. 选择租户；2. 选择规格。
#### 重要数据变更日志
> [重要数据变更日志](http://chaos.hzxingzai.cn/#/system/resourceChangeRecords/detail?resource_id=cmdb_host-18c604e365924ed98e1f432c30f72f12)
> 仅展示变更的数据，保持页面的清爽。
![import_resource_audit_log](./images/import_resource_audit_log.png)

#### 图标释义
- ✅ 已完成：该功能已经开发完成并上线。
- 🏃 进行中：该功能正在开发中，预计近期完成。
- 🆙 规划中：该功能处于规划阶段，尚未开始开发。
- ❓ 待确认：该功能的需求或方案尚未最终确定，需要进一步讨论。
