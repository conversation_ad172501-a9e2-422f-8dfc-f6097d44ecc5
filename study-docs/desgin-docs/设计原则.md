# README
> 本项目为 【星在科技】【django+vue3】的【前后端分离】框架中的【后端】源码，包括基础控制层、基础配置文件、基础工具、基础修饰器等。**<span style="color: red"> 在能力范围内，用新（比最新版要低一到3个小版本，大的最新版本千万不当小白鼠）不用旧</span>**


## 凡事预则立，不预则废。规范很重要！很重要！很重要！
## 凡事预则立，不预则废。规范很重要！很重要！很重要！
## 凡事预则立，不预则废。规范很重要！很重要！很重要！

## 致`开发者`及`参与者`的一封信
### Chaos 名字的由来？
> 【混沌】、【浑沌】之意。混沌，天地之始也。
- 古代传说中指世界开辟前元气未分、模糊一团的状态。
- 从无序到有序，都是一点一滴积累而来，终会达到一种平衡状态。
引用文献：
- 《西游记》第一回：“混沌未分天地乱，茫茫渺渺无人见。”
- 汉班固《白虎通·天地》：“混沌相连，视之不见，听之不闻，然后剖判。”

### 如何安装基础环境？
```
基础环境要求：
Python >= 3.12.0 (最低3.9+版本)
Nodejs >= 16.0 （<当前本地环境版本：node:20.12.2 npm: 10.5.0>） 
Mysql >= 8.0 (可选，默认数据库sqlite3，支持5.7+，推荐8.0版本。<当前本地环境版本：MySQL8.0.37>)
Redis (可选，最新版)
```

### 为甚么要使用微服务架构
- 简单灵活、独立部署
- 专注、专业、高效、可靠小团队
- 松耦合、高内聚、易扩展
- 语言、工具无关
- 每个APP可独立运行
### 设计原则
> 本项目为微服务框架，所有功能做好明确分工，模块间需要联调 `禁止使用数据库直接相连, 必须使用接口`。

- AKF 拆分原则
- 前后端分离
- 无状态服务
- Restful 通信风格
#### AKF 拆分原则
![akf](base-docs/images/akf.png)

AKF扩展立方体(参考《The Art of Scalability》)，是一个叫AKF的公司的技术专家抽象总结的应用扩展的三个维度。理论上按照这三个扩展模式，可以将一个单体系统，进行无限扩展。
X 轴 ：指的是水平复制，很好理解，就是讲单体系统多运行几个实例，做个集群加负载均衡的模式。
Z 轴 ：是基于类似的数据分区，比如一个互联网打车应用突然或了，用户量激增，集群模式撑不住了，那就按照用户请求的地区进行数据分区，北京、上海、四川等多建几个集群。
Y 轴 ：就是我们所说的微服务的拆分模式，就是基于不同的业务拆分。
场景说明：比如打车应用，一个集群撑不住时，分了多个集群，后来用户激增还是不够用，经过分析发现是乘客和车主访问量很大，就将打车应用拆成了三个乘客服务、车主服务、支付服务。三个服务的业务特点各不相同，独立维护，各自都可以再次按需扩展。

#### 前后端分离
![frontend-backend-split](base-docs/images/front-backend-split.png)

前后端分离原则，简单来讲就是前端和后端的代码分离也就是技术上做分离，我们推荐的模式是最好直接采用物理分离的方式部署，进一步促使进行更彻底的分离。不要继续以前的服务端模板技术，比如JSP ，把Java JS HTML CSS 都堆到一个页面里，稍复杂的页面就无法维护。这种分离模式的方式有几个好处：
前后端技术分离，可以由各自的专家来对各自的领域进行优化，这样前端的用户体验优化效果会更好。
分离模式下，前后端交互界面更加清晰，就剩下了接口和模型，后端的接口简洁明了，更容易维护。
前端多渠道集成场景更容易实现，后端服务无需变更，采用统一的数据和模型，可以支撑前端的web UI\ 移动App等访问。

#### 无状态服务
![non-status-service](base-docs/images/non-status-service.png)

对于无状态服务，首先说一下什么是状态：如果一个数据需要被多个服务共享，才能完成一笔交易，那么这个数据被称为状态。进而依赖这个“状态”数据的服务被称为有状态服务，反之称为无状态服务。
那么这个无状态服务原则并不是说在微服务架构里就不允许存在状态，表达的真实意思是要把有状态的业务服务改变为无状态的计算类服务，那么状态数据也就相应的迁移到对应的“有状态数据服务”中。
场景说明：例如我们以前在本地内存中建立的数据缓存、Session 缓存，到现在的微服务架构中就应该把这些数据迁移到分布式缓存中存储，让业务服务变成一个无状态的计算节点。迁移后，就可以做到按需动态伸缩，微服务应用在运行时动态增删节点，就不再需要考虑缓存数据如何同步的问题。

#### Restful API
[TODO] 
![restful-api](base-docs/images/restful-api.png)

作为一个原则来讲本来应该是个“无状态通信原则”，在这里我们直接推荐一个实践优选的 Restful 通信风格 ，因为他有很多好处：
无状态协议 HTTP，具备先天优势，扩展能力很强。例如需要安全加密是，有现成的成熟方案 HTTPS 可用。
JSON 报文序列化，轻量简单，人与机器均可读，学习成本低，搜索引擎友好。
语言无关，各大热门语言都提供成熟的 Restful API 框架，相对其他的一些RPC框架生态更完善。
当然在有些特殊业务场景下，也需要采用其他的RPC框架，如 thrift、avro-rpc、grpc 。但绝大多数情况下 Restful 就足够用了。

### 微服务带来的问题
#### 依赖接口变更困难
> 这是必然的事情，对于各模块间的调用必须要严格按照这些来处理。牺牲复杂度来降低项目之间的耦合性。
1. 部分模块重复构建
此问题可以通过搭建私服来解决。
2. 分布式带来的问题
这个问题对于小型项目确实是个问题，对于大型项目这是必然要经历的阶段。
3. 运维复杂度陡增 
必经之路。 docker、k8s、rancher 等都是基本技能了。

- [关于 DBA 相关建议](base-docs/about-dba-standard.md)
- [关于 运维 相关建议](base-docs/about-operation-standard.md)
- [关于 测试 相关建议](base-docs/about-test-standard.md)
- [关于 http 状态码](base-docs/about_http_code.md)
- [关于 restful 设计指南](base-docs/about_restful_api.md)

### 关于`代码建议性问题`
#### 代码风格问题
建议按照 python 的命名规范进行 code, 虽然 python 对变量进行命名没有严格的规定。
`如果你对规范不是很清楚，建议使用 Pycharm 编译器会对你有很大的帮助。`
详情参见下文中的`友情链接`。


#### 文档的观赏性问题
为了更舒适的观赏代码，建议统一开启中英文加空格的方式进行编写文档。如：
```
这是描述关于翻译的文档，gitee 即是中国目前流行的 git 仓库管理网站，用着挺有 easy 的。
```

## 友情链接
- [关于微服务 4 个原则和 19 个解决方案](https://www.cnblogs.com/stulzq/p/8573828.html)
- [什么是微服务？](https://www.zhihu.com/question/65502802)
- [Python3中的命名规范大全---基于PEP8标准](https://blog.csdn.net/weixin_39723544/article/details/82144280)
- [PEP8](https://www.python.org/dev/peps/pep-0008/)
- [Restful API 设计指南](http://www.ruanyifeng.com/blog/2014/05/restful_api.html)
- [w3c http code](https://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html)
[[理解 restful 架构](http://www.ruanyifeng.com/blog/2011/09/restful.html)
