# 多地域接入文档
> 最后一次更新时间: `2025-07-14`

> 全新 `Openstack` 节点接入

## 部署 `Openstack` 管理节点
略（详见 Openstack 部署文档）
## 配置平台内 `Openstack` 管理节点
### 创建 `Openstack` 管理节点的应用程序凭证
    
    1. 使用  `管理员账号` ，登录 `Openstack` 管理节点。
    2. 切换到 `admin` 项目（保证有足够的权限）。
    3. 创建 `Openstack` 管理节点的应用程序凭证。
       - 创建 `Openstack` 管理节点的应用程序凭证。
       - 注意勾选`未限制`选项。
       - 注意勾选 `admin` 角色选项。
       - 创建成功后，记录 `Openstack` 管理节点的应用程序凭证的 `用户名` 和 `密码`，推荐保存 `clouds.yaml` 或 `openrc.yaml`。

![创建 `Openstack` 管理节点的应用程序凭证](./images/openstack/admin-create-credits.png)

### 配置平台内 `Openstack` 管理节点的应用程序凭证    
```text
# 需要配置的参数：如下:
'auth_url': 'https://1.1.1.1:5000/v3',
'auth_type': 'v3applicationcredential',
'project_name': 'admin',
'username': 'admin',
'password': '123456',
'user_domain_name': 'Default',
'project_domain_name': 'default',
'region_name': 'RegionOne',
'openstack_application_credential_id': '123456',
'openstack_application_credential_secret': '123456',
'admin_user_id': '替换为实际的管理用户ID(用于授权新项目admin用户可直接切换到新项目管理项目)',
'admin_role_id': '替换为实际的管理角色ID(用于授权新项目admin用户可直接切换到新项目管理项目)',
```
![配置平台内 `Openstack` 管理节点的应用程序凭证](./images/openstack/platform-config-credits.png)
注意事项：
- 节点简称一定要填写为 `Openstack` 节点简称（用于前后端的识别）。
- 修改配置项值时(包含`修改启用状态`），一定要重新配置`配置项值` 因为在后端需要加密存储。
- 正常运行时，`对应的所有节点配置项`一定要`全部激活`，不激活的配置项将不会被加载，会导致sdk初始化失败。
- 节点配置项的`启用状态`一定要`全部激活`，不激活的配置项将不会被加载，会导致sdk初始化失败。
- 节点配置时无法加载问题时，请在区域内加一个配配置项如下：
    1. node: 指定要开启的区域。比如要启用 node = ”杭州"，则 node<节点简称> = "杭州"
    2. name: 启用。 固定值 name<配置项名称> = "启用"
    3. key: node_is_enable。 固定值 比如 key<配置项键名> = "node_is_enable"
    4. value: 任意值，启用即可。 比如 value<配置项值> = "true"
配置节点获取源代码：
```Python
class TenantOpenstackSettings:
    @classmethod
    def get_op_distinct_nodes(cls):
        key = cls.get_op_distinct_nodes_key()
        all_nodes = cache.get(key)
        if all_nodes:
            return all_nodes
        node_list = cls.objects.filter(
            name='启用', key='node_is_enable',
            enabled=True, is_deleted=False,
        ).values_list('node',  flat=True).distinct()
        # 去除每个节点的前后空格并去重
        all_nodes = list({node.strip() for node in node_list if node})
        if all_nodes:
            cache.set(key, all_nodes, timeout=3600)  # 缓存有效期为1小时
        return all_nodes
```