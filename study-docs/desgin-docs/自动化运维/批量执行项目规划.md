## 批量执行功能规划

#### 需求分析

##### 模板模块

- 模版增删改查
- 模板中可添加参数 其类似于jenkins的参数配置；能够让脚本根据变量动态的执行；如需要安装docker 24.0.1 或者docker 26.0.0 版本，可能只需修改docker_version这样的参数即可，最终根据模版执行任务的时候，根据$docker_version参数去安装指定版本的docker
- 模版中也可添加主机，这样可以在任务模块中直接点击执行即可，无需再次选择主机

##### 任务模块

- 任务的每次执行都作为一个记录
- 用户可选择模版、添加执行主机，然后点击执行任务
- 点击任务开始执行：对任务进行添加、IO流异步执行任务
- 包含任务的增、查

#### 技术选型

- 前端
  - xterm、websocket、element-plus、vue3、codemirror
- 后端
  - celery、redis、python3、paramiko | ansible

#### 库表设计

```sql
1. ExecTemplate 模板表
CREATE TABLE `default`.`ExecTemplate`  (
  `name` varchar(255) NULL,
  `body` tinytext NULL COMMENT '模板code内容',
  `interperter` varchar(255) NULL COMMENT 'sh or python default sh', 
  `host_ids` tinytext NULL,
  `desc` tinytext NULL,
  `parameters` tinytext NULL,
  `id` int NULL
);

2. ExecHistory 任务表
CREATE TABLE `default`.`ExecHistory`  (
  `id` int NOT NULL,
  `user` varchar(255) NULL,
  `digest` varchar(255) NULL COMMENT '任务的token值\r\n对于任务执行的日志 不进行入库 只在第一次执行的时候进行展示\r\n根据该token值(频道)结合发布订阅 、websocket实现日志实时输出',
  `command` varchar(255) NULL COMMENT '对模版code内容补充后 最后执行的command',
  `interperter` varchar(255) NULL COMMENT 'sh or python default sh',
  `params` varchar(255) NULL,
  `host_ids` tinytext NULL,
    NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `template_id` FOREIGN KEY () REFERENCES `default`.`ExecTemplate` (`id`)
);
```

#### 前端设计

##### 模板相关

- 模板列表页

![image-20250119104604035](images/模板列表.png)

- 添加模板

![image-20250119104655804](images/模板添加.png)

##### 任务相关

- 任务首页

![image-20250119104944380](images/任务首页.png)

- 任务执行页

![image-20250119105010974](images/任务执行页.png)

#### 功能流程图![批量任务执行流程图](images/批量任务执行流程图.png)

#### 项目排期

- P0
  - 模版模块
  - 任务模块

#### 接口文档 -- 后续补充