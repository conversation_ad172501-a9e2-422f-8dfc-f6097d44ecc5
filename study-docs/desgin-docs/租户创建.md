# Chaos 系统租户创建
### 创建客户
1. 进入菜单 `采销管理` -> `客户管理`，点击 `添加`，填入必要字段，点击 `确定`。
![创建客户](./images/租户创建/1-创建客户.png)
### 创建租户账号
1. 进入菜单 `运维管理` -> `租户账号`，点击 `添加`，填入必要字段，点击 `确定`。
![创建租户账号](./images/租户创建/2-创建租户账号.png)
### 创建openstack项目
1. 进入菜单 `云资源池` -> `基础配置` -> `openstack项目`，点击 `添加`，填入必要字段，点击 `确定`。
![创建openstack项目](./images/租户创建/3-创建openstack项目.png)
### 创建项目对应的网络
1. 进入菜单 `云资源池` -> `网络配置` -> `vlan配置`，`选择一个未配置项目的物理网段`，选择需要（此前已创建）创建的项目名称，点击 `确定`。
![配置openstack项目使用的vlan](./images/租户创建/4-1-配置openstack项目.png)
2. 选择对应账号的openstack项目，点击 `创建物理网络`，【点击后请**刷新后**再次点击创建物理网络(防止重复创建的情况)】。
![创建项目对应的物理网络](./images/租户创建/4-2-创建物理网络.png)

### 后续优化
- 在【客户管理】野蛮添加一键创建租户账号功能。此功能自动串联所有关联关系，创建成功后，自动生成对应账号的openstack项目，项目的配额及可选区域，并关联项目对应的物理网络。
