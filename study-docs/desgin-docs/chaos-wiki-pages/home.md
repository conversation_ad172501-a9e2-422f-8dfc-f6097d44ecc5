# Django-Vue3-Admin

[![img](https://img.shields.io/badge/license-MIT-blue.svg)](https://gitee.com/liqianglog/django-vue-admin/blob/master/LICENSE)  [![img](https://img.shields.io/badge/python-%3E=3.10.x-green.svg)](https://python.org/)  [![PyPI - Django Version badge](https://img.shields.io/badge/django%20versions-5.0-blue)](https://docs.djangoproject.com/zh-hans/3.2/) [![img](https://img.shields.io/badge/node-%3E%3D%2012.0.0-brightgreen)](https://nodejs.org/zh-cn/) [![img](https://gitee.com/liqianglog/django-vue-admin/badge/star.svg?theme=dark)](https://gitee.com/liqianglog/django-vue-admin)

## 平台简介

💡 `django-chaos-backend` 本项目采用[django-vue3-admin开源项目为基础框架](https://gitee.com/huge-dream/django-vue3-admin.git) 是一套快速开发平台，包含 `CRM`、`资产管理`、`工单流程管理`、`低代码开发架构`等功能的后端。


    django-chaos-backend 基于RBAC模型的权限控制的一整套基础开发平台，权限粒度达到列级别，前后端分离，后端采用django + django-rest-framework，前端采用基于 vue3 + CompositionAPI + typescript + vite + element plus。

* 🧑‍🤝‍🧑前端采用 Vue3+TS+pinia+fastcrud(感谢[vue-next-admin](https://lyt-top.gitee.io/vue-next-admin-doc-preview/))
* 👭后端采用 Python 语言 Django 框架以及强大的 [Django REST Framework](https://pypi.org/project/djangorestframework)。
* 👫权限认证使用[Django REST Framework SimpleJWT](https://pypi.org/project/djangorestframework-simplejwt)，支持多终端认证系统。
* 👬支持加载动态权限菜单，多方式轻松权限控制。
* 👬全新的列权限管控，粒度细化到每一列。

#### 🏭 环境支持

| Edge      | Firefox      | Chrome      | Safari      |
| --------- | ------------ | ----------- | ----------- |
| Edge ≥ 79 | Firefox ≥ 78 | Chrome ≥ 64 | Safari ≥ 12 |

> 由于 Vue3 不再支持 IE11，故而 ElementPlus 也不支持 IE11 及之前版本。



## 在线体验

👩‍👧‍👦演示地址：[http://chaos.dev.hzxingzai.cn](http://chaos.dev.hzxingzai.cn)

功能规划：

1. ✅ `已完成` 行权限-数据权限 
2. 🏃 `进行中` 资源管理
3. 🏃 `进行中` 资产管理
4. 🆙 `规划中` 工单管理
5. 🆙 `规划中` 流程管理
6. 🆙 `规划中` 资源关系图前端展示
7. ❓ `待确认` CRM 客户管理
8. ❓ `待确认` DataV 大屏展示
9. ❓ `待确认` 低代码平台
10. ❓ `待确认` 超级工作表
11. ❓ `待确认` AI对话式生成平台



## 内置功能

1.  👨‍⚕️菜单管理：配置系统菜单，操作权限，按钮权限标识、后端接口权限等。
2.  🧑‍⚕️部门管理：配置系统组织机构（公司、部门、角色）。
3.  👩‍⚕️角色管理：角色菜单权限分配、数据权限分配、设置角色按部门进行数据范围权限划分。
4.  🧑‍🎓按钮权限控制：授权角色的按钮权限和接口权限,可做到每一个接口都能授权数据范围。
5.  🧑‍🎓字段列权限控制：授权页面的字段显示权限，具体到某一列的显示权限。
7.  👨‍🎓用户管理：用户是系统操作者，该功能主要完成系统用户配置。
8.  👬接口白名单：配置不需要进行权限校验的接口。
9.  🧑‍🔧字典管理：对系统中经常使用的一些较为固定的数据进行维护。
10.  🧑‍🔧地区管理：对省市县区域进行管理。
11.  📁附件管理：对平台上所有文件、图片等进行统一管理。
12.  🗓️操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。


## 演示图✅

![image-01](study-docs/desgin-docs/base-docs/readme/1-login.png)

![image-02](study-docs/desgin-docs/base-docs/readme/2-home.png)
![image-03](study-docs/desgin-docs/base-docs/readme/3-menu.png)
![image-04](study-docs/desgin-docs/base-docs/readme/4-area.png)
![image-05](study-docs/desgin-docs/base-docs/readme/5-resouce-example.png)
![image-06](study-docs/desgin-docs/base-docs/readme/6-general-consumable.png)
