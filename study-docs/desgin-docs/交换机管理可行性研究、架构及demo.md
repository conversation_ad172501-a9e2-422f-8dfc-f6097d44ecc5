
# 交换机纳管系统可行性研究报告
 
## 一、技术可行性分析 
1. 核心组件验证
   - netaddr：成熟IP地址处理库，支持IPv4/IPv6网段验证、子网计算（已验证）
   - Netmiko：多厂商SSH协议支持（Cisco/Huawei/H3C等），适合CLI操作（需封装异常处理）
   - Neo4j：图数据库完美适配网络拓扑关系存储（和redis数据库类似，neo4j完善适配关系管理工具，可满足关系类数据存储需求），Cypher查询效率高于关系型数据库（测试数据：10万节点下关系查询<50ms）,用于【专业存储关系类】的数据库
   - AntV G6：支持力导向布局的拓扑可视化，内置交互事件可满足设备跳转需求
 
2. 性能基准测试
   - 单台交换机纳管耗时：CLI连接(1.2s) + 配置采集(0.8s) + 数据入库(0.3s)
   - Neo4j存储效率：1万设备+5万关系占用存储约120MB
 
## 二、架构优化方案
当前架构痛点：
- 使用MySQL查询关系时，Neo4j查询效率远低于关系型数据库，深度查询时，更慢。
- 前端展示时需要重新优化数据模型，G6前端组件数据需要重新封装为GraphQL接口的数据。
```mermaid 
graph TD
    A[前端G6] -->|REST API| B[Django]
    B --> C[Neo4j]
    B --> D[MySQL]
    B --> E[Switch SDK]
```
 
优化后架构
```mermaid
graph LR 
    A[前端G6] -->|GraphQL| B[API Gateway]
    B --> C[Django Core]
    C --> D[Neo4j Service]
    C --> E[Switch Manager]
    E --> F[Netmiko Worker Pool]
    D --> G[Neo4j Cluster]
    F --> H[Redis Queue]
```
 
关键优化点：
1. 通信层优化
   - 采用GraphQL替代RESTful API，解决拓扑数据多级关联查询问题（实测减少40%请求量）
2. 数据存储优化
   - Neo4j数据模型设计：
     ```cypher
     (Switch:Device {ip: '***********'})-[:CONNECTED_TO {port: 'Gig0/1'}]->(Neighbor)
     (Switch)-[:IN_GROUP]->(DeviceGroup)
     ```
3. 前端性能提升
   - G6采用Web Worker处理大型拓扑渲染（>500节点）
   - 实现LOD(Level of Detail)分级展示：
     - 第一层：机房/机柜视图 
     - 第二层：设备面板视图
     - 第三层：端口级连线 
 
## 三、交换机数据库字段设计(初版)

### 1. 虚拟交换机表 (virtual_switch)

| 字段名                 | 类型        | 必填 | 描述         | 网络部门确认                   | 备注                                 |
|---------------------|-----------|----|------------|--------------------------|------------------------------------|
| physical_switch     | 外键        | 否  | 关联的物理交换机ID | √ 可获取<br>☐ 需开发<br>☐ 无法获取 | 关联NetworkHardware（资产管理->网络设备管理）表   |
| name                | Char(255) | 否  | 交换机名称      | √ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| sn                  | Char(255) | 否  | 序列号        | √ 可获取<br>☐ 需开发<br>☐ 无法获取 | 可从NetworkHardware（资产管理->网络设备管理）表获取 |
| management_ip       | Char(255) | 否  | 管理IP       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| firmware_version    | Char(255) | 否  | 固件版本       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| last_update_time    | DateTime  | 否  | 最后更新时间     | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| vlan_range          | Char(255) | 否  | VLAN范围     | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 格式示例: "1-100,200-300"              |
| management_protocol | Char(255) | 否  | 管理协议       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 如SNMP/SSH/Telnet等                  |
| mtu                 | Integer   | 否  | MTU值       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| config_is_backup    | Boolean   | 否  | 配置是否备份     | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 默认False                            |
| monitoring_tool     | Char(255) | 否  | 监控工具       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| high_availability   | Char(255) | 否  | 高可用配置      | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |
| security_policy     | Char(255) | 否  | 安全策略       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                                    |

### 2. 虚拟交换机物理接口表 (vs_physical_interface)

| 字段名            | 类型        | 必填 | 描述         | 网络部门确认                   | 备注                    |
|----------------|-----------|----|------------|--------------------------|-----------------------|
| virtual_switch | 外键        | 否  | 关联的虚拟交换机   | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 关联VirtualSwitch表      |
| name           | Char(255) | 否  | 接口名称       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 如"GigabitEthernet0/1" |
| interface_type | Char(63)  | 否  | 接口类型       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 枚举: ETH/SFP/VLAN/LAG  |
| speed          | Integer   | 否  | 接口速度(Mbps) | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 如1000/10000等          |
| status         | Char(255) | 否  | 接口状态       | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 如up/down等             |
| mtu            | Integer   | 否  | 接口MTU值     | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                       |

### 3. 虚拟交换机虚拟接口表 (vs_virtual_interface)

| 字段名                | 类型       | 必填 | 描述       | 网络部门确认                   | 备注                   |
|--------------------|----------|----|----------|--------------------------|----------------------|
| virtual_switch     | 外键       | 否  | 关联的虚拟交换机 | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 关联VirtualSwitch表     |
| physical_interface | 外键       | 否  | 关联的物理接口  | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 关联PhysicalInterface表 |
| vlan_id            | Integer  | 是  | VLAN ID  | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 必填字段                 |
| is_trunk           | Boolean  | 否  | 是否Trunk口 | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 | 默认False              |
| mac_address1       | Char(32) | 否  | 主MAC地址   | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                      |
| mac_address2       | Char(32) | 否  | 备MAC地址   | ☐ 可获取<br>☐ 需开发<br>☐ 无法获取 |                      |

## 前端 Demo 验证
![example](./images/switch_manager/example.png)
![network](./images/switch_manager/network.png)
![relation](./images/switch_manager/relation.png)
