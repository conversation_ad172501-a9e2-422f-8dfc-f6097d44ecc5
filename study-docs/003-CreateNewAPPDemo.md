在Django中创建并开发一个简单的API，你需要按照以下步骤操作：

### 步骤 1: 创建应用

首先，确保你已经在项目中创建了应用。如果还没有，可以通过以下命令创建一个名为`api`的新应用：

```bash
python manage.py startapp api
```

### 步骤 2: 定义模型

在`api/models.py`中定义一个简单的模型。例如，我们创建一个`Book`模型：

```python
from django.db import models

class Book(models.Model):
    title = models.CharField(max_length=200)
    author = models.CharField(max_length=100)
    published_date = models.DateField()

    def __str__(self):
        return self.title
```

记得在`settings.py`中添加你的应用到`INSTALLED_APPS`：

```python
INSTALLED_APPS = [
    # ...
    'api',
]
```

然后运行迁移：

```bash
python manage.py makemigrations
python manage.py migrate
```

### 步骤 3: 创建序列化器

在`api`应用目录下创建一个`serializers.py`文件，定义序列化器来处理模型数据的转换：

```python
from rest_framework import serializers
from .models import Book

class BookSerializer(serializers.ModelSerializer):
    class Meta:
        model = Book
        fields = ('id', 'title', 'author', 'published_date')
```

### 步骤 4: 创建视图

在`api/views.py`中，创建一个视图来处理HTTP请求：

```python
from rest_framework import generics
from .models import Book
from .serializers import BookSerializer

class BookList(generics.ListCreateAPIView):
    queryset = Book.objects.all()
    serializer_class = BookSerializer

class BookDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Book.objects.all()
    serializer_class = BookSerializer
```

### 步骤 5: 配置URL

在`api`应用目录下创建一个`urls.py`文件，定义URL路由：

```python
from django.urls import path
from . import views

urlpatterns = [
    path('books/', views.BookList.as_view()),
    path('books/<int:pk>/', views.BookDetail.as_view()),
]
```

然后在项目级别的`urls.py`中包含你的应用URL：

```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include('api.urls')),
]
```

### 步骤 6: 安装和配置Django REST框架

确保在`settings.py`的`INSTALLED_APPS`中包含`rest_framework`：

```python
INSTALLED_APPS = [
    # ...
    'rest_framework',
]
```

### 步骤 7: 测试API

现在你可以运行你的Django开发服务器：

```bash
python manage.py runserver
```

然后在浏览器或使用如Postman这样的工具访问`http://127.0.0.1:8000/api/books/`来测试你的API。

以上就是创建一个基本Django REST API的完整流程。你可以根据需要添加更多的功能，比如认证、权限控制等。