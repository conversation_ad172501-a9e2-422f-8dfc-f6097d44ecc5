Django 是一个高级的 Python Web 框架，它鼓励快速开发和干净、实用的设计。下面是如何使用 Django 创建一个新项目的步骤：

### 1. 确保已安装 Python 和 Django

首先，你需要确保你的计算机上已经安装了 Python 和 Django。你可以通过在终端或命令提示符中运行以下命令来检查它们是否已经安装：

```bash
python --version
```

这应该会返回你正在使用的 Python 版本。同样地，检查 Django 是否安装：

```bash
python -m django --version
```

如果没有安装 Django，你可以通过 pip 安装它：

```bash
pip install django
```

或者如果你使用的是 Python 的虚拟环境：

```bash
pipenv install django
```

### 2. 创建 Django 项目

一旦确认 Django 已经安装，接下来就可以创建一个新的 Django 项目。打开终端或命令提示符，然后运行以下命令：

```bash
django-admin startproject Demo
```

这里，“Demo”是你新项目的名称。这个命令会在当前目录下创建一个名为“Demo”的新目录，并在这个目录内生成一些基本的 Django 项目文件和结构。

### 3. 进入项目目录

创建完项目后，进入项目目录：

```bash
cd Demo
```

### 4. 启动开发服务器

在项目目录中，你可以启动 Django 的内置开发服务器，用于测试和开发你的应用：

```bash
python manage.py runserver
```

这将启动一个开发服务器，你可以在浏览器中通过访问 http://127.0.0.1:8000/ 来查看你的新 Django 应用。

### 5. 创建应用

Django 项目通常由多个应用组成。现在，让我们为项目创建一个应用。在项目目录中运行：

```bash
python manage.py startapp myapp
```

这里，“myapp”是你新应用的名称。这将在项目目录下创建一个名为“myapp”的新目录，其中包含了应用的基本文件结构。

### 6. 添加应用到项目

为了使 Django 知道你的应用存在，你需要在项目的 settings.py 文件中添加它。打开 `Demo/settings.py` 文件，在 `INSTALLED_APPS` 列表中添加 `'myapp',`。

```python
INSTALLED_APPS = [
    # ...
    'myapp',
]
```

### 7. 创建模型

在 `myapp/models.py` 文件中，你可以定义数据模型。例如，你可以创建一个简单的博客文章模型：

```python
from django.db import models

class Post(models.Model):
    title = models.CharField(max_length=200)
    content = models.TextField()
    pub_date = models.DateTimeField('date published')
```

### 8. 迁移数据库

每当模型发生变化时，你都需要运行迁移来更新数据库。在项目目录中运行：

```bash
python manage.py makemigrations myapp
python manage.py migrate
```

### 9. 创建视图

在 `myapp/views.py` 文件中，定义视图函数来处理请求和响应。例如：

```python
from django.http import HttpResponse
from .models import Post

def index(request):
    latest_post_list = Post.objects.order_by('-pub_date')[:5]
    output = ', '.join([p.title for p in latest_post_list])
    return HttpResponse(output)
```

### 10. URL 分配

在 `myapp/urls.py` 文件中定义 URL 模式，并在 `Demo/urls.py` 中包含你的应用的 URL 配置。

```python
# myapp/urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('', views.index, name='index'),
]

# Demo/urls.py
from django.contrib import admin
from django.urls import include, path

urlpatterns = [
    path('admin/', admin.site.urls),
    path('myapp/', include('myapp.urls')),
]
```

### 11. 运行服务器并测试

再次运行服务器并访问 http://127.0.0.1:8000/myapp/ 来测试你的应用。

以上就是使用 Django 创建一个新项目的基本步骤。你可以继续扩展你的应用，添加更多的模型、视图和模板，以及处理更复杂的业务逻辑。