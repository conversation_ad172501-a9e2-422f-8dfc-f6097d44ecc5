OpenStack Generic Switch 概述及配置详解 

## 问： openstack genericswtich 概述及配置详解？
一、Generic Switch 概述 
 
1. 基本概念 
OpenStack Generic Switch 是 Neutron 的一个机制驱动(mechanism driver)，它允许 OpenStack 网络服务通过标准协议(如 SNMP、NETCONF、CLI等)与物理网络设备交互，而无需依赖厂商特定的驱动。
 
2. 核心功能 
- 多厂商支持：通过通用协议管理不同厂商的交换机 
- 物理网络集成：实现虚拟网络与物理网络的桥接 
- VLAN 管理：在物理交换机上自动配置 VLAN 
- 端口绑定：管理虚拟端口与物理端口的映射关系 
 
3. 适用场景 
- 混合云环境中需要统一管理物理网络 
- 使用不支持特定厂商驱动的网络设备 
- 需要自动化配置物理交换机的场景 
 
二、架构与工作原理 
 
1. 系统架构 
```
+-------------------+     +-------------------+     +-------------------+
|   OpenStack       |     |   Generic Switch  |     |   Physical        |
|   Neutron Server  |---->|   Mechanism Driver |---->|   Network Switch  |
+-------------------+     +-------------------+     +-------------------+
```
 
2. 工作流程 
1. Neutron 接收到网络配置请求 
2. Generic Switch 机制驱动处理请求 
3. 通过配置的协议(SSH/SNMP/NETCONF)与物理交换机通信 
4. 在物理交换机上执行相应的配置变更 
 
三、详细配置指南 
 
1. 前提条件 
- 已安装并运行 OpenStack Neutron 服务 
- 物理交换机支持以下至少一种协议：
  - SNMP (v2c 或 v3)
  - NETCONF 
  - SSH/CLI (通过 expect 脚本)
 
2. 主要配置步骤 
 
2.1 启用 Generic Switch 机制驱动 
修改 `/etc/neutron/plugins/ml2/ml2_conf.ini`：
 
```ini 
[ml2]
mechanism_drivers = openvswitch,genericswitch 
```
 
2.2 配置 Generic Switch 
创建或修改 `/etc/neutron/plugins/ml2/ml2_conf_genericswitch.ini`：
 
```ini 
[genericswitch:switch1]
device_type = netmiko_linux 
ip = *********** 
username = admin 
password = password123 
secret = enablepassword 
可选参数 
port = 22 
nativeless = True 
```
 
2.3 支持的设备类型 
```ini 
Cisco IOS 设备 
device_type = netmiko_cisco_ios 
 
Juniper JunOS 设备 
device_type = netmiko_juniper 
 
Huawei 设备 
device_type = netmiko_huawei 
 
通用 Linux 设备 
device_type = netmiko_linux 
 
SNMP 设备 
device_type = snmp 
```
 
2.4 SNMP 配置示例 
```ini 
[genericswitch:switch2]
device_type = snmp 
ip = *********** 
community = public 
write_community = private 
```
 
3. 网络配置 
 
3.1 配置物理网络 
在 `/etc/neutron/plugins/ml2/ml2_conf.ini` 中：
 
```ini 
[ml2_type_vlan]
network_vlan_ranges = physnet1:1000:2000 
 
[ovs]
bridge_mappings = physnet1:br-eth1 
```
 
3.2 配置计算节点 
在每个计算节点的 `/etc/neutron/plugins/ml2/openvswitch_agent.ini` 中：
 
```ini 
[ovs]
bridge_mappings = physnet1:br-eth1 
```
 
4. 服务重启 
```bash 
systemctl restart neutron-server 
systemctl restart neutron-openvswitch-agent 
```
 
四、高级配置 
 
1. 多交换机配置 
```ini 
[genericswitch:switch1]
device_type = netmiko_cisco_ios 
ip = *********** 
username = admin 
password = password123 
 
[genericswitch:switch2]
device_type = netmiko_juniper 
ip = *********** 
username = admin 
password = password123 
```
 
2. 自定义命令模板 
可以覆盖默认的命令模板以适应特殊设备：
 
```ini 
[genericswitch:switch1]
...
add_vlan = config terminal;vlan {vlan_id};name {segmentation_id};end 
```
 
3. 故障排查配置 
```ini 
[genericswitch:switch1]
...
debug = True 
logging = True 
```
 
五、验证与测试 
 
1. 检查日志 
```bash 
tail -f /var/log/neutron/server.log 
```
 
2. 创建测试网络 
```bash 
openstack network create --provider-physical-network physnet1 \
                         --provider-network-type vlan \ 
                         --provider-segment 1001 test-net 
```
 
3. 检查交换机配置 
登录交换机验证 VLAN 和端口配置是否已更新。
 
六、常见问题解决 
 
1. 连接失败
   - 检查 IP、用户名和密码 
   - 验证网络连通性 
   - 检查防火墙设置 
 
2. 命令执行失败
   - 确认设备类型选择正确 
   - 检查交换机权限 
   - 尝试启用 debug 模式查看详细日志 
 
3. VLAN 不生效
   - 确认 bridge_mappings 配置正确 
   - 检查物理端口是否已正确绑定 
 
七、性能优化建议 
 
1. 对于大规模部署，考虑使用 NETCONF 而不是 SSH/CLI 
2. 启用连接池减少连接建立开销 
3. 对于频繁变更的环境，适当调整轮询间隔 
 
通过以上配置，OpenStack Generic Switch 可以实现虚拟网络与物理网络的无缝集成，为混合云环境提供灵活的网络解决方案。


## 问：如果我的是混合云模式，有一部分旧网段不需要Generic Switch 进行自动化纳管，是否支持一定的vlan段进行自动化管理？
OpenStack Generic Switch 在混合云中的选择性 VLAN 管理 
 
是的，OpenStack Generic Switch 完全支持在混合云环境中只对特定 VLAN 段进行自动化管理，而保留旧网段不受干扰。以下是具体实现方案：
 
一、选择性 VLAN 管理的实现方式 
 
1. 通过 network_vlan_ranges 限定管理范围 
 
在 `/etc/neutron/plugins/ml2/ml2_conf.ini` 中配置：
 
```ini 
[ml2_type_vlan]
只自动管理 1000-2000 的 VLAN，旧 VLAN(如1-999)不会被触碰 
network_vlan_ranges = physnet1:1000:2000 
```
 
2. 端口级控制（更精细的管理）
 
在交换机配置中指定哪些端口参与自动化管理：
 
```ini 
[genericswitch:switch1]
...
managed_ports = GigabitEthernet1/0/1-24  # 只管理这些端口 
excluded_ports = GigabitEthernet1/0/25-48 # 明确排除的端口 
```
 
二、混合云场景的具体配置方案 
 
1. 保留旧 VLAN 的配置示例 
 
假设：
- 旧 VLAN 范围：1-999（手工管理）
- 新 VLAN 范围：1000-2000（自动管理）
 
`ml2_conf.ini` 配置：
 
```ini 
[ml2]
tenant_network_types = vlan 
mechanism_drivers = openvswitch,genericswitch 
 
[ml2_type_vlan]
network_vlan_ranges = physnet1:1000:2000  # 只声明需要自动管理的范围 
 
[securitygroup]
firewall_driver = neutron.agent.linux.iptables_firewall.OVSHybridIptablesFirewallDriver 
```
 
2. 物理网络映射配置 
 
在计算节点 `/etc/neutron/plugins/ml2/openvswitch_agent.ini` 中：
 
```ini 
[ovs]
bridge_mappings = physnet1:br-eth1 
 
[agent]
prevent_arp_spoofing = True 
```
 
三、创建网络时的控制方法 
 
1. 通过 CLI 创建网络时指定范围 
 
```bash 
创建自动管理的网络（会在物理交换机上自动配置）
openstack network create \
  --provider-physical-network physnet1 \
  --provider-network-type vlan \
  --provider-segment 1001 \
  auto-managed-net 
 
创建手工管理的网络（不会触发Generic Switch配置）
openstack network create \
  --provider-physical-network physnet1 \
  --provider-network-type vlan \
  --provider-segment 100 \  # 在1000以下的VLAN 
  manual-net 
```
 
2. 通过 Dashboard 控制 
 
在 Horizon 面板创建网络时：
- 选择 "Provider Network" 类型 
- 物理网络选择配置了 Generic Switch 的 physnet1 
- 只有输入 1000-2000 的 VLAN ID 才会触发自动化配置 
 
四、验证配置是否生效 
 
1. 检查已创建的 VLAN 
   ```bash 
   openstack network list --long -c Name -c "Provider Network Type" -c "Provider Segmentation ID"
   ```
 
2. 物理交换机验证
   - 登录交换机查看 VLAN 配置 
   - 确认只有 1000-2000 的 VLAN 被自动创建 
   - 确认旧 VLAN 配置未被修改 
 
3. Generic Switch 日志检查
   ```bash 
   grep "Genericswitch" /var/log/neutron/server.log 
   ```
   应该只看到 1000-2000 VLAN 的操作日志 
 
五、高级场景：部分端口参与管理 
 
如果需要在同一交换机上实现部分端口自动化、部分端口手工管理：
 
```ini 
[genericswitch:switch1]
device_type = netmiko_cisco_ios 
ip = *********** 
username = admin 
password = password123 
managed_ports = Gi1/0/1-12,Gi1/0/24  # 只管理这些端口 
```
 
六、注意事项 
 
1. ID 冲突预防：确保自动管理的 VLAN ID 不会与手工管理的 ID 冲突 
2. 权限隔离：Generic Switch 使用的账户应该有适当权限限制 
3. 变更管理：建议对旧 VLAN 添加注释/标记，避免误操作 
4. 监控报警：设置监控确保自动管理不会影响到手工配置的 VLAN 
 
通过以上配置，您可以实现精细化的 VLAN 管理策略，在混合云环境中既能享受自动化带来的便利，又能保留对传统网络配置的完全控制。


## 问: ml2_type_vlan 此配置在哪个组件和代码内调用，方便验证？
`ml2_type_vlan` 配置的调用机制及验证方法 
 
一、配置调用的核心组件 
 
`ml2_type_vlan` 配置主要在以下 OpenStack 组件中被调用和处理：
 
1. Neutron Server (核心服务)
   - `neutron/plugins/ml2/drivers/type_vlan.py` (主处理逻辑)
   - `neutron/plugins/ml2/plugin.py` (ML2 插件入口)
 
2. ML2 插件机制
   - 通过 `TypeManager` 加载 VLAN 类型驱动 
 
3. OVS Agent (实现层)
   - `neutron/plugins/ml2/drivers/openvswitch/agent/agent.py`
 
二、关键代码调用路径 
 
1. 初始化阶段调用 
 
`neutron/plugins/ml2/plugin.py`:
```python 
def __init__(self):
    self.type_manager = TypeManager()
    self.type_manager.initialize()  # 这里加载所有type driver包括vlan 
```
 
`neutron/plugins/ml2/managers.py`:
```python 
def initialize(self):
    for driver in cfg.CONF.ml2.type_drivers:
        if driver == 'vlan':
            self.drivers['vlan'] = importutils.import_object(
                'neutron.plugins.ml2.drivers.type_vlan.VlanTypeDriver')
```
 
2. 网络创建时调用 
 
`neutron/plugins/ml2/drivers/type_vlan.py`:
```python 
def reserve_provider_segment(self, session, segment):
    # 这里会检查network_vlan_ranges配置 
    vlan_ranges = self._get_vlan_ranges(segment[api.PHYSICAL_NETWORK])
    # 验证请求的VLAN是否在配置范围内 
```
 
3. 配置加载位置 
 
`neutron/plugins/ml2/config.py`:
```python 
def register_ml2_plugin_opts(cfg.CONF):
    cfg.CONF.register_opts(ml2_type_vlan_opts, "ml2_type_vlan")
    # 注册ml2_type_vlan配置组 
```
 
三、验证方法 
 
1. 代码级验证 
 
```bash 
查找所有引用 ml2_type_vlan 的代码 
grep -r "ml2_type_vlan" /usr/lib/python3.6/site-packages/neutron/
 
特别关注以下文件 
/usr/lib/python3.6/site-packages/neutron/plugins/ml2/config.py 
/usr/lib/python3.6/site-packages/neutron/plugins/ml2/drivers/type_vlan.py 
```
 
2. 运行时验证 
 
方法1：日志追踪 
```bash 
在neutron-server节点上 
tail -f /var/log/neutron/server.log | grep -i vlan 
 
创建测试网络时观察日志 
openstack network create --provider-physical-network physnet1 \
                         --provider-network-type vlan \
                         --provider-segment 1001 test-vlan-net 
```
 
方法2：调试端点 
```python 
通过Python调试器注入(需开发环境)
from neutron.plugins.ml2 import plugin as ml2_plugin 
from neutron.plugins.ml2 import managers 
 
获取当前VLAN配置 
vlan_driver = ml2_plugin.plugin.type_manager.drivers['vlan']
print(vlan_driver._networks)  # 显示当前VLAN分配情况 
```
 
3. 数据库验证 
```sql 
-- 连接到Neutron数据库 
SELECT * FROM ml2_network_segments WHERE network_type = 'vlan';
```
 
四、核心处理逻辑详解 
 
`neutron/plugins/ml2/drivers/type_vlan.py` 中的关键方法：
 
```python 
def _get_vlan_ranges(self, physical_network):
    """从配置中获取指定物理网络的VLAN范围"""
    return self.vlan_ranges.get(physical_network, [])
    
def reserve_provider_segment(self, session, segment):
    """保留VLAN段的核心方法""" 
    vlan_id = segment[api.SEGMENTATION_ID]
    if not self.validate_provider_segment(segment):  # 这里会检查配置范围 
        raise exc.InvalidInput(error_message="VLAN ID不在允许范围内")
```
 
五、配置覆盖范围 
 
该配置影响以下操作：
1. 创建 provider network (`openstack network create --provider-network-type vlan`)
2. 自动分配 VLAN ID (当不指定--provider-segment时)
3. 网络迁移和更新操作 
4. 网络删除时的资源释放 
 
六、典型问题排查 
 
1. VLAN 无法创建
   ```bash 
   # 检查配置是否加载 
   neutron-debug config-show | grep ml2_type_vlan 
   
   # 检查日志中的错误 
   grep "VlanTypeDriver" /var/log/neutron/server.log 
   ```
 
2. 范围验证失败
   ```python 
   # 通过Python交互验证 
   from neutron.plugins.ml2.drivers.type_vlan import VlanTypeDriver 
   driver = VlanTypeDriver()
   driver.vlan_ranges  # 显示当前加载的VLAN范围 
   ```
 
通过以上方法，您可以全面验证 `ml2_type_vlan` 配置的加载和使用情况，确保VLAN自动化管理按预期工作。
