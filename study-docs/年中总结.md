1.【交换机管理】完成交换机管理的可行性调研并形成报告、后端交换机管理的数据库字段设计、前端关系类数据（如交换机接口连接关系、资源关系图）展示选型及Demo开发、后端交换机管理NetmikoSdk验证（可行）。
1.【云平台多地域管理功能优化】完成主机工单关联自动创建主机等异步任务多地域支持；公网IP及策略关联性多地域支持。
2. 【云平台多地域管理功能前端适配优化】完成主机创建、申请订单、规格、镜像、网络、公网IP及策略、等页面支持地域查询及验证，跟随所选地域自动关联查询。
1.【云平台多地域管理功能优化】完成OpenstackSdk内可用节点验证、查询时加入验证地域信息+资源UUID的方式、定时同步Openstack资源任务适配多地域。（由于牵扯到异步任务的默认节点为node='金华，出于安全、及防止脏数据考虑，暂不发布到生产环境，预计下周可发布到生产环境）。
2. 【全自动裸机注册】完成初步调研，mac-address通过交换机查询时，已thunk的主机无法获取第二个主mac地址，最终方案可采用和公网管理类似的方式，通过Chaos平台进行配置创建及自动回收，在thunk前获取两个主地址。
3. 1.【裸机节点管理】完成前端功能适配动作测试，根据状态显示可进行的操作，包含：配置维护、开机、关机、重启、移动至Available、移动至Manageable、删除节点动作。
2.【裸机节点注册】优化裸机节点注册任务为异步任务（每台注册完等待2~5秒），当有注册任务后自动触发更新裸机节点的同步openstack数据的任务。
3. 【报修记录】完成初版v1.0报修记录前后端开发。
4. 【核心资源概览展示】优化Buffer池主机分类图表当数据量较少时，图表的Y轴间隔小于1的问题优化，优化最小间隔为1。
5. 1.【裸机节点管理】前端功能适配动作已完成动作显示（由于暂无测试机器，目前所有操作未真正对接到openstack），包含：配置维护、开机、关机、移动至Available、删除节点、检查节点动作。
2.【裸机节点注册】由于注册功能需要满足批量注册功能（记录完整的注册日志及注册任务状态），注册裸机节点任务独立出来，已完成前后端功能对接。
3. 【钉钉群通知配置】由于钉钉群机器人通知频率限制，需优化钉钉群为非内部群，已完成优化配置。
4. 【核心资源展示】交付频率页优化，最后一个数据点为截止查询当日那个月的交付次数。
5. 1. 【合同管理】完成自定义导出Execl功能，目前已完成根据数据库模型自定义配置并导出数据，指定列的扩展导出功能SDK开发，前端正在适配中。
2.【合同管理】 完成合同功能财务、销售主管、销售角色等权限测试、及用户操作流程测试。
3. 【主机页面优化】完成云主机、裸金属两个页面分类展示优化。
4. 【裸机节点管理】前端功能适配动作，完成进度 70%，后端批量注册管理数据模型开发（用于记录原始配置信息、记录注册任务日志、任务进度）
5. 1. 【裸机节点管理】完成后端完整注册流程SDK及API开发，支持创建完成后验证裸机节点验证及状态配置；优化裸机节点注册active状态的主机禁止更改状态（如：manageable、enroll、avaliable）。
2.【合同管理】 完成日志展示功能指定列跳过记录的优化，合同管理前端日志功能展示；账期数据已从列表数据独立为数据库单表关联数据优化。
3. 【合同管理】自定义导出Execl功能，目前已完成根据数据库模型自定义配置并导出数据，指定列的扩展导出功能正在开发中。整体进度70%。
4. 【合同管理】功能、权限测试。发现【审核】【编辑】按钮权限无法正常显示及隐藏，问题已找到正在优化中。
1. 完成裸机节点通过API创建调研及测试，验证无问题，已在测试环境通过API注册裸机节点。
2. 完成合同管理后端日志功能优化，账期数据数据存储优化、定时通知任务规划优化。
3. 合同管理功能、主机管理锁定/解锁功能，发布到生产环境。
4. 1. 报表模块：完成报表页历史数据功能开发，加入保存按钮，报表管理页跳转后显示保存的历史数据。
2. 报表模块：完成以租户为维度的资源概览开发，并对接报表导出、保存历史数据的功能。
3. 报表模块：完成优化上海裸机监控的数据对接，并可以按机房生成对应的数据。
4. 优化裸金属资源页在删除时，确认删除对话框内显示公网IP（若存在公网IP）。