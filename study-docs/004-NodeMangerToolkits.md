NVM（Node Version Manager）是一个跨平台的工具，用于轻松地在多个Node.js版本之间切换。以下是NVM的安装和使用的详细步骤，分为Windows和类Unix系统（如Linux和macOS）：

### Windows

#### 安装NVM for Windows

1. 访问NVM for Windows的GitHub页面：https://github.com/coreybutler/nvm-windows
2. 下载最新的`nvm-setup.zip`文件。
3. 解压缩下载的文件。
4. 运行`nvm-setup.exe`安装程序。
5. 在安装过程中，选择NVM的安装路径和Node.js的安装路径。确保路径名称中不要有空格。
6. 完成安装。

#### 使用NVM

- 打开命令提示符或PowerShell。
- 输入 `nvm` 来查看NVM的版本和一些帮助信息。
- 使用 `nvm ls` 来查看已安装的Node.js版本。
- 使用 `nvm install <version>` 来安装特定版本的Node.js。
- 使用 `nvm use <version>` 来切换到特定版本的Node.js。
- 使用 `nvm uninstall <version>` 来卸载特定版本的Node.js。

### 类Unix系统（Linux/macOS）

#### 安装NVM

1. 打开终端。
2. 使用以下命令安装NVM（确保你的系统中已经安装了curl或wget）：

   - 使用curl：
     ```bash
     curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
     ```
   - 使用wget：
     ```bash
     wget -qO- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
     ```

3. 关闭并重新打开终端窗口，或运行以下命令使更改生效：
   ```bash
   source ~/.bashrc
   ```
   或者如果是ZSH shell：
   ```bash
   source ~/.zshrc
   ```

#### 使用NVM

- 输入 `nvm` 或 `nvm --help` 来查看NVM的版本和一些帮助信息。
- 使用 `nvm ls` 来查看已安装的Node.js版本。
- 使用 `nvm install <version>` 来安装特定版本的Node.js。
- 使用 `nvm use <version>` 来切换到特定版本的Node.js。
- 使用 `nvm uninstall <version>` 来卸载特定版本的Node.js。

### 配置环境变量

在安装NVM后，你可能需要确保Node.js和NPM的路径被正确地添加到了环境变量中。通常情况下，NVM会自动处理这些设置，但在某些系统或配置中，你可能需要手动编辑`.bashrc`、`.zshrc`或Windows的环境变量设置来确保`$PATH`包含NVM的bin目录。

以上步骤应能帮助你顺利地在你的操作系统上安装和使用NVM。如果你在使用过程中遇到任何问题，NVM的GitHub页面上有详细的文档和社区支持。