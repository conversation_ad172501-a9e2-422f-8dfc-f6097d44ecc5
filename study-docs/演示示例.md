# 演示示例
1. 核心概念
2. 流程图
3. 业务逻辑
4. 演示场景
5. 答疑解惑


## 1. 示例-演示公网IP管理
### 核心概念
1. 公网IP池管理
    - 未被使用的公网IP
    - 被使用的公网IP
2. 公网策略管理
    - 策略类型：
      - 全端口开放（订单-创建测试类主机）
      - 指定端口开放（生产环境-安全策略）
3. 对接防火墙
    1. 调用策略采用权限模式最小化，及REST-Conf 调用，权限颗粒度最小，进授权

### 业务逻辑


### 演示场景
1. 在订单创建测试类主机，选择公网IP，会自动创建一个策略，并把该全端口开放策略绑定到该主机上。
2. 在生产环境-安全策略管理中，选择公网IP，选择主机，会自动创建一个策略，并把该指定端口策略绑定到该主机上（若之前创建了全端口开放策略，则会自动删除全端口开放策略，并把该指定端口策略绑定到该主机上）。
