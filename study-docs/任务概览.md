# 任务概览分类
## 报表模块
### 按功能分类-报表模块需要满足的功能
1. 展示组件
    - 图表展示：柱状图、饼状图、折线图、仪表盘
    - 统计展示
    - Execl报表展示
2. 功能组件
    - 导出为PDF
    - 导出为Execl
    - 导出为Word
    - 导出为HTML（可交互）- 如：监控数据可以点击展示不同时间段的数据（较为复杂，暂不考虑）
    - 报告持久化与重新展示
### 按页面分类
1. 资源总览页
    - 机房/包间数
    - 总机柜数
    - 总服务器数
2. IDC资源水位页
    - **已上线服务器资源表，即online表**（数据来源：运维管理→主机管理，主机状态：启用。tips：是否应该去除我们用来做功能测试使用的机器）
    - **空闲服务器资源表，即Buffer资源池表**（数据来源：运维管理→主机管理，主机状态：空闲+下架）
    - **业务在线率/服务器出租率**（数据来源：online表/服务器总量）
    - **已出租机柜数**、**闲置机柜数**、**机柜使用率**(仪表板/饼状图展示<非紧急>)
    - **机柜合计总功率**、**机柜实际已用总功率**、**资源利用率**(仪表板/饼状图展示<非紧急>)
3. 客户资源使用页
   - **客户资源清单：**按客户分类，统计每个客户使用的服务器列表（仅展示客户名下资源）
   - **资源占用统计：**按客户分类，统计每个客户占用的资源比例，以及TOP3 (或TOP5) 客户排名
4. 订单统计页
   - **资源交付时长：**资源从订单发起至订单完成的时间，各阶段的耗时<非必须>
   - **资源交付数量：**每周/月交付资源类型及台数
5. 运维报表页(待商榷是否需要展示？)
    - 维护记录、月度故障次数、故障率等
6. 财务营收报表分析页（待商榷）
    - **收入统计：**按客户/机柜角度统计，汇总 总收入（可能涉及到合同金额，需销售/财务提供）
    - **成本分析：**按客户/机柜角度统计，平均成本情况

### 总结
1. 报表模块以总资源为维度统计展示已上线服务资源使用情况，以及资源使用率。
2. 报表模块以客户为维度统计展示客户资源使用情况，以及资源使用率。
3. 平台适配常见报表展示组件，如：柱状图、饼状图、折线图、仪表盘。

## 运维裸机节点管理及裸金属相关内容详情的展示及跳转优化
1. 裸金属详情(通过tab页切换展示不同模块的内容)<核心内容>
    - 裸金属详情
    - 裸金属节点（BMC）详情
    - 公网映射详情
    - 软件安装日志详情(通过订单管理创建的裸金属)
    - 通过webssh 登录后操作（此操作类似通过jump-server连接（稳定性待验证，适合查看日志，等一般性操作，重要操作不推荐使用），此方法需要考虑是否需要支持）
    - 必要监控数据关联 监控数据展示（如：CPU/内存/硬盘/网卡/GPU/温度/）`待商榷及验证`
2. 裸金属节点管理
    - 裸金属节点详情
    - 裸金属节点列表
    - 裸金属操作功能规范（如：已分配的节点禁止修改`配置状态`）
    - 批量操作`维护状态`（配置后就不会再被分配调度了，在指定调度主机时,需要批量修改）
    - 批量操作`配置状态`（配置后，可以再次被分配调度）
    - 创建/批量导入裸金属节点（规划中）
3. 运维管理->主机管理
   - 主机详情页（可通过裸金属页面跳转）
       - 机柜详细信息关联
       - 资产管理->服务器详情关联（可通过主机详情页跳转）
4. 租户相关优化
   - 裸金属节点详情页（可通过裸金属页面跳转)适配租户
   - 报告内容优化（主机数据添加GPU详细信息）
   - 报告文件优化为PDF(加水印)

### 总结
1. 裸金属详情页展示核心内容，通过tab页切换展示不同模块的内容。
2. 裸金属关联的资产信息、运维信息、裸机节点信息，通过关联跳转展示。
3. 租户订单报告及可查看的内容优化。
