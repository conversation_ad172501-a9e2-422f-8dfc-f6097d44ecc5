"""
旧 OPA 项目下，主机数据迁移到 Chaos 平台内
"""
import datetime
import json
import os
import sys
import django


# 设置Django环境
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "application.settings")
django.setup()


from operatorcmdb.models import Host
from dvadmin.system.models import Users
from xresource.models import MachineRoom, PrivateRoom, IDCRackMachine, PhysicalServerMachine
from customer.models import Customer


class MigrateHosts(object):
    def __init__(self, backup_filename=None):
        self.backup_filename = backup_filename
        self.list_data = []

    def read_file(self):
        with open(self.backup_filename, 'r', encoding='utf-8') as f:
            temp_data = f.read()
            data = json.loads(temp_data)['rows']
        self.list_data = data

    def format_data(self):
        formatted_data = []
        user_info = Users.objects.get(id=1)
        for item in self.list_data:
            # start_time 特殊处理
            start_time = None
            if item['start_time']:
                if 'T' in item['start_time'] and 'Z' in item['start_time']:
                    start_time = datetime.datetime.strptime(item['start_time'], '%Y-%m-%dT%H:%M:%S.000Z')
                    start_time = start_time + datetime.timedelta(hours=8)
                    start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    start_time = datetime.datetime.strptime(item['start_time'], '%Y-%m-%d %H:%M:%S')
                    start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
            t_machine_room = item['idc']
            machine_room = None
            if t_machine_room:
                if 'EM101' in t_machine_room:
                    t_machine_room = '金华移动EM101机房'
                if 'ET100' in t_machine_room:
                    t_machine_room = '上海临港ET100机房'
                machine_room_obj = MachineRoom.objects.filter(name=t_machine_room).first()
                machine_room = machine_room_obj.id

            t_private_room = item['room']
            private_room = None
            if t_private_room:
                private_room_obj = PrivateRoom.objects.filter(name=t_private_room).first()
                private_room = private_room_obj.id

            t_idc_rack_machine = item['cabinet']
            idc_rack_machine = None
            if t_idc_rack_machine:
                idc_rack_machine_obj = IDCRackMachine.objects.filter(
                    rack_sn='{idc_rack}.{private_room}'.format(
                        private_room=t_private_room, idc_rack=t_idc_rack_machine
                    )
                ).first()
                if not idc_rack_machine_obj:
                    print('IDCRackMachine not found: ', t_idc_rack_machine)
                else:
                    idc_rack_machine = idc_rack_machine_obj.id

            t_physical_server_machine = item['sn']
            physical_server_machine = None
            not_found_sn = ''
            if t_physical_server_machine:
                physical_server_machine_obj = PhysicalServerMachine.objects.filter(
                    physical_machine_sn=t_physical_server_machine
                ).first()
                if not physical_server_machine_obj:
                    print('PhysicalServerMachine not found: ', t_physical_server_machine)
                    not_found_sn = t_physical_server_machine
                else:
                    physical_server_machine = physical_server_machine_obj.id

            t_customer_id = item['name']
            customer_id = None
            if t_customer_id:
                customer_obj = Customer.objects.filter(description=t_customer_id).first()
                if not customer_obj:
                    print('Customer not found: ', t_customer_id)
                else:
                    customer_id = customer_obj.id
            desc = None
            if not_found_sn:
                desc = item['comment'] or '' + '服务器资产关联失败: 旧-><{sn}>'.format(sn=not_found_sn)

            item_data = {
                'instance_id': item['instance_id'],
                'area': item['area'],
                'name': item['instance_id'],
                'description': desc,
                'host_type': item['type'],
                'ip_private': item['ip_private'],
                'ip_public': item['ip_public'],
                'ip_bmc': item['ip_bmc'],
                'kvm_ip_host': item['ip_kvm_host'],
                'bmc_user': item['bmc_user'],
                'bmc_passwd': item['bmc_passwd'],
                'host_status': item['status'],
                'os': item['os'],
                'cpu': item['cpu'],
                'mem': item['mem'],
                'sys_disk': item['sys_disk'],
                'data_disk': item['data_disk'],
                'gpu_model': item['gpu_model'],
                'gpu_count': item['gpu_count'],
                'ssh_port': item['ssh_port'],
                'ssh_user': item['ssh_user'],
                'ssh_passwd': item['ssh_passwd'],
                'start_time': start_time,
                'expire_time': item['expire_time'] or None,
                'is_buffer': item['is_buffer'] or False,
                'is_ipmi_mon': item['ipmi_mon'] or False,
                'is_system_mon': item['node_mon'] or False,
                'is_deleted': item['is_deleted'] or False,
                'create_datetime': item['create_time'] or None,
                'update_datetime': item['update_time'] or None,
                'dept_belong_id': 5,

                'machine_room_id': machine_room,
                'private_room_id': private_room,
                'idc_rack_machine_id': idc_rack_machine,
                'physical_server_machine_id': physical_server_machine,
                'customer_id': customer_id,
            }

            had = Host.objects.filter(instance_id=item_data['instance_id']).first()

            if had:
                continue

            obj = Host(**item_data)
            obj.save()
        return formatted_data

    def run_main(self):
        directory_path = os.path.dirname(os.path.abspath(__file__))
        sql_file = os.path.join(directory_path, '20240909-hosts-v2.json')
        self.backup_filename = sql_file
        self.read_file()
        self.format_data()


if __name__ == '__main__':
    MigrateHosts().run_main()
