from abc import ABC, abstractmethod
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
from calendar import monthrange
from collections import namedtuple
from django.db.models import Q
from django.utils.timezone import now
from typing import Callable

from dvadmin.utils.query_to_obj import query2Obj
from operatorcmdb.models import Host, HostMaintenance, ReliabilityMetrics


MaintenanceStats = namedtuple('MaintenanceStats', ['total_downtime', 'total_repair_time', 'fault_count'])


def get_current_month_runtime_hours() -> float:
    now = datetime.now()
    start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    elapsed = now - start_of_month
    return elapsed.total_seconds() / 3600


class MaintenanceStatsFetcher:
    UNIT_CONVERSION = {
        "秒": 1 / 3600,
        "分": 1 / 60,
        "时": 1,
    }
    RESOURCE_UNIT_MAP = {
        "显卡": 8,
        "磁盘": 1
    }

    def __init__(self, issue_type: str, start_time: datetime, end_time: datetime):
        self.issue_type = issue_type
        self.start_time = start_time
        self.end_time = end_time

    def get_stats(self) -> MaintenanceStats:
        queryset = HostMaintenance.objects.filter(
            issue_type=self.issue_type,
            start_resolve_time__gte=self.start_time,
            start_resolve_time__lte=self.end_time
        ).only("start_resolve_time", "end_resolve_time", "downtime_duration", "downtime_duration_unit")

        total_downtime = 0
        total_repair_time = 0
        fault_count = 0

        for item in queryset:
            fault_count += 1
            unit = item.downtime_duration_unit or "时"
            factor = self.UNIT_CONVERSION.get(unit.strip(), 1)
            downtime = (item.downtime_duration or 0) * factor

            if item.start_resolve_time and item.end_resolve_time:
                repair_hours = (item.end_resolve_time - item.start_resolve_time).total_seconds() / 3600
            else:
                repair_hours = downtime  # fallback

            total_downtime += downtime
            total_repair_time += repair_hours

        # TODO: 这里只是简单的单位转换，后续想得到更精准的计算可以拓展BaseReliabilityCalculator类中的calculate_host_resource...方法,这样可以根据某一BMC某一设备的数量和维修时间得到总的停机、维修时间
        total_downtime = total_downtime * self.RESOURCE_UNIT_MAP.get(self.issue_type, 1)
        total_repair_time = total_repair_time * self.RESOURCE_UNIT_MAP.get(self.issue_type, 1)
        return MaintenanceStats(
            total_downtime=round(total_downtime, 2),
            total_repair_time=round(total_repair_time, 2),
            fault_count=fault_count
        )


class BaseReliabilityCalculator(ABC):
    """
    可靠性指标抽象类，子类应实现 calculate_reliability 方法
    """
    # 故障次数仅为 1 时的保守修正系数
    FAULT_MTTF_REDUCTION_FACTOR = 0.9
    FAULT_AVAILABILITY_REDUCTION = 0.98

    def __init__(self, metrics_type: str, maintenance_type: str):
        """
        初始化可靠性计算器
        :param metrics_type: 可靠性指标类型（如 "4090显卡"）
        :param maintenance_type: 维护类型（如 "显卡"）
        """
        self.metrics_type = metrics_type
        self.maintenance_type = maintenance_type
        self.elapsed_hours = get_current_month_runtime_hours()
        self.start_of_month, self.end_of_month = self._get_current_month_range()
        self._maintenance_stats = None

    def _get_current_month_range(self):
        now = datetime.now()
        start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        last_day = monthrange(now.year, now.month)[1]
        end = now.replace(day=last_day, hour=23, minute=59, second=59, microsecond=999999)
        return start, end

    def get_maintenance_stats(self):
        if not self._maintenance_stats:
            self._maintenance_stats = MaintenanceStatsFetcher(
                issue_type=self.maintenance_type,
                start_time=self.start_of_month,
                end_time=self.end_of_month
            ).get_stats()
        return self._maintenance_stats

    def get_all_reliability(self) -> dict:
        self.calculate_reliability()
        items = query2Obj(ReliabilityMetrics.objects.filter(metrics_type=self.metrics_type, is_deleted=False))
        for item in items:
            item['time'] = item['create_datetime'].strftime("%Y-%m") 
        return items

    def calculate_metrics(self, total_runtime: float, total_resource_count:int, stats: MaintenanceStats) -> dict:
        if stats.fault_count:
            mttf = (total_runtime - stats.total_downtime) / stats.fault_count
            mttr = stats.total_repair_time / stats.fault_count
            availability = total_runtime / (total_runtime + stats.total_downtime)

            if stats.fault_count == 1:
                # 仅一次故障时应用修正系数
                mttf *= self.FAULT_MTTF_REDUCTION_FACTOR
                availability *= self.FAULT_AVAILABILITY_REDUCTION
        else:
            mttf = total_runtime
            mttr = 0.0
            availability = 1.0

        mtbf = mttf + mttr
        obj,create = ReliabilityMetrics.objects.update_or_create(
            defaults={
                'mttf': self._round(mttf),
                'mttr': self._round(mttr),
                'mtbf': self._round(mtbf),
                'availability': self._round(availability),
                'total_resource_count': total_resource_count,
                'total_runtime': self._round(total_runtime),
                'total_fault_count': stats.fault_count,
                'total_downtime': self._round(stats.total_downtime),
                'total_repair_time': self._round(stats.total_repair_time),
            },
            metrics_type=self.metrics_type,
            create_datetime__gte=self.start_of_month
        )
        return query2Obj(obj)

    def calculate_host_resource_runtime(self, get_resource_unit: Callable[[Host], int]) -> float:
        """
        统一逻辑：遍历主机，考虑下架时间，按资源单位数量统计总运行时间
        `get_resource_unit(host)`：获取每台主机的资源数量（如 GPU 数）
        """
        start_of_month = self.start_of_month

        alive_hosts = Host.objects.filter(is_deleted=False, host_type="裸金属")
        removed_hosts = Host.objects.filter(
            is_deleted=True, host_type="裸金属", update_datetime__gte=start_of_month
        )

        total_resource_count = 0
        total_runtime = 0.0

        for host in alive_hosts:
            resource_units = get_resource_unit(host)
            total_runtime += resource_units * self.elapsed_hours
            total_resource_count += resource_units

        for host in removed_hosts:
            resource_units = get_resource_unit(host)
            delta = (host.update_datetime - start_of_month).total_seconds() / 3600
            delta = min(delta, self.elapsed_hours)
            total_runtime += resource_units * delta
            total_resource_count += resource_units

        return round(total_runtime, 2),total_resource_count

    def _round(self, value, precision=2):
        return Decimal(value).quantize(Decimal(f'1.{"0"*precision}'), rounding=ROUND_HALF_UP)

    @abstractmethod
    def calculate_reliability(self): ...


class GPU4090ReliabilityCalculator(BaseReliabilityCalculator):
    """
    子类只需计算不同资源的总运行时间
    """
    def calculate_reliability(self):
        def get_gpu_count(host: Host) -> int:
            return 8 if host.gpu_model == "4090" else 0  # 所有主机 GPU 固定为 8 块

        total_gpu_runtime,total_gpu_count = self.calculate_host_resource_runtime(get_gpu_count)
        stats = self.get_maintenance_stats()
        return self.calculate_metrics(total_gpu_runtime,total_gpu_count,stats)


class GPU3090ReliabilityCalculator(BaseReliabilityCalculator):
    """
    子类只需计算不同资源的总运行时间
    """
    def calculate_reliability(self):
        def get_gpu_count(host: Host) -> int:
            return 8 if host.gpu_model == "3090" else 0  # 所有主机 GPU 固定为 8 块

        total_gpu_runtime,total_gpu_count = self.calculate_host_resource_runtime(get_gpu_count)
        stats = self.get_maintenance_stats()
        return self.calculate_metrics(total_gpu_runtime,total_gpu_count,stats)


class GPUA800ReliabilityCalculator(BaseReliabilityCalculator):
    """
    子类只需计算不同资源的总运行时间
    """
    def calculate_reliability(self):
        def get_gpu_count(host: Host) -> int:
            return 8 if host.gpu_model == "A800-PCIe" else 0  # 所有主机 GPU 固定为 8 块

        total_gpu_runtime,total_gpu_count = self.calculate_host_resource_runtime(get_gpu_count)
        stats = self.get_maintenance_stats()
        return self.calculate_metrics(total_gpu_runtime,total_gpu_count,stats)


class DiskReliabilityCalculator(BaseReliabilityCalculator):
    def calculate_reliability(self):
        def get_disk_count(host: Host) -> int:
            return host.disk_count or 1  # 每台主机磁盘数量可能不同

        total_disk_runtime,total_disk_count = self.calculate_host_resource_runtime(get_disk_count)
        stats = self.get_maintenance_stats()
        return self.calculate_metrics(total_disk_runtime,total_disk_count,stats)


class ReliabilityStrategy(ABC):
    @abstractmethod
    def compute_metrics(self) -> dict:
        ...


class GPU4090ReliabilityStrategy(ReliabilityStrategy):
    def compute_metrics(self):
        calculator = GPU4090ReliabilityCalculator("4090显卡","显卡")
        return calculator.get_all_reliability()


class GPU3090ReliabilityStrategy(ReliabilityStrategy):
    def compute_metrics(self):
        calculator = GPU3090ReliabilityCalculator("3090显卡","3090显卡")
        return calculator.get_all_reliability()


class GPUA800ReliabilityStrategy(ReliabilityStrategy):
    def compute_metrics(self):
        calculator = GPUA800ReliabilityCalculator("A800显卡","A800显卡")
        return calculator.get_all_reliability()


class DiskReliabilityStrategy(ReliabilityStrategy):
    def compute_metrics(self):
        calculator = DiskReliabilityCalculator("磁盘","磁盘")
        return calculator.get_all_reliability()


class ReliabilityContext:
    def __init__(self, strategy: ReliabilityStrategy):
        self.strategy = strategy

    def set_strategy(self, strategy: ReliabilityStrategy):
        self.strategy = strategy

    def get_all_reliability(self):
        return self.strategy.compute_metrics()