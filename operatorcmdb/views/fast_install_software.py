import json

from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import SuccessResponse, ErrorResponse

from operatorcmdb.models import FastInstallSoftware
from operatorcmdb.serializers.fast_install_software import (
    FastInstallSoftwareSerializer,
    FastInstallSoftwareImportSerializer
)


class FastInstallSoftwareViewSet(ChaosCustomModelViewSet):
    """
    快速安装软件包管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = FastInstallSoftware.objects.order_by('-create_datetime')
    serializer_class = FastInstallSoftwareSerializer
    filter_fields = '__all__'

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = FastInstallSoftwareImportSerializer
    import_field_dict = {

    }
    permission_classes = []

    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def get_all_softwaress(self, request, *args, **kwargs):
        """
        获取软件展示列表
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True, request=request)
            data = [{**item, 'current_version': json.loads(item['versions'])[0]['value'] if item['versions'] else '--'} for item in serializer.data]
            return self.get_paginated_response(data)
        serializer = self.get_serializer(queryset, many=True, request=request)

        data = [{**item, 'current_version': json.loads(item['versions'])[0]['value'] if item['versions'] else '--'} for
                item in serializer.data]
        return SuccessResponse(data=data, msg="获取成功")
