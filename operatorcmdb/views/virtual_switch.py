import logging

from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

logger = logging.getLogger(__name__)

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import SuccessResponse, ErrorResponse
import logging

from xresource.models import MachineRoom, PrivateRoom, IDCRackMachine, NetworkHardware
from operatorcmdb.models import (
    VirtualSwitch,
    VirtualSwitchPhysicalInterface,
    VirtualSwitchTrunkInterface,
    VirtualSwitchMacAddress
    )
from operatorcmdb.serializers.virtual_switch import (
    VirtualSwitchSerializer,
    VirtualSwitchPhysicalInterfaceSerializer,
    VirtualSwitchTrunkInterfaceSerializer,
    VirtualSwitchMacAddressSerializer
    )
from operatorcmdb.services.switch_sync_service import switch_sync_service
# 添加Neo4j同步服务的导入
from operatorcmdb.services.neo4j_sync_service import neo4j_sync_service
# 添加拓扑查询服务的导入
from operatorcmdb.services.switch_rel_service import switch_topology_query_service
# 添加G6数据服务的导入
from operatorcmdb.services.g6_data_service import g6_data_service


logger = logging.getLogger(__name__)


class VirtualSwitchViewSet(ChaosCustomModelViewSet):
    """虚拟交换机管理接口"""
    queryset = VirtualSwitch.objects.order_by('-create_datetime')
    serializer_class = VirtualSwitchSerializer
    filter_fields = '__all__'

    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
        }

    import_field_dict = {
        "physical_switch": {
            "title": "物理交换机",
            "choices": {
                "queryset": NetworkHardware.objects.filter(
                    is_deleted=False
                    ), "values_name": "name", "key_name": "id"
                }
            },
        "machine_room": {
            "title": "机房",
            "choices": {
                "queryset": MachineRoom.objects.filter(
                    is_deleted=False
                    ), "values_name": "name", "key_name": "id"
                }
            },
        "private_room": {
            "title": "包间",
            "choices": {
                "queryset": PrivateRoom.objects.filter(
                    is_deleted=False
                    ), "values_name": "name", "key_name": "id"
                }
            },
        "idc_rack_machine": {
            "title": "机柜",
            "choices": {
                "queryset": IDCRackMachine.objects.filter(
                    is_deleted=False
                    ), "values_name": "rack_sn", "key_name": "id"
                }
            },
        }

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def sync_interfaces(self, request, id):
        """同步交换机接口信息"""
        virtual_switch = self.get_object()

        # 调用同步服务
        result = switch_sync_service.sync_switch_data_by_instance(virtual_switch)

        if result.success:
            # 添加Neo4j同步结果
            neo4j_result = None
            try:
                with neo4j_sync_service:
                    neo4j_sync_result = neo4j_sync_service.sync_switch_to_neo4j(virtual_switch.id)
                    neo4j_result = {
                        'success': neo4j_sync_result.success,
                        'message': neo4j_sync_result.message,
                        'nodes_created': neo4j_sync_result.nodes_created,
                        'relationships_created': neo4j_sync_result.relationships_created
                        }
            except Exception as e:
                logger.error(f"同步到Neo4j失败: {str(e)}")
                neo4j_result = {
                    'success': False,
                    'message': f"同步到Neo4j失败: {str(e)}",
                    'nodes_created': 0,
                    'relationships_created': 0
                    }

            return SuccessResponse(
                msg=result.message,
                data={
                    'physical_interfaces': result.physical_interfaces,
                    'trunk_interfaces': result.trunk_interfaces,
                    'mac_addresses': result.mac_addresses,
                    'switch_info': result.switch_info,
                    'neo4j_sync': neo4j_result  # 添加Neo4j同步结果
                    }
                )
        else:
            return ErrorResponse(msg=result.message)

    # 可以添加一个专门同步到Neo4j的接口
    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def sync_to_neo4j(self, request, pk=None):
        """同步交换机数据到Neo4j"""
        virtual_switch = self.get_object()

        try:
            # 调用Neo4j同步服务
            with neo4j_sync_service:
                result = neo4j_sync_service.sync_switch_to_neo4j(virtual_switch.id)

            if result.success:
                return SuccessResponse(
                    msg=result.message,
                    data={
                        'nodes_created': result.nodes_created,
                        'relationships_created': result.relationships_created
                        }
                    )
            else:
                return ErrorResponse(msg=result.message)
        except Exception as e:
            logger.error(f"同步到Neo4j失败: {str(e)}")
            return ErrorResponse(msg=f"同步到Neo4j失败: {str(e)}")

    # 添加批量同步接口
    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated])
    def sync_all_to_neo4j(self, request):
        """批量同步所有交换机到Neo4j"""
        try:
            with neo4j_sync_service:
                result = neo4j_sync_service.sync_all_switches_to_neo4j()

            if result.success:
                return SuccessResponse(
                    msg=result.message,
                    data={
                        'nodes_created': result.nodes_created,
                        'relationships_created': result.relationships_created
                        }
                    )
            else:
                return ErrorResponse(msg=result.message)
        except Exception as e:
            logger.error(f"批量同步到Neo4j失败: {str(e)}")
            return ErrorResponse(msg=f"批量同步到Neo4j失败: {str(e)}")

    # 添加获取交换机拓扑数据的接口
    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def get_topology_data(self, request, id):
        """获取交换机拓扑数据"""
        virtual_switch = self.get_object()

        try:
            topology_data = switch_topology_query_service.get_switch_topology_data(virtual_switch.id)

            if 'error' in topology_data:
                return ErrorResponse(msg=topology_data['error'])

            return SuccessResponse(data=topology_data)
        except Exception as e:
            logger.error(f"获取交换机拓扑数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取拓扑数据失败: {str(e)}")

    # 添加获取机房内交换机拓扑数据的接口
    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def get_room_switch_topology_data(self, request):
        """获取机房内交换机拓扑数据"""
        machine_room_id = request.query_params.get('machine_room_id')

        if not machine_room_id:
            return ErrorResponse(msg="缺少参数: machine_room_id")

        try:
            topology_data = switch_topology_query_service.get_room_switches_topology(machine_room_id)

            if 'error' in topology_data:
                return ErrorResponse(msg=topology_data['error'])

            return SuccessResponse(data=topology_data)
        except Exception as e:
            logger.error(f"获取机房交换机拓扑数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取拓扑数据失败: {str(e)}")

    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def get_g6_data(self, request, id=None):
        """获取交换机G6格式数据"""
        virtual_switch = self.get_object()

        try:
            g6_data = g6_data_service.get_switch_g6_data(virtual_switch.id)

            if 'error' in g6_data:
                return ErrorResponse(msg=g6_data['error'])

            return SuccessResponse(
                msg="获取G6数据成功",
                data=g6_data
            )
        except Exception as e:
            logger.error(f"获取交换机G6数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取G6数据失败: {str(e)}")

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def get_room_g6_data(self, request):
        """获取机房交换机G6格式数据"""
        machine_room_id = request.query_params.get('machine_room_id')

        if not machine_room_id:
            return ErrorResponse(msg="缺少参数: machine_room_id")

        try:
            g6_data = g6_data_service.get_room_switches_g6_data(machine_room_id)

            if 'error' in g6_data:
                return ErrorResponse(msg=g6_data['error'])

            return SuccessResponse(
                msg="获取机房G6数据成功",
                data=g6_data
            )
        except Exception as e:
            logger.error(f"获取机房G6数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取机房G6数据失败: {str(e)}")

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def interfaces(self, request):
        """获取交换机接口信息（兼容原有接口）"""
        switch_id = request.query_params.get('switch_id')

        if not switch_id:
            return ErrorResponse(msg="缺少参数: switch_id")

        try:
            virtual_switch = VirtualSwitch.objects.get(id=switch_id, is_deleted=False)

            # 获取接口数据
            physical_interfaces = VirtualSwitchPhysicalInterface.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            )
            trunk_interfaces = VirtualSwitchTrunkInterface.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            )
            mac_addresses = VirtualSwitchMacAddress.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            )

            # 序列化数据
            physical_data = VirtualSwitchPhysicalInterfaceSerializer(physical_interfaces, many=True).data
            trunk_data = VirtualSwitchTrunkInterfaceSerializer(trunk_interfaces, many=True).data
            mac_data = VirtualSwitchMacAddressSerializer(mac_addresses, many=True).data

            return SuccessResponse(
                msg="获取接口信息成功",
                data={
                    'switch_info': VirtualSwitchSerializer(virtual_switch).data,
                    'physical_interfaces': physical_data,
                    'trunk_interfaces': trunk_data,
                    'mac_addresses': mac_data,
                    'summary': {
                        'physical_count': physical_interfaces.count(),
                        'trunk_count': trunk_interfaces.count(),
                        'mac_count': mac_addresses.count(),
                        'active_physical': physical_interfaces.filter(state='up').count(),
                        'active_trunk': trunk_interfaces.filter(state='up').count()
                    }
                }
            )

        except VirtualSwitch.DoesNotExist:
            return ErrorResponse(msg=f"未找到ID为{switch_id}的虚拟交换机")
        except Exception as e:
            logger.error(f"获取接口信息失败: {str(e)}")
            return ErrorResponse(msg=f"获取接口信息失败: {str(e)}")

    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def get_interface_tree(self, request, id=None):
        """获取交换机接口树形结构数据，支持展开查看MAC地址"""
        virtual_switch = self.get_object()

        try:
            # 获取接口数据
            physical_interfaces = VirtualSwitchPhysicalInterface.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            ).order_by('name')

            trunk_interfaces = VirtualSwitchTrunkInterface.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            ).order_by('name')

            mac_addresses = VirtualSwitchMacAddress.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            ).order_by('interface', 'mac_address')

            # 构建树形结构数据
            tree_data = self._build_interface_tree_data(
                virtual_switch, physical_interfaces, trunk_interfaces, mac_addresses
            )

            return SuccessResponse(
                msg="获取接口树形数据成功",
                data=tree_data
            )

        except Exception as e:
            logger.error(f"获取接口树形数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取接口树形数据失败: {str(e)}")

    def _build_interface_tree_data(self, virtual_switch, physical_interfaces, trunk_interfaces, mac_addresses):
        """构建接口树形结构数据"""

        # 创建MAC地址映射
        mac_map = {}
        for mac in mac_addresses:
            interface_name = mac.interface
            if interface_name not in mac_map:
                mac_map[interface_name] = []
            mac_map[interface_name].append({
                'id': f'mac-{mac.id}',
                'title': mac.mac_address,
                'key': f'mac-{mac.id}',
                'type': 'mac_address',
                'isLeaf': True,
                'icon': 'mac',
                'data': {
                    'mac_address': mac.mac_address,
                    'interface': mac.interface,
                    'last_sync_at': mac.last_sync_at.isoformat() if mac.last_sync_at else None,
                    'node': mac.node
                }
            })

        tree_nodes = []

        # 1. 交换机根节点
        switch_node = {
            'id': f'switch-{virtual_switch.id}',
            'title': virtual_switch.name or f'Switch-{virtual_switch.id[:8]}',
            'key': f'switch-{virtual_switch.id}',
            'type': 'switch',
            'icon': 'cluster',
            'data': {
                'name': virtual_switch.name,
                'management_ip': virtual_switch.management_ip,
                'vendor': virtual_switch.vendor,
                'sn': virtual_switch.sn,
                'last_sync_at': virtual_switch.last_sync_at.isoformat() if virtual_switch.last_sync_at else None,
                'physical_count': physical_interfaces.count(),
                'trunk_count': trunk_interfaces.count(),
                'mac_count': mac_addresses.count()
            },
            'children': []
        }

        # 2. 物理接口分组
        if physical_interfaces.exists():
            physical_group = {
                'id': f'physical-group-{virtual_switch.id}',
                'title': f'物理接口 ({physical_interfaces.count()})',
                'key': f'physical-group-{virtual_switch.id}',
                'type': 'interface_group',
                'icon': 'api',
                'data': {
                    'group_type': 'physical',
                    'total_count': physical_interfaces.count(),
                    'active_count': physical_interfaces.filter(state='up').count()
                },
                'children': []
            }

            # 按速度分组物理接口
            speed_groups = {}
            for interface in physical_interfaces:
                speed = interface.speed or 0
                speed_key = f'{speed}Mbps' if speed > 0 else 'Unknown Speed'

                if speed_key not in speed_groups:
                    speed_groups[speed_key] = []
                speed_groups[speed_key].append(interface)

            # 为每个速度组创建子节点
            for speed_key, interfaces in speed_groups.items():
                speed_group = {
                    'id': f'speed-{speed_key}-{virtual_switch.id}',
                    'title': f'{speed_key} ({len(interfaces)})',
                    'key': f'speed-{speed_key}-{virtual_switch.id}',
                    'type': 'speed_group',
                    'icon': 'thunderbolt',
                    'data': {
                        'speed': speed_key,
                        'interface_count': len(interfaces),
                        'active_count': sum(1 for iface in interfaces if iface.state == 'up')
                    },
                    'children': []
                }

                # 添加具体接口
                for interface in interfaces:
                    interface_children = mac_map.get(interface.name, [])

                    interface_node = {
                        'id': f'physical-{interface.id}',
                        'title': f'{interface.name} ({interface.state or "unknown"})',
                        'key': f'physical-{interface.id}',
                        'type': 'physical_interface',
                        'icon': 'api' if interface.state == 'up' else 'disconnect',
                        'isLeaf': len(interface_children) == 0,
                        'data': {
                            'name': interface.name,
                            'speed': interface.speed,
                            'state': interface.state,
                            'line_state': interface.line_state,
                            'mac_address': interface.mac_address,
                            'last_sync_at': interface.last_sync_at.isoformat() if interface.last_sync_at else None,
                            'node': interface.node,
                            'mac_count': len(interface_children)
                        },
                        'children': interface_children
                    }
                    speed_group['children'].append(interface_node)

                physical_group['children'].append(speed_group)

            switch_node['children'].append(physical_group)

        # 3. 聚合接口分组
        if trunk_interfaces.exists():
            trunk_group = {
                'id': f'trunk-group-{virtual_switch.id}',
                'title': f'聚合接口 ({trunk_interfaces.count()})',
                'key': f'trunk-group-{virtual_switch.id}',
                'type': 'interface_group',
                'icon': 'cluster',
                'data': {
                    'group_type': 'trunk',
                    'total_count': trunk_interfaces.count(),
                    'active_count': trunk_interfaces.filter(state='up').count()
                },
                'children': []
            }

            # 添加聚合接口
            for trunk in trunk_interfaces:
                trunk_children = mac_map.get(trunk.name, [])
                members = trunk.get_members_list()

                # 添加成员接口信息
                member_nodes = []
                for member_name in members:
                    member_interface = physical_interfaces.filter(name=member_name).first()
                    if member_interface:
                        member_node = {
                            'id': f'member-{member_interface.id}',
                            'title': f'{member_name} (成员)',
                            'key': f'member-{member_interface.id}',
                            'type': 'member_interface',
                            'icon': 'link',
                            'isLeaf': True,
                            'data': {
                                'name': member_name,
                                'state': member_interface.state,
                                'speed': member_interface.speed,
                                'parent_trunk': trunk.name
                            }
                        }
                        member_nodes.append(member_node)

                all_children = trunk_children + member_nodes

                trunk_node = {
                    'id': f'trunk-{trunk.id}',
                    'title': f'{trunk.name} ({trunk.state or "unknown"})',
                    'key': f'trunk-{trunk.id}',
                    'type': 'trunk_interface',
                    'icon': 'cluster' if trunk.state == 'up' else 'disconnect',
                    'isLeaf': len(all_children) == 0,
                    'data': {
                        'name': trunk.name,
                        'state': trunk.state,
                        'line_state': trunk.line_state,
                        'mac_address': trunk.mac_address,
                        'members': members,
                        'member_count': len(members),
                        'last_sync_at': trunk.last_sync_at.isoformat() if trunk.last_sync_at else None,
                        'node': trunk.node,
                        'mac_count': len(trunk_children)
                    },
                    'children': all_children
                }
                trunk_group['children'].append(trunk_node)

            switch_node['children'].append(trunk_group)

        tree_nodes.append(switch_node)

        return {
            'tree_data': tree_nodes,
            'summary': {
                'switch_name': virtual_switch.name,
                'physical_interfaces': physical_interfaces.count(),
                'trunk_interfaces': trunk_interfaces.count(),
                'mac_addresses': mac_addresses.count(),
                'active_physical': physical_interfaces.filter(state='up').count(),
                'active_trunk': trunk_interfaces.filter(state='up').count(),
                'last_sync_at': virtual_switch.last_sync_at.isoformat() if virtual_switch.last_sync_at else None
            }
        }


class VirtualSwitchPhysicalInterfaceViewSet(ChaosCustomModelViewSet):
    """虚拟交换机物理接口管理"""
    queryset = VirtualSwitchPhysicalInterface.objects.order_by('-create_datetime')
    serializer_class = VirtualSwitchPhysicalInterfaceSerializer
    filter_fields = '__all__'


class VirtualSwitchTrunkInterfaceViewSet(ChaosCustomModelViewSet):
    """虚拟交换机聚合接口管理"""
    queryset = VirtualSwitchTrunkInterface.objects.order_by('-create_datetime')
    serializer_class = VirtualSwitchTrunkInterfaceSerializer
    filter_fields = '__all__'


class VirtualSwitchMacAddressViewSet(ChaosCustomModelViewSet):
    """虚拟交换机MAC地址管理"""
    queryset = VirtualSwitchMacAddress.objects.order_by('-create_datetime')
    serializer_class = VirtualSwitchMacAddressSerializer
    filter_fields = '__all__'