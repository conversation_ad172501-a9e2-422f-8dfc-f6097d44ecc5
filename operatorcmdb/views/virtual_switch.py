import logging

from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

logger = logging.getLogger(__name__)

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import SuccessResponse, ErrorResponse
import logging

from xresource.models import MachineRoom, PrivateRoom, IDCRackMachine, NetworkHardware
from operatorcmdb.models import (
    VirtualSwitch,
    VirtualSwitchPhysicalInterface,
    VirtualSwitchTrunkInterface,
    VirtualSwitchMacAddress
    )
from operatorcmdb.serializers.virtual_switch import (
    VirtualSwitchSerializer,
    VirtualSwitchPhysicalInterfaceSerializer,
    VirtualSwitchTrunkInterfaceSerializer,
    VirtualSwitchMacAddressSerializer
    )
from operatorcmdb.services.switch_sync_service import switch_sync_service
# 添加Neo4j同步服务的导入
from operatorcmdb.services.neo4j_sync_service import neo4j_sync_service
# 添加拓扑查询服务的导入
from operatorcmdb.services.switch_rel_service import switch_topology_query_service
# 添加G6数据服务的导入
from operatorcmdb.services.g6_data_service import g6_data_service


logger = logging.getLogger(__name__)


class VirtualSwitchViewSet(ChaosCustomModelViewSet):
    """虚拟交换机管理接口"""
    queryset = VirtualSwitch.objects.order_by('-create_datetime')
    serializer_class = VirtualSwitchSerializer
    filter_fields = '__all__'

    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
        }

    import_field_dict = {
        "physical_switch": {
            "title": "物理交换机",
            "choices": {
                "queryset": NetworkHardware.objects.filter(
                    is_deleted=False
                    ), "values_name": "name", "key_name": "id"
                }
            },
        "machine_room": {
            "title": "机房",
            "choices": {
                "queryset": MachineRoom.objects.filter(
                    is_deleted=False
                    ), "values_name": "name", "key_name": "id"
                }
            },
        "private_room": {
            "title": "包间",
            "choices": {
                "queryset": PrivateRoom.objects.filter(
                    is_deleted=False
                    ), "values_name": "name", "key_name": "id"
                }
            },
        "idc_rack_machine": {
            "title": "机柜",
            "choices": {
                "queryset": IDCRackMachine.objects.filter(
                    is_deleted=False
                    ), "values_name": "rack_sn", "key_name": "id"
                }
            },
        }

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def sync_interfaces(self, request, id):
        """同步交换机接口信息"""
        virtual_switch = self.get_object()

        # 调用同步服务
        result = switch_sync_service.sync_switch_data_by_instance(virtual_switch)

        if result.success:
            # 添加Neo4j同步结果
            neo4j_result = None
            try:
                with neo4j_sync_service:
                    neo4j_sync_result = neo4j_sync_service.sync_switch_to_neo4j(virtual_switch.id)
                    neo4j_result = {
                        'success': neo4j_sync_result.success,
                        'message': neo4j_sync_result.message,
                        'nodes_created': neo4j_sync_result.nodes_created,
                        'relationships_created': neo4j_sync_result.relationships_created
                        }
            except Exception as e:
                logger.error(f"同步到Neo4j失败: {str(e)}")
                neo4j_result = {
                    'success': False,
                    'message': f"同步到Neo4j失败: {str(e)}",
                    'nodes_created': 0,
                    'relationships_created': 0
                    }

            return SuccessResponse(
                msg=result.message,
                data={
                    'physical_interfaces': result.physical_interfaces,
                    'trunk_interfaces': result.trunk_interfaces,
                    'mac_addresses': result.mac_addresses,
                    'switch_info': result.switch_info,
                    'neo4j_sync': neo4j_result  # 添加Neo4j同步结果
                    }
                )
        else:
            return ErrorResponse(msg=result.message)

    # 可以添加一个专门同步到Neo4j的接口
    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def sync_to_neo4j(self, request, pk=None):
        """同步交换机数据到Neo4j"""
        virtual_switch = self.get_object()

        try:
            # 调用Neo4j同步服务
            with neo4j_sync_service:
                result = neo4j_sync_service.sync_switch_to_neo4j(virtual_switch.id)

            if result.success:
                return SuccessResponse(
                    msg=result.message,
                    data={
                        'nodes_created': result.nodes_created,
                        'relationships_created': result.relationships_created
                        }
                    )
            else:
                return ErrorResponse(msg=result.message)
        except Exception as e:
            logger.error(f"同步到Neo4j失败: {str(e)}")
            return ErrorResponse(msg=f"同步到Neo4j失败: {str(e)}")

    # 添加批量同步接口
    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated])
    def sync_all_to_neo4j(self, request):
        """批量同步所有交换机到Neo4j"""
        try:
            with neo4j_sync_service:
                result = neo4j_sync_service.sync_all_switches_to_neo4j()

            if result.success:
                return SuccessResponse(
                    msg=result.message,
                    data={
                        'nodes_created': result.nodes_created,
                        'relationships_created': result.relationships_created
                        }
                    )
            else:
                return ErrorResponse(msg=result.message)
        except Exception as e:
            logger.error(f"批量同步到Neo4j失败: {str(e)}")
            return ErrorResponse(msg=f"批量同步到Neo4j失败: {str(e)}")

    # 添加获取交换机拓扑数据的接口
    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def get_topology_data(self, request, id):
        """获取交换机拓扑数据"""
        virtual_switch = self.get_object()

        try:
            topology_data = switch_topology_query_service.get_switch_topology_data(virtual_switch.id)

            if 'error' in topology_data:
                return ErrorResponse(msg=topology_data['error'])

            return SuccessResponse(data=topology_data)
        except Exception as e:
            logger.error(f"获取交换机拓扑数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取拓扑数据失败: {str(e)}")

    # 添加获取机房内交换机拓扑数据的接口
    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def get_room_switch_topology_data(self, request):
        """获取机房内交换机拓扑数据"""
        machine_room_id = request.query_params.get('machine_room_id')

        if not machine_room_id:
            return ErrorResponse(msg="缺少参数: machine_room_id")

        try:
            topology_data = switch_topology_query_service.get_room_switches_topology(machine_room_id)

            if 'error' in topology_data:
                return ErrorResponse(msg=topology_data['error'])

            return SuccessResponse(data=topology_data)
        except Exception as e:
            logger.error(f"获取机房交换机拓扑数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取拓扑数据失败: {str(e)}")

    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def get_g6_data(self, request, pk=None):
        """获取交换机G6格式数据"""
        virtual_switch = self.get_object()

        try:
            g6_data = g6_data_service.get_switch_g6_data(virtual_switch.id)

            if 'error' in g6_data:
                return ErrorResponse(msg=g6_data['error'])

            return SuccessResponse(
                msg="获取G6数据成功",
                data=g6_data
            )
        except Exception as e:
            logger.error(f"获取交换机G6数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取G6数据失败: {str(e)}")

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def get_room_g6_data(self, request):
        """获取机房交换机G6格式数据"""
        machine_room_id = request.query_params.get('machine_room_id')

        if not machine_room_id:
            return ErrorResponse(msg="缺少参数: machine_room_id")

        try:
            g6_data = g6_data_service.get_room_switches_g6_data(machine_room_id)

            if 'error' in g6_data:
                return ErrorResponse(msg=g6_data['error'])

            return SuccessResponse(
                msg="获取机房G6数据成功",
                data=g6_data
            )
        except Exception as e:
            logger.error(f"获取机房G6数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取机房G6数据失败: {str(e)}")

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def interfaces(self, request):
        """获取交换机接口信息（兼容原有接口）"""
        switch_id = request.query_params.get('switch_id')

        if not switch_id:
            return ErrorResponse(msg="缺少参数: switch_id")

        try:
            virtual_switch = VirtualSwitch.objects.get(id=switch_id, is_deleted=False)

            # 获取接口数据
            physical_interfaces = VirtualSwitchPhysicalInterface.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            )
            trunk_interfaces = VirtualSwitchTrunkInterface.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            )
            mac_addresses = VirtualSwitchMacAddress.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            )

            # 序列化数据
            physical_data = VirtualSwitchPhysicalInterfaceSerializer(physical_interfaces, many=True).data
            trunk_data = VirtualSwitchTrunkInterfaceSerializer(trunk_interfaces, many=True).data
            mac_data = VirtualSwitchMacAddressSerializer(mac_addresses, many=True).data

            return SuccessResponse(
                msg="获取接口信息成功",
                data={
                    'switch_info': VirtualSwitchSerializer(virtual_switch).data,
                    'physical_interfaces': physical_data,
                    'trunk_interfaces': trunk_data,
                    'mac_addresses': mac_data,
                    'summary': {
                        'physical_count': physical_interfaces.count(),
                        'trunk_count': trunk_interfaces.count(),
                        'mac_count': mac_addresses.count(),
                        'active_physical': physical_interfaces.filter(state='up').count(),
                        'active_trunk': trunk_interfaces.filter(state='up').count()
                    }
                }
            )

        except VirtualSwitch.DoesNotExist:
            return ErrorResponse(msg=f"未找到ID为{switch_id}的虚拟交换机")
        except Exception as e:
            logger.error(f"获取接口信息失败: {str(e)}")
            return ErrorResponse(msg=f"获取接口信息失败: {str(e)}")


class VirtualSwitchPhysicalInterfaceViewSet(ChaosCustomModelViewSet):
    """虚拟交换机物理接口管理"""
    queryset = VirtualSwitchPhysicalInterface.objects.order_by('-create_datetime')
    serializer_class = VirtualSwitchPhysicalInterfaceSerializer
    filter_fields = '__all__'


class VirtualSwitchTrunkInterfaceViewSet(ChaosCustomModelViewSet):
    """虚拟交换机聚合接口管理"""
    queryset = VirtualSwitchTrunkInterface.objects.order_by('-create_datetime')
    serializer_class = VirtualSwitchTrunkInterfaceSerializer
    filter_fields = '__all__'


class VirtualSwitchMacAddressViewSet(ChaosCustomModelViewSet):
    """虚拟交换机MAC地址管理"""
    queryset = VirtualSwitchMacAddress.objects.order_by('-create_datetime')
    serializer_class = VirtualSwitchMacAddressSerializer
    filter_fields = '__all__'