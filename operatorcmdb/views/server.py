# import openstack
# from openstack import connection
# from rest_framework.views import APIView
# from dvadmin.utils.json_response import DetailResponse, SuccessResponse, ErrorResponse
# from rest_framework import status
#
# openstack.enable_logging(debug=False)
#
#
# class ServerViewSet(APIView):
#     permission_classes = []
#     authentication_classes = []
#
#     def get(self, request):
#         server_list = get_instances()
#         # server_list = [{"name": "good"}, {"name": "ddd"}, {"name": "bajdla"}]
#         return SuccessResponse(data=server_list)
#
#     def post(self, request):
#         # 接收数据
#         name = request.data.get('name')
#         flavor_id = request.data.get('flavor_id')
#         image_id = request.data.get('image_id')
#         network_id = request.data.get('network_id')
#
#         # 创建vm
#         server = conn.compute.create_server(
#             name=name,
#             image_id=image_id,
#             flavor_id=flavor_id,
#             networks=[{'uuid': network_id}],
#         )
#
#         # 查询vm
#         server = conn.compute.get_server(server.id)
#
#         return SuccessResponse(data=server.to_dict())
#
#
# def get_openstack_conn():
#     # Initialize connection
#     # conn = openstack.connect(cloud='jinhua')
#
#     # 创建连接到OpenStack的连接对象
#     # 替换以下变量为你的OpenStack环境的具体信息
#     auth_url = 'https://10.10.70.188:5000/v3'
#     project_name = 'xingzai'
#     username = 'admin'
#     password = 'f5170ef8b7b4a93d2a1e8ae4b84bd0c6c8aaa235d9de'
#     user_domain_name = 'Default'  # 或者你的用户域名称
#     project_domain_name = 'default'  # 或者你的项目域名称
#
#     conn = connection.Connection(
#         region_name='RegionOne',
#         auth={
#             'auth_url': auth_url,
#             'project_name': project_name,
#             'username': username,
#             'password': password,
#             'user_domain_name': user_domain_name,
#             'project_domain_name': project_domain_name
#         },
#         verify=False
#     )
#     return conn
#
#
# conn = get_openstack_conn()
#
#
# def get_instances():
#     # 查询vm
#     i = 0
#     myli = []
#     for server in conn.compute.servers():
#         dic = {}
#         # i+=1
#         # if i==2:
#         #     break
#         myserver = server.to_dict()
#         for netname in myserver.get('addresses'):
#             ipaddr = myserver.get('addresses').get(netname)[0].get('addr')
#         compute_host = myserver.get('compute_host')
#         insid = myserver.get('id')
#         status = myserver.get('status')
#         vm_state = myserver.get('vm_state')
#         name = myserver.get('name')
#         owner = myserver.get('metadata').get('owner')
#         app = myserver.get('metadata').get('app')
#         myenv = myserver.get('metadata').get('env')
#         team = myserver.get('metadata').get('team')
#         flavorname = myserver.get('flavor').get('name')
#         dic['name'] = name
#         dic["ipaddr"] = ipaddr
#         dic["flavorname"] = flavorname
#         dic['owner'] = owner
#         dic['app'] = app
#         dic['myenv'] = myenv
#         dic['team'] = team
#         dic["compute_host"] = compute_host
#         dic["netname"] = netname
#         dic['insid'] = insid
#         dic['status'] = status
#         dic['vm_state'] = vm_state
#
#         myli.append(dic)
#     # print(myli)
#     # for i in myli:
#     #     print(i)
#     return myli
#
#
# # # 获取所有宿主机列表
# # hypervisors = conn.list_hypervisors()
# # # 遍历宿主机列表并输出信息
# # for hypervisor in hypervisors:
# #     print(hypervisor)
# #     print("\n")
#
#
# def create_instance(vm_name):
#     # 创建vm
#     # 设置虚拟机参数
#     # vm_name = 'fx_sdk_create_vm'
#     image_name = 'centos-7.9'  # 或者您环境中可用的image名称
#     flavor_name = 'ecs.e-1c1m40g'  # 或者您环境中可用的flavor名称
#
#     # 查找镜像和 flavor、网络
#     image = conn.compute.find_image(image_name)
#     flavor = conn.compute.find_flavor(flavor_name)
#     network = conn.network.find_network(name_or_id="10-10-12-0")
#
#     # 创建虚拟机
#     vm = conn.compute.create_server(
#         name=vm_name,
#         image_id=image.id,
#         flavor_id=flavor.id,
#         networks=[{"uuid": network.id}]  # 替换为您的网络UUID
#     )
#
#     # 等待虚拟机创建完成
#     conn.compute.wait_for_server(vm)
#     print(f"虚拟机 {vm_name} 创建成功，ID: {vm.id}")
#
#
# def change_password(vm_id, new_password):
#     # 修改虚拟机的密码
#     conn.compute.change_server_password(vm_id, new_password)
#     print(f'虚拟机 {vm_id} 已创建并修改密码。')
#
#
# # 关闭虚拟机
# def stop_instance(instance_id):
#     server = conn.compute.find_server(instance_id)
#     conn.compute.stop_server(server)
#
#
# # 启动虚拟机
# def start_instance(instance_id):
#     server = conn.compute.find_server(instance_id)
#     conn.compute.start_server(server)
#
#
# # 查看网络
# def get_networks():
#     # 列出所有网络
#     networks = list(conn.network.networks())
#     # 打印每个网络的详细信息
#     for network in networks:
#         print(f'ID: {network.id}, 名称: {network.name}, 子网: {network.subnet_ids}')
#
#
# # 查看子网
# def get_subnets():
#     # 列出所有子网
#     subnets = list(conn.network.subnets())
#     # 打印每个子网的详细信息
#     for subnet in subnets:
#         # print(subnet)
#         print(
#             f'ID: {subnet.id}, 名称: {subnet.name}, 网络ID: {subnet.network_id}, 网段: {subnet.cidr}, GW: {subnet.gateway_ip}')
#
#
# # 查看镜像
# def get_images():
#     # 列出所有镜像
#     images = list(conn.image.images())
#     # 打印每个镜像的详细信息
#     for image in images:
#         # print(image)
#         print(f'ID: {image.id}, 名称: {image.name}, 状态: {image.status}, 镜像类型: {image.disk_format}')
#
#
# # 查看flavor
# def get_flavors():
#     # 列出所有flavor
#     flavors = list(conn.compute.flavors())
#     # 打印每个flavor的详细信息
#     for flavor in flavors:
#         print(flavor)
#         print(f'ID: {flavor.id}, 名称: {flavor.name}, 内存: {flavor.ram}, CPU: {flavor.vcpus}, 规格: {flavor.disk}')
#
#
# if __name__ == '__main__':
#     # ins_id = '856b1ecb-ec6c-4f1b-8d12-6ec3e24d2dd6'
#     # start_instance(ins_id)
#     # create_instance("sdk-create-test02")
#     # change_password("7271465f-78c2-4569-9ccd-c5474f7c654c", "abcd@1234")
#     # get_networks()
#     #
#     # get_subnets()
#
#     get_images()
#
#     get_flavors()
