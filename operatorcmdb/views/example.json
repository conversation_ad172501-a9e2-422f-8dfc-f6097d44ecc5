{"id": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04", "name": "测试001", "count": 86, "label": "41/86", "currency": "Interfaces", "rate": 1, "status": "R", "variableName": "Uptime", "variableValue": 0, "variableUp": false, "children": [{"id": "physical-interfaces-8310", "name": "Physical Interfaces", "count": 56, "label": "25/56", "currency": "Ports", "rate": 0.44642857142857145, "status": "R", "variableName": "Active", "variableValue": 0.44642857142857145, "variableUp": false, "children": [{"id": "speed-Unknown-9979", "name": "Unknown Interfaces", "count": 56, "label": "25/56", "currency": "Ports", "rate": 0.44642857142857145, "status": "R", "variableName": "Utilization", "variableValue": 0.44642857142857145, "variableUp": false, "collapsed": true, "children": [{"id": "interface-cmdb_vs_physical_interface-dfcf31dcddf649a78fabf5cfef228188", "name": "25GE1/0/7", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-c08e61db169148108b187b419b13d32a", "name": "25GE1/0/48", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-5a71507a33d241539efa9125344937b4", "name": "25GE1/0/47", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-c46607cba51645f5a0aa42e7a034b097", "name": "25GE1/0/46", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-da0328663bea444dbc833fa436a63639", "name": "25GE1/0/45", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-bb3a7fbb4d5a486bb71132e60cb252f0", "name": "25GE1/0/44", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-f2d937565d0243afa920cde799fa0c28", "name": "25GE1/0/15", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-a86985ce88fe4d19abad63ec51661a08", "name": "25GE1/0/14", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-818a5627d29a400184de88801c3c606e", "name": "25GE1/0/13", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-cefdbdbf3d304ed5af664e7f57ffb594", "name": "25GE1/0/12", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-b52f64a721e847f48d7107682b1869d2", "name": "25GE1/0/11", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-acac6936089b40058f636e0d22b81b8a", "name": "25GE1/0/22", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-14b19676cd8b4d7391316add7c3ec57d", "name": "25GE1/0/21", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-00e286b4915542c3af32d19bffb75c5f", "name": "25GE1/0/20", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-68c5f2af23fd45dcaf2d9ac9b1c72fef", "name": "25GE1/0/19", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-00811a92b2dd452bb02d550419cc6f9e", "name": "25GE1/0/17", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-0a896e8cb2ef44a28c9264a7bcfb4625", "name": "25GE1/0/6", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-d77ade73391d46349901d257eba16497", "name": "25GE1/0/5", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-2db0b0a8d4824232bb89b5a7b4a18fc8", "name": "25GE1/0/4", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-9a86ddc94bab41a7b826d1beb71dc663", "name": "25GE1/0/3", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-379773c9248b4b039f9bef105d4f4e1e", "name": "25GE1/0/2", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-d5be871e8c7048ebb11634f19547abe3", "name": "25GE1/0/1", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-f2707b3056b74addbc8e872515898661", "name": "25GE1/0/34", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-b6b15a41c06c4ed3bc7400ce430142d4", "name": "25GE1/0/33", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-4aa88f2af331442d80eeaaa403ef3016", "name": "25GE1/0/9", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-9f441ffcde184301bca8715750824387", "name": "25GE1/0/8", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-6dc848fb27ab47b1a31e839c3f3de7a0", "name": "100GE1/0/8", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-9cc9e843a11a4c6daec691d93609813e", "name": "100GE1/0/7", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-f822de67e30e4087ac82971dd44735a8", "name": "25GE1/0/43", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-d43244194dfe48298e6900e7a824d289", "name": "25GE1/0/42", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-71ff3f1200534339a63b923e1669fb84", "name": "25GE1/0/41", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-0fdebdacb4c04523be806be9164ea63a", "name": "25GE1/0/40", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-cc27d42f0ac841a89d022d7d6a2c3309", "name": "25GE1/0/39", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-4cd198bd551444cdadfdf51b3292978e", "name": "25GE1/0/38", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-6445019f3c794ccab9f4b83a5344cc4d", "name": "25GE1/0/37", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-91ac9f7cc951482ea62dae9e9b4eb804", "name": "25GE1/0/36", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-6311972da5904bfd88619bf543f187b5", "name": "25GE1/0/35", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-f7492f602d4a4f16a259acdb95f8191c", "name": "25GE1/0/32", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-9016431d58334d73bc3663c11e8eeeb6", "name": "25GE1/0/31", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-b218a0213515444c9965e0f5f7fc438b", "name": "25GE1/0/30", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-4c1d9e02c2ad4f5192854e7eabf5c441", "name": "25GE1/0/29", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-b6b4513331dc4d6aa21aaf9aac747273", "name": "25GE1/0/28", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-a52069bcbb1e4005b64f97b2b7aa7ff0", "name": "25GE1/0/27", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-923a8820e0e24a0c80f4e0ba7258b766", "name": "25GE1/0/26", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-dd1035d8b86f495088e024e3302f70bb", "name": "25GE1/0/25", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-43544a61d2d342baa0ed33b6b34c26c1", "name": "25GE1/0/23", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-20cab834d20a4edb81c5a58c01ba50ee", "name": "100GE1/0/2", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-dae192e2fcbe48a6b28c822b0968c84f", "name": "100GE1/0/1", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-565e4369fde744b8aa4faf691ebcbd38", "name": "25GE1/0/24", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-4546ee041e874d9d84dfa50a0ebb3001", "name": "25GE1/0/18", "count": 1, "label": "up", "currency": "Status", "rate": 1, "status": "G", "variableName": "Speed", "variableValue": 0, "variableUp": true, "children": []}, {"id": "interface-cmdb_vs_physical_interface-2610957c0c4846cc8120c8253d12449b", "name": "25GE1/0/16", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-90c86b49da7445328cd9aaa58d61caac", "name": "25GE1/0/10", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-845da4d5a7ab4ba0b501a92098345f7c", "name": "100GE1/0/6", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-5fc0c96fc22c43559f1f0db1d3f78514", "name": "100GE1/0/5", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-f6f66a7de5f044c4b6b4983c07abd265", "name": "100GE1/0/4", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}, {"id": "interface-cmdb_vs_physical_interface-d13319cf7ee44a5a898ec4f48f6f53f2", "name": "100GE1/0/3", "count": 1, "label": "down", "currency": "Status", "rate": 0, "status": "R", "variableName": "Speed", "variableValue": 0, "variableUp": false, "children": []}]}]}, {"id": "trunk-interfaces-8703", "name": "Trunk Interfaces", "count": 30, "label": "16/30", "currency": "Trunks", "rate": 0.5333333333333333, "status": "B", "variableName": "Active", "variableValue": 0.5333333333333333, "variableUp": true, "collapsed": true, "children": [{"id": "trunk-cmdb_vs_trunk_interface-d620ee5c9f084e2c9355cad630a0f11e", "name": "Eth-Trunk77", "count": 1, "label": "1 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.1, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-627af24789204554975d94231be24020", "name": "Eth-Trunk48", "count": 1, "label": "1 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.1, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-dd3c4c6dcbec4191a33c732dfbd5a29e", "name": "Eth-Trunk47", "count": 1, "label": "1 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.1, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-befbad796f16452197602fc9f0cffa29", "name": "Eth-Trunk46", "count": 1, "label": "1 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.1, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-57e827c0552441fdb9f56242d6aa561e", "name": "Eth-Trunk45", "count": 1, "label": "1 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.1, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-95f635b3649d42dda72dfb93138ac268", "name": "Eth-Trunk44", "count": 1, "label": "1 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.1, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-1b1ca4ea2cf1402bb280ccea1052bf81", "name": "Eth-Trunk34", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-91d50880fbf2495b8568df6bd993c750", "name": "Eth-Trunk33", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-7d4ee5db78bb4a73be625c28d9db75a4", "name": "Eth-Trunk32", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-dee6239188d9410a8206e46987ed7fd9", "name": "Eth-Trunk31", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-025b7ec2a4bb40999298c84d7c4dd5a4", "name": "Eth-Trunk30", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-c9223c2ad8074734965858680d124773", "name": "Eth-Trunk28", "count": 0, "label": "0 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-4edd9e329e0a45edb75a644d08f58267", "name": "Eth-Trunk27", "count": 0, "label": "0 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-ffe38d59d84041a399482a64da1ca8de", "name": "Eth-Trunk24", "count": 0, "label": "0 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-f3beb66a30c94042be133da6e8d3584a", "name": "Eth-Trunk22", "count": 1, "label": "1 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.1, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-d31975ccd6e94293b0d7783cc66e0a64", "name": "Eth-Trunk21", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-d90f6f730ba749e3a30ced4ce0eec29b", "name": "Eth-Trunk20", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-8109f51db82a46159b6c69135604324c", "name": "Eth-Trunk19", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-f53d79eceb704700a0467b4080189899", "name": "Eth-Trunk17", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-26e7cc4b71134b15a0db29eb1231c55f", "name": "Eth-Trunk16", "count": 1, "label": "1 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.1, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-b3ef2e93c6874f0e94d89b7a3475ef69", "name": "Eth-Trunk15", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-1870dc0244bf4c51a9d96f4aa851b2ce", "name": "Eth-Trunk14", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-f673dd4da7274a83a26eb40056b78aac", "name": "Eth-Trunk13", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-45f347bf6ba14434a00a1c11eb3e62d3", "name": "Eth-Trunk12", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-8a6ecb4cc2a74a4c9e2f3ae68db9bb09", "name": "Eth-Trunk11", "count": 2, "label": "2 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-a55b4681035346ba89a3d723ca3f3210", "name": "Eth-Trunk9", "count": 2, "label": "2 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.2, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-04f794b6aed248479c936ed1034ba491", "name": "Eth-Trunk8", "count": 1, "label": "1 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.1, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-8a93034cff0940c0a77c816191a95885", "name": "Eth-Trunk100", "count": 3, "label": "3 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.3, "variableUp": true, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-5a67c98a1ea74c2b91584dcd69c8cff9", "name": "Eth-Trunk7", "count": 1, "label": "1 members", "currency": "Members", "rate": 0, "status": "R", "variableName": "Aggregation", "variableValue": 0.1, "variableUp": false, "children": []}, {"id": "trunk-cmdb_vs_trunk_interface-a0ab47e1768a4740a22530507496cfd7", "name": "Eth-Trunk1", "count": 4, "label": "4 members", "currency": "Members", "rate": 1, "status": "G", "variableName": "Aggregation", "variableValue": 0.4, "variableUp": true, "children": []}]}, {"id": "mac-addresses-7347", "name": "MAC Addresses", "count": 5, "label": "5", "currency": "MACs", "rate": 1, "status": "B", "variableName": "Learned", "variableValue": 0.05, "variableUp": true, "collapsed": true, "children": []}, {"id": "infrastructure-1322", "name": "Infrastructure", "count": 1, "label": "Location", "currency": "Info", "rate": 1, "status": "B", "variableName": "<PERSON><PERSON>", "variableValue": 0.023809523809523808, "variableUp": true, "collapsed": true, "children": [{"id": "room-machine_room-a1d6b5715d464690882b23fc0328d7d1", "name": "芜湖机房", "count": 1, "label": "Machine Room", "currency": "Location", "rate": 1, "status": "G", "variableName": "Region", "variableValue": 0.8, "variableUp": true, "children": []}, {"id": "rack-idc_rack_machine-a68065e380cf4a00a62b60ae34bdac85", "name": "E21.D4-3.EM101", "count": 1, "label": "U1", "currency": "Position", "rate": 1, "status": "B", "variableName": "Utilization", "variableValue": 0.023809523809523808, "variableUp": true, "children": []}]}]}