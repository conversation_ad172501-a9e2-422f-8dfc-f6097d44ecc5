from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet

from dvadmin.system.models import Dictionary
from dvadmin.utils.json_response import SuccessResponse, ErrorResponse

from customer.models import Customer
from xresource.models import MachineRoom, PrivateRoom, IDCRackMachine, PhysicalServerMachine
from operatorcmdb.models import Host
from operatorcmdb.serializers.host import HostSerializer, HostImportSerializer, HostUpdateSerializer
import operatorcmdb.dictionary_enum_keys as ENUM_KEYS
from operatorcmdb.utils.calculate_reliability import ReliabilityContext,GPU4090ReliabilityStrategy,GPU3090ReliabilityStrategy,GPUA800ReliabilityStrategy


class HostViewSet(ChaosCustomModelViewSet):
    """
    客户信息接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Host.objects.order_by('-create_datetime')
    serializer_class = HostSerializer
    update_serializer_class = HostUpdateSerializer
    filter_fields = '__all__'

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = HostImportSerializer
    import_field_dict = {
        "instance_id": "实例ID",
        "name": "实例名称",
        "description": "实例描述",
        "area": "地区",
        "host_type": {
            "title": "主机类型(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.OPERATOR_CMDB_HOST_HOST_TYPE
            ), "values_name": "label", "key_name": "value"}
        },
        "host_status": {
            "title": "主机状态(非必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.OPERATOR_CMDB_HOST_HOST_STATUS
            ), "values_name": "label", "key_name": "value"}
        },
        "os": {
            "title": "操作系统(非必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.OPERATOR_CMDB_HOST_OS
            ), "values_name": "label", "key_name": "value"}
        },
        "ip_private": "内网IP",
        "ip_public": "公网IP",
        "ip_bmc": "BMC-IP",
        "kvm_ip_host": "宿主机IP",
        "bmc_user": "BMC用户",
        "bmc_passwd": "BMC密码",
        "machine_room": {
            "title": "机房",
            "choices": {"queryset": MachineRoom.objects.filter(
                is_deleted=False
            ), "values_name": "name", "key_name": "id"}
        },
        "private_room": {
            "title": "包间",
            "choices": {"queryset": PrivateRoom.objects.filter(
                is_deleted=False
            ), "values_name": "name", "key_name": "id"}
        },
        "idc_rack_machine": {
            "title": "主机类型",
            "choices": {"queryset": IDCRackMachine.objects.filter(
                is_deleted=False,
            ), "values_name": "rack_sn", "key_name": "id"}
        },
        "physical_server_machine": {
            "title": "物理机",
            "choices": {"queryset": PhysicalServerMachine.objects.filter(
                is_deleted=False
            ), "values_name": "physical_machine_sn", "key_name": "id"}
        },
        "customer": {
            "title": "客户",
            "choices": {"queryset": Customer.objects.filter(
                is_deleted=False,
            ), "values_name": "name", "key_name": "id"}
        },
        "cpu": "CPU",
        "mem": "内存",
        "sys_disk": "系统盘",
        "data_disk": "数据盘",
        "gpu_model": "GPU类型",
        "gpu_count": "GPU数量",
        "ssh_port": "SSH端口",
        "ssh_user": "SSH用户",
        "ssh_passwd": "SSH密码",
        "start_time": "启用时间",
        "expire_time": "到期时间",
        "is_buffer": "Buffer池",
        "is_ipmi_mon": "是否开启IPMI监控(0或1)",
        "is_system_mon": "是否开启系统监控(0或1)",
        "u_position": "U位",
    }
    permission_classes = []

    @action(methods=['get'], detail=True, permission_classes=[IsAuthenticated])
    def get_host_human_info(self, request, id):
        """
        获取已整合的主机信息
        """
        try:
            host = Host.objects.filter(
                id=id,
                is_deleted=False
                ).select_related(
                'machine_room',
                'private_room',
                'idc_rack_machine',
                'physical_server_machine',
                'customer'
                ).first()

            if not host:
                return ErrorResponse(data={}, msg="未找到该主机")

            # 构建返回数据
            data = {
                'id': host.id,
                'name': host.name,
                'instance_id': host.instance_id,
                'area': host.area,
                'host_type': host.host_type,
                'u_position': host.u_position,
                'resource_category': host.resource_category,
                'ip_private': host.ip_private,
                'ip_public': host.ip_public,
                'ip_bmc': host.ip_bmc,
                'kvm_ip_host': host.kvm_ip_host,
                'os': host.os,
                'cpu': host.cpu,
                'mem': host.mem,
                'sys_disk': host.sys_disk,
                'data_disk': host.data_disk,
                # 关联字段的name信息
                'machine_room_name': host.machine_room.name if host.machine_room else None,
                'private_room_name': host.private_room.name if host.private_room else None,
                'idc_rack_machine_name': host.idc_rack_machine.rack_sn if host.idc_rack_machine else None,
                'physical_server_machine_name': host.physical_server_machine.physical_machine_sn if host.physical_server_machine else None,
                'customer_name': host.customer.name if host.customer else None,
                }

            return SuccessResponse(data=data, msg="获取成功")

        except Exception as e:
            return ErrorResponse(data={}, msg=f"获取主机信息失败: {str(e)}")


    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def get_metrics(self,request,*args,**kwargs):
        """
        统计bmc中显卡等故障的MTTF、MTTR指标
        """
        try:
            reliability_obj = ReliabilityContext(GPU4090ReliabilityStrategy())
            data = reliability_obj.get_all_reliability()
            reliability_obj.set_strategy(GPUA800ReliabilityStrategy())
            data.extend(reliability_obj.get_all_reliability())
            reliability_obj.set_strategy(GPU3090ReliabilityStrategy())
            data.extend(reliability_obj.get_all_reliability())
            return SuccessResponse(data=data, msg="获取成功")
        except Exception as e:
            return ErrorResponse(data={}, msg=f"获取MTTF等指标信息失败: {str(e)}")