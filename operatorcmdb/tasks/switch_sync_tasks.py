"""
交换机同步异步任务
这里提供了异步任务的示例实现，您可以根据实际使用的异步任务框架进行调整
"""

import logging
from typing import List, Optional

from operatorcmdb.models import VirtualSwitch
from operatorcmdb.services.switch_sync_service import switch_sync_service

logger = logging.getLogger(__name__)


def sync_single_switch_task(virtual_switch_id: int) -> dict:
    """
    同步单个交换机的异步任务

    Args:
        virtual_switch_id: 虚拟交换机ID

    Returns:
        dict: 同步结果
    """
    logger.info(f"开始异步同步交换机 ID: {virtual_switch_id}")

    try:
        # 调用同步服务
        result = switch_sync_service.sync_switch_data(virtual_switch_id)

        if result.success:
            logger.info(f"交换机同步成功 ID: {virtual_switch_id} - {result.message}")
            return {
                'success': True,
                'switch_id': virtual_switch_id,
                'message': result.message,
                'data': {
                    'physical_interfaces': result.physical_interfaces,
                    'trunk_interfaces': result.trunk_interfaces,
                    'mac_addresses': result.mac_addresses,
                    'switch_info': result.switch_info
                    }
                }
        else:
            logger.error(f"交换机同步失败 ID: {virtual_switch_id} - {result.message}")
            return {
                'success': False,
                'switch_id': virtual_switch_id,
                'message': result.message,
                'error': str(result.error) if result.error else None
                }

    except Exception as e:
        logger.error(f"异步任务执行异常 ID: {virtual_switch_id} - {str(e)}", exc_info=True)
        return {
            'success': False,
            'switch_id': virtual_switch_id,
            'message': f"任务执行异常: {str(e)}",
            'error': str(e)
            }


def sync_multiple_switches_task(virtual_switch_ids: List[int]) -> dict:
    """
    批量同步多个交换机的异步任务

    Args:
        virtual_switch_ids: 虚拟交换机ID列表

    Returns:
        dict: 批量同步结果
    """
    logger.info(f"开始批量异步同步交换机，数量: {len(virtual_switch_ids)}")

    results = {
        'total': len(virtual_switch_ids),
        'success_count': 0,
        'failed_count': 0,
        'details': []
        }

    for switch_id in virtual_switch_ids:
        try:
            # 同步单个交换机
            result = switch_sync_service.sync_switch_data(switch_id)

            detail = {
                'switch_id': switch_id,
                'success': result.success,
                'message': result.message
                }

            if result.success:
                results['success_count'] += 1
                detail['data'] = {
                    'physical_interfaces': result.physical_interfaces,
                    'trunk_interfaces': result.trunk_interfaces,
                    'mac_addresses': result.mac_addresses
                    }
            else:
                results['failed_count'] += 1
                detail['error'] = str(result.error) if result.error else None

            results['details'].append(detail)

        except Exception as e:
            logger.error(f"批量同步中单个交换机异常 ID: {switch_id} - {str(e)}")
            results['failed_count'] += 1
            results['details'].append(
                {
                    'switch_id': switch_id,
                    'success': False,
                    'message': f"同步异常: {str(e)}",
                    'error': str(e)
                    }
                )

    logger.info(f"批量同步完成，成功: {results['success_count']}, 失败: {results['failed_count']}")
    return results


def sync_all_active_switches_task() -> dict:
    """
    同步所有活跃交换机的异步任务

    Returns:
        dict: 同步结果
    """
    logger.info("开始同步所有活跃交换机")

    try:
        # 获取所有有管理信息的活跃交换机
        active_switches = VirtualSwitch.objects.filter(
            is_deleted=False,
            management_ip__isnull=False,
            management_username__isnull=False,
            management_password__isnull=False
            ).exclude(
            management_ip='',
            management_username='',
            management_password=''
            )

        switch_ids = list(active_switches.values_list('id', flat=True))

        if not switch_ids:
            logger.warning("未找到配置了管理信息的活跃交换机")
            return {
                'success': True,
                'message': "未找到需要同步的交换机",
                'total': 0,
                'success_count': 0,
                'failed_count': 0,
                'details': []
                }

        # 批量同步
        result = sync_multiple_switches_task(switch_ids)
        result['success'] = True
        result[
            'message'] = f"批量同步完成，总计: {result['total']}, 成功: {result['success_count']}, 失败: {result['failed_count']}"

        return result

    except Exception as e:
        logger.error(f"同步所有活跃交换机异常: {str(e)}", exc_info=True)
        return {
            'success': False,
            'message': f"同步所有交换机失败: {str(e)}",
            'error': str(e)
            }


def sync_switches_by_machine_room_task(machine_room_id: int) -> dict:
    """
    按机房同步交换机的异步任务

    Args:
        machine_room_id: 机房ID

    Returns:
        dict: 同步结果
    """
    logger.info(f"开始同步机房交换机 机房ID: {machine_room_id}")

    try:
        # 获取指定机房的交换机
        switches = VirtualSwitch.objects.filter(
            is_deleted=False,
            machine_room_id=machine_room_id,
            management_ip__isnull=False,
            management_username__isnull=False,
            management_password__isnull=False
            ).exclude(
            management_ip='',
            management_username='',
            management_password=''
            )

        switch_ids = list(switches.values_list('id', flat=True))

        if not switch_ids:
            logger.warning(f"机房 {machine_room_id} 中未找到配置了管理信息的交换机")
            return {
                'success': True,
                'message': f"机房中未找到需要同步的交换机",
                'machine_room_id': machine_room_id,
                'total': 0,
                'success_count': 0,
                'failed_count': 0,
                'details': []
                }

        # 批量同步
        result = sync_multiple_switches_task(switch_ids)
        result['success'] = True
        result['machine_room_id'] = machine_room_id
        result[
            'message'] = f"机房交换机同步完成，总计: {result['total']}, 成功: {result['success_count']}, 失败: {result['failed_count']}"

        return result

    except Exception as e:
        logger.error(f"同步机房交换机异常 机房ID: {machine_room_id} - {str(e)}", exc_info=True)
        return {
            'success': False,
            'message': f"同步机房交换机失败: {str(e)}",
            'machine_room_id': machine_room_id,
            'error': str(e)
            }


# ============================================================================
# 以下是针对不同异步任务框架的装饰器示例，您可以根据实际使用的框架进行调整
# ============================================================================

# Celery 示例 (如果使用 Celery)
"""
from celery import shared_task

@shared_task(bind=True, max_retries=3)
def celery_sync_single_switch(self, virtual_switch_id: int):
    try:
        return sync_single_switch_task(virtual_switch_id)
    except Exception as e:
        logger.error(f"Celery任务失败: {str(e)}")
        raise self.retry(countdown=60, exc=e)

@shared_task
def celery_sync_all_switches():
    return sync_all_active_switches_task()

@shared_task
def celery_sync_switches_by_room(machine_room_id: int):
    return sync_switches_by_machine_room_task(machine_room_id)
"""

# Django-RQ 示例 (如果使用 Django-RQ)
"""
import django_rq

def enqueue_switch_sync(virtual_switch_id: int):
    queue = django_rq.get_queue('default')
    return queue.enqueue(sync_single_switch_task, virtual_switch_id)

def enqueue_batch_sync(virtual_switch_ids: List[int]):
    queue = django_rq.get_queue('default')
    return queue.enqueue(sync_multiple_switches_task, virtual_switch_ids)
"""

# Django-Q 示例 (如果使用 Django-Q)
"""
from django_q.tasks import async_task

def queue_switch_sync(virtual_switch_id: int):
    return async_task(sync_single_switch_task, virtual_switch_id)

def queue_batch_sync(virtual_switch_ids: List[int]):
    return async_task(sync_multiple_switches_task, virtual_switch_ids)
"""
