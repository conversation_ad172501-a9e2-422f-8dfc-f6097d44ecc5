from django.urls import path
from rest_framework.routers import SimpleRouter

from operatorcmdb.views.host import HostViewSet
from operatorcmdb.views.fast_install_software import FastInstallSoftwareViewSet
from operatorcmdb.views.host_maintenance import HostMaintenanceViewSet
from operatorcmdb.views.virtual_switch import (
    VirtualSwitchViewSet,
    VirtualSwitchPhysicalInterfaceViewSet,
    VirtualSwitchTrunkInterfaceViewSet,
    VirtualSwitchMacAddressViewSet
)


router = SimpleRouter()
router.register(r'host', HostViewSet)
router.register(r'fast_install_software', FastInstallSoftwareViewSet)
router.register(r'host_maintenance', HostMaintenanceViewSet)
router.register(r'virtual_switch', VirtualSwitchViewSet)
router.register(r'virtual_switch_physical_interface', VirtualSwitchPhysicalInterfaceViewSet)
router.register(r'virtual_switch_trunk_interface', VirtualSwitchTrunkInterfaceViewSet)
router.register(r'virtual_switch_mac_address', VirtualSwitchMacAddressViewSet)


app_name = 'operatorcmdb'

urlpatterns = [
]
urlpatterns += router.urls
