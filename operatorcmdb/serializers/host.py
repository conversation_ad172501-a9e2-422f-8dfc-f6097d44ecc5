from datetime import timedelta

from django.utils import timezone
from django.db.models import Q

from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from operatorcmdb.models import Host
from notice.utils.host_expire_notice import ManagerHostStatusNotice


class HostImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = Host
        exclude = ()


class HostSerializer(ChaosCustomModelSerializer):
    """
    主机-序列化器
    """

    class Meta:
        model = Host
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


class HostUpdateSerializer(ChaosCustomModelSerializer):
    """
    主机-序列化器
    """

    class Meta:
        model = Host
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]

    def update(self, instance, validated_data):
        none_open_status = ['空闲', '静默', '下线']
        host_status = validated_data.get("host_status")
        host_type = validated_data.get("host_type")
        ip_bmc = validated_data.get("ip_bmc")
        if host_status in none_open_status:
            # 若主机状态为空闲、静默、下线，则将 IPMI监控 和 系统监控 自动设置为 0，即关闭。
            validated_data["is_ipmi_mon"] = False
            validated_data["is_system_mon"] = False
        elif host_status == '启用' and host_type == '裸金属' and ip_bmc:
            validated_data['is_ipmi_mon'] = True
        super().update(instance, validated_data)
        return instance


def check_operator_cmdb_host_expire_at():
    # 定义过期时间点
    now_time = timezone.now()
    start_expire_time = now_time - timedelta(days=3)
    end_expire_time = timezone.now() + timedelta(days=3)

    # 获取所有即将到期的主机
    expire_near_hosts = Host.objects.filter(
        ~Q(host_status__in=["下线", "静默", "空闲"]),
        expire_time__gte=start_expire_time,
        expire_time__lte=end_expire_time,
        is_deleted=False,
    ).all()
    expired_hosts = []
    expiring_hosts = []
    for host in expire_near_hosts:
        if host.expire_time < now_time:
            expired_hosts.append(host.to_dict)
        else:
            expiring_hosts.append(host.to_dict)
    ManagerHostStatusNotice().send_operator_cmdb_host_expire_notice(
        '【主机过期提醒】',
        expiring_hosts=expiring_hosts,
        expired_hosts=expired_hosts,
    )


def check_operator_cmdb_host_quite_buffer():
    # 定义过期时间点
    now_time = timezone.now()
    start_expire_time = now_time - timedelta(days=3)

    # 获取所有即将到期的主机
    quite_hosts = Host.objects.filter(
        host_status="静默",
        expire_time__lte=start_expire_time,
        is_deleted=False,
    ).all()
    # TODO 暂时使用旧业务更新，测试通知一致后再进行修改数据
    # for quite_host in quite_hosts:
    #     quite_host.host_status = "空闲"
    #     quite_host.is_buffer = 1
    #     quite_host.save()
    # 转换数据格式
    quite_hosts = [i.to_dict for i in quite_hosts]
    ManagerHostStatusNotice().send_operator_cmdb_host_quite_notice(
        '【新增Buffer主机提醒】',
        quite_hosts=quite_hosts,
    )
