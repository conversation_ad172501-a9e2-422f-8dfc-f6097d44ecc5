from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from operatorcmdb.models import FastInstallSoftware


class FastInstallSoftwareImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = FastInstallSoftware
        exclude = ()


class FastInstallSoftwareSerializer(ChaosCustomModelSerializer):
    """
    主机-序列化器
    """

    class Meta:
        model = FastInstallSoftware
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]

