from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from operatorcmdb.models import (
    VirtualSwitch,
    VirtualSwitchPhysicalInterface,
    VirtualSwitchTrunkInterface,
    VirtualSwitchMacAddress
    )


class VirtualSwitchSerializer(ChaosCustomModelSerializer):
    """虚拟交换机序列化器"""

    class Meta:
        model = VirtualSwitch
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


class VirtualSwitchPhysicalInterfaceSerializer(ChaosCustomModelSerializer):
    """虚拟交换机物理接口序列化器"""

    class Meta:
        model = VirtualSwitchPhysicalInterface
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


class VirtualSwitchTrunkInterfaceSerializer(ChaosCustomModelSerializer):
    """虚拟交换机聚合接口序列化器"""

    class Meta:
        model = VirtualSwitchTrunkInterface
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


class VirtualSwitchMacAddressSerializer(ChaosCustomModelSerializer):
    """虚拟交换机MAC地址序列化器"""

    class Meta:
        model = VirtualSwitchMacAddress
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]