from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from operatorcmdb.models import HostMaintenance


class HostMaintenanceImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = HostMaintenance
        exclude = ()


class HostMaintenanceSerializer(ChaosCustomModelSerializer):
    """
    主机-序列化器
    """

    class Meta:
        model = HostMaintenance
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]

