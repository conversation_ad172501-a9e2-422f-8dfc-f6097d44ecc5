"""
G6图形展示数据格式化服务
将交换机数据转换为G6可视化组件所需的层次化树形结构
"""

import logging
import random
from typing import Dict, List, Any, Optional
from django.utils import timezone

from operatorcmdb.models import (
    VirtualSwitch,
    VirtualSwitchPhysicalInterface,
    VirtualSwitchTrunkInterface,
    VirtualSwitchMacAddress
)
from xresource.models import MachineRoom, PrivateRoom, IDCRackMachine

logger = logging.getLogger(__name__)


class G6DataService:
    """G6数据格式化服务"""
    
    def __init__(self):
        self.logger = logger
    
    def get_switch_g6_data(self, virtual_switch_id: str) -> Dict[str, Any]:
        """
        获取单个交换机的G6格式数据
        
        Args:
            virtual_switch_id: 虚拟交换机ID
            
        Returns:
            Dict: G6格式的层次化数据
        """
        try:
            virtual_switch = VirtualSwitch.objects.select_related(
                'physical_switch', 'machine_room', 'private_room', 'idc_rack_machine'
            ).get(id=virtual_switch_id, is_deleted=False)
            
            # 获取接口数据
            physical_interfaces = VirtualSwitchPhysicalInterface.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            )
            trunk_interfaces = VirtualSwitchTrunkInterface.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            )
            mac_addresses = VirtualSwitchMacAddress.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
            )
            
            # 构建G6数据结构
            g6_data = self._build_switch_g6_structure(
                virtual_switch, physical_interfaces, trunk_interfaces, mac_addresses
            )
            
            return g6_data
            
        except VirtualSwitch.DoesNotExist:
            return {
                'error': f'未找到ID为{virtual_switch_id}的虚拟交换机'
            }
        except Exception as e:
            self.logger.error(f"获取交换机G6数据失败: {str(e)}")
            return {
                'error': f'获取数据失败: {str(e)}'
            }
    
    def get_room_switches_g6_data(self, machine_room_id: str) -> Dict[str, Any]:
        """
        获取机房内所有交换机的G6格式数据
        
        Args:
            machine_room_id: 机房ID
            
        Returns:
            Dict: G6格式的层次化数据
        """
        try:
            machine_room = MachineRoom.objects.get(id=machine_room_id, is_deleted=False)
            
            # 获取机房内的交换机
            switches = VirtualSwitch.objects.filter(
                machine_room=machine_room, is_deleted=False
            ).select_related('physical_switch', 'private_room', 'idc_rack_machine')
            
            if not switches.exists():
                return {
                    'id': f'room-{machine_room_id}',
                    'name': machine_room.name,
                    'count': 0,
                    'label': '0',
                    'currency': 'Devices',
                    'rate': 1,
                    'status': 'G',
                    'variableName': 'Switches',
                    'variableValue': 0,
                    'variableUp': False,
                    'children': []
                }
            
            # 构建机房级别的G6数据
            g6_data = self._build_room_g6_structure(machine_room, switches)
            
            return g6_data
            
        except MachineRoom.DoesNotExist:
            return {
                'error': f'未找到ID为{machine_room_id}的机房'
            }
        except Exception as e:
            self.logger.error(f"获取机房G6数据失败: {str(e)}")
            return {
                'error': f'获取数据失败: {str(e)}'
            }
    
    def _build_switch_g6_structure(self, virtual_switch, physical_interfaces, trunk_interfaces, mac_addresses) -> Dict[str, Any]:
        """构建单个交换机的G6数据结构"""
        
        # 计算统计数据
        total_interfaces = physical_interfaces.count() + trunk_interfaces.count()
        active_interfaces = physical_interfaces.filter(state='up').count() + trunk_interfaces.filter(state='up').count()
        interface_rate = active_interfaces / total_interfaces if total_interfaces > 0 else 0
        
        # 交换机根节点
        switch_data = {
            'id': f'switch-{virtual_switch.id}',
            'name': virtual_switch.name or f'Switch-{virtual_switch.id[:8]}',
            'count': total_interfaces,
            'label': f'{active_interfaces}/{total_interfaces}',
            'currency': 'Interfaces',
            'rate': 1,
            'status': self._get_switch_status(virtual_switch, interface_rate),
            'variableName': 'Uptime',
            'variableValue': self._calculate_uptime_ratio(virtual_switch),
            'variableUp': interface_rate > 0.8,
            'children': []
        }
        
        # 添加物理接口分组
        if physical_interfaces.exists():
            physical_group = self._build_physical_interfaces_group(physical_interfaces)
            switch_data['children'].append(physical_group)
        
        # 添加聚合接口分组
        if trunk_interfaces.exists():
            trunk_group = self._build_trunk_interfaces_group(trunk_interfaces)
            switch_data['children'].append(trunk_group)
        
        # 添加MAC地址分组
        if mac_addresses.exists():
            mac_group = self._build_mac_addresses_group(mac_addresses)
            switch_data['children'].append(mac_group)
        
        # 添加基础设施信息
        if virtual_switch.machine_room or virtual_switch.private_room or virtual_switch.idc_rack_machine:
            infrastructure_group = self._build_infrastructure_group(virtual_switch)
            switch_data['children'].append(infrastructure_group)
        
        return switch_data
    
    def _build_physical_interfaces_group(self, physical_interfaces) -> Dict[str, Any]:
        """构建物理接口分组"""
        total_count = physical_interfaces.count()
        up_count = physical_interfaces.filter(state='up').count()
        rate = up_count / total_count if total_count > 0 else 0
        
        group_data = {
            'id': f'physical-interfaces-{random.randint(1000, 9999)}',
            'name': 'Physical Interfaces',
            'count': total_count,
            'label': f'{up_count}/{total_count}',
            'currency': 'Ports',
            'rate': rate,
            'status': 'G' if rate > 0.8 else 'R' if rate < 0.5 else 'B',
            'variableName': 'Active',
            'variableValue': rate,
            'variableUp': rate > 0.5,
            'children': []
        }
        
        # 按速度分组接口
        speed_groups = {}
        for interface in physical_interfaces:
            speed = interface.speed or 0
            speed_key = f'{speed}Mbps' if speed > 0 else 'Unknown'
            
            if speed_key not in speed_groups:
                speed_groups[speed_key] = []
            speed_groups[speed_key].append(interface)
        
        # 为每个速度组创建子节点
        for speed_key, interfaces in speed_groups.items():
            speed_group = self._build_interface_speed_group(speed_key, interfaces)
            group_data['children'].append(speed_group)
        
        return group_data
    
    def _build_interface_speed_group(self, speed_key: str, interfaces: List) -> Dict[str, Any]:
        """构建接口速度分组"""
        total_count = len(interfaces)
        up_count = sum(1 for iface in interfaces if iface.state == 'up')
        rate = up_count / total_count if total_count > 0 else 0
        
        speed_group = {
            'id': f'speed-{speed_key}-{random.randint(1000, 9999)}',
            'name': f'{speed_key} Interfaces',
            'count': total_count,
            'label': f'{up_count}/{total_count}',
            'currency': 'Ports',
            'rate': rate,
            'status': 'G' if rate > 0.8 else 'R' if rate < 0.5 else 'B',
            'variableName': 'Utilization',
            'variableValue': rate,
            'variableUp': rate > 0.5,
            'collapsed': True,
            'children': []
        }
        
        # 添加具体接口
        for interface in interfaces:
            interface_node = {
                'id': f'interface-{interface.id}',
                'name': interface.name,
                'count': 1,
                'label': interface.state or 'unknown',
                'currency': 'Status',
                'rate': 1 if interface.state == 'up' else 0,
                'status': 'G' if interface.state == 'up' else 'R',
                'variableName': 'Speed',
                'variableValue': (interface.speed or 0) / 10000,  # 标准化到0-1
                'variableUp': interface.state == 'up',
                'children': []
            }
            speed_group['children'].append(interface_node)
        
        return speed_group
    
    def _build_trunk_interfaces_group(self, trunk_interfaces) -> Dict[str, Any]:
        """构建聚合接口分组"""
        total_count = trunk_interfaces.count()
        up_count = trunk_interfaces.filter(state='up').count()
        rate = up_count / total_count if total_count > 0 else 0
        
        group_data = {
            'id': f'trunk-interfaces-{random.randint(1000, 9999)}',
            'name': 'Trunk Interfaces',
            'count': total_count,
            'label': f'{up_count}/{total_count}',
            'currency': 'Trunks',
            'rate': rate,
            'status': 'G' if rate > 0.8 else 'R' if rate < 0.5 else 'B',
            'variableName': 'Active',
            'variableValue': rate,
            'variableUp': rate > 0.5,
            'collapsed': True,
            'children': []
        }
        
        # 添加具体聚合接口
        for trunk in trunk_interfaces:
            member_count = len(trunk.get_members_list())
            trunk_node = {
                'id': f'trunk-{trunk.id}',
                'name': trunk.name,
                'count': member_count,
                'label': f'{member_count} members',
                'currency': 'Members',
                'rate': 1 if trunk.state == 'up' else 0,
                'status': 'G' if trunk.state == 'up' else 'R',
                'variableName': 'Aggregation',
                'variableValue': member_count / 10,  # 标准化
                'variableUp': trunk.state == 'up',
                'children': []
            }
            group_data['children'].append(trunk_node)
        
        return group_data
    
    def _build_mac_addresses_group(self, mac_addresses) -> Dict[str, Any]:
        """构建MAC地址分组"""
        total_count = mac_addresses.count()
        
        group_data = {
            'id': f'mac-addresses-{random.randint(1000, 9999)}',
            'name': 'MAC Addresses',
            'count': total_count,
            'label': f'{total_count}',
            'currency': 'MACs',
            'rate': 1,
            'status': 'B',
            'variableName': 'Learned',
            'variableValue': min(total_count / 100, 1),  # 标准化到0-1
            'variableUp': total_count > 0,
            'collapsed': True,
            'children': []
        }
        
        return group_data
    
    def _build_infrastructure_group(self, virtual_switch) -> Dict[str, Any]:
        """构建基础设施信息分组"""
        group_data = {
            'id': f'infrastructure-{random.randint(1000, 9999)}',
            'name': 'Infrastructure',
            'count': 1,
            'label': 'Location',
            'currency': 'Info',
            'rate': 1,
            'status': 'B',
            'variableName': 'Rack',
            'variableValue': virtual_switch.rack_unit / 42 if virtual_switch.rack_unit > 0 else 0,
            'variableUp': True,
            'collapsed': True,
            'children': []
        }
        
        # 添加机房信息
        if virtual_switch.machine_room:
            room_node = {
                'id': f'room-{virtual_switch.machine_room.id}',
                'name': virtual_switch.machine_room.name,
                'count': 1,
                'label': 'Machine Room',
                'currency': 'Location',
                'rate': 1,
                'status': 'G',
                'variableName': 'Region',
                'variableValue': 0.8,
                'variableUp': True,
                'children': []
            }
            group_data['children'].append(room_node)
        
        # 添加机柜信息
        if virtual_switch.idc_rack_machine:
            rack_node = {
                'id': f'rack-{virtual_switch.idc_rack_machine.id}',
                'name': virtual_switch.idc_rack_machine.rack_sn or 'Unknown Rack',
                'count': 1,
                'label': f'U{virtual_switch.rack_unit}',
                'currency': 'Position',
                'rate': 1,
                'status': 'B',
                'variableName': 'Utilization',
                'variableValue': virtual_switch.rack_unit / 42 if virtual_switch.rack_unit > 0 else 0,
                'variableUp': True,
                'children': []
            }
            group_data['children'].append(rack_node)
        
        return group_data
    
    def _build_room_g6_structure(self, machine_room, switches) -> Dict[str, Any]:
        """构建机房级别的G6数据结构"""
        total_switches = switches.count()
        
        # 机房根节点
        room_data = {
            'id': f'room-{machine_room.id}',
            'name': machine_room.name,
            'count': total_switches,
            'label': f'{total_switches}',
            'currency': 'Switches',
            'rate': 1,
            'status': 'G',
            'variableName': 'Capacity',
            'variableValue': min(total_switches / 50, 1),  # 假设机房容量50台
            'variableUp': total_switches > 0,
            'children': []
        }
        
        # 按包间分组交换机
        private_room_groups = {}
        no_room_switches = []
        
        for switch in switches:
            if switch.private_room:
                room_key = switch.private_room.id
                if room_key not in private_room_groups:
                    private_room_groups[room_key] = {
                        'room': switch.private_room,
                        'switches': []
                    }
                private_room_groups[room_key]['switches'].append(switch)
            else:
                no_room_switches.append(switch)
        
        # 为每个包间创建分组
        for room_key, room_data_item in private_room_groups.items():
            private_room_group = self._build_private_room_group(room_data_item['room'], room_data_item['switches'])
            room_data['children'].append(private_room_group)
        
        # 添加未分配包间的交换机
        if no_room_switches:
            unassigned_group = self._build_unassigned_switches_group(no_room_switches)
            room_data['children'].append(unassigned_group)
        
        return room_data
    
    def _build_private_room_group(self, private_room, switches) -> Dict[str, Any]:
        """构建包间分组"""
        switch_count = len(switches)
        
        group_data = {
            'id': f'private-room-{private_room.id}',
            'name': private_room.name,
            'count': switch_count,
            'label': f'{switch_count}',
            'currency': 'Switches',
            'rate': 1,
            'status': 'B',
            'variableName': 'Utilization',
            'variableValue': min(switch_count / 20, 1),  # 假设包间容量20台
            'variableUp': switch_count > 0,
            'collapsed': True,
            'children': []
        }
        
        # 添加交换机节点
        for switch in switches:
            switch_node = self._build_simple_switch_node(switch)
            group_data['children'].append(switch_node)
        
        return group_data
    
    def _build_unassigned_switches_group(self, switches) -> Dict[str, Any]:
        """构建未分配包间的交换机分组"""
        switch_count = len(switches)
        
        group_data = {
            'id': f'unassigned-switches-{random.randint(1000, 9999)}',
            'name': 'Unassigned Switches',
            'count': switch_count,
            'label': f'{switch_count}',
            'currency': 'Switches',
            'rate': 1,
            'status': 'DI',
            'variableName': 'Unassigned',
            'variableValue': 0.5,
            'variableUp': False,
            'collapsed': True,
            'children': []
        }
        
        # 添加交换机节点
        for switch in switches:
            switch_node = self._build_simple_switch_node(switch)
            group_data['children'].append(switch_node)
        
        return group_data
    
    def _build_simple_switch_node(self, switch) -> Dict[str, Any]:
        """构建简单的交换机节点"""
        return {
            'id': f'switch-{switch.id}',
            'name': switch.name or f'Switch-{switch.id[:8]}',
            'count': 1,
            'label': switch.management_ip or 'No IP',
            'currency': 'Device',
            'rate': 1,
            'status': self._get_switch_status(switch, 0.8),
            'variableName': 'Health',
            'variableValue': random.uniform(0.6, 0.9),
            'variableUp': True,
            'children': []
        }
    
    def _get_switch_status(self, switch, interface_rate: float) -> str:
        """获取交换机状态"""
        if not switch.last_sync_at:
            return 'DI'  # 未同步
        elif interface_rate > 0.8:
            return 'G'   # 良好
        elif interface_rate > 0.5:
            return 'B'   # 一般
        else:
            return 'R'   # 风险
    
    def _calculate_uptime_ratio(self, switch) -> float:
        """计算运行时间比率"""
        if switch.last_sync_at:
            # 简单计算：基于最后同步时间
            now = timezone.now()
            diff = now - switch.last_sync_at
            hours_since_sync = diff.total_seconds() / 3600
            # 如果24小时内同步过，认为状态良好
            return max(0, 1 - (hours_since_sync / 24))
        return 0.5


# 创建全局服务实例
g6_data_service = G6DataService()
