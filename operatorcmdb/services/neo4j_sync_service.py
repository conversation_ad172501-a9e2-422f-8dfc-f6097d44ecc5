"""
Neo4j图数据库同步服务
将交换机相关的数据关系同步到Neo4j数据库中
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

try:
    from neo4j import GraphDatabase

    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False
    GraphDatabase = None

from django.conf import settings
from django.utils import timezone

from operatorcmdb.models import (
    VirtualSwitch,
    VirtualSwitchPhysicalInterface,
    VirtualSwitchTrunkInterface,
    VirtualSwitchMacAddress
    )
from xresource.models import MachineRoom, PrivateRoom, IDCRackMachine, NetworkHardware

logger = logging.getLogger(__name__)


class Neo4jSyncResult:
    """Neo4j同步结果"""

    def __init__(self):
        self.success = False
        self.message = ""
        self.nodes_created = 0
        self.nodes_updated = 0
        self.relationships_created = 0
        self.relationships_updated = 0
        self.error = None


class Neo4jSwitchSyncService:
    """Neo4j交换机数据同步服务"""

    def __init__(self, uri: str = None, username: str = None, password: str = None):
        """
        初始化Neo4j连接

        Args:
            uri: Neo4j数据库URI
            username: 用户名
            password: 密码
        """
        if not NEO4J_AVAILABLE:
            raise ImportError("neo4j package is not installed. Please install it with: pip install neo4j")

        # 从Django设置或参数获取连接信息
        self.uri = uri or getattr(settings, 'NEO4J_URI', 'bolt://localhost:7687')
        self.username = username or getattr(settings, 'NEO4J_USERNAME', 'neo4j')
        self.password = password or getattr(settings, 'NEO4J_PASSWORD', 'password')

        self.driver = None
        self.logger = logger

    def __enter__(self):
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def connect(self):
        """建立Neo4j连接"""
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            # 测试连接
            with self.driver.session() as session:
                session.run("RETURN 1")
            self.logger.info("Neo4j连接成功")
        except Exception as e:
            self.logger.error(f"Neo4j连接失败: {str(e)}")
            raise

    def close(self):
        """关闭Neo4j连接"""
        if self.driver:
            self.driver.close()
            self.logger.info("Neo4j连接已关闭")

    def sync_switch_to_neo4j(self, virtual_switch_id: int) -> Neo4jSyncResult:
        """
        将指定交换机的数据同步到Neo4j

        Args:
            virtual_switch_id: 虚拟交换机ID

        Returns:
            Neo4jSyncResult: 同步结果
        """
        result = Neo4jSyncResult()

        try:
            # 获取交换机实例
            virtual_switch = VirtualSwitch.objects.select_related(
                'physical_switch', 'machine_room', 'private_room', 'idc_rack_machine'
                ).get(id=virtual_switch_id, is_deleted=False)

            # 确保接口数据已同步 - 修改调用方式

            with self.driver.session() as session:
                # 开始事务
                with session.begin_transaction() as tx:
                    # 1. 同步基础设施节点
                    self._sync_infrastructure_nodes(tx, virtual_switch, result)

                    # 2. 同步交换机节点
                    self._sync_switch_node(tx, virtual_switch, result)

                    # 3. 同步接口节点
                    self._sync_interface_nodes(tx, virtual_switch, result)

                    # 4. 同步MAC地址节点
                    self._sync_mac_nodes(tx, virtual_switch, result)

                    # 5. 创建关系
                    self._create_relationships(tx, virtual_switch, result)

                    # 提交事务
                    tx.commit()

            result.success = True
            result.message = f"交换机 {virtual_switch.name} 同步到Neo4j成功"
            self.logger.info(result.message)

        except VirtualSwitch.DoesNotExist:
            result.message = f"未找到ID为{virtual_switch_id}的虚拟交换机"
            self.logger.error(result.message)
        except Exception as e:
            result.message = f"同步到Neo4j失败: {str(e)}"
            result.error = e
            self.logger.error(result.message, exc_info=True)

        return result

    def _sync_infrastructure_nodes(self, tx, virtual_switch: VirtualSwitch, result: Neo4jSyncResult):
        """同步基础设施节点（机房、包间、机柜等）"""

        # 同步机房
        if virtual_switch.machine_room:
            machine_room = virtual_switch.machine_room
            query = """
            MERGE (mr:MachineRoom {id: $id})
            SET mr.name = $name,
                mr.description = $description,
                mr.province = $province,
                mr.city = $city,
                mr.district = $district,
                mr.address = $address,
                mr.updated_at = $updated_at
            RETURN mr
            """
            tx.run(
                query, {
                    'id': machine_room.id,
                    'name': machine_room.name,
                    'description': machine_room.description or '',
                    'province': machine_room.province or '',
                    'city': machine_room.city or '',
                    'district': machine_room.district or '',
                    'address': machine_room.address or '',
                    'updated_at': timezone.now().isoformat()
                    }
                )
            result.nodes_created += 1

        # 同步包间
        if virtual_switch.private_room:
            private_room = virtual_switch.private_room
            query = """
            MERGE (pr:PrivateRoom {id: $id})
            SET pr.name = $name,
                pr.private_room_type = $private_room_type,
                pr.description = $description,
                pr.updated_at = $updated_at
            RETURN pr
            """
            tx.run(
                query, {
                    'id': private_room.id,
                    'name': private_room.name,
                    'private_room_type': private_room.private_room_type or '',
                    'description': private_room.description or '',
                    'updated_at': timezone.now().isoformat()
                    }
                )
            result.nodes_created += 1

        # 同步机柜
        if virtual_switch.idc_rack_machine:
            rack = virtual_switch.idc_rack_machine
            query = """
            MERGE (rack:IDCRack {id: $id})
            SET rack.rack_sn = $rack_sn,
                rack.rack_type = $rack_type,
                rack.rack_height = $rack_height,
                rack.updated_at = $updated_at
            RETURN rack
            """
            tx.run(
                query, {
                    'id': rack.id,
                    'rack_sn': rack.rack_sn or '',
                    'rack_type': rack.rack_type or '',
                    'rack_height': rack.rack_height or 0,
                    'updated_at': timezone.now().isoformat()
                    }
                )
            result.nodes_created += 1

        # 同步物理交换机
        if virtual_switch.physical_switch:
            physical_switch = virtual_switch.physical_switch
            query = """
            MERGE (ps:PhysicalSwitch {id: $id})
            SET ps.physical_machine_sn = $sn,
                ps.machine_type = $machine_type,
                ps.manufacturer = $manufacturer,
                ps.machine_specs = $machine_specs,
                ps.description = $description,
                ps.updated_at = $updated_at
            RETURN ps
            """
            tx.run(
                query, {
                    'id': physical_switch.id,
                    'sn': physical_switch.physical_machine_sn or '',
                    'machine_type': physical_switch.machine_type or '',
                    'manufacturer': physical_switch.manufacturer or '',
                    'machine_specs': physical_switch.machine_specs or '',
                    'description': physical_switch.description or '',
                    'updated_at': timezone.now().isoformat()
                    }
                )
            result.nodes_created += 1

    def _sync_switch_node(self, tx, virtual_switch: VirtualSwitch, result: Neo4jSyncResult):
        """同步虚拟交换机节点"""
        query = """
        MERGE (vs:VirtualSwitch {id: $id})
        SET vs.name = $name,
            vs.sn = $sn,
            vs.management_ip = $management_ip,
            vs.management_port = $management_port,
            vs.management_protocol = $management_protocol,
            vs.vendor = $vendor,
            vs.firmware_version = $firmware_version,
            vs.vlan_range = $vlan_range,
            vs.rack_unit = $rack_unit,
            vs.node = $node,
            vs.last_sync_at = $last_sync_at,
            vs.updated_at = $updated_at
        RETURN vs
        """
        tx.run(
            query, {
                'id': virtual_switch.id,
                'name': virtual_switch.name or '',
                'sn': virtual_switch.sn or '',
                'management_ip': virtual_switch.management_ip or '',
                'management_port': virtual_switch.management_port or 22,
                'management_protocol': virtual_switch.management_protocol or '',
                'vendor': virtual_switch.vendor or '',
                'firmware_version': virtual_switch.firmware_version or '',
                'vlan_range': virtual_switch.vlan_range or '',
                'rack_unit': virtual_switch.rack_unit or -1,
                'node': virtual_switch.node or '',
                'last_sync_at': virtual_switch.last_sync_at.isoformat() if virtual_switch.last_sync_at else '',
                'updated_at': timezone.now().isoformat()
                }
            )
        result.nodes_created += 1

    def _sync_interface_nodes(self, tx, virtual_switch: VirtualSwitch, result: Neo4jSyncResult):
        """同步接口节点"""

        # 同步物理接口
        physical_interfaces = VirtualSwitchPhysicalInterface.objects.filter(
            virtual_switch=virtual_switch, is_deleted=False
            )

        for interface in physical_interfaces:
            query = """
            MERGE (pi:PhysicalInterface {id: $id})
            SET pi.name = $name,
                pi.speed = $speed,
                pi.state = $state,
                pi.line_state = $line_state,
                pi.mac_address = $mac_address,
                pi.node = $node,
                pi.switch_id = $switch_id,
                pi.last_sync_at = $last_sync_at,
                pi.updated_at = $updated_at
            RETURN pi
            """
            tx.run(
                query, {
                    'id': interface.id,
                    'name': interface.name or '',
                    'speed': interface.speed or 0,
                    'state': interface.state or '',
                    'line_state': interface.line_state or '',
                    'mac_address': interface.mac_address or '',
                    'node': interface.node or '',
                    'switch_id': virtual_switch.id,
                    'last_sync_at': interface.last_sync_at.isoformat() if interface.last_sync_at else '',
                    'updated_at': timezone.now().isoformat()
                    }
                )
            result.nodes_created += 1

        # 同步聚合接口
        trunk_interfaces = VirtualSwitchTrunkInterface.objects.filter(
            virtual_switch=virtual_switch, is_deleted=False
            )

        for trunk in trunk_interfaces:
            query = """
            MERGE (ti:TrunkInterface {id: $id})
            SET ti.name = $name,
                ti.state = $state,
                ti.line_state = $line_state,
                ti.mac_address = $mac_address,
                ti.members = $members,
                ti.node = $node,
                ti.switch_id = $switch_id,
                ti.last_sync_at = $last_sync_at,
                ti.updated_at = $updated_at
            RETURN ti
            """
            tx.run(
                query, {
                    'id': trunk.id,
                    'name': trunk.name or '',
                    'state': trunk.state or '',
                    'line_state': trunk.line_state or '',
                    'mac_address': trunk.mac_address or '',
                    'members': trunk.members or '',
                    'node': trunk.node or '',
                    'switch_id': virtual_switch.id,
                    'last_sync_at': trunk.last_sync_at.isoformat() if trunk.last_sync_at else '',
                    'updated_at': timezone.now().isoformat()
                    }
                )
            result.nodes_created += 1

    def _sync_mac_nodes(self, tx, virtual_switch: VirtualSwitch, result: Neo4jSyncResult):
        """同步MAC地址节点"""
        mac_addresses = VirtualSwitchMacAddress.objects.filter(
            virtual_switch=virtual_switch, is_deleted=False
            )

        for mac in mac_addresses:
            query = """
            MERGE (mac:MacAddress {id: $id})
            SET mac.mac_address = $mac_address,
                mac.interface = $interface,
                mac.node = $node,
                mac.switch_id = $switch_id,
                mac.last_sync_at = $last_sync_at,
                mac.updated_at = $updated_at
            RETURN mac
            """
            tx.run(
                query, {
                    'id': mac.id,
                    'mac_address': mac.mac_address or '',
                    'interface': mac.interface or '',
                    'node': mac.node or '',
                    'switch_id': virtual_switch.id,
                    'last_sync_at': mac.last_sync_at.isoformat() if mac.last_sync_at else '',
                    'updated_at': timezone.now().isoformat()
                    }
                )
            result.nodes_created += 1

    def _create_relationships(self, tx, virtual_switch: VirtualSwitch, result: Neo4jSyncResult):
        """创建节点之间的关系"""

        # 1. 交换机与基础设施的关系
        if virtual_switch.machine_room:
            query = """
            MATCH (vs:VirtualSwitch {id: $switch_id})
            MATCH (mr:MachineRoom {id: $room_id})
            MERGE (vs)-[:LOCATED_IN]->(mr)
            """
            tx.run(
                query, {
                    'switch_id': virtual_switch.id,
                    'room_id': virtual_switch.machine_room.id
                    }
                )
            result.relationships_created += 1

        if virtual_switch.private_room:
            query = """
            MATCH (vs:VirtualSwitch {id: $switch_id})
            MATCH (pr:PrivateRoom {id: $private_room_id})
            MERGE (vs)-[:LOCATED_IN]->(pr)
            """
            tx.run(
                query, {
                    'switch_id': virtual_switch.id,
                    'private_room_id': virtual_switch.private_room.id
                    }
                )
            result.relationships_created += 1

        if virtual_switch.idc_rack_machine:
            query = """
            MATCH (vs:VirtualSwitch {id: $switch_id})
            MATCH (rack:IDCRack {id: $rack_id})
            MERGE (vs)-[:INSTALLED_IN {rack_unit: $rack_unit}]->(rack)
            """
            tx.run(
                query, {
                    'switch_id': virtual_switch.id,
                    'rack_id': virtual_switch.idc_rack_machine.id,
                    'rack_unit': virtual_switch.rack_unit
                    }
                )
            result.relationships_created += 1

        if virtual_switch.physical_switch:
            query = """
            MATCH (vs:VirtualSwitch {id: $switch_id})
            MATCH (ps:PhysicalSwitch {id: $physical_switch_id})
            MERGE (ps)-[:HAS_VIRTUAL_SWITCH]->(vs)
            """
            tx.run(
                query, {
                    'switch_id': virtual_switch.id,
                    'physical_switch_id': virtual_switch.physical_switch.id
                    }
                )
            result.relationships_created += 1

            # 同时保留原有的VIRTUALIZED_FROM关系，确保兼容性
            query = """
            MATCH (vs:VirtualSwitch {id: $switch_id})
            MATCH (ps:PhysicalSwitch {id: $physical_switch_id})
            MERGE (vs)-[:VIRTUALIZED_FROM]->(ps)
            """
            tx.run(
                query, {
                    'switch_id': virtual_switch.id,
                    'physical_switch_id': virtual_switch.physical_switch.id
                    }
                )
            result.relationships_created += 1

        # 2. 基础设施之间的关系
        if virtual_switch.private_room and virtual_switch.machine_room:
            query = """
            MATCH (pr:PrivateRoom {id: $private_room_id})
            MATCH (mr:MachineRoom {id: $room_id})
            MERGE (pr)-[:BELONGS_TO]->(mr)
            """
            tx.run(
                query, {
                    'private_room_id': virtual_switch.private_room.id,
                    'room_id': virtual_switch.machine_room.id
                    }
                )
            result.relationships_created += 1

        if virtual_switch.idc_rack_machine and virtual_switch.private_room:
            query = """
            MATCH (rack:IDCRack {id: $rack_id})
            MATCH (pr:PrivateRoom {id: $private_room_id})
            MERGE (rack)-[:LOCATED_IN]->(pr)
            """
            tx.run(
                query, {
                    'rack_id': virtual_switch.idc_rack_machine.id,
                    'private_room_id': virtual_switch.private_room.id
                    }
                )
            result.relationships_created += 1

        # 3. 接口与交换机的关系
        query = """
        MATCH (vs:VirtualSwitch {id: $switch_id})
        MATCH (pi:PhysicalInterface {switch_id: $switch_id})
        MERGE (vs)-[:HAS_INTERFACE]->(pi)
        """
        tx.run(query, {'switch_id': virtual_switch.id})

        query = """
        MATCH (vs:VirtualSwitch {id: $switch_id})
        MATCH (ti:TrunkInterface {switch_id: $switch_id})
        MERGE (vs)-[:HAS_INTERFACE]->(ti)
        """
        tx.run(query, {'switch_id': virtual_switch.id})

        # 4. MAC地址与交换机的关系
        query = """
        MATCH (vs:VirtualSwitch {id: $switch_id})
        MATCH (mac:MacAddress {switch_id: $switch_id})
        MERGE (vs)-[:HAS_MAC]->(mac)
        """
        tx.run(query, {'switch_id': virtual_switch.id})

        # 5. 聚合接口与成员接口的关系
        trunk_interfaces = VirtualSwitchTrunkInterface.objects.filter(
            virtual_switch=virtual_switch, is_deleted=False
            )

        for trunk in trunk_interfaces:
            if trunk.members:
                member_names = trunk.get_members_list()
                for member_name in member_names:
                    query = """
                    MATCH (ti:TrunkInterface {id: $trunk_id})
                    MATCH (pi:PhysicalInterface {name: $member_name, switch_id: $switch_id})
                    MERGE (ti)-[:AGGREGATES]->(pi)
                    """
                    tx.run(
                        query, {
                            'trunk_id': trunk.id,
                            'member_name': member_name,
                            'switch_id': virtual_switch.id
                            }
                        )
                    result.relationships_created += 1

        # 添加物理接口与聚合接口的反向关系（多对一）
        # 这里创建从物理接口到聚合接口的关系
        for trunk in trunk_interfaces:
            if trunk.members:
                member_names = trunk.get_members_list()
                for member_name in member_names:
                    query = """
                    MATCH (pi:PhysicalInterface {name: $member_name, switch_id: $switch_id})
                    MATCH (ti:TrunkInterface {id: $trunk_id})
                    MERGE (pi)-[:BELONGS_TO]->(ti)
                    """
                    tx.run(
                        query, {
                            'member_name': member_name,
                            'switch_id': virtual_switch.id,
                            'trunk_id': trunk.id
                            }
                        )
                    result.relationships_created += 1

    def sync_all_switches_to_neo4j(self) -> Neo4jSyncResult:
        """同步所有交换机到Neo4j"""
        result = Neo4jSyncResult()

        try:
            switches = VirtualSwitch.objects.filter(is_deleted=False)
            total_switches = switches.count()

            if total_switches == 0:
                result.success = True
                result.message = "没有需要同步的交换机"
                return result

            success_count = 0
            failed_count = 0

            for switch in switches:
                try:
                    switch_result = self.sync_switch_to_neo4j(switch.id)
                    if switch_result.success:
                        success_count += 1
                        result.nodes_created += switch_result.nodes_created
                        result.relationships_created += switch_result.relationships_created
                    else:
                        failed_count += 1
                        self.logger.error(f"同步交换机失败 {switch.name}: {switch_result.message}")
                except Exception as e:
                    failed_count += 1
                    self.logger.error(f"同步交换机异常 {switch.name}: {str(e)}")

            result.success = True
            result.message = f"批量同步完成: 成功 {success_count}, 失败 {failed_count}, 总计 {total_switches}"
            self.logger.info(result.message)

        except Exception as e:
            result.message = f"批量同步失败: {str(e)}"
            result.error = e
            self.logger.error(result.message, exc_info=True)

        return result

    def clear_switch_data(self, virtual_switch_id: str) -> Neo4jSyncResult:
        """清除指定交换机的Neo4j数据"""
        result = Neo4jSyncResult()

        try:
            with self.driver.session() as session:
                # 删除交换机及其相关的所有节点和关系
                query = """
                MATCH (vs:VirtualSwitch {id: $switch_id})
                OPTIONAL MATCH (vs)-[r1]-()
                OPTIONAL MATCH (pi:PhysicalInterface {switch_id: $switch_id})
                OPTIONAL MATCH (pi)-[r2]-()
                OPTIONAL MATCH (ti:TrunkInterface {switch_id: $switch_id})
                OPTIONAL MATCH (ti)-[r3]-()
                OPTIONAL MATCH (mac:MacAddress {switch_id: $switch_id})
                OPTIONAL MATCH (mac)-[r4]-()
                DELETE r1, r2, r3, r4, vs, pi, ti, mac
                """
                session.run(query, {'switch_id': virtual_switch_id})

                result.success = True
                result.message = f"交换机 {virtual_switch_id} 的Neo4j数据已清除"
                self.logger.info(result.message)

        except Exception as e:
            result.message = f"清除Neo4j数据失败: {str(e)}"
            result.error = e
            self.logger.error(result.message, exc_info=True)

        return result

    def query_switch_topology(self, virtual_switch_id: str) -> Dict[str, Any]:
        """查询交换机拓扑关系"""
        try:
            with self.driver.session() as session:
                query = """
                MATCH (vs:VirtualSwitch {id: $switch_id})
                OPTIONAL MATCH (vs)-[:LOCATED_IN]->(mr:MachineRoom)
                OPTIONAL MATCH (vs)-[:LOCATED_IN]->(pr:PrivateRoom)
                OPTIONAL MATCH (vs)-[:INSTALLED_IN]->(rack:IDCRack)
                OPTIONAL MATCH (vs)-[:VIRTUALIZED_FROM]->(ps:PhysicalSwitch)
                OPTIONAL MATCH (vs)-[:HAS_INTERFACE]->(pi:PhysicalInterface)
                OPTIONAL MATCH (vs)-[:HAS_INTERFACE]->(ti:TrunkInterface)
                OPTIONAL MATCH (vs)-[:HAS_MAC]->(mac:MacAddress)
                OPTIONAL MATCH (ti)-[:AGGREGATES]->(member:PhysicalInterface)
                RETURN vs, mr, pr, rack, ps,
                       collect(DISTINCT pi) as physical_interfaces,
                       collect(DISTINCT ti) as trunk_interfaces,
                       collect(DISTINCT mac) as mac_addresses,
                       collect(DISTINCT member) as trunk_members
                """
                result = session.run(query, {'switch_id': virtual_switch_id})
                record = result.single()

                if record:
                    return {
                        'switch': dict(record['vs']) if record['vs'] else None,
                        'machine_room': dict(record['mr']) if record['mr'] else None,
                        'private_room': dict(record['pr']) if record['pr'] else None,
                        'rack': dict(record['rack']) if record['rack'] else None,
                        'physical_switch': dict(record['ps']) if record['ps'] else None,
                        'physical_interfaces': [dict(pi) for pi in record['physical_interfaces']],
                        'trunk_interfaces': [dict(ti) for ti in record['trunk_interfaces']],
                        'mac_addresses': [dict(mac) for mac in record['mac_addresses']],
                        'trunk_members': [dict(member) for member in record['trunk_members']]
                        }
                else:
                    return {}

        except Exception as e:
            self.logger.error(f"查询交换机拓扑失败: {str(e)}")
            return {}

    def query_switches_by_room(self, machine_room_id: str) -> List[Dict[str, Any]]:
        """查询指定机房的所有交换机"""
        try:
            with self.driver.session() as session:
                query = """
                MATCH (mr:MachineRoom {id: $room_id})<-[:LOCATED_IN]-(vs:VirtualSwitch)
                OPTIONAL MATCH (vs)-[:HAS_INTERFACE]->(pi:PhysicalInterface)
                OPTIONAL MATCH (vs)-[:HAS_INTERFACE]->(ti:TrunkInterface)
                RETURN vs, count(DISTINCT pi) as physical_count, count(DISTINCT ti) as trunk_count
                ORDER BY vs.name
                """
                result = session.run(query, {'room_id': machine_room_id})

                switches = []
                for record in result:
                    switch_data = dict(record['vs'])
                    switch_data['physical_interface_count'] = record['physical_count']
                    switch_data['trunk_interface_count'] = record['trunk_count']
                    switches.append(switch_data)

                return switches

        except Exception as e:
            self.logger.error(f"查询机房交换机失败: {str(e)}")
            return []

    def query_interface_connections(self, interface_mac: str) -> List[Dict[str, Any]]:
        """根据MAC地址查询接口连接关系"""
        try:
            with self.driver.session() as session:
                query = """
                MATCH (mac:MacAddress {mac_address: $mac_address})
                MATCH (mac)<-[:HAS_MAC]-(vs:VirtualSwitch)
                OPTIONAL MATCH (vs)-[:LOCATED_IN]->(mr:MachineRoom)
                OPTIONAL MATCH (vs)-[:INSTALLED_IN]->(rack:IDCRack)
                RETURN vs, mr, rack, mac
                """
                result = session.run(query, {'mac_address': interface_mac})

                connections = []
                for record in result:
                    connections.append(
                        {
                            'switch': dict(record['vs']),
                            'machine_room': dict(record['mr']) if record['mr'] else None,
                            'rack': dict(record['rack']) if record['rack'] else None,
                            'mac_info': dict(record['mac'])
                            }
                        )

                return connections

        except Exception as e:
            self.logger.error(f"查询接口连接失败: {str(e)}")
            return []

    def create_indexes(self):
        """创建Neo4j索引以提高查询性能"""
        try:
            with self.driver.session() as session:
                indexes = [
                    "CREATE INDEX IF NOT EXISTS FOR (vs:VirtualSwitch) ON (vs.id)",
                    "CREATE INDEX IF NOT EXISTS FOR (vs:VirtualSwitch) ON (vs.name)",
                    "CREATE INDEX IF NOT EXISTS FOR (vs:VirtualSwitch) ON (vs.management_ip)",
                    "CREATE INDEX IF NOT EXISTS FOR (pi:PhysicalInterface) ON (pi.id)",
                    "CREATE INDEX IF NOT EXISTS FOR (pi:PhysicalInterface) ON (pi.switch_id)",
                    "CREATE INDEX IF NOT EXISTS FOR (ti:TrunkInterface) ON (ti.id)",
                    "CREATE INDEX IF NOT EXISTS FOR (ti:TrunkInterface) ON (ti.switch_id)",
                    "CREATE INDEX IF NOT EXISTS FOR (mac:MacAddress) ON (mac.id)",
                    "CREATE INDEX IF NOT EXISTS FOR (mac:MacAddress) ON (mac.mac_address)",
                    "CREATE INDEX IF NOT EXISTS FOR (mr:MachineRoom) ON (mr.id)",
                    "CREATE INDEX IF NOT EXISTS FOR (pr:PrivateRoom) ON (pr.id)",
                    "CREATE INDEX IF NOT EXISTS FOR (rack:IDCRack) ON (rack.id)",
                    "CREATE INDEX IF NOT EXISTS FOR (ps:PhysicalSwitch) ON (ps.id)"
                    ]

                for index_query in indexes:
                    session.run(index_query)

                self.logger.info("Neo4j索引创建完成")

        except Exception as e:
            self.logger.error(f"创建Neo4j索引失败: {str(e)}")


# 创建全局服务实例
neo4j_sync_service = Neo4jSwitchSyncService()
