"""
交换机同步服务
用于异步任务调用的独立同步逻辑
"""

import logging
from typing import Dict, List, Optional, Tuple
from django.utils import timezone
from django.db import transaction

from operatorcmdb.models import (
    VirtualSwitch,
    VirtualSwitchPhysicalInterface,
    VirtualSwitchTrunkInterface,
    VirtualSwitchMacAddress
    )
from dvadmin.utils.sdk.network_switch.huawei.hw_switch_read import HuaweiSwitchReader

logger = logging.getLogger(__name__)

# 尝试导入Neo4j同步服务
try:
    from .neo4j_sync_service import neo4j_sync_service

    NEO4J_SYNC_AVAILABLE = True
except ImportError:
    NEO4J_SYNC_AVAILABLE = False
    neo4j_sync_service = None


class SwitchSyncResult:
    """同步结果数据类"""

    def __init__(self):
        self.success = False
        self.message = ""
        self.physical_interfaces = {'created': 0, 'updated': 0}
        self.trunk_interfaces = {'created': 0, 'updated': 0}
        self.mac_addresses = {'created': 0, 'updated': 0}
        self.switch_info = {}
        self.neo4j_sync = {'success': False, 'message': ''}
        self.error = None


class VirtualSwitchSyncService:
    """虚拟交换机同步服务"""

    def __init__(self, enable_neo4j_sync: bool = True):
        self.logger = logger
        self.enable_neo4j_sync = enable_neo4j_sync and NEO4J_SYNC_AVAILABLE

    def sync_switch_data(self, virtual_switch_id: int) -> SwitchSyncResult:
        """
        同步指定交换机的所有数据

        Args:
            virtual_switch_id: 虚拟交换机ID

        Returns:
            SwitchSyncResult: 同步结果
        """
        result = SwitchSyncResult()

        try:
            # 获取交换机实例
            virtual_switch = VirtualSwitch.objects.get(id=virtual_switch_id, is_deleted=False)

            # 验证连接信息
            if not self._validate_connection_info(virtual_switch):
                result.message = "交换机管理信息不完整，请检查IP、用户名和密码"
                return result

            # 执行同步
            with transaction.atomic():
                result = self._perform_sync(virtual_switch)

        except VirtualSwitch.DoesNotExist:
            result.message = f"未找到ID为{virtual_switch_id}的虚拟交换机"
            self.logger.error(result.message)
        except Exception as e:
            result.message = f"同步过程中发生异常: {str(e)}"
            result.error = e
            self.logger.error(f"交换机同步异常 ID:{virtual_switch_id} - {str(e)}", exc_info=True)

        return result

    def sync_switch_data_by_instance(self, virtual_switch: VirtualSwitch) -> SwitchSyncResult:
        """
        通过交换机实例同步数据

        Args:
            virtual_switch: 虚拟交换机实例

        Returns:
            SwitchSyncResult: 同步结果
        """
        result = SwitchSyncResult()

        try:
            # 验证连接信息
            if not self._validate_connection_info(virtual_switch):
                result.message = "交换机管理信息不完整，请检查IP、用户名和密码"
                return result

            # 执行同步
            with transaction.atomic():
                result = self._perform_sync(virtual_switch)

        except Exception as e:
            result.message = f"同步过程中发生异常: {str(e)}"
            result.error = e
            self.logger.error(
                f"交换机同步异常 {virtual_switch.name}({virtual_switch.management_ip}) - {str(e)}", exc_info=True
                )

        return result

    def _validate_connection_info(self, virtual_switch: VirtualSwitch) -> bool:
        """验证交换机连接信息"""
        return all(
            [
                virtual_switch.management_ip,
                virtual_switch.management_username,
                virtual_switch.management_password
                ]
            )

    def _perform_sync(self, virtual_switch: VirtualSwitch) -> SwitchSyncResult:
        """执行实际的同步操作"""
        result = SwitchSyncResult()

        try:
            # 确定设备类型
            device_type = self._determine_device_type(virtual_switch)

            # 建立连接并获取数据
            with HuaweiSwitchReader(
                    ip=virtual_switch.management_ip,
                    username=virtual_switch.management_username,
                    password=virtual_switch.management_password,
                    port=virtual_switch.management_port or 22,
                    device_type=device_type,
                    verbose=False  # 异步任务中不需要详细输出
                    ) as switch_mgr:

                self.logger.info(f"开始同步交换机 {virtual_switch.name}({virtual_switch.management_ip})")

                # 获取交换机数据
                physical_interfaces = switch_mgr.get_physical_interfaces()
                trunk_interfaces = switch_mgr.get_eth_trunks()
                version_info = switch_mgr.get_version()

                # 更新交换机基本信息
                self._update_switch_info(virtual_switch, version_info)

                # 同步各类接口数据
                result.physical_interfaces = self._sync_physical_interfaces(virtual_switch, physical_interfaces)
                result.trunk_interfaces = self._sync_trunk_interfaces(virtual_switch, trunk_interfaces)

                # 收集并同步MAC地址
                mac_addresses = self._collect_mac_addresses(physical_interfaces)
                result.mac_addresses = self._sync_mac_addresses(virtual_switch, mac_addresses)

                # 设置成功结果
                result.success = True
                result.message = "交换机数据同步完成"
                result.switch_info = {
                    'vendor': virtual_switch.vendor,
                    'firmware_version': virtual_switch.firmware_version,
                    'last_sync_at': virtual_switch.last_sync_at.isoformat() if virtual_switch.last_sync_at else None
                    }

                # 同步到Neo4j（如果启用）
                if self.enable_neo4j_sync:
                    result.neo4j_sync = self._sync_to_neo4j(virtual_switch)

                self.logger.info(
                    f"交换机同步完成 {virtual_switch.name}: "
                    f"物理接口 {result.physical_interfaces}, "
                    f"聚合接口 {result.trunk_interfaces}, "
                    f"MAC地址 {result.mac_addresses}, "
                    f"Neo4j同步 {result.neo4j_sync['success']}"
                    )

        except ConnectionError as e:
            result.message = f"连接交换机失败: {str(e)}"
            result.error = e
            self.logger.error(f"交换机连接失败 {virtual_switch.management_ip}: {str(e)}")
        except Exception as e:
            result.message = f"同步失败: {str(e)}"
            result.error = e
            self.logger.error(f"同步接口信息失败 {virtual_switch.management_ip}: {str(e)}")

        return result

    def _determine_device_type(self, virtual_switch: VirtualSwitch) -> str:
        """确定设备类型"""
        if virtual_switch.vendor and 'h3c' in virtual_switch.vendor.lower():
            return 'h3c'
        return 'huawei'  # 默认华为

    def _update_switch_info(self, virtual_switch: VirtualSwitch, version_info: Dict) -> None:
        """更新交换机基本信息"""
        virtual_switch.vendor = version_info.get('vendor', virtual_switch.vendor)
        virtual_switch.firmware_version = version_info.get('software_version', virtual_switch.firmware_version)
        virtual_switch.last_sync_at = timezone.now()
        virtual_switch.save()

    def _collect_mac_addresses(self, physical_interfaces: List[Dict]) -> List[Dict]:
        """收集MAC地址信息"""
        mac_addresses = []
        for interface in physical_interfaces:
            if interface.get('mac') and interface['mac'] != 'N/A':
                mac_addresses.append(
                    {
                        'mac_address': interface['mac'],
                        'interface': interface['name']
                        }
                    )
        return mac_addresses

    def _sync_physical_interfaces(self, virtual_switch: VirtualSwitch, interfaces_data: List[Dict]) -> Dict[str, int]:
        """同步物理接口信息"""
        created_count = 0
        updated_count = 0

        for interface_data in interfaces_data:
            # 解析速度信息
            speed_value = self._parse_speed_to_mbps(interface_data.get('speed', 'N/A'))

            # 查找或创建接口记录
            interface, created = VirtualSwitchPhysicalInterface.objects.get_or_create(
                virtual_switch=virtual_switch,
                name=interface_data['name'],
                defaults={
                    'speed': speed_value,
                    'state': interface_data.get('status', ''),
                    'line_state': interface_data.get('protocol', ''),
                    'mac_address': interface_data.get('mac', ''),
                    'last_sync_at': timezone.now()
                    }
                )

            if created:
                created_count += 1
            else:
                # 更新现有记录
                interface.speed = speed_value
                interface.state = interface_data.get('status', '')
                interface.line_state = interface_data.get('protocol', '')
                interface.mac_address = interface_data.get('mac', '')
                interface.last_sync_at = timezone.now()
                interface.save()
                updated_count += 1

        return {'created': created_count, 'updated': updated_count}

    def _sync_trunk_interfaces(self, virtual_switch: VirtualSwitch, trunk_data: List[Dict]) -> Dict[str, int]:
        """同步聚合接口信息"""
        created_count = 0
        updated_count = 0

        for trunk_info in trunk_data:
            # 查找或创建聚合接口记录
            trunk_interface, created = VirtualSwitchTrunkInterface.objects.get_or_create(
                virtual_switch=virtual_switch,
                name=trunk_info['name'],
                defaults={
                    'state': trunk_info.get('status', ''),
                    'line_state': trunk_info.get('protocol', ''),
                    'members': ','.join(trunk_info.get('members', [])),
                    'last_sync_at': timezone.now()
                    }
                )

            if created:
                created_count += 1
            else:
                # 更新现有记录
                trunk_interface.state = trunk_info.get('status', '')
                trunk_interface.line_state = trunk_info.get('protocol', '')
                trunk_interface.members = ','.join(trunk_info.get('members', []))
                trunk_interface.last_sync_at = timezone.now()
                trunk_interface.save()
                updated_count += 1

        return {'created': created_count, 'updated': updated_count}

    def _sync_mac_addresses(self, virtual_switch: VirtualSwitch, mac_data: List[Dict]) -> Dict[str, int]:
        """同步MAC地址信息"""
        created_count = 0
        updated_count = 0

        for mac_info in mac_data:
            # 查找或创建MAC地址记录
            mac_record, created = VirtualSwitchMacAddress.objects.get_or_create(
                virtual_switch=virtual_switch,
                mac_address=mac_info['mac_address'],
                defaults={
                    'interface': mac_info.get('interface', ''),
                    'last_sync_at': timezone.now()
                    }
                )

            if created:
                created_count += 1
            else:
                # 更新现有记录
                mac_record.interface = mac_info.get('interface', '')
                mac_record.last_sync_at = timezone.now()
                mac_record.save()
                updated_count += 1

        return {'created': created_count, 'updated': updated_count}

    @staticmethod
    def _parse_speed_to_mbps(speed_str: str) -> Optional[int]:
        """将速度字符串转换为Mbps数值"""
        if not speed_str or speed_str == 'N/A':
            return None

        speed_map = {
            '100Mbps': 100,
            '1Gbps': 1000,
            '10Gbps': 10000,
            '25Gbps': 25000,
            '40Gbps': 40000,
            '100Gbps': 100000,
            }

        return speed_map.get(speed_str, None)

    def _sync_to_neo4j(self, virtual_switch: VirtualSwitch) -> Dict[str, any]:
        """同步数据到Neo4j图数据库"""
        if not self.enable_neo4j_sync or not neo4j_sync_service:
            return {'success': False, 'message': 'Neo4j同步未启用或不可用'}

        try:
            with neo4j_sync_service:
                neo4j_result = neo4j_sync_service.sync_switch_to_neo4j(virtual_switch.id)
                return {
                    'success': neo4j_result.success,
                    'message': neo4j_result.message,
                    'nodes_created': neo4j_result.nodes_created,
                    'relationships_created': neo4j_result.relationships_created
                    }
        except Exception as e:
            self.logger.error(f"Neo4j同步失败 {virtual_switch.name}: {str(e)}")
            return {
                'success': False,
                'message': f'Neo4j同步失败: {str(e)}'
                }


# 创建全局服务实例
switch_sync_service = VirtualSwitchSyncService()
