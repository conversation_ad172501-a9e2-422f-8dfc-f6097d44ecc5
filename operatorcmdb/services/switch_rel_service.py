"""
Neo4j图数据库同步服务
将交换机相关的数据关系同步到Neo4j数据库中
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

try:
    from neo4j import GraphDatabase

    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False
    GraphDatabase = None

from django.conf import settings
from django.utils import timezone

from operatorcmdb.models import (
    VirtualSwitch,
    VirtualSwitchPhysicalInterface,
    VirtualSwitchTrunkInterface,
    VirtualSwitchMacAddress
    )
from xresource.models import MachineRoom, PrivateRoom, IDCRackMachine, NetworkHardware

logger = logging.getLogger(__name__)


class Neo4jSyncResult:
    """Neo4j同步结果"""

    def __init__(self):
        self.success = False
        self.message = ""
        self.nodes_created = 0
        self.nodes_updated = 0
        self.relationships_created = 0
        self.relationships_updated = 0
        self.error = None


class SwitchTopologyQueryService:
    """交换机拓扑数据查询服务(直接查询数据库)"""

    def get_switch_topology_data(self, virtual_switch_id: id) -> Dict[str, Any]:
        """
        获取指定交换机的所有关联数据，用于前端展示拓扑图

        Args:
            virtual_switch_id: 虚拟交换机ID

        Returns:
            Dict: 包含nodes和edges的拓扑数据
        """
        try:
            # 获取交换机实例
            virtual_switch = VirtualSwitch.objects.select_related(
                'physical_switch', 'machine_room', 'private_room', 'idc_rack_machine'
                ).get(id=virtual_switch_id, is_deleted=False)

            nodes = []
            edges = []

            # 添加交换机节点
            switch_node = {
                'id': f'switch-{virtual_switch.id}',
                'data': {
                    'name': virtual_switch.name,
                    'type': 'switch',
                    'status': 'success',
                    'sn': virtual_switch.sn or '',
                    'ip': virtual_switch.management_ip or '',
                    'vendor': virtual_switch.vendor or '',
                    }
                }
            nodes.append(switch_node)

            # 添加机房节点
            if virtual_switch.machine_room:
                room = virtual_switch.machine_room
                room_node = {
                    'id': f'room-{room.id}',
                    'data': {
                        'name': room.name,
                        'type': 'room',
                        'status': 'success',
                        'description': room.description or '',
                        }
                    }
                nodes.append(room_node)

                # 添加连接关系
                edges.append(
                    {
                        'source': f'switch-{virtual_switch.id}',
                        'target': f'room-{room.id}',
                        }
                    )

            # 添加包间节点
            if virtual_switch.private_room:
                private_room = virtual_switch.private_room
                room_node = {
                    'id': f'private-room-{private_room.id}',
                    'data': {
                        'name': private_room.name,
                        'type': 'private_room',
                        'status': 'success',
                        'description': private_room.description or '',
                        }
                    }
                nodes.append(room_node)

                # 添加连接关系
                edges.append(
                    {
                        'source': f'switch-{virtual_switch.id}',
                        'target': f'private-room-{private_room.id}',
                        }
                    )

                # 如果有机房，添加包间与机房的关系
                if virtual_switch.machine_room:
                    edges.append(
                        {
                            'source': f'private-room-{private_room.id}',
                            'target': f'room-{virtual_switch.machine_room.id}',
                            }
                        )

            # 添加机柜节点
            if virtual_switch.idc_rack_machine:
                rack = virtual_switch.idc_rack_machine
                rack_node = {
                    'id': f'rack-{rack.id}',
                    'data': {
                        'name': rack.rack_sn or f'Rack-{rack.id}',
                        'type': 'rack',
                        'status': 'success',
                        'rack_type': rack.rack_type or '',
                        }
                    }
                nodes.append(rack_node)

                # 添加连接关系
                edges.append(
                    {
                        'source': f'switch-{virtual_switch.id}',
                        'target': f'rack-{rack.id}',
                        }
                    )

                # 如果有包间，添加机柜与包间的关系
                if virtual_switch.private_room:
                    edges.append(
                        {
                            'source': f'rack-{rack.id}',
                            'target': f'private-room-{virtual_switch.private_room.id}',
                            }
                        )

            # 添加物理交换机节点
            if virtual_switch.physical_switch:
                physical_switch = virtual_switch.physical_switch
                ps_node = {
                    'id': f'physical-switch-{physical_switch.id}',
                    'data': {
                        'name': physical_switch.physical_machine_sn or f'PS-{physical_switch.id}',
                        'type': 'physical_switch',
                        'status': 'success',
                        'machine_type': physical_switch.machine_type or '',
                        'manufacturer': physical_switch.manufacturer or '',
                        }
                    }
                nodes.append(ps_node)

                # 添加连接关系
                edges.append(
                    {
                        'source': f'physical-switch-{physical_switch.id}',
                        'target': f'switch-{virtual_switch.id}',
                        }
                    )

            # 添加物理接口节点
            physical_interfaces = VirtualSwitchPhysicalInterface.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
                )

            for i, interface in enumerate(physical_interfaces):
                pi_node = {
                    'id': f'interface-{interface.id}',
                    'data': {
                        'name': interface.name,
                        'type': 'physical_interface',
                        'status': 'success' if interface.state == 'up' else 'warning',
                        'speed': interface.speed or 0,
                        'state': interface.state or '',
                        'line_state': interface.line_state or '',
                        }
                    }
                nodes.append(pi_node)

                # 添加连接关系
                edges.append(
                    {
                        'source': f'switch-{virtual_switch.id}',
                        'target': f'interface-{interface.id}',
                        }
                    )

            # 添加聚合接口节点
            trunk_interfaces = VirtualSwitchTrunkInterface.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
                )

            for i, trunk in enumerate(trunk_interfaces):
                trunk_node = {
                    'id': f'trunk-{trunk.id}',
                    'data': {
                        'name': trunk.name,
                        'type': 'trunk_interface',
                        'status': 'success' if trunk.state == 'up' else 'warning',
                        'state': trunk.state or '',
                        'line_state': trunk.line_state or '',
                        'members': trunk.members or '',
                        }
                    }
                nodes.append(trunk_node)

                # 添加连接关系
                edges.append(
                    {
                        'source': f'switch-{virtual_switch.id}',
                        'target': f'trunk-{trunk.id}',
                        }
                    )

                # 添加聚合接口与成员接口的关系
                if trunk.members:
                    member_names = trunk.get_members_list()
                    for member_name in member_names:
                        # 查找对应的物理接口
                        member_interface = physical_interfaces.filter(name=member_name).first()
                        if member_interface:
                            edges.append(
                                {
                                    'source': f'trunk-{trunk.id}',
                                    'target': f'interface-{member_interface.id}',
                                    }
                                )

            # 添加MAC地址节点
            mac_addresses = VirtualSwitchMacAddress.objects.filter(
                virtual_switch=virtual_switch, is_deleted=False
                )

            for i, mac in enumerate(mac_addresses):
                mac_node = {
                    'id': f'mac-{mac.id}',
                    'data': {
                        'name': mac.mac_address,
                        'type': 'mac_address',
                        'status': 'success',
                        'interface': mac.interface or '',
                        }
                    }
                nodes.append(mac_node)

                # 添加连接关系
                edges.append(
                    {
                        'source': f'switch-{virtual_switch.id}',
                        'target': f'mac-{mac.id}',
                        }
                    )

            return {
                'nodes': nodes,
                'edges': edges
                }

        except VirtualSwitch.DoesNotExist:
            return {
                'nodes': [],
                'edges': [],
                'error': f'未找到ID为{virtual_switch_id}的虚拟交换机'
                }
        except Exception as e:
            logger.error(f"查询交换机拓扑数据失败: {str(e)}")
            return {
                'nodes': [],
                'edges': [],
                'error': f'查询失败: {str(e)}'
                }

    def get_room_switches_topology(self, machine_room_id: str) -> Dict[str, Any]:
        """
        获取指定机房内所有交换机的拓扑数据

        Args:
            machine_room_id: 机房ID

        Returns:
            Dict: 包含nodes和edges的拓扑数据
        """
        try:
            nodes = []
            edges = []

            # 添加机房节点
            try:
                machine_room = MachineRoom.objects.get(id=machine_room_id)
                room_node = {
                    'id': f'room-{machine_room.id}',
                    'data': {
                        'name': machine_room.name,
                        'type': 'room',
                        'status': 'success',
                        'description': machine_room.description or '',
                        },
                    'style': {'x': 400, 'y': 50}
                    }
                nodes.append(room_node)
            except MachineRoom.DoesNotExist:
                return {
                    'nodes': [],
                    'edges': [],
                    'error': f'未找到ID为{machine_room_id}的机房'
                    }

            # 获取该机房内的所有交换机
            switches = VirtualSwitch.objects.filter(
                machine_room_id=machine_room_id,
                is_deleted=False
                )

            # 添加交换机节点
            for i, switch in enumerate(switches):
                switch_node = {
                    'id': f'switch-{switch.id}',
                    'data': {
                        'name': switch.name,
                        'type': 'switch',
                        'status': 'success',
                        'sn': switch.sn or '',
                        'ip': switch.management_ip or '',
                        },
                    'style': {'x': 100 + (i % 4) * 200, 'y': 200 + (i // 4) * 150}
                    }
                nodes.append(switch_node)

                # 添加连接关系
                edges.append(
                    {
                        'source': f'switch-{switch.id}',
                        'target': f'room-{machine_room.id}',
                        'data': {'type': 'located_in'}
                        }
                    )

            return {
                'nodes': nodes,
                'edges': edges
                }

        except Exception as e:
            logger.error(f"查询机房交换机拓扑数据失败: {str(e)}")
            return {
                'nodes': [],
                'edges': [],
                'error': f'查询失败: {str(e)}'
                }


switch_topology_query_service = SwitchTopologyQueryService()