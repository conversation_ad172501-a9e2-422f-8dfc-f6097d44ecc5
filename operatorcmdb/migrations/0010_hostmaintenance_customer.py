# Generated by Django 5.0.6 on 2025-06-23 10:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0002_remove_customer_usage_type_customer_officer_and_more'),
        ('operatorcmdb', '0009_hostmaintenance'),
    ]

    operations = [
        migrations.AddField(
            model_name='hostmaintenance',
            name='customer',
            field=models.ForeignKey(blank=True, db_comment='客户ID', help_text='客户ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='operatorcmdb_customer_query', to='customer.customer', to_field='id', verbose_name='客户ID'),
        ),
    ]
