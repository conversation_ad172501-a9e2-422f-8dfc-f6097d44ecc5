# Generated by Django 5.0.6 on 2024-08-14 17:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customer', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Host',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.Char<PERSON>ield(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('instance_id', models.CharField(db_comment='实例ID', default=None, help_text='实例ID', max_length=255, null=True, unique=True, verbose_name='实例ID')),
                ('name', models.CharField(blank=True, db_comment='实例名称', default=None, help_text='实例名称', max_length=255, null=True, verbose_name='实例名称')),
                ('host_type', models.CharField(db_comment='主机类型', help_text='主机类型', max_length=63, verbose_name='主机类型')),
                ('ip_private', models.CharField(blank=True, db_comment='内网IP', help_text='内网IP', max_length=63, null=True, verbose_name='内网IP')),
                ('ip_public', models.CharField(blank=True, db_comment='公网IP', help_text='公网IP', max_length=63, null=True, verbose_name='公网IP')),
                ('ip_bmc', models.CharField(blank=True, db_comment='BMC-IP', help_text='BMC-IP', max_length=63, null=True, verbose_name='BMC-IP')),
                ('kvm_ip_host', models.CharField(blank=True, db_comment='宿主机IP', help_text='宿主机IP', max_length=63, null=True, verbose_name='宿主机IP')),
                ('bmc_user', models.CharField(blank=True, db_comment='BMC用户', help_text='BMC用户', max_length=63, null=True, verbose_name='BMC用户')),
                ('bmc_passwd', models.CharField(blank=True, db_comment='BMC密码', help_text='BMC密码', max_length=63, null=True, verbose_name='BMC密码')),
                ('host_status', models.CharField(blank=True, db_comment='主机状态', help_text='主机状态', max_length=63, null=True, verbose_name='主机状态')),
                ('os', models.CharField(blank=True, db_comment='操作系统', help_text='操作系统', max_length=63, null=True, verbose_name='操作系统')),
                ('cpu', models.CharField(blank=True, db_comment='CPU', help_text='CPU', max_length=63, null=True, verbose_name='CPU')),
                ('mem', models.CharField(blank=True, db_comment='内存', help_text='内存', max_length=63, null=True, verbose_name='内存')),
                ('sys_disk', models.CharField(blank=True, db_comment='系统盘', help_text='系统盘', max_length=63, null=True, verbose_name='系统盘')),
                ('data_disk', models.CharField(blank=True, db_comment='数据盘', help_text='数据盘', max_length=63, null=True, verbose_name='数据盘')),
                ('gpu_model', models.CharField(blank=True, db_comment='GPU模型', help_text='GPU模型', max_length=63, null=True, verbose_name='GPU模型')),
                ('gpu_count', models.CharField(blank=True, db_comment='GPU数量', help_text='GPU数量', max_length=63, null=True, verbose_name='GPU数量')),
                ('ssh_port', models.CharField(blank=True, db_comment='SSH端口', help_text='SSH端口', max_length=63, null=True, verbose_name='SSH端口')),
                ('ssh_user', models.CharField(blank=True, db_comment='SSH用户', help_text='SSH用户', max_length=63, null=True, verbose_name='SSH用户')),
                ('ssh_passwd', models.CharField(blank=True, db_comment='SSH密码', help_text='SSH密码', max_length=63, null=True, verbose_name='SSH密码')),
                ('start_time', models.DateTimeField(blank=True, db_comment='启用时间', default=None, help_text='启用时间', null=True, verbose_name='启用时间')),
                ('expire_time', models.DateTimeField(blank=True, db_comment='到期时间', default=None, help_text='到期时间', null=True, verbose_name='到期时间')),
                ('is_buffer', models.BooleanField(blank=True, db_comment='Buffer机', default=False, help_text='Buffer机', null=True, verbose_name='Buffer机')),
                ('is_ipmi_mon', models.BooleanField(blank=True, db_comment='IPMI监控', default=False, help_text='IPMI监控', null=True, verbose_name='IPMI监控')),
                ('is_system_mon', models.BooleanField(blank=True, db_comment='系统监控', default=False, help_text='系统监控', null=True, verbose_name='系统监控')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('customer', models.ForeignKey(blank=True, db_comment='客户ID', help_text='客户ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='host_customer', to='customer.customer', to_field='id', verbose_name='客户ID')),
                ('idc_rack_machine', models.ForeignKey(blank=True, db_comment='机柜ID', help_text='机柜ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='host_idc_rack_machine', to='xresource.idcrackmachine', to_field='id', verbose_name='机柜ID')),
                ('machine_room', models.ForeignKey(blank=True, db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='host_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房ID')),
                ('physical_server_machine', models.ForeignKey(blank=True, db_comment='物理机ID', help_text='物理机ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='host_physical_server_machine', to='xresource.physicalservermachine', to_field='id', verbose_name='物理机ID')),
                ('private_room', models.ForeignKey(blank=True, db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='host_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间ID')),
            ],
            options={
                'verbose_name': '虚拟主机表',
                'verbose_name_plural': '虚拟主机表',
                'db_table': 'operator_cmdb_hosts',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
