# Generated by Django 5.0.6 on 2025-06-13 14:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operatorcmdb', '0008_host_resource_category'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='HostMaintenance',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('issue_type', models.CharField(blank=True, db_comment='问题类型', help_text='问题类型', max_length=63, null=True, verbose_name='问题类型')),
                ('issue_desc', models.TextField(blank=True, db_comment='问题描述', help_text='问题描述', null=True, verbose_name='问题描述')),
                ('issue_severity', models.CharField(blank=True, db_comment='问题严重程度', help_text='问题严重程度', max_length=63, null=True, verbose_name='问题严重程度')),
                ('reporter_name', models.CharField(blank=True, db_comment='报告人', help_text='报告人', max_length=63, null=True, verbose_name='报告人')),
                ('report_time', models.DateTimeField(blank=True, db_comment='报告时间', default=None, help_text='报告时间', null=True, verbose_name='报告时间')),
                ('issue_status', models.CharField(blank=True, db_comment='问题状态', help_text='问题状态', max_length=63, null=True, verbose_name='问题状态')),
                ('resolution_desc', models.TextField(blank=True, db_comment='解决描述', help_text='解决描述', null=True, verbose_name='解决描述')),
                ('start_resolve_time', models.DateTimeField(blank=True, db_comment='开始解决时间', default=None, help_text='开始解决时间', null=True, verbose_name='开始解决时间')),
                ('end_resolve_time', models.DateTimeField(blank=True, db_comment='结束解决时间', default=None, help_text='结束解决时间', null=True, verbose_name='结束解决时间')),
                ('downtime_duration', models.IntegerField(blank=True, db_comment='停机时长', default=0, help_text='停机时长', null=True, verbose_name='停机时长')),
                ('downtime_duration_unit', models.CharField(blank=True, db_comment='停机时长单位', help_text='停机时长单位', max_length=63, null=True, verbose_name='停机时长单位')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('operatorcmdb_host', models.ForeignKey(db_comment='主机ID', help_text='主机ID', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='operatorcmdb_hosts_query', to='operatorcmdb.host', to_field='id', verbose_name='主机ID')),
                ('technician', models.ForeignKey(db_comment='处理技术人员ID', help_text='处理技术人员ID', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technician_id_maintenances', to=settings.AUTH_USER_MODEL, verbose_name='处理技术人员ID')),
            ],
            options={
                'verbose_name': '运维主机维修表',
                'verbose_name_plural': '运维主机维修表',
                'db_table': 'operator_cmdb_host_maintenances',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
