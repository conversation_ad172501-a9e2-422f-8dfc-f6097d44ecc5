# Generated by Django 5.0.6 on 2025-02-21 17:42

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operatorcmdb', '0005_alter_host_host_status'),
        ('scheduletask', '0002_alter_work_status'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FastInstallSoftware',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(blank=True, db_comment='软件名称', help_text='软件名称', max_length=255, null=True, verbose_name='软件名称')),
                ('key', models.CharField(blank=True, db_comment='软件简称', help_text='软件简称', max_length=63, null=True, verbose_name='软件简称')),
                ('show_value', models.CharField(blank=True, db_comment='软件显示名称', help_text='软件显示名称', max_length=255, null=True, verbose_name='软件显示名称')),
                ('options', models.JSONField(blank=True, db_comment='软件选项配置', default=dict, help_text='软件选项配置', null=True, verbose_name='软件选项配置')),
                ('category', models.CharField(blank=True, db_comment='分类', default='AI驱动', help_text='分类', max_length=255, null=True, verbose_name='分类')),
                ('enabled', models.BooleanField(blank=True, db_comment='启用状态', default=False, help_text='启用状态', null=True, verbose_name='启用状态')),
                ('status', models.CharField(blank=True, db_comment='状态', default='开发中', help_text='状态', max_length=31, null=True, verbose_name='状态')),
                ('adaptation_system_version', models.CharField(blank=True, db_comment='适配系统版本', default=None, help_text='适配系统版本', max_length=1023, null=True, verbose_name='适配系统版本')),
                ('is_support_repeat', models.BooleanField(blank=True, db_comment='是否支持重试安装', default=False, help_text='是否支持重试安装', null=True, verbose_name='是否支持重试安装')),
                ('install_path', models.CharField(blank=True, db_comment='安装路径', default='/opt', help_text='安装路径', max_length=255, null=True, verbose_name='安装路径')),
                ('ansible_template', models.ForeignKey(db_comment='ansible模板', help_text='ansible模板', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='ansible_template_query', to='scheduletask.anstemplate', to_field='id', verbose_name='ansible模板')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '快速安装软件表',
                'verbose_name_plural': '快速安装软件表',
                'db_table': 'operator_cmdb_fi_softwaress',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
