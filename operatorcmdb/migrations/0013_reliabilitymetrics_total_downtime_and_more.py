# Generated by Django 5.0.6 on 2025-07-31 14:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operatorcmdb', '0012_reliabilitymetrics'),
    ]

    operations = [
        migrations.AddField(
            model_name='reliabilitymetrics',
            name='total_downtime',
            field=models.DecimalField(blank=True, db_comment='设备总停机时间(小时)', decimal_places=2, help_text='设备总停机时间(小时)', max_digits=10, null=True, verbose_name='设备总停机时间(小时)'),
        ),
        migrations.AddField(
            model_name='reliabilitymetrics',
            name='total_fault_count',
            field=models.IntegerField(blank=True, db_comment='设备故障次数', default=-1, help_text='设备故障次数', null=True, verbose_name='设备故障次数'),
        ),
        migrations.AddField(
            model_name='reliabilitymetrics',
            name='total_repair_time',
            field=models.DecimalField(blank=True, db_comment='设备总维修时间(小时)', decimal_places=2, help_text='设备总维修时间(小时)', max_digits=10, null=True, verbose_name='设备总维修时间(小时)'),
        ),
        migrations.AddField(
            model_name='reliabilitymetrics',
            name='total_resource_count',
            field=models.IntegerField(blank=True, db_comment='设备数量', default=-1, help_text='设备数量', null=True, verbose_name='设备数量'),
        ),
        migrations.AddField(
            model_name='reliabilitymetrics',
            name='total_runtime',
            field=models.DecimalField(blank=True, db_comment='设备总运行时间(小时)', decimal_places=2, help_text='设备总运行时间(小时)', max_digits=10, null=True, verbose_name='设备总运行时间(小时)'),
        ),
        migrations.AlterField(
            model_name='reliabilitymetrics',
            name='metrics_type',
            field=models.CharField(blank=True, db_comment='设备类型', help_text='设备类型', max_length=63, null=True, verbose_name='设备类型'),
        ),
    ]
