# Generated by Django 5.0.6 on 2024-10-10 10:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0002_remove_customer_usage_type_customer_officer_and_more'),
        ('operatorcmdb', '0003_host_u_position_alter_host_is_buffer'),
        # ('xresource', '0006_alter_generalconsumable_machine_room_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='host',
            name='customer',
            field=models.ForeignKey(blank=True, db_comment='客户ID', help_text='客户ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='host_customer', to='customer.customer', to_field='id', verbose_name='客户'),
        ),
        migrations.AlterField(
            model_name='host',
            name='idc_rack_machine',
            field=models.ForeignKey(blank=True, db_comment='机柜ID', help_text='机柜ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='host_idc_rack_machine', to='xresource.idcrackmachine', to_field='id', verbose_name='机柜'),
        ),
        migrations.AlterField(
            model_name='host',
            name='machine_room',
            field=models.ForeignKey(blank=True, db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='host_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='host',
            name='physical_server_machine',
            field=models.ForeignKey(blank=True, db_comment='物理机ID', help_text='物理机ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='host_physical_server_machine', to='xresource.physicalservermachine', to_field='id', verbose_name='物理机'),
        ),
        migrations.AlterField(
            model_name='host',
            name='private_room',
            field=models.ForeignKey(blank=True, db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='host_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
    ]
