# Generated by Django 5.0.6 on 2025-07-15 09:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operatorcmdb', '0010_hostmaintenance_customer'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='host',
            name='sn',
            field=models.CharField(blank=True, db_comment='SN号', help_text='SN号', max_length=255, null=True, verbose_name='SN号'),
        ),
        migrations.CreateModel(
            name='VirtualSwitch',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.Char<PERSON>ield(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('rack_unit', models.IntegerField(db_comment='U位', default=-1, help_text='U位', verbose_name='U位')),
                ('node', models.CharField(blank=True, db_comment='节点', help_text='节点', max_length=30, null=True, verbose_name='节点')),
                ('name', models.CharField(blank=True, db_comment='名称', help_text='名称', max_length=255, null=True, verbose_name='名称')),
                ('sn', models.CharField(blank=True, db_comment='序列号', help_text='序列号', max_length=255, null=True, verbose_name='序列号')),
                ('management_ip', models.CharField(blank=True, db_comment='管理IP', help_text='管理IP', max_length=255, null=True, verbose_name='管理IP')),
                ('management_username', models.CharField(blank=True, db_comment='管理用户名', help_text='管理用户名', max_length=32, null=True, verbose_name='管理用户名')),
                ('management_port', models.IntegerField(blank=True, db_comment='管理端口', default=None, help_text='管理端口', null=True, verbose_name='管理端口')),
                ('management_password', models.CharField(blank=True, db_comment='管理密码', help_text='管理密码', max_length=255, null=True, verbose_name='管理密码')),
                ('management_protocol', models.CharField(blank=True, db_comment='管理协议', help_text='管理协议', max_length=64, null=True, verbose_name='管理协议')),
                ('vendor', models.CharField(blank=True, db_comment='厂商', help_text='厂商', max_length=255, null=True, verbose_name='厂商')),
                ('firmware_version', models.TextField(blank=True, db_comment='固件版本', help_text='固件版本', null=True, verbose_name='固件版本')),
                ('last_sync_at', models.DateTimeField(blank=True, db_comment='最后一次同步时间', default=None, help_text='最后一次同步时间', null=True, verbose_name='最后一次同步时间')),
                ('vlan_range', models.TextField(blank=True, db_comment='VLAN范围', help_text='VLAN范围', max_length=255, null=True, verbose_name='VLAN范围')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('idc_rack_machine', models.ForeignKey(blank=True, db_comment='机柜ID', help_text='机柜ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vs_idc_rack_machine', to='xresource.idcrackmachine', to_field='id', verbose_name='机柜')),
                ('machine_room', models.ForeignKey(blank=True, db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vs_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房')),
                ('physical_switch', models.ForeignKey(db_comment='物理交换机ID', help_text='物理交换机ID', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='physical_switch_query', to='xresource.networkhardware', to_field='id', verbose_name='物理交换机ID')),
                ('private_room', models.ForeignKey(blank=True, db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vs_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间')),
            ],
            options={
                'verbose_name': '虚拟交换机表',
                'verbose_name_plural': '虚拟交换机表',
                'db_table': 'operator_cmdb_virtual_switch',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='VirtualSwitchMacAddress',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('mac_address', models.CharField(blank=True, db_comment='MAC地址', help_text='MAC地址', max_length=64, null=True, verbose_name='MAC地址')),
                ('node', models.CharField(blank=True, db_comment='节点', help_text='节点', max_length=30, null=True, verbose_name='节点')),
                ('interface', models.CharField(blank=True, db_comment='接口名称', help_text='接口名称', max_length=255, null=True, verbose_name='接口名称')),
                ('last_sync_at', models.DateTimeField(blank=True, db_comment='最后一次同步时间', default=None, help_text='最后一次同步时间', null=True, verbose_name='最后一次同步时间')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('virtual_switch', models.ForeignKey(db_comment='虚拟交换机ID', help_text='虚拟交换机ID', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='virtual_switch_query', to='operatorcmdb.virtualswitch', to_field='id', verbose_name='虚拟交换机ID')),
            ],
            options={
                'verbose_name': '虚拟交换机MAC地址表',
                'verbose_name_plural': '虚拟交换机MAC地址表',
                'db_table': 'operator_cmdb_vs_mac_address',
                'ordering': ('-create_datetime',),
                'unique_together': {('virtual_switch', 'mac_address')},
            },
        ),
        migrations.CreateModel(
            name='VirtualSwitchPhysicalInterface',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('node', models.CharField(blank=True, db_comment='节点', help_text='节点', max_length=30, null=True, verbose_name='节点')),
                ('name', models.CharField(blank=True, db_comment='名称', help_text='名称', max_length=255, null=True, verbose_name='名称')),
                ('speed', models.IntegerField(blank=True, db_comment='速度', default=None, help_text='速度', null=True, verbose_name='速度')),
                ('state', models.CharField(blank=True, db_comment='状态', help_text='状态', max_length=30, null=True, verbose_name='状态')),
                ('line_state', models.CharField(blank=True, db_comment='协议状态', help_text='协议状态', max_length=30, null=True, verbose_name='协议状态')),
                ('last_sync_at', models.DateTimeField(blank=True, db_comment='最后一次同步时间', default=None, help_text='最后一次同步时间', null=True, verbose_name='最后一次同步时间')),
                ('mac_address', models.CharField(blank=True, db_comment='MAC地址', help_text='MAC地址', max_length=64, null=True, verbose_name='MAC地址')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('virtual_switch', models.ForeignKey(db_comment='虚拟交换机ID', help_text='虚拟交换机ID', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='virtual_switch_query', to='operatorcmdb.virtualswitch', to_field='id', verbose_name='虚拟交换机ID')),
            ],
            options={
                'verbose_name': '虚拟交换机物理接口表',
                'verbose_name_plural': '虚拟交换机物理接口表',
                'db_table': 'operator_cmdb_vs_physical_interface',
                'ordering': ('-create_datetime',),
                'unique_together': {('virtual_switch', 'name')},
            },
        ),
        migrations.CreateModel(
            name='VirtualSwitchTrunkInterface',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('members', models.TextField(blank=True, db_comment='物理接口名称', default='', help_text='物理接口名称', null=True, verbose_name='物理接口名称')),
                ('node', models.CharField(blank=True, db_comment='节点', help_text='节点', max_length=30, null=True, verbose_name='节点')),
                ('state', models.CharField(blank=True, db_comment='状态', help_text='状态', max_length=30, null=True, verbose_name='状态')),
                ('line_state', models.CharField(blank=True, db_comment='协议状态', help_text='协议状态', max_length=30, null=True, verbose_name='协议状态')),
                ('name', models.CharField(db_comment='接口名称', default='', help_text='接口名称', max_length=255, verbose_name='接口名称')),
                ('last_sync_at', models.DateTimeField(blank=True, db_comment='最后一次同步时间', default=None, help_text='最后一次同步时间', null=True, verbose_name='最后一次同步时间')),
                ('mac_address', models.CharField(blank=True, db_comment='MAC地址', help_text='MAC地址', max_length=64, null=True, verbose_name='MAC地址')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('virtual_switch', models.ForeignKey(db_comment='虚拟交换机ID', help_text='虚拟交换机ID', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='virtual_switch_query', to='operatorcmdb.virtualswitch', to_field='id', verbose_name='虚拟交换机ID')),
            ],
            options={
                'verbose_name': '虚拟交换机Trunk接口表',
                'verbose_name_plural': '虚拟交换机Trunk接口表',
                'db_table': 'operator_cmdb_vs_trunk_interface',
                'ordering': ('-create_datetime',),
                'unique_together': {('virtual_switch', 'name')},
            },
        ),
    ]
