# Generated by Django 5.0.6 on 2025-07-23 15:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operatorcmdb', '0011_host_sn_virtualswitch_virtualswitchmacaddress_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReliabilityMetrics',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.Char<PERSON>ield(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('mttf', models.DecimalField(blank=True, db_comment='mttf值', decimal_places=2, help_text='mttf值', max_digits=10, null=True, verbose_name='mttf值')),
                ('mttr', models.DecimalField(blank=True, db_comment='mttr值', decimal_places=2, help_text='mttr值', max_digits=10, null=True, verbose_name='mttr值')),
                ('mtbf', models.DecimalField(blank=True, db_comment='mtbf值', decimal_places=2, help_text='mtbf值', max_digits=10, null=True, verbose_name='mtbf值')),
                ('availability', models.DecimalField(blank=True, db_comment='availability值', decimal_places=2, help_text='availability值', max_digits=10, null=True, verbose_name='availability值')),
                ('metrics_type', models.CharField(blank=True, db_comment='指标类型', help_text='指标类型', max_length=63, null=True, verbose_name='指标类型')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '可靠性指标记录表',
                'verbose_name_plural': '可靠性指标记录表',
                'db_table': 'operator_cmdb_reliability_metrics',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
