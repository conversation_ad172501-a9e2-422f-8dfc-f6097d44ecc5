from email.policy import default

from django.db import models
from xresource.models import MachineRoom, PrivateRoom, IDCRackMachine, PhysicalServerMachine, NetworkHardware
from customer.models import Customer

from dvadmin.utils.chaos_models import ChaosCoreModel
from dvadmin.system.models import Users
from scheduletask.models import AnsTemplate


TABLE_PREFIX = 'operator_cmdb_'  # 数据库表名前缀


# FX: 新增主机类型
class Host(ChaosCoreModel):
    """
    主机设备
    """
    instance_id = models.CharField(
        max_length=255, default=None, blank=False, null=True,
        verbose_name='实例ID', help_text='实例ID', db_comment='实例ID')
    area = models.CharField(
        max_length=63, default=None, blank=False, null=False,
        verbose_name='地区简称', help_text='地区简称', db_comment='地区简称')
    name = models.CharField(
        max_length=255, default=None, blank=True, null=True,
        verbose_name='实例名称', help_text='实例名称', db_comment='实例名称')
    host_type = models.CharField(
        max_length=63, blank=False, null=False,
        verbose_name='主机类型', help_text='主机类型', db_comment='主机类型')
    resource_category = models.CharField(
        max_length=63, blank=False, null=False, default='裸金属机',
        verbose_name='资源分类', help_text='资源分类', db_comment='资源分类')
    ip_private = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='内网IP', help_text='内网IP', db_comment='内网IP')
    ip_public = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='公网IP', help_text='公网IP', db_comment='公网IP')
    ip_bmc = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='BMC-IP', help_text='BMC-IP', db_comment='BMC-IP')
    kvm_ip_host = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='宿主机IP', help_text='宿主机IP', db_comment='宿主机IP')
    bmc_user = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='BMC用户', help_text='BMC用户', db_comment='BMC用户')
    bmc_passwd = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='BMC密码', help_text='BMC密码', db_comment='BMC密码')
    machine_room = models.ForeignKey(
        MachineRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='host_machine_room', blank=True, null=True, db_index=True,
        verbose_name='机房', help_text='机房ID', db_comment='机房ID')
    private_room = models.ForeignKey(
        PrivateRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='host_private_room', blank=True, null=True, db_index=True,
        verbose_name='包间', help_text='包间ID', db_comment='包间ID')
    idc_rack_machine = models.ForeignKey(
        IDCRackMachine, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='host_idc_rack_machine', blank=True, null=True,
        db_index=True, verbose_name='机柜', help_text='机柜ID', db_comment='机柜ID')
    physical_server_machine = models.ForeignKey(
        PhysicalServerMachine, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='host_physical_server_machine', blank=True, null=True,
        db_index=True, verbose_name='物理机', help_text='物理机ID', db_comment='物理机ID')
    customer = models.ForeignKey(
        Customer, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='host_customer', blank=True, null=True,
        db_index=True, verbose_name='客户', help_text='客户ID', db_comment='客户ID'
    )
    host_status = models.CharField(
        max_length=63, blank=True, null=True, default='',
        verbose_name='主机状态', help_text='主机状态', db_comment='主机状态')
    os = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='操作系统', help_text='操作系统', db_comment='操作系统')
    cpu = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='CPU', help_text='CPU', db_comment='CPU')
    mem = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='内存', help_text='内存', db_comment='内存')
    sys_disk = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='系统盘', help_text='系统盘', db_comment='系统盘')
    data_disk = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='数据盘', help_text='数据盘', db_comment='数据盘')
    gpu_model = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='GPU模型', help_text='GPU模型', db_comment='GPU模型')
    gpu_count = models.IntegerField(
        blank=True, null=True, default=0,
        verbose_name='GPU数量', help_text='GPU数量', db_comment='GPU数量')
    ssh_port = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='SSH端口', help_text='SSH端口', db_comment='SSH端口')
    ssh_user = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='SSH用户', help_text='SSH用户', db_comment='SSH用户')
    ssh_passwd = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='SSH密码', help_text='SSH密码', db_comment='SSH密码')
    start_time = models.DateTimeField(
        default=None, null=True, blank=True,
        verbose_name='启用时间', help_text='启用时间', db_comment='启用时间')
    expire_time = models.DateTimeField(
        default=None, null=True, blank=True,
        verbose_name='到期时间', help_text='到期时间', db_comment='到期时间')
    is_buffer = models.BooleanField(
        default=False, null=True, blank=True,
        verbose_name='Buffer池', help_text='Buffer池', db_comment='Buffer池')
    is_ipmi_mon = models.BooleanField(
        default=False, null=True, blank=True,
        verbose_name='IPMI监控', help_text='IPMI监控', db_comment='IPMI监控')
    is_system_mon = models.BooleanField(
        default=False, null=True, blank=True,
        verbose_name='系统监控', help_text='系统监控', db_comment='系统监控')
    u_position = models.IntegerField(
        default=None, null=True, blank=True,
        verbose_name='U位', help_text='U位', db_comment='U位')
    sn = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='SN号', help_text='SN号', db_comment='SN号')


    def __str__(self):
        return f'{self.instance_id}'

    class Meta:
        db_table = TABLE_PREFIX + "hosts"
        unique_together = [
            ("instance_id", "area"),
        ]
        verbose_name = "虚拟主机表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class FastInstallSoftware(ChaosCoreModel):
    """
    适配快速部署应用的软件包
    """
    name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='软件名称', help_text='软件名称', db_comment='软件名称')
    key = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='软件简称', help_text='软件简称', db_comment='软件简称')
    versions = models.JSONField(
        blank=True, null=True, default=list,
        verbose_name='软件版本', help_text='软件版本', db_comment='软件版本')
    options = models.JSONField(
        blank=True, null=True, default=dict,
        verbose_name='软件选项配置', help_text='软件选项配置', db_comment='软件选项配置')
    # AI驱动，安全软件，监控工具，中间件，备份工具
    category = models.CharField(
        max_length=255, blank=True, null=True, default='AI驱动',
        verbose_name='分类', help_text='分类', db_comment='分类')
    enabled = models.BooleanField(
        default=False, null=True, blank=True,
        verbose_name='启用状态', help_text='启用状态', db_comment='启用状态')
    # 新需求，开发中，已发布
    status = models.CharField(
        max_length=31, blank=True, null=True, default='开发中',
        verbose_name='状态', help_text='状态', db_comment='状态')
    adaptation_system_version = models.CharField(
        default=None, null=True, blank=True, max_length=1023,
        verbose_name='适配系统版本', help_text='适配系统版本', db_comment='适配系统版本')
    is_support_repeat = models.BooleanField(
        default=False, null=True, blank=True,
        verbose_name='是否支持重试安装', help_text='是否支持重试安装', db_comment='是否支持重试安装')
    install_path = models.CharField(
        max_length=255, blank=True, null=True, default='/opt',
        verbose_name='安装路径', help_text='安装路径', db_comment='安装路径')
    ansible_template = models.ForeignKey(
        AnsTemplate, to_field='id', related_query_name='ansible_template_query', null=True,
        verbose_name='ansible模板', help_text='ansible模板', on_delete=models.SET_NULL, db_comment='ansible模板'
    )

    def __str__(self):
        return self.name

    class Meta:
        db_table = TABLE_PREFIX + "fi_softwaress"
        verbose_name = "快速安装软件表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class HostMaintenance(ChaosCoreModel):
    """
    主机维护记录表
    """
    operatorcmdb_host = models.ForeignKey(
        Host, to_field='id', related_query_name='operatorcmdb_hosts_query', null=True,
        verbose_name='主机ID', help_text='主机ID', on_delete=models.SET_NULL, db_comment='主机ID'
    )
    customer = models.ForeignKey(
        Customer, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='operatorcmdb_customer_query', blank=True, null=True,
        verbose_name='客户ID', help_text='客户ID', db_comment='客户ID'
        )
    issue_type = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='问题类型', help_text='问题类型', db_comment='问题类型')
    issue_desc = models.TextField(
        blank=True, null=True,
        verbose_name='问题描述', help_text='问题描述', db_comment='问题描述')
    issue_severity = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='问题严重程度', help_text='问题严重程度', db_comment='问题严重程度')
    reporter_name = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='报告人', help_text='报告人', db_comment='报告人')
    report_time = models.DateTimeField(
        default=None, null=True, blank=True,
        verbose_name='报告时间', help_text='报告时间', db_comment='报告时间')
    technician = models.ForeignKey(
        Users, to_field='id', null=True, related_name='technician_id_maintenances',
        verbose_name='处理技术人员ID', help_text='处理技术人员ID', on_delete=models.SET_NULL, db_comment='处理技术人员ID'
    )
    issue_status = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='问题状态', help_text='问题状态', db_comment='问题状态')
    resolution_desc = models.TextField(
        blank=True, null=True,
        verbose_name='解决描述', help_text='解决描述', db_comment='解决描述')
    start_resolve_time = models.DateTimeField(
        default=None, null=True, blank=True,
        verbose_name='开始解决时间', help_text='开始解决时间', db_comment='开始解决时间')
    end_resolve_time = models.DateTimeField(
        default=None, null=True, blank=True,
        verbose_name='结束解决时间', help_text='结束解决时间', db_comment='结束解决时间')
    downtime_duration = models.IntegerField(
        default=0, null=True, blank=True,
        verbose_name='停机时长', help_text='停机时长', db_comment='停机时长')
    downtime_duration_unit = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='停机时长单位', help_text='停机时长单位', db_comment='停机时长单位')

    def __str__(self):
        return self.name

    class Meta:
        db_table = TABLE_PREFIX + "host_maintenances"
        verbose_name = "运维主机维修表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class ReliabilityMetrics(ChaosCoreModel):
    """
    MTTF、MTTR、MTBF指标记录表
    """
    mttf = models.DecimalField(
        max_digits=10,decimal_places=2,blank=True,null=True,
        verbose_name='mttf值', help_text='mttf值', db_comment='mttf值')
    mttr = models.DecimalField(
        max_digits=10,decimal_places=2,blank=True,null=True,
        verbose_name='mttr值', help_text='mttr值', db_comment='mttr值')
    mtbf = models.DecimalField(
        max_digits=10,decimal_places=2,blank=True,null=True,
        verbose_name='mtbf值', help_text='mtbf值', db_comment='mtbf值')
    availability = models.DecimalField(
        max_digits=10,decimal_places=2,blank=True,null=True,
        verbose_name='availability值', help_text='availability值', db_comment='availability值')
    metrics_type = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='设备类型', help_text='设备类型', db_comment='设备类型')
    total_resource_count = models.IntegerField(
        default=-1, blank=True, null=True,verbose_name='设备数量', help_text='设备数量', db_comment='设备数量')
    total_fault_count = models.IntegerField(
        default=-1, blank=True, null=True,verbose_name='设备故障次数', help_text='设备故障次数', db_comment='设备故障次数')
    total_downtime = models.DecimalField(
        max_digits=10,decimal_places=2,blank=True,null=True,
        verbose_name='设备总停机时间(小时)', help_text='设备总停机时间(小时)', db_comment='设备总停机时间(小时)')
    total_repair_time = models.DecimalField(
        max_digits=10,decimal_places=2,blank=True,null=True,
        verbose_name='设备总维修时间(小时)', help_text='设备总维修时间(小时)', db_comment='设备总维修时间(小时)')
    total_runtime = models.DecimalField(
        max_digits=10,decimal_places=2,blank=True,null=True,
        verbose_name='设备总运行时间(小时)', help_text='设备总运行时间(小时)', db_comment='设备总运行时间(小时)')

    class Meta:
        db_table = TABLE_PREFIX + "reliability_metrics"
        verbose_name = "可靠性指标记录表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class VirtualSwitch(ChaosCoreModel):
    """
    虚拟交换机表
    """
    physical_switch = models.ForeignKey(
        NetworkHardware, to_field='id', related_query_name='physical_switch_query', null=True,
        verbose_name='物理交换机ID', help_text='物理交换机ID', on_delete=models.SET_NULL, db_comment='物理交换机ID'
    )
    machine_room = models.ForeignKey(
        MachineRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='vs_machine_room', blank=True, null=True, db_index=True,
        verbose_name='机房', help_text='机房ID', db_comment='机房ID'
        )
    private_room = models.ForeignKey(
        PrivateRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='vs_private_room', blank=True, null=True, db_index=True,
        verbose_name='包间', help_text='包间ID', db_comment='包间ID'
        )
    idc_rack_machine = models.ForeignKey(
        IDCRackMachine, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='vs_idc_rack_machine', blank=True, null=True,
        db_index=True, verbose_name='机柜', help_text='机柜ID', db_comment='机柜ID'
        )
    rack_unit = models.IntegerField(
        default=-1, blank=False, verbose_name='U位', help_text='U位', db_comment='U位'
        )
    node = models.CharField(
        max_length=30, null=True, blank=True, verbose_name='节点', help_text='节点', db_comment='节点')
    name = models.CharField(
        max_length=255, null=True, blank=True, verbose_name='名称', help_text='名称', db_comment='名称')
    sn = models.CharField(
        max_length=255, null=True, blank=True, verbose_name='序列号', help_text='序列号', db_comment='序列号')
    management_ip = models.CharField(
        max_length=255, null=True, blank=True, verbose_name='管理IP', help_text='管理IP', db_comment='管理IP')
    management_username = models.CharField(
        max_length=32, null=True, blank=True, verbose_name='管理用户名', help_text='管理用户名', db_comment='管理用户名'
        )
    management_port = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='管理端口', help_text='管理端口', db_comment='管理端口')
    management_password = models.CharField(
        max_length=255, null=True, blank=True, verbose_name='管理密码', help_text='管理密码', db_comment='管理密码'
        )
    management_protocol = models.CharField(
        max_length=64, null=True, blank=True, verbose_name='管理协议', help_text='管理协议', db_comment='管理协议'
        )
    vendor = models.CharField(
        max_length=255, null=True, blank=True, verbose_name='厂商', help_text='厂商', db_comment='厂商')
    firmware_version = models.TextField(
        null=True, blank=True, verbose_name='固件版本', help_text='固件版本', db_comment='固件版本')
    last_sync_at = models.DateTimeField(
        default=None, null=True, blank=True, verbose_name='最后一次同步时间', help_text='最后一次同步时间', db_comment='最后一次同步时间')
    vlan_range = models.TextField(
        max_length=255, null=True, blank=True, verbose_name='VLAN范围', help_text='VLAN范围', db_comment='VLAN范围')

    def __str__(self):
        return f"{self.name} (PhysicalSwitch: {self.physical_switch is not None: self.physical_switch.name})"

    class Meta:
        db_table = TABLE_PREFIX + "virtual_switch"
        verbose_name = "虚拟交换机表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class VirtualSwitchPhysicalInterface(ChaosCoreModel):
    """
    虚拟交换机物理接口表
    """
    virtual_switch = models.ForeignKey(
        VirtualSwitch, to_field='id', related_query_name='virtual_switch_query', null=True,
        verbose_name='虚拟交换机ID', help_text='虚拟交换机ID', on_delete=models.SET_NULL, db_comment='虚拟交换机ID'
    )
    node = models.CharField(
        max_length=30, null=True, blank=True, verbose_name='节点', help_text='节点', db_comment='节点')
    name = models.CharField(
        max_length=255, null=True, blank=True, verbose_name='名称', help_text='名称', db_comment='名称')
    speed = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='速度', help_text='速度', db_comment='速度')
    state = models.CharField(
        max_length=30, null=True, blank=True, verbose_name='状态', help_text='状态', db_comment='状态')
    line_state = models.CharField(
        max_length=30, null=True, blank=True, verbose_name='协议状态', help_text='协议状态', db_comment='协议状态')
    last_sync_at = models.DateTimeField(
        default=None, null=True, blank=True, verbose_name='最后一次同步时间', help_text='最后一次同步时间', db_comment='最后一次同步时间')
    mac_address = models.CharField(
        max_length=64, null=True, blank=True, verbose_name='MAC地址', help_text='MAC地址', db_comment='MAC地址')

    def __str__(self):
        return f"{self.name} (VirtualSwitch: {self.virtual_switch is not None: self.virtual_switch.name})"

    class Meta:
        unique_together = (
            ('virtual_switch', 'name'),
        )
        db_table = TABLE_PREFIX + "vs_physical_interface"
        verbose_name = "虚拟交换机物理接口表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class VirtualSwitchTrunkInterface(ChaosCoreModel):
    """
    虚拟交换机聚合接口表
    """
    virtual_switch = models.ForeignKey(
        VirtualSwitch, to_field='id', related_query_name='virtual_switch_query', null=True,
        verbose_name='虚拟交换机ID', help_text='虚拟交换机ID', on_delete=models.SET_NULL, db_comment='虚拟交换机ID'
    )
    members = models.TextField(
        null=True, blank=True, default="",
        verbose_name='物理接口名称', help_text='物理接口名称', db_comment='物理接口名称'
    )
    node = models.CharField(
        max_length=30, null=True, blank=True, verbose_name='节点', help_text='节点', db_comment='节点')
    state = models.CharField(
        max_length=30, null=True, blank=True, verbose_name='状态', help_text='状态', db_comment='状态')
    line_state = models.CharField(
        max_length=30, null=True, blank=True, verbose_name='协议状态', help_text='协议状态', db_comment='协议状态')
    name = models.CharField(
        max_length=255, default="", null=False, blank=False,
        verbose_name='接口名称', help_text='接口名称', db_comment='接口名称')
    last_sync_at = models.DateTimeField(
        default=None, null=True, blank=True, verbose_name='最后一次同步时间', help_text='最后一次同步时间', db_comment='最后一次同步时间')
    mac_address = models.CharField(
        max_length=64, null=True, blank=True, verbose_name='MAC地址', help_text='MAC地址', db_comment='MAC地址')

    def __str__(self):
        return f"{self.name} (VirtualSwitch: {self.virtual_switch is not None: self.virtual_switch.name})"

    def get_members_list(self):
        """将存储的字符串转换为列表"""
        return [m.strip() for m in self.members.split(',') if m.strip()] if self.members else []

    def set_members(self, interfaces):
        """设置成员端口列表"""
        self.members = ','.join(interfaces)

    class Meta:
        unique_together = [
            ("virtual_switch", "name"),
        ]
        db_table = TABLE_PREFIX + "vs_trunk_interface"
        verbose_name = "虚拟交换机Trunk接口表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class VirtualSwitchMacAddress(ChaosCoreModel):
    """
    虚拟交换机MAC地址表
    """
    virtual_switch = models.ForeignKey(
        VirtualSwitch, to_field='id', related_query_name='virtual_switch_query', null=True,
        verbose_name='虚拟交换机ID', help_text='虚拟交换机ID', on_delete=models.SET_NULL, db_comment='虚拟交换机ID'
    )
    mac_address = models.CharField(
        max_length=64, null=True, blank=True, verbose_name='MAC地址', help_text='MAC地址', db_comment='MAC地址')
    node = models.CharField(
        max_length=30, null=True, blank=True, verbose_name='节点', help_text='节点', db_comment='节点')
    interface = models.CharField(
        max_length=255, null=True, blank=True, verbose_name='接口名称', help_text='接口名称', db_comment='接口名称')
    last_sync_at = models.DateTimeField(
        default=None, null=True, blank=True, verbose_name='最后一次同步时间', help_text='最后一次同步时间', db_comment='最后一次同步时间')

    class Meta:
        unique_together = [
            ("virtual_switch", "mac_address"),
        ]
        db_table = TABLE_PREFIX + "vs_mac_address"
        verbose_name = "虚拟交换机MAC地址表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)
