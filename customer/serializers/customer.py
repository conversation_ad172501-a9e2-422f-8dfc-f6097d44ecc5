from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from customer.models import Customer


class CustomerImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = Customer
        exclude = ()


class CustomerSerializer(ChaosCustomModelSerializer):
    """
    日志-序列化器
    """

    class Meta:
        model = Customer
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]
