from django.db import models
from dvadmin.utils.chaos_models import ChaosCoreModel


TABLE_PREFIX = 'customer_'


# Create your models here.
class Customer(ChaosCoreModel):
    name = models.CharField(
        max_length=255, blank=False, null=False,
        verbose_name='客户名称', help_text='客户名称', db_comment='客户名称')
    description = models.TextField(
        blank=True,  null=True,
        verbose_name='客户描述', help_text='客户描述', db_comment='客户描述')
    province = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='省份', help_text='省份', db_comment='省份')
    city = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='城市', help_text='城市', db_comment='城市')
    district = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='区县', help_text='区县', db_comment='区县')
    phone = models.CharField(
        max_length=15, blank=True, null=True,
        verbose_name='客户联系人手机号', help_text='客户联系人手机号', db_comment='客户联系人手机号')
    email = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='客户联系人邮箱', help_text='客户联系人邮箱', db_comment='客户联系人邮箱')
    type = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='客户类型', help_text='客户类型', db_comment='客户类型')
    officer = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='客户联系人', help_text='客户联系人', db_comment='客户联系人')
    customer_manager_name = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='客户经理名称', help_text='客户经理名称', db_comment='客户经理名称')
    customer_manager_phone = models.CharField(
        max_length=15, blank=True, null=True,
        verbose_name='客户经理电话', help_text='客户经理电话', db_comment='客户经理电话')
    customer_manager_email = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='客户经理邮箱', help_text='客户经理邮箱', db_comment='客户经理邮箱')

    class Meta:
        db_table = TABLE_PREFIX + "customers"
        verbose_name = "客户信息表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)

