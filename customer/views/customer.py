from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet

from dvadmin.system.models import Area

from customer.models import Customer
from customer.serializers.customer import CustomerSerializer, CustomerImportSerializer


class CustomerViewSet(ChaosCustomModelViewSet):
    """
    客户信息接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Customer.objects.order_by('-create_datetime')
    serializer_class = CustomerSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = CustomerImportSerializer
    import_field_dict = {
        "name": "客户名称",
        "description": "公司描述",
        "province": {
            "title": "省份",
            "choices": {"queryset": Area.objects.filter(
                level=1
            ), "values_name": "name", "key_name": "code"}
        },
        "city": {
            "title": "城市",
            "choices": {"queryset": Area.objects.filter(
                level=2
            ), "values_name": "name", "key_name": "code"}
        },
        "type": "客户类型",
        "district": {
            "title": "区县",
            "choices": {"queryset": Area.objects.filter(
                level=3
            ), "values_name": "name", "key_name": "code"}
        },
        'officer': "客户联系人",
        "phone": "联系方式",
        "email": "邮箱",
        "customer_manager_name": "客户经理名称",
        "customer_manager_phone": "客户经理电话",
        "customer_manager_email": "客户经理邮箱"
    }
    # permission_classes = []
