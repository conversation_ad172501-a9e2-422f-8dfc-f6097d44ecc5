# Generated by Django 5.0.6 on 2024-08-14 17:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.Char<PERSON>ield(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('name', models.CharField(db_comment='客户名称', help_text='客户名称', max_length=255, verbose_name='客户名称')),
                ('description', models.TextField(blank=True, db_comment='客户描述', help_text='客户描述', null=True, verbose_name='客户描述')),
                ('province', models.CharField(blank=True, db_comment='省份', help_text='省份', max_length=255, null=True, verbose_name='省份')),
                ('city', models.CharField(blank=True, db_comment='城市', help_text='城市', max_length=255, null=True, verbose_name='城市')),
                ('district', models.CharField(blank=True, db_comment='区县', help_text='区县', max_length=255, null=True, verbose_name='区县')),
                ('phone', models.CharField(blank=True, db_comment='客户联系人手机号', help_text='客户联系人手机号', max_length=15, null=True, verbose_name='客户联系人手机号')),
                ('email', models.CharField(blank=True, db_comment='客户联系人邮箱', help_text='客户联系人邮箱', max_length=255, null=True, verbose_name='客户联系人邮箱')),
                ('usage_type', models.CharField(blank=True, db_comment='使用类型', help_text='使用类型', max_length=63, null=True, verbose_name='使用类型')),
                ('customer_manager_name', models.CharField(blank=True, db_comment='客户经理名称', help_text='客户经理名称', max_length=63, null=True, verbose_name='客户经理名称')),
                ('customer_manager_phone', models.CharField(blank=True, db_comment='客户经理电话', help_text='客户经理电话', max_length=15, null=True, verbose_name='客户经理电话')),
                ('customer_manager_email', models.CharField(blank=True, db_comment='客户经理邮箱', help_text='客户经理邮箱', max_length=255, null=True, verbose_name='客户经理邮箱')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '客户信息表',
                'verbose_name_plural': '客户信息表',
                'db_table': 'customer_customers',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
