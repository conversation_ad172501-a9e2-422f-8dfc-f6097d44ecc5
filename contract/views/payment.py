import json,os
import base64
import tempfile
from urllib.parse import quote
from django.http import FileResponse
from django.forms.models import model_to_dict
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

from application.logger import logger
from django.db import transaction
from contract.utils.utils import generate_billing_periods
from contract.models import Payment,Accessory,Contract,PaymentPeriodHistory
from contract.serializers.payment import PaymentImportSerializer, PaymentSerializer
from contract.serializers.payment_period_history import PaymentPeriodHistorySerializer
from contract.serializers.contract import ContractSerializer
from contract.utils.minio import MinioClient
from dvadmin.utils.query_to_obj import query2Obj
from dvadmin.utils.sdk.xlsxwriter_client import ExcelWriter
from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import DetailResponse, ErrorResponse, SuccessResponse
from customer.models import Customer


class PaymentModelViewSet(ChaosCustomModelViewSet):
    queryset = Payment.objects.order_by('-create_datetime')
    serializer_class = PaymentSerializer

    def list(self, request, *args, **kwargs):
        try:
            logger.info("进入回款列表接口...")
            queryset = self.filter_queryset(self.get_queryset())
            query_params = dict(request.query_params)
            if "contract_name" in query_params.keys():
                contract_codes = list(Contract.objects.filter(name__icontains=query_params.get("contract_name")[0]).values_list("code",flat=True))
                queryset = queryset.filter(contract_code__in=contract_codes)
            
            page = self.paginate_queryset(queryset)
            serializer = None
            if page is not None:
                serializer = self.get_serializer(page, many=True, request=request)
            else:
                serializer = self.get_serializer(queryset, many=True, request=request)

            # 获取合同名称
            contract_codes = {item.get("contract_code") for item in serializer.data if item.get("contract_code")}
            contracts = Contract.objects.filter(code__in=contract_codes).only("code", "name")
            contract_map = {c.code: c.name for c in contracts}

            for payment_info in serializer.data:
                contract_code = payment_info.get("contract_code")
                payment_info["contract_name"] = contract_map.get(contract_code, "")
                payment_info['payment_list'] = query2Obj(PaymentPeriodHistory.objects.filter(payment_id=payment_info.get('id'),is_deleted=False))

            if page is not None:
                return self.get_paginated_response(serializer.data)
        except Exception as e:
            logger.error(f"获取回款列表失败,<Detail: {str(e)}>")
            return ErrorResponse(msg=f"获取回款列表失败,<Detail: {str(e)}>")
        return SuccessResponse(data=serializer.data, msg="获取成功")


    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def save(self, request):
        req_data = request.data
        payment_id = req_data['payment'].get('id')
        payment_list = req_data['payment'].get('payment_list')
        contract_code = req_data['payment'].get('contract_code')
        accesory_list = req_data['accessory']
        delete_accessory_list = req_data.get("delete_accessory_list",None)
        action = None
        try:
            if payment_id:
                # 更新
                action = "更新"
                payment_query = Payment.objects.get(id=payment_id)
                payment_serializer = PaymentSerializer(payment_query, data=req_data['payment'], request=self.request, partial=True)

            else:
                # 添加
                action = "添加"
                is_payment = Payment.objects.filter(contract_code=contract_code,is_deleted=False)
                Contract.objects.filter(code=contract_code).update(is_payment=True)
                if is_payment:
                    return ErrorResponse(msg=f"当前合同的回款已存在")
                payment_serializer = PaymentSerializer(data=req_data['payment'],request=self.request)

            payment_serializer.is_valid(raise_exception=True)
            payment_serializer.save()
            payment_serializer_id = payment_serializer.data['id']

            # 更新或创建回款历史表
            if action == "更新":
                pay_history_querys = PaymentPeriodHistory.objects.filter(payment_id=payment_id)
                if not payment_list:
                    pay_history_querys.update(is_deleted=True)
                else:
                    # 对回款记录进行添加或删除
                    req_pay_history_ids = [item.get("id") for item in payment_list if item.get("id")]
                    cur_pay_history_ids = list(map(lambda x: x['id'], query2Obj(pay_history_querys)))
                    ids_to_delete = set(cur_pay_history_ids) - set(req_pay_history_ids)
                    # 1. 更新已有记录
                    for item in payment_list:
                        if item.get("id") in cur_pay_history_ids:
                            PaymentPeriodHistory.objects.filter(id=item["id"]).update(**item)

                    # 2. 新增记录（无 ID 的为新建）
                    for item in payment_list:
                        if not item.get("id"):
                            item["payment"] = payment_id 
                            payment_history_serializer = PaymentPeriodHistorySerializer(data=item,request=self.request)
                            payment_history_serializer.is_valid(raise_exception=True)
                            payment_history_serializer.save()

                    # 3. 删除数据库中存在但前端不存在的记录
                    if ids_to_delete:
                        PaymentPeriodHistory.objects.filter(id__in=ids_to_delete).update(is_deleted=True)
            else:
                for item in payment_list:
                    item['payment'] = payment_serializer_id
                payment_history_serializer = PaymentPeriodHistorySerializer(data=payment_list,many=True,request=self.request)
                payment_history_serializer.is_valid(raise_exception=True)
                payment_history_serializer.save()
              
            # 更新创建回款的附件 -- 进行关联 
            Accessory.objects.filter(id__in=delete_accessory_list).update(is_deleted=True)

            accesory_ids = list(map(lambda x: x['id'], accesory_list))
            Accessory.objects.filter(id__in=accesory_ids,is_deleted=False).update(resource_id=payment_serializer_id)
        except Exception as e:
            logger.error(f"{action}回款失败,<Detail: {str(e)}>")
            return ErrorResponse(msg=f"{action}回款失败,<Detail: {str(e)}>")
        return SuccessResponse(msg="success")


    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def detail_info(self, request, *args, **kwargs):
        payment_id = request.query_params.get("id")
        item = {}
        try:
            accessory_data = []
            try:
                accessory_data = query2Obj(Accessory.objects.filter(resource_id=payment_id,is_deleted=False))
            except Exception as e:
                pass
            
            payment_dict = query2Obj(Payment.objects.get(id=payment_id))
            payment_history_list = query2Obj(PaymentPeriodHistory.objects.filter(payment_id=payment_id,is_deleted=False))
            payment_dict['payment_list'] = payment_history_list
            item = {
              'payment': payment_dict,
              'accessory': accessory_data
            }
        except Exception as e:
            logger.error(f"查看详情失败,<Detail: {str(e)}>")
            return ErrorResponse(msg=f"查看详情失败,<Detail: {str(e)}>")
        return SuccessResponse(data=item, msg="success")
        

    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def generate(self, request, *args, **kwargs):
        contract_id = request.data['id']
        generate_type = request.data['type']
        payment_status = "未回款"
        pre_payment_status = "未回款"
        item = {}
        try:
            contract_query = Contract.objects.get(id=contract_id)
            if not contract_query.start_time or not contract_query.end_time:
                return ErrorResponse(msg="请先签订合同的始止日期")

            is_payment = Payment.objects.filter(contract_code=contract_query.code,is_deleted=False)
            if is_payment:
                return ErrorResponse(msg="当前合同的回款已存在")

            customer_info = query2Obj(Customer.objects.get(id=contract_query.customer_id))
            province = customer_info.get("province") or ""
            city = customer_info.get("city") or ""
            district = customer_info.get("district") or ""
            code, payment_list = generate_billing_periods(str(contract_query.start_time).split(" ")[0], str(contract_query.end_time).split(" ")[0], generate_type, contract_query.pay_type)
            if not code:
                return ErrorResponse(msg=f"生成回款账期失败,<Detail: {payment_list}>")

            if int(contract_query.amount) <= 0:
                payment_status = "全部回款"
                pre_payment_status = "全部回款"
                
            item = {
              "amount": contract_query.amount,
              "invoice_title": customer_info.get("name"),
              "invoice_phone": customer_info.get("phone"),
              "invoice_address": province + city + district,
              "contract_code": contract_query.code,
              "cash_pledge": contract_query.cash_pledge,
              "status": payment_status,
              "pre_payment_status": pre_payment_status
            }

            payment_serializer = PaymentSerializer(data=item,request=self.request)
            payment_serializer.is_valid(raise_exception=True)
            payment_serializer.save()
            payment_serializer_id = payment_serializer.data['id']

            for pay in payment_list:
                pay['payment'] = payment_serializer_id
     
            payment_history_serializer = PaymentPeriodHistorySerializer(data=payment_list,many=True,request=self.request)
            payment_history_serializer.is_valid(raise_exception=True)
            payment_history_serializer.save()
        except Exception as e:
            logger.error(f"生成预回款失败,<Detail: {str(e)}>")
            return ErrorResponse(msg=f"生成预回款失败,<Detail: {str(e)}>")
        return SuccessResponse(msg="success")


    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def export_all_payments(self, request, *args, **kwargs):
        """
        导出所有回款
        """
        try:
            export_data = query2Obj(PaymentPeriodHistory.objects.filter(is_deleted=False).all())
            pay_ids = list(map(lambda x: x['payment_id'], export_data))
            payments = query2Obj(Payment.objects.filter(id__in=pay_ids))
            pay_map = {p['id']: p for p in payments}
            contract_codes = list(map(lambda x: x['contract_code'], payments))
            contracts = query2Obj(Contract.objects.filter(code__in=contract_codes))
            contract_map = {c['code']: c for c in contracts}

            # 进行数据处理
            for item in export_data:
                item['collaborate_range'] = f"""{str(item['payment_start_time']).split(" ")[0].replace("-",".")} - {str(item['payment_end_time']).split(" ")[0].replace("-",".")}"""
                item['customer'] = pay_map[item['payment_id']].get("invoice_title")
                item['code'] = pay_map[item['payment_id']].get("contract_code")
                item['contract_month'] = contract_map[pay_map[item['payment_id']].get("contract_code")].get("sign_time") if contract_map.get(pay_map[item['payment_id']].get("contract_code"), "") else ""
                item['intermediary_fee'] = pay_map[item['payment_id']]['agent_info'].get('cost') if pay_map[item['payment_id']]['agent_info'] else ""
                item['intermediary_party'] = pay_map[item['payment_id']]['agent_info'].get('customer') if pay_map[item['payment_id']]['agent_info'] else ""
                fields_to_str = ['payment_amount']  # 需要转换的字段列表 
                item['payment_data_list'] = [
                    {
                        k: str(v) if k in fields_to_str and k in item else v 
                        for k, v in item.items() 
                    }
                    for item in item['payment_data_list']]

                if not item['payment_data_list']:
                    item['payment_data_list'].append({'payment_amount':"",'payment_time':""})

            temp_file_path = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx").name
            # 使用示例
            with ExcelWriter(temp_file_path) as writer:
                # 方法1: 自动生成 field_map
                writer.write_data(
                    data=export_data,
                    keep_columns=['customer','collaborate_range','status','code','description','contract_month','intermediary_party','intermediary_fee'],
                    expand_column='payment_data_list',
                    header_mapping={
                        "customer": "用户",
                        "collaborate_range": "周期",
                        "status": "收款状态",
                        "code": "合同/订单流程编号",
                        "description": "备注",
                        "contract_month": "合同月份",
                        "intermediary_party": "居间方",
                        "intermediary_fee": "居间费",
                        "payment_amount": "结算金额",
                        "payment_time": "付款时间",
                        },
                    expand_prefix="[回款]",
                    use_model_verbose_name=True,
                    )

            if not os.path.exists(temp_file_path):
                return ErrorResponse(msg="回款导出文件未生成")

            filename = quote("回款导出数据.xlsx")
            response = FileResponse(open(temp_file_path, 'rb'), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            response['Access-Control-Expose-Headers'] = 'Content-Disposition' 
            return response
        except Exception as e:
            logger.error(f"回款导出失败,<Detail: {str(e)}>")
            return ErrorResponse(msg=f"回款导出失败,<Detail: {str(e)}>")