import json
import base64
from django.forms.models import model_to_dict
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

from application.logger import logger
from contract.models import Product
from contract.serializers.product import ProductImportSerializer,ProductSerializer
from contract.utils.minio import MinioClient
from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import DetailResponse, ErrorResponse, SuccessResponse


class ProductModelViewSet(ChaosCustomModelViewSet):
    queryset = Product.objects.order_by('-create_datetime')
    serializer_class = ProductSerializer

