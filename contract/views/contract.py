import json,os
import base64
import tempfile
from urllib.parse import quote
from django.http import FileResponse
from django.forms.models import model_to_dict
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from datetime import datetime, timedelta

from application.logger import logger
from contract.models import Contract, Accessory
from customer.models import Customer
from contract.serializers.contract import ContractImportSerializer,ContractSerializer,check_contract_wait_approval
from contract.utils.minio import MinioClient
from contract.utils.utils import fast_month_diff
from dvadmin.utils.query_to_obj import query2Obj
from dvadmin.utils.sdk.xlsxwriter_client import ExcelWriter
from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import DetailResponse, ErrorResponse, SuccessResponse


class ContractModelViewSet(ChaosCustomModelViewSet):
    queryset = Contract.objects.order_by('-create_datetime')
    serializer_class = ContractSerializer

    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def save(self, request):
        req_data = request.data
        contract_id = req_data['contract'].get('id')
        req_data['contract']['customer'] = req_data['contract']['customer'].get('id')
        accesory_list = req_data['accessory']
        delete_accessory_list = req_data.get("delete_accessory_list", None)
        action = None
        try:
            if contract_id:
                # 更新
                action = "更新"
                contract_query = Contract.objects.get(id=contract_id)
                contract_serializer = ContractSerializer(
                    contract_query, data=req_data['contract'], request=self.request, partial=True
                    )

            else:
                # 添加
                action = "添加"
                req_data['contract']['applicant'] = request.user.name
                contract_serializer = ContractSerializer(data=req_data['contract'],request=self.request)

            contract_serializer.is_valid(raise_exception=True)
            contract_serializer.save()
            contract_serializer_id = contract_serializer.data['id']

            if contract_serializer.data['status'] == "待审批":
                check_contract_wait_approval(contract_serializer.data)

            # 更新创建合同的附件 -- 进行关联
            Accessory.objects.filter(id__in=delete_accessory_list).update(is_deleted=True)

            accesory_ids = list(map(lambda x: x['id'], accesory_list))
            Accessory.objects.filter(id__in=accesory_ids, is_deleted=False).update(resource_id=contract_serializer_id)
        except Exception as e:
            logger.error(f"{action}合同失败,<Detail: {str(e)}>")
            return ErrorResponse(msg=f"{action}合同失败,<Detail: {str(e)}>")
        return SuccessResponse(msg="success")

    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def detail_info(self, request, *args, **kwargs):
        contract_id = request.query_params.get("id")
        item = {}
        try:
            accessory_data = []
            try:
                accessory_data = query2Obj(Accessory.objects.filter(resource_id=contract_id, is_deleted=False))
            except Exception as e:
                pass

            contract_info = query2Obj(Contract.objects.get(id=contract_id))
            contract_info["customer"] = query2Obj(Customer.objects.get(id=contract_info['customer_id']))
            
            item = {
              'contract': contract_info,
              'accessory': accessory_data
            }
        except Exception as e:
            logger.error(f"查看详情失败,<Detail: {str(e)}>")
            return ErrorResponse(msg=f"查看详情失败,<Detail: {str(e)}>")
        return SuccessResponse(data=item, msg="success")

    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def export_all_contracts(self, request, *args, **kwargs):
        """
        导出所有合同数据
        """
        try:
            export_data = query2Obj(Contract.objects.filter(is_deleted=False).all())

            # 进行数据处理
            for item in export_data:
                item['collaborate_range'] = f"""{str(item['start_time']).split(" ")[0].replace("-",".")} - {str(item['end_time']).split(" ")[0].replace("-",".")}"""
                item['month_num'] = fast_month_diff(item['start_time'], item['end_time'])
                item['sku_list'] = [{**_, 'total':''} for _ in item['sku_list']]
                item['amount'] = str(item['amount'])
                fields_to_remove = {'unit', 'attributes'}
                item['sku_list'] = [
                {k: v for k, v in sku.items()  if k not in fields_to_remove}
                for sku in item['sku_list']]
                fields_to_str = ['price', 'total_price']  # 需要转换的字段列表 
                item['sku_list'] = [
                    {
                        k: str(v) if k in fields_to_str and k in item else v 
                        for k, v in item.items() 
                    }
                    for item in item['sku_list']]

                if not item['sku_list']:
                    item['sku_list'].append({
                      "name": "",
                      "price": "",
                      "count": "",
                      "total": "",
                      "description": "",
                      "label": "",
                      "source": "",
                      "total_price": ""
                    })

            temp_file_path = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx").name
            # 使用示例
            with ExcelWriter(temp_file_path) as writer:
                # 方法1: 自动生成 field_map
                writer.write_data(
                    data=export_data,
                    # custom_config=custom_config_data,
                    keep_columns=['name','customer_name', 'sign_time','collaborate_range','month_num','amount','status','sign_name','code'],
                    expand_column='sku_list',
                    header_mapping={
                        "name": "订单号",
                        "collaborate_range": "合作时间",
                        "month_num": "签约月数",
                        "customer_name": "客户名称",
                        "sign_time": "双签回传时间",
                        "sign_name": "负责人",
                        "status": "状态",
                        "amount": "订单总价",
                        "code": "流程编号",
                        "[产品]name": "名称",
                        "[产品]price": "单价(元/台)",
                        "[产品]count": "数量",
                        "[产品]total": "合计",
                        "[产品]description": "备注",
                        "[产品]label": "合同类型",
                        "[产品]source": "来源",
                        "[产品]total_price": "计算金额",
                        },
                    expand_prefix="[产品]",
                    use_model_verbose_name=True,
                    )

            if not os.path.exists(temp_file_path):
                return ErrorResponse(msg="合同导出文件未生成")

            filename = quote("合同导出数据.xlsx")
            response = FileResponse(open(temp_file_path, 'rb'), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            response['Access-Control-Expose-Headers'] = 'Content-Disposition' 
            return response
        except Exception as e:
            logger.error(f"合同导出失败,<Detail: {str(e)}>")
            return ErrorResponse(msg=f"合同导出失败,<Detail: {str(e)}>")