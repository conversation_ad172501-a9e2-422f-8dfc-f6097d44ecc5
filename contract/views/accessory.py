import json
import base64
from django.forms.models import model_to_dict
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

from application.logger import logger
from contract.models import Accessory
from contract.serializers.accessory import AccessoryImportSerializer,AccessorySerializer
from contract.utils.minio import MinioClient
from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import DetailResponse, ErrorResponse, SuccessResponse


class AccessoryModelViewSet(ChaosCustomModelViewSet):
    queryset = Accessory.objects.order_by('-create_datetime')
    serializer_class = AccessorySerializer

    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def upload(self, request, *args, **kwargs):
        try:
            logger.info("进入上传附件接口...")
            minio_client = MinioClient()
            file_list = request.FILES.getlist('project_package')
            if not file_list:
              return ErrorResponse(msg="请上传文件")

            user_id = request.user.id
            file_obj = file_list[0]
            key = minio_client.generate_key()
            key_b64 = base64.b64encode(key).decode('utf-8')
            code, object_list = minio_client.upload_files(file_list,encryption_key=key)

            if not code:
                return ErrorResponse(msg=f"上传附件失败,<Detail: {object_list[0]}>")

            item_query = Accessory.objects.create(
              name=file_obj.name,
              filename=object_list[0],
              size=file_obj.size,
              creator_id=user_id
            )
            item = model_to_dict(item_query)

        except Exception as e:
            return ErrorResponse(msg=f"上传附件失败,<Detail: {str(e)}>")
        return SuccessResponse(data=item, msg="success")


    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def link(self, request, *args, **kwargs):
        id = request.query_params.get("id")
        accessory_obj = Accessory.objects.get(id=id)
        object_name = accessory_obj.filename
        minio_client = MinioClient()
        code, data = minio_client.generate_download_url(object_name)
        print(data,code)
        # 生成带预签名下载链接
        if not code:
            return ErrorResponse(msg=f"获取附件链接失败,<Detail: {data}>")

        return SuccessResponse(data=data, msg="success")
