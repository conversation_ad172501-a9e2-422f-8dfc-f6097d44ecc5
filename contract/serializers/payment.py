from contract.models import Payment
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer


class PaymentImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = Payment
        exclude = ()


# 这里是创建/更新时的列化器
class PaymentSerializer(ChaosCustomModelSerializer):
    class Meta:
        model = Payment
        fields = '__all__'
