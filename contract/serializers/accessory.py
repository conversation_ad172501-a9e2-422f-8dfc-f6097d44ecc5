from contract.models import Accessory
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer


class AccessoryImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = Accessory
        exclude = ()


# 这里是创建/更新时的列化器
class AccessorySerializer(ChaosCustomModelSerializer):
    class Meta:
        model = Accessory
        fields = '__all__'
