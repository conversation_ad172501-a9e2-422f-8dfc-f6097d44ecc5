from contract.models import Product
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer


class ProductImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = Product
        exclude = ()


# 这里是创建/更新时的列化器
class ProductSerializer(ChaosCustomModelSerializer):
    class Meta:
        model = Product
        fields = '__all__'
