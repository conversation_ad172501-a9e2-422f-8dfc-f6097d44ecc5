from contract.models import PaymentPeriodHistory,Payment,Contract
from django.utils.timezone import now
from datetime import datetime, timedelta
from dvadmin.utils.query_to_obj import query2Obj
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from notice.utils.contract_payment_notice import ManagerContractPaymentNotice

class PaymentPeriodHistoryImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = PaymentPeriodHistory
        exclude = ()


# 这里是创建/更新时的列化器
class PaymentPeriodHistorySerializer(ChaosCustomModelSerializer):
    class Meta:
        model = PaymentPeriodHistory
        fields = '__all__'


def check_payment_period_overdue():
    current_time = now().date()
    pay_historys = PaymentPeriodHistory.objects.filter(
        payment_end_time__lt=current_time,is_deleted=False
    ).exclude(status="全部回款")
    
    items = query2Obj(pay_historys)
    for item in items:
        pay_info = Payment.objects.get(id=item['payment_id'])
        contract_info = Contract.objects.get(code=pay_info.contract_code)
        item['contract'] = query2Obj(contract_info)
        item['payment'] = query2Obj(pay_info)

    if items:
        ManagerContractPaymentNotice().send_payment_common_notice(
          title = "【逾期未回款账单通知】",
          items = items
        )
  

def check_payment_period_collection_current():
    current_time = now().date()

    # 找出当前时间 ≥ payment_start_time - 7天，且 < payment_end_time，状态不为“全部回款”
    pay_historys = PaymentPeriodHistory.objects.filter(
        payment_start_time__lte=current_time + timedelta(days=7),
        payment_end_time__gte=current_time,
        is_deleted=False
    ).exclude(status="全部回款")

    items = query2Obj(pay_historys)
    indexs_to_remove = []
    for index,item in enumerate(items):
        pay_info = Payment.objects.get(id=item['payment_id'])
        contract_info = Contract.objects.get(code=pay_info.contract_code)
        if contract_info.pay_type == "后付":
            indexs_to_remove.append(index)
            continue
        item['contract'] = query2Obj(contract_info)
        item['payment'] = query2Obj(pay_info)

    items = [v for i, v in enumerate(items) if i not in indexs_to_remove]
    if items:
        ManagerContractPaymentNotice().send_payment_common_notice(
            title="【当期未回款账单通知】",
            items=items
        )