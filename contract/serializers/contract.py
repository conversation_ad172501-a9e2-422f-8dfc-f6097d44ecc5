from contract.models import Contract
from datetime import datetime, timedelta
from django.utils.timezone import now
from dvadmin.utils.query_to_obj import query2Obj
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from notice.utils.contract_payment_notice import ManagerContractPaymentNotice

class ContractImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = Contract
        exclude = ()


# 这里是创建/更新时的列化器
class ContractSerializer(ChaosCustomModelSerializer):
    class Meta:
        model = Contract
        fields = '__all__'


def check_contract_expired():
    """
    检查合同是否过期（过期时间在 1 周内），并发送通知
    """
    current_time = now().date()
    one_week_ago = current_time - timedelta(days=7)
    contracts = Contract.objects.filter(
        end_time__lt=current_time,      # 截止时间已过
        end_time__gte=one_week_ago      
    )
    items = query2Obj(contracts)
    if items:
        ManagerContractPaymentNotice().send_contract_common_notice(
          title = "【合同过期提醒】",
          items = items
        )
  

def check_contract_is_sign():
    """
    定时未双签合同
    """
    items = query2Obj(Contract.objects.filter(sign_time=None))
    if items:
        ManagerContractPaymentNotice().send_contract_common_notice(
          title = "【合同未双签提醒】",
          notice_type = "sign",
          items = items
        )
  

def check_contract_is_archive():
    """
    定时处理归档合同 将状态=>已归档
    """
    current_time = now().date()
    Contract.objects.filter(end_time__lt=current_time).update(status="已归档")


def check_contract_wait_approval(item: dict = {}):
    if item:
        ManagerContractPaymentNotice().send_contract_wait_approval_notice(
          title="【合同待审批处理】",
          item = item
        )