from rest_framework import routers
from django.urls import path
from contract.views.contract import ContractModelViewSet
from contract.views.accessory import AccessoryModelViewSet
from contract.views.payment import PaymentModelViewSet
from contract.views.product import ProductModelViewSet

contract_url = routers.SimpleRouter()
contract_url.register(r'accessory', AccessoryModelViewSet)
contract_url.register(r'contract', ContractModelViewSet)
contract_url.register(r'payment', PaymentModelViewSet)
contract_url.register(r'product', ProductModelViewSet)

app_name = 'contract'

urlpatterns = [
    # path('create_template/', AnsTemplateViewSet.as_view(), name='create_template'),
]
urlpatterns += contract_url.urls
