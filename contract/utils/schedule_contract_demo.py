import os,sys
import django
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))  
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
django.setup()

from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from contract.serializers.contract import (
  check_contract_expired,
  check_contract_is_archive,
  check_contract_is_sign,
  check_contract_wait_approval
)
from contract.serializers.payment_period_history import (
  check_payment_period_overdue,
  check_payment_period_collection_current
)

def use_contract_payment_notice():
    # check_contract_expired()
    check_contract_is_archive()
    # check_contract_is_sign()
    # check_contract_wait_approval()
    # check_payment_period_overdue()
    # check_payment_period_collection_current()


if __name__ == "__main__":
    use_contract_payment_notice()
