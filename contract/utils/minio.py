from minio import <PERSON>o
from minio.error import S3Error
from minio.sse import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from application.settings.base import MINIO_CONFIG
import uuid
import os
import uuid
from datetime import timed<PERSON><PERSON>
import urllib3
from typing import Tuple, List, Union
from application.settings.base import BASE_DIR

class MinioClient:
    def __init__(self):
        self.client = Minio(
            MINIO_CONFIG['endpoint'],
            access_key=MINIO_CONFIG['access_key'],
            secret_key=MINIO_CONFIG['secret_key'],
            secure=MINIO_CONFIG['secure'],
        )
        self.bucket = MINIO_CONFIG['bucket_name']
        if not self.client.bucket_exists(self.bucket):
            self.client.make_bucket(self.bucket)


    @staticmethod
    def generate_key() -> bytes:
        """生成 256-bit 加密密钥"""
        return os.urandom(32)

    def upload_files(self, files: List[dict], encryption_key: Union[bytes, None] = None) -> <PERSON><PERSON>[int, List[str]]:
        """
        files: List of dicts with keys:
            - data: binary file data (file stream)
            - filename: original filename
            - content_type: e.g., "image/png"
        encryption_key: 可选字节串，加密密钥
        """
        code, object_list = 0, []
        try:
            ssec = SseCustomerKey(encryption_key) if encryption_key else None

            for file in files:
                object_name = f"{uuid.uuid4()}_{file.name}"
                self.client.put_object(
                    bucket_name=self.bucket,
                    object_name=object_name,
                    data=file.file,
                    length=-1,
                    part_size=10 * 1024 * 1024,
                    content_type=file.content_type,
                )
                object_list.append(object_name)
        except Exception as e:
            return code, [str(e)]
        return 200, object_list


    def delete_file(self, object_name: str) -> Tuple[int, str]:
        try:
            self.client.remove_object(self.bucket, object_name)
            return 200, object_name
        except S3Error as e:
            return 0, f"Delete failed: {e.code} - {e.message}"
        except Exception as e:
            return 0, f"Delete failed: {str(e)}"


    def generate_download_url(self, object_name: str, encryption_key: Union[bytes, None] = None, expires: int = 3600) -> Tuple[int, str]:
        """
        生成带预签名下载链接（注意 expires 要是 timedelta）
        """
        try:
            ssec = SseCustomerKey(encryption_key) if encryption_key else None
            url = self.client.presigned_get_object(
                bucket_name=self.bucket,
                object_name=object_name,
                expires=timedelta(seconds=expires)
            )
            return 200, url
        except Exception as e:
            return 0, str(e)

