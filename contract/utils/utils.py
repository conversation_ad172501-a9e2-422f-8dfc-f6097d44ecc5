from datetime import datetime, timedelta
from typing import Optional 
from dateutil.relativedelta import relativedelta

def generate_billing_periods(start_time: str, end_time: str, period_type: str, pay_mode: str = '后付'):
    """
    生成账单周期
    :param start_time: 合同开始时间（YYYY-MM-DD）
    :param end_time: 合同结束时间（YYYY-MM-DD）
    :param period_type: 分期类型 ('month' | 'quarter')
    :param pay_mode: '前付'（账期向前挪1月），'后付'（账期向后挪1月），默认 '后付'
    :return: (status_code, list of dicts)
    """
    try:
        start_date = datetime.strptime(start_time, "%Y-%m-%d")
        end_date = datetime.strptime(end_time, "%Y-%m-%d")

        # 根据付款方式调整初始时间
        if pay_mode == '前付':
            current_start = start_date
        elif pay_mode == '后付':
            current_start = start_date
        else:
            return 0, "Unsupported pay_mode. Use '前付' or '后付'."

        result = []

        while current_start < end_date:
            # 计算下一个周期的起始时间
            if period_type == 'month':
                next_start = current_start + relativedelta(months=1)
            elif period_type == 'quarter':
                next_start = current_start + relativedelta(months=3)
            else:
                return 0, "Unsupported period type. Use 'month' or 'quarter'."

            current_end = next_start - timedelta(days=1)
            if current_end > end_date:
                current_end = end_date

            result.append({
                'payment_start_time': current_start.strftime('%Y-%m-%d'),
                'payment_end_time': current_end.strftime('%Y-%m-%d'),
                'description': f"{current_start.strftime('%Y-%m-%d')} 至 {current_end.strftime('%Y-%m-%d')} 的账单",
                'status': '未回款',
            })

            current_start = current_end + timedelta(days=1)

        return 200, result

    except Exception as e:
        return 0, str(e)

def fast_month_diff(
    start: Optional[datetime], 
    end: Optional[datetime],
    default_return: int = 0  # 空值默认返回值 
    ) -> int:
    """
    快速计算两个日期的月份差（批量处理优化版）
    
    参数：
        start: 开始日期（允许None）
        end: 结束日期（允许None）
        default_return: 当任一日期为空时的默认返回值 
    
    返回：
        整月数差值（可为负值） 
    
    示例：
        >>> fast_month_diff(datetime(2025,3,1), datetime(2025,5,1))
        2 
        >>> fast_month_diff(None, datetime(2025,5,1))
        0 
    """
    if start is None or end is None:
        return default_return 
    
    # 核心计算逻辑 
    try:
        return (end.year  - start.year)  * 12 + (end.month  - start.month) 
    except AttributeError as e:
        raise TypeError("输入必须为datetime对象") from e 


if __name__ == "__main__":
    # 按月账单，前付
    monthly_prepaid = generate_billing_periods("2025-05-12", "2025-06-27", 'month', pay_mode='前付')
    # print(monthly_prepaid)

    # 按季度账单，后付（默认）
    quarterly_postpaid = generate_billing_periods("2024-03-11", "2025-05-06", 'quarter', pay_mode='后付')
    # print(quarterly_postpaid)
