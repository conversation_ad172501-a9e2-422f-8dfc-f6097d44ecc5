# Generated by Django 5.0.6 on 2025-05-13 11:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contract', '0003_remove_payment_payment_list_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='contract',
            name='customer',
        ),
        migrations.AlterField(
            model_name='contract',
            name='amount',
            field=models.DecimalField(decimal_places=2, help_text='合同总价', max_digits=20, null=True, verbose_name='合同总价'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='applicant',
            field=models.CharField(help_text='合同申请人', max_length=255, null=True, verbose_name='合同申请人'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='cash_pledge',
            field=models.DecimalField(decimal_places=2, help_text='押金', max_digits=20, null=True, verbose_name='押金'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='code',
            field=models.CharField(help_text='合同编号', max_length=255, unique=True, verbose_name='合同编号'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='customer_name',
            field=models.CharField(help_text='客户名称', max_length=255, null=True, verbose_name='客户名称'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='end_time',
            field=models.DateTimeField(help_text='合同截至时间', null=True, verbose_name='合同截至时间'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='name',
            field=models.CharField(help_text='合同名称', max_length=255, verbose_name='合同名称'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='parent_code',
            field=models.CharField(help_text='父级合同Code: 用于区别续签与新签', max_length=255, null=True, unique=True, verbose_name='父级合同Code: 用于区别续签与新签'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='pay_type',
            field=models.CharField(default='后付', help_text='付款方式：前付|后付', max_length=255, verbose_name='付款方式：前付|后付'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='second_party',
            field=models.CharField(help_text='合同乙方', max_length=255, null=True, verbose_name='合同乙方'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='share_name',
            field=models.CharField(help_text='合同共享人', max_length=255, null=True, verbose_name='合同共享人'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='sign_name',
            field=models.CharField(help_text='合同签订人', max_length=255, verbose_name='合同签订人'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='sign_time',
            field=models.CharField(help_text='合同签订时间', max_length=255, null=True, verbose_name='合同签订时间'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='sku_list',
            field=models.JSONField(default=list, help_text='sku列表', null=True, verbose_name='sku列表'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='start_time',
            field=models.DateTimeField(help_text='合同开始时间', null=True, verbose_name='合同开始时间'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='status',
            field=models.CharField(default='待提交', help_text='合同状态', max_length=255, null=True, verbose_name='合同状态'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='type',
            field=models.CharField(default='新签', help_text='合同种类', max_length=255, verbose_name='合同种类'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='agent_info',
            field=models.JSONField(help_text='代理信息', max_length=dict, null=True, verbose_name='代理信息'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='amount',
            field=models.DecimalField(decimal_places=2, help_text='预回款金额', max_digits=20, null=True, verbose_name='预回款金额'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='cash_pledge',
            field=models.DecimalField(decimal_places=2, help_text='押金', max_digits=20, null=True, verbose_name='押金'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='cashple_pay_time',
            field=models.DateTimeField(help_text='押金回款日期', null=True, verbose_name='押金回款日期'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='contract_code',
            field=models.CharField(help_text='合同流程编号', max_length=255, null=True, verbose_name='合同流程编号'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='invoice_account',
            field=models.CharField(help_text='银行账户', max_length=255, null=True, verbose_name='银行账户'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='invoice_address',
            field=models.CharField(blank=True, help_text='付款方地址', max_length=255, null=True, verbose_name='付款方地址'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='invoice_bank',
            field=models.CharField(help_text='开户行', max_length=255, null=True, verbose_name='开户行'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='invoice_banking',
            field=models.CharField(help_text='银行营业网点', max_length=255, null=True, verbose_name='银行营业网点'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='invoice_phone',
            field=models.CharField(help_text='电话号码', max_length=255, null=True, verbose_name='电话号码'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='invoice_tax',
            field=models.CharField(help_text='纳税人识别号', max_length=255, null=True, verbose_name='纳税人识别号'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='invoice_title',
            field=models.CharField(help_text='付款方名称', max_length=255, null=True, verbose_name='付款方名称'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='invoice_type',
            field=models.CharField(default='企业', help_text='付款类型：个人|企业', max_length=255, verbose_name='付款类型：个人|企业'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='is_invoice',
            field=models.BooleanField(default=False, help_text='是否是有发票回款', verbose_name='是否是有发票回款'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='payee',
            field=models.CharField(default='杭州星哉科技有限公司', help_text='收款方', max_length=255, null=True, verbose_name='收款方'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='payment_time',
            field=models.DateTimeField(help_text='预回款日期', null=True, verbose_name='预回款日期'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='pre_payment_status',
            field=models.CharField(default='未回款', help_text='往期账单回款状态：未回款|全部回款', max_length=255, null=True, verbose_name='往期账单回款状态：未回款|全部回款'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='status',
            field=models.CharField(default='未回款', help_text='回款状态：未回款|部分回款|全部回款', max_length=255, null=True, verbose_name='回款状态：未回款|部分回款|全部回款'),
        ),
        migrations.AlterField(
            model_name='paymentperiodhistory',
            name='payment_data_list',
            field=models.JSONField(default=list, help_text='账期内回款列表', null=True, verbose_name='账期内回款列表'),
        ),
        migrations.AlterField(
            model_name='paymentperiodhistory',
            name='payment_end_time',
            field=models.DateTimeField(help_text='预回款结束日期', null=True, verbose_name='预回款结束日期'),
        ),
        migrations.AlterField(
            model_name='paymentperiodhistory',
            name='payment_start_time',
            field=models.DateTimeField(help_text='预回款开始日期', null=True, verbose_name='预回款开始日期'),
        ),
        migrations.AlterField(
            model_name='paymentperiodhistory',
            name='status',
            field=models.CharField(default='未回款', help_text='回款状态：未回款|全部回款|部分回款', max_length=255, null=True, verbose_name='回款状态：未回款|全部回款|部分回款'),
        ),
        migrations.AlterField(
            model_name='product',
            name='attributes',
            field=models.CharField(help_text='产品规格', max_length=1024, null=True, verbose_name='产品规格'),
        ),
        migrations.AlterField(
            model_name='product',
            name='label',
            field=models.CharField(default='新签', help_text='产品标签', max_length=255, null=True, verbose_name='产品标签'),
        ),
        migrations.AlterField(
            model_name='product',
            name='name',
            field=models.CharField(help_text='产品名称', max_length=255, verbose_name='产品名称'),
        ),
        migrations.AlterField(
            model_name='product',
            name='price',
            field=models.DecimalField(decimal_places=2, help_text='产品单价', max_digits=20, null=True, verbose_name='产品单价'),
        ),
        migrations.AlterField(
            model_name='product',
            name='source',
            field=models.CharField(default='自行开发', help_text='产品来源', max_length=255, null=True, verbose_name='产品来源'),
        ),
        migrations.AlterField(
            model_name='product',
            name='type',
            field=models.CharField(help_text='种类', max_length=255, null=True, verbose_name='种类'),
        ),
        migrations.AlterField(
            model_name='product',
            name='unit',
            field=models.CharField(default='元/(台·月)', help_text='计费单位: 月/季/年', max_length=255, null=True, verbose_name='计费单位: 月/季/年'),
        ),
    ]
