# Generated by Django 5.0.6 on 2025-05-13 10:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contract', '0002_alter_payment_options'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='payment',
            name='payment_list',
        ),
        migrations.AddField(
            model_name='payment',
            name='pre_payment_status',
            field=models.CharField(default='未回款', max_length=255, null=True, verbose_name='往期账单回款状态：未回款|全部回款'),
        ),
        migrations.CreateModel(
            name='PaymentPeriodHistory',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.Char<PERSON><PERSON>(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('status', models.CharField(default='未回款', max_length=255, null=True, verbose_name='回款状态：未回款|已回款|部分回款|延期回款')),
                ('payment_data_list', models.JSONField(default=list, null=True, verbose_name='账期内回款列表')),
                ('payment_start_time', models.DateTimeField(null=True, verbose_name='预回款开始日期')),
                ('payment_end_time', models.DateTimeField(null=True, verbose_name='预回款结束日期')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('payment', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payment_period_history', to='contract.payment', to_field='id')),
            ],
            options={
                'verbose_name': '回款记录表',
                'verbose_name_plural': '回款记录表',
                'db_table': 'contract_pay_pid_histories',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
