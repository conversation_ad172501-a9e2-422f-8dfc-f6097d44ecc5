# Generated by Django 5.0.6 on 2025-05-13 11:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contract', '0004_remove_contract_customer_alter_contract_amount_and_more'),
        ('customer', '0002_remove_customer_usage_type_customer_officer_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='contract',
            name='customer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='contract', to='customer.customer', to_field='id'),
        ),
    ]
