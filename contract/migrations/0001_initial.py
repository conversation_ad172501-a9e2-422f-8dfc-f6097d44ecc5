# Generated by Django 5.0.6 on 2025-05-08 09:01

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Accessory',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(blank=True, help_text='名称', max_length=200, null=True, verbose_name='名称')),
                ('filename', models.CharField(blank=True, help_text='别名', max_length=200, null=True, verbose_name='别名：上传至对象存储的名称')),
                ('resource_id', models.CharField(blank=True, help_text='资源ID', max_length=200, null=True, verbose_name='资源ID')),
                ('url', models.CharField(blank=True, help_text='文件地址', max_length=255, null=True, verbose_name='文件地址')),
                ('size', models.CharField(blank=True, help_text='文件大小', max_length=36, null=True, verbose_name='文件大小')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '附件表',
                'verbose_name_plural': '附件表',
                'db_table': 'contract_accessory',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='Contract',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('parent_code', models.CharField(max_length=255, null=True, unique=True, verbose_name='父级合同Code: 用于区别续签与新签')),
                ('name', models.CharField(max_length=255, verbose_name='合同名称')),
                ('sign_time', models.CharField(max_length=255, null=True, verbose_name='合同签订时间')),
                ('start_time', models.DateTimeField(null=True, verbose_name='合同开始时间')),
                ('end_time', models.DateTimeField(null=True, verbose_name='合同截至时间')),
                ('sign_name', models.CharField(max_length=255, verbose_name='合同签订人')),
                ('second_party', models.CharField(max_length=255, null=True, verbose_name='合同乙方')),
                ('cash_pledge', models.DecimalField(decimal_places=2, max_digits=20, null=True, verbose_name='押金')),
                ('status', models.CharField(default='待提交', max_length=255, null=True, verbose_name='合同状态')),
                ('type', models.CharField(default='新签', max_length=255, verbose_name='合同种类')),
                ('customer', models.JSONField(default=dict, null=True, verbose_name='客户信息')),
                ('customer_name', models.CharField(max_length=255, null=True, verbose_name='客户名称')),
                ('share_name', models.CharField(max_length=255, null=True, verbose_name='合同共享人')),
                ('applicant', models.CharField(max_length=255, null=True, verbose_name='合同申请人')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=20, null=True, verbose_name='合同总价')),
                ('code', models.CharField(max_length=255, unique=True, verbose_name='合同编号')),
                ('pay_type', models.CharField(default='后付', max_length=255, verbose_name='付款方式：前付|后付')),
                ('sku_list', models.JSONField(default=list, null=True, verbose_name='sku列表')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '合同表',
                'verbose_name_plural': '合同表',
                'db_table': 'contract_contract',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=20, null=True, verbose_name='预回款金额')),
                ('status', models.CharField(default='未回款', max_length=255, null=True, verbose_name='回款状态：未回款|部分回款|全部回款')),
                ('payee', models.CharField(default='杭州星哉科技有限公司', max_length=255, null=True, verbose_name='收款方')),
                ('payment_time', models.DateTimeField(null=True, verbose_name='预回款日期')),
                ('cashple_pay_time', models.DateTimeField(null=True, verbose_name='押金回款日期')),
                ('cash_pledge', models.DecimalField(decimal_places=2, max_digits=20, null=True, verbose_name='押金')),
                ('is_invoice', models.BooleanField(default=False, verbose_name='是否是有发票回款')),
                ('invoice_type', models.CharField(default='企业', max_length=255, verbose_name='付款类型：个人|企业')),
                ('invoice_title', models.CharField(max_length=255, null=True, verbose_name='付款方名称')),
                ('invoice_tax', models.CharField(max_length=255, null=True, verbose_name='纳税人识别号')),
                ('invoice_phone', models.CharField(max_length=255, null=True, verbose_name='电话号码')),
                ('invoice_address', models.CharField(blank=True, max_length=255, null=True, verbose_name='付款方地址')),
                ('invoice_bank', models.CharField(max_length=255, null=True, verbose_name='开户行')),
                ('invoice_account', models.CharField(max_length=255, null=True, verbose_name='银行账户')),
                ('invoice_banking', models.CharField(max_length=255, null=True, verbose_name='银行营业网点')),
                ('contract_code', models.CharField(max_length=255, null=True, verbose_name='合同流程编号')),
                ('payment_list', models.JSONField(default=list, null=True, verbose_name='回款历史记录')),
                ('agent_info', models.JSONField(max_length=dict, null=True, verbose_name='代理信息')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '合同表',
                'verbose_name_plural': '合同表',
                'db_table': 'contract_payment',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(max_length=255, verbose_name='产品名称')),
                ('attributes', models.CharField(max_length=1024, null=True, verbose_name='产品规格')),
                ('unit', models.CharField(default='元/(台·月)', max_length=255, null=True, verbose_name='计费单位: 月/季/年')),
                ('price', models.DecimalField(decimal_places=2, max_digits=20, null=True, verbose_name='产品单价')),
                ('label', models.CharField(default='新签', max_length=255, null=True, verbose_name='产品标签')),
                ('source', models.CharField(default='自行开发', max_length=255, null=True, verbose_name='产品来源')),
                ('type', models.CharField(max_length=255, null=True, verbose_name='种类')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '产品表',
                'verbose_name_plural': '产品表',
                'db_table': 'contract_product',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
