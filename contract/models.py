from django.db import models
from customer.models import Customer
from dvadmin.utils.chaos_models import ChaosCoreModel

TABLE_PREFIX = 'contract_'

class Product(ChaosCoreModel):
    name = models.CharField(max_length=255,verbose_name="产品名称",help_text="产品名称")
    attributes = models.CharField(max_length=1024,verbose_name="产品规格",help_text="产品规格",null=True)
    unit = models.CharField(max_length=255,default="元/(台·月)",verbose_name="计费单位: 月/季/年",help_text="计费单位: 月/季/年",null=True)
    price = models.DecimalField(max_digits=20,decimal_places=2,verbose_name='产品单价',help_text="产品单价",null=True)
    label = models.CharField(max_length=255,default="新签",verbose_name="产品标签",help_text="产品标签",null=True)
    source = models.CharField(max_length=255,default="自行开发",verbose_name="产品来源",help_text="产品来源",null=True)
    type = models.CharField(max_length=255,verbose_name="种类",help_text="种类",null=True)

    class Meta:
        db_table = TABLE_PREFIX + "product"
        verbose_name = "产品表"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)

class Contract(ChaosCoreModel):
    parent_code = models.CharField(max_length=255,verbose_name="父级合同Code: 用于区别续签与新签",help_text="父级合同Code: 用于区别续签与新签",null=True,unique=True)
    name = models.CharField(max_length=255,verbose_name="合同名称",help_text="合同名称")
    sign_time = models.CharField(max_length=255,null=True, verbose_name="合同签订时间",help_text="合同签订时间")
    start_time = models.DateTimeField(null=True, verbose_name="合同开始时间",help_text="合同开始时间")
    end_time = models.DateTimeField(null=True, verbose_name="合同截至时间",help_text="合同截至时间")
    sign_name = models.CharField(max_length=255, verbose_name="合同签订人",help_text="合同签订人")
    second_party = models.CharField(max_length=255, verbose_name="合同乙方",help_text="合同乙方",null=True)
    cash_pledge = models.DecimalField(max_digits=20, decimal_places=2, verbose_name='押金',help_text="押金", null=True)
    status = models.CharField(null=True,default="待提交",max_length=255, verbose_name="合同状态",help_text="合同状态")
    type = models.CharField(max_length=255, default="新签", verbose_name="合同种类", help_text="合同种类")
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name="contract",to_field='id',null=True)
    customer_name = models.CharField(max_length=255, verbose_name="客户名称",help_text="客户名称",null=True)
    share_name = models.CharField(max_length=255, verbose_name="合同共享人",help_text="合同共享人", null=True)
    applicant = models.CharField(max_length=255, verbose_name="合同申请人",help_text="合同申请人", null=True)
    amount = models.DecimalField(max_digits=20,decimal_places=2,verbose_name='合同总价',help_text="合同总价",null=True)
    code = models.CharField(max_length=255, verbose_name="合同编号",help_text="合同编号",unique=True)
    pay_type = models.CharField(max_length=255,default="后付",verbose_name="付款方式：前付|后付",help_text="付款方式：前付|后付")
    sku_list = models.JSONField(default=list, verbose_name="sku列表",help_text="sku列表", null=True)
    is_payment = models.BooleanField(
        verbose_name='是否生成了回款', help_text="是否生成了回款", default=False, null=True
        )

    class Meta:
        db_table = TABLE_PREFIX + "contract"
        verbose_name = "合同表"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)


class Payment(ChaosCoreModel):
    amount = models.DecimalField(max_digits=20, decimal_places=2, verbose_name='预回款金额',help_text="预回款金额", null=True)
    pre_payment_status = models.CharField(max_length=255,default="未回款",verbose_name="往期账单回款状态：未回款|全部回款",help_text="往期账单回款状态：未回款|全部回款", null=True)
    status = models.CharField(max_length=255,default="未回款",verbose_name="回款状态：未回款|部分回款|全部回款",help_text="回款状态：未回款|部分回款|全部回款", null=True)
    payee = models.CharField(max_length=255,default="杭州星哉科技有限公司",verbose_name="收款方",help_text="收款方",null=True)
    payment_time = models.DateTimeField(null=True, verbose_name="预回款日期",help_text="预回款日期")
    cashple_pay_time = models.DateTimeField(null=True, verbose_name="押金回款日期",help_text="押金回款日期")
    cash_pledge = models.DecimalField(max_digits=20, decimal_places=2, verbose_name='押金',help_text="押金", null=True)
    is_invoice = models.BooleanField(default=False, verbose_name='是否是有发票回款',help_text="是否是有发票回款")
    invoice_type = models.CharField(max_length=255, default="企业",verbose_name="付款类型：个人|企业",help_text="付款类型：个人|企业")
    invoice_title = models.CharField(max_length=255, verbose_name="付款方名称",help_text="付款方名称",null=True)
    invoice_tax = models.CharField(max_length=255, verbose_name="纳税人识别号",help_text="纳税人识别号", null=True)
    invoice_phone = models.CharField(max_length=255, verbose_name="电话号码",help_text="电话号码", null=True)
    invoice_address = models.CharField(max_length=255, verbose_name="付款方地址",help_text="付款方地址",null=True,blank=True)
    invoice_bank = models.CharField(max_length=255, verbose_name="开户行",help_text="开户行", null=True)
    invoice_account = models.CharField(max_length=255, verbose_name="银行账户",help_text="银行账户", null=True)
    invoice_banking = models.CharField(max_length=255, verbose_name="银行营业网点",help_text="银行营业网点", null=True)
    contract_code = models.CharField(max_length=255, verbose_name="合同流程编号",help_text="合同流程编号", null=True)
    agent_info = models.JSONField(max_length=dict, verbose_name="代理信息",help_text="代理信息", null=True)
    class Meta:
        db_table = TABLE_PREFIX + "payment"
        verbose_name = "回款表"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)

class PaymentPeriodHistory(ChaosCoreModel):
    status = models.CharField(max_length=255,default="未回款",verbose_name="回款状态：未回款|全部回款|部分回款",help_text="回款状态：未回款|全部回款|部分回款", null=True)
    payment_data_list = models.JSONField(default=list, verbose_name="账期内回款列表",help_text="账期内回款列表", null=True)
    payment_start_time = models.DateTimeField(null=True, verbose_name="预回款开始日期",help_text="预回款开始日期")
    payment_end_time = models.DateTimeField(null=True, verbose_name="预回款结束日期",help_text="预回款结束日期")
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name="payment_period_history",to_field='id',null=True)

    class Meta:
        db_table = TABLE_PREFIX + "pay_pid_histories"
        verbose_name = "回款记录表"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)


class Accessory(ChaosCoreModel):
    name = models.CharField(max_length=200, null=True, blank=True, verbose_name="名称", help_text="名称")
    filename = models.CharField(max_length=200, null=True, blank=True, verbose_name="别名：上传至对象存储的名称", help_text="别名")
    resource_id = models.CharField(max_length=200, null=True, blank=True, verbose_name="资源ID", help_text="资源ID")
    url = models.CharField(max_length=255, blank=True,null=True,verbose_name="文件地址", help_text="文件地址")
    size = models.CharField(max_length=36, blank=True, null=True,verbose_name="文件大小", help_text="文件大小")

    class Meta:
        db_table = TABLE_PREFIX + "accessory"
        verbose_name = "附件表"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)