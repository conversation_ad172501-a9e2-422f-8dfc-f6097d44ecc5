# 常见问题
1. django.db.utils.OperationalError: 1050解决方案 
```markdown
1. 问题描述
执行manage.py migrate，报错： django.db.utils.OperationalError: (1050, “Table ‘表名’ already exists）

2. 解决方法
数据库表字段变更比较频繁。models.py表类中添加了一个class类后。执行manage.py makemigrations 未提示错误信息，但manage.py migrate时进行同步数据库时出现问题;django.db.utils.OperationalError: (1050, “Table ‘表名’ already exists）错误信息

根据stackoverflow上找到解决方案，执行：

python manage.py migrate --fake

数据库表结构同步成功。
```

2. DjangoRestFramework-simplejwt 安全漏洞
> 待修复
# 1111
- [漏洞详情](https://devhub.checkmarx.com/cve-details/CVE-2024-22513/)

