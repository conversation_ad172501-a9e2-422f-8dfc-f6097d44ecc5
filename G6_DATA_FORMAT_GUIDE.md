# G6数据格式集成指南

## 概述

本指南介绍如何使用新增的G6数据格式API接口，这些接口专门为AntV G6图可视化组件提供标准化的层次化树形数据结构。

## API接口

### 1. 获取单个交换机G6数据

**接口:** `GET /api/virtual-switches/{switch_id}/get_g6_data/`

**描述:** 获取指定交换机的G6格式层次化数据

**响应示例:**
```json
{
  "code": 2000,
  "msg": "获取G6数据成功",
  "data": {
    "id": "switch-123",
    "name": "Core-Switch-01",
    "count": 48,
    "label": "42/48",
    "currency": "Interfaces",
    "rate": 1,
    "status": "G",
    "variableName": "Uptime",
    "variableValue": 0.95,
    "variableUp": true,
    "children": [
      {
        "id": "physical-interfaces-1234",
        "name": "Physical Interfaces",
        "count": 48,
        "label": "42/48",
        "currency": "Ports",
        "rate": 0.875,
        "status": "G",
        "variableName": "Active",
        "variableValue": 0.875,
        "variableUp": true,
        "collapsed": false,
        "children": [
          {
            "id": "speed-1000Mbps-5678",
            "name": "1000Mbps Interfaces",
            "count": 24,
            "label": "20/24",
            "currency": "Ports",
            "rate": 0.833,
            "status": "G",
            "variableName": "Utilization",
            "variableValue": 0.833,
            "variableUp": true,
            "collapsed": true,
            "children": [
              {
                "id": "interface-1",
                "name": "GigabitEthernet0/0/1",
                "count": 1,
                "label": "up",
                "currency": "Status",
                "rate": 1,
                "status": "G",
                "variableName": "Speed",
                "variableValue": 0.1,
                "variableUp": true,
                "children": []
              }
            ]
          }
        ]
      },
      {
        "id": "trunk-interfaces-2345",
        "name": "Trunk Interfaces",
        "count": 2,
        "label": "2/2",
        "currency": "Trunks",
        "rate": 1,
        "status": "G",
        "variableName": "Active",
        "variableValue": 1,
        "variableUp": true,
        "collapsed": true,
        "children": []
      }
    ]
  }
}
```

### 2. 获取机房交换机G6数据

**接口:** `GET /api/virtual-switches/get_room_g6_data/?machine_room_id={room_id}`

**描述:** 获取指定机房内所有交换机的G6格式层次化数据

**参数:**
- `machine_room_id`: 机房ID

**响应示例:**
```json
{
  "code": 2000,
  "msg": "获取机房G6数据成功",
  "data": {
    "id": "room-456",
    "name": "北京机房A",
    "count": 15,
    "label": "15",
    "currency": "Switches",
    "rate": 1,
    "status": "G",
    "variableName": "Capacity",
    "variableValue": 0.3,
    "variableUp": true,
    "children": [
      {
        "id": "private-room-789",
        "name": "核心网络包间",
        "count": 8,
        "label": "8",
        "currency": "Switches",
        "rate": 1,
        "status": "B",
        "variableName": "Utilization",
        "variableValue": 0.4,
        "variableUp": true,
        "collapsed": true,
        "children": [
          {
            "id": "switch-1",
            "name": "Core-Switch-01",
            "count": 1,
            "label": "192.168.1.100",
            "currency": "Device",
            "rate": 1,
            "status": "G",
            "variableName": "Health",
            "variableValue": 0.85,
            "variableUp": true,
            "children": []
          }
        ]
      }
    ]
  }
}
```

### 3. 获取交换机接口信息（兼容接口）

**接口:** `GET /api/virtual-switches/interfaces/?switch_id={switch_id}`

**描述:** 获取交换机的详细接口信息，兼容原有接口格式

**参数:**
- `switch_id`: 交换机ID

## 数据结构说明

### 节点字段定义

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `id` | string | 唯一标识符 | "switch-123" |
| `name` | string | 显示名称 | "Core-Switch-01" |
| `count` | number | 计数值 | 48 |
| `label` | string | 标签文本 | "42/48" |
| `currency` | string | 单位描述 | "Interfaces" |
| `rate` | number | 比率值 (0-1) | 0.875 |
| `status` | string | 状态标识 | "G", "B", "R", "DI" |
| `variableName` | string | 变量名称 | "Uptime" |
| `variableValue` | number | 变量值 (0-1) | 0.95 |
| `variableUp` | boolean | 变量趋势 | true |
| `collapsed` | boolean | 是否折叠 | true |
| `children` | array | 子节点数组 | [] |

### 状态值说明

| 状态 | 含义 | 颜色建议 |
|------|------|----------|
| `G` | 良好 (Good) | 绿色 |
| `B` | 一般 (Basic) | 蓝色 |
| `R` | 风险 (Risk) | 红色 |
| `DI` | 禁用/未知 (Disabled/Unknown) | 灰色 |

## 前端集成示例

### React + G6 集成

```jsx
import React, { useEffect, useRef } from 'react';
import G6 from '@antv/g6';

const SwitchTopologyChart = ({ switchId }) => {
  const containerRef = useRef(null);
  const graphRef = useRef(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // 创建G6图实例
    const graph = new G6.TreeGraph({
      container: containerRef.current,
      width: 800,
      height: 600,
      modes: {
        default: ['collapse-expand', 'drag-canvas', 'zoom-canvas']
      },
      defaultNode: {
        size: 26,
        anchorPoints: [[0, 0.5], [1, 0.5]],
        style: {
          fill: '#C6E5FF',
          stroke: '#5B8FF9'
        }
      },
      defaultEdge: {
        type: 'cubic-horizontal',
        style: {
          stroke: '#A3B1BF'
        }
      },
      layout: {
        type: 'compactBox',
        direction: 'LR',
        getId: (d) => d.id,
        getHeight: () => 16,
        getWidth: () => 16,
        getVGap: () => 10,
        getHGap: () => 100
      }
    });

    // 自定义节点样式
    graph.node((node) => {
      const statusColors = {
        'G': '#52c41a',  // 绿色
        'B': '#1890ff',  // 蓝色
        'R': '#ff4d4f',  // 红色
        'DI': '#d9d9d9'  // 灰色
      };

      return {
        label: `${node.name}\n${node.label}`,
        labelCfg: {
          offset: 10,
          position: node.children && node.children.length > 0 ? 'left' : 'right',
          style: {
            fontSize: 12,
            fill: '#000'
          }
        },
        style: {
          fill: statusColors[node.status] || '#C6E5FF',
          stroke: statusColors[node.status] || '#5B8FF9'
        }
      };
    });

    graphRef.current = graph;

    // 加载数据
    loadSwitchData();

    return () => {
      if (graphRef.current) {
        graphRef.current.destroy();
      }
    };
  }, [switchId]);

  const loadSwitchData = async () => {
    try {
      const response = await fetch(`/api/virtual-switches/${switchId}/get_g6_data/`);
      const result = await response.json();
      
      if (result.code === 2000) {
        graphRef.current.data(result.data);
        graphRef.current.render();
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    }
  };

  return <div ref={containerRef} style={{ width: '100%', height: '600px' }} />;
};

export default SwitchTopologyChart;
```

### Vue + G6 集成

```vue
<template>
  <div ref="container" class="topology-container"></div>
</template>

<script>
import G6 from '@antv/g6';

export default {
  name: 'SwitchTopologyChart',
  props: {
    switchId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      graph: null
    };
  },
  mounted() {
    this.initGraph();
    this.loadData();
  },
  beforeDestroy() {
    if (this.graph) {
      this.graph.destroy();
    }
  },
  methods: {
    initGraph() {
      this.graph = new G6.TreeGraph({
        container: this.$refs.container,
        width: 800,
        height: 600,
        modes: {
          default: ['collapse-expand', 'drag-canvas', 'zoom-canvas']
        },
        defaultNode: {
          size: 26,
          anchorPoints: [[0, 0.5], [1, 0.5]]
        },
        defaultEdge: {
          type: 'cubic-horizontal'
        },
        layout: {
          type: 'compactBox',
          direction: 'LR',
          getId: (d) => d.id,
          getHeight: () => 16,
          getWidth: () => 16,
          getVGap: () => 10,
          getHGap: () => 100
        }
      });

      // 自定义节点渲染
      this.graph.node((node) => {
        return {
          label: `${node.name}\n${node.label}`,
          labelCfg: {
            offset: 10,
            position: node.children && node.children.length > 0 ? 'left' : 'right'
          }
        };
      });
    },
    async loadData() {
      try {
        const response = await this.$http.get(`/api/virtual-switches/${this.switchId}/get_g6_data/`);
        
        if (response.data.code === 2000) {
          this.graph.data(response.data.data);
          this.graph.render();
        }
      } catch (error) {
        console.error('加载数据失败:', error);
      }
    }
  }
};
</script>

<style scoped>
.topology-container {
  width: 100%;
  height: 600px;
  border: 1px solid #e8e8e8;
}
</style>
```

## 数据层次结构

```
交换机 (VirtualSwitch)
├── 物理接口组 (Physical Interfaces)
│   ├── 1000Mbps接口组
│   │   ├── GigabitEthernet0/0/1
│   │   ├── GigabitEthernet0/0/2
│   │   └── ...
│   ├── 10000Mbps接口组
│   │   ├── XGigabitEthernet0/0/1
│   │   └── ...
│   └── ...
├── 聚合接口组 (Trunk Interfaces)
│   ├── Eth-Trunk1
│   ├── Eth-Trunk2
│   └── ...
├── MAC地址组 (MAC Addresses)
│   └── (MAC地址统计信息)
└── 基础设施组 (Infrastructure)
    ├── 机房信息
    ├── 机柜信息
    └── ...
```

## 测试和验证

运行测试脚本验证数据格式：

```bash
python test_g6_data.py
```

测试内容包括：
- 单个交换机G6数据格式测试
- 机房交换机G6数据格式测试
- 数据结构验证
- API接口测试

## 最佳实践

1. **性能优化**
   - 对于大型网络，考虑分页或按需加载
   - 使用`collapsed`属性控制初始展开状态
   - 合理设置节点大小和间距

2. **用户体验**
   - 提供搜索和过滤功能
   - 支持节点点击查看详细信息
   - 添加图例说明状态颜色含义

3. **数据更新**
   - 实现数据自动刷新机制
   - 支持增量更新避免重新渲染整个图
   - 添加加载状态提示

4. **错误处理**
   - 处理网络请求失败情况
   - 提供友好的错误提示
   - 实现重试机制

通过以上API接口和集成示例，您可以轻松地将交换机拓扑数据集成到G6可视化组件中，实现丰富的网络拓扑可视化效果。
