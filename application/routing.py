# -*- coding: utf-8 -*-
from django.urls import path
from application.websocketConfig import Meg<PERSON><PERSON>
from tenant.utils.channels import websocket
from scheduletask.utils.channels.consumer import LogConsumer

websocket_urlpatterns = [
    path('ws/<str:service_uid>/', MegCenter.as_asgi()),  # consumers.DvadminWebSocket 是该路由的消费者
    path('webssh/', websocket.TerminalConsumer.as_asgi()),  # 开头是webssh请求交给websocket.WebSSH处理
    path('work/<str:work_id>/', LogConsumer.as_asgi()),  # 任务日志消费者
]


