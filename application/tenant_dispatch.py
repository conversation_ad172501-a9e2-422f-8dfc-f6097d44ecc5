from dvadmin.system.models import Users


def get_user_all_info(user_id=None, tenant_id=None, account_id=None):
    """
    获取用户所有信息，系统用户信息、OpenStack 账号信息、Tenant 账号信息
    :param user_id: 系统用户 ID
    :param tenant_id: 租户唯一标识 ID
    :param account_id: 租户 OpenStack 账号ID
    :return: dictionary of user
    """
    user_info = {}
    if not user_id or not tenant_id or not account_id:
        raise ValueError('You must provide a user ID or a tenant ID or account ID')

    if user_id:
        user_obj = Users.objects.filter(
            is_active=True,
            id=user_id,
        ).first()
        if not user_obj:
            return None
        if user_obj:
            user_info = {
                "id": user_obj.id,
                "name": user_obj.name,
                "mobile": user_obj.mobile,
                "email": user_obj.email,
                "dept": user_obj.dept.name if user_obj.dept else "",
                "role": user_obj.role.name if user_obj.role else "",
            }
            return user_info

    queryset = Users.objects.filter(status=True)
    data = []
    for instance in queryset:
        data.append(
            {
                "id": instance.id,
                "name": instance.name,
                "mobile": instance.mobile,
                "email": instance.email,
                "dept": instance.dept.name if instance.dept else "",
                "role": instance.role.name if instance.role else "",
            }
        )
    return data
