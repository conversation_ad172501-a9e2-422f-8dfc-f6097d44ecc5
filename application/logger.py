import time
import logging
from functools import wraps

# 使用Setting中 配置的默认logger设置
logger = logging.getLogger('')


def logging_func_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        function_name = func.__name__
        logger.info(f'**************<开始执行任务: {function_name}>**************')
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f'**************<结束执行任务: {function_name}>**************')
        run_time = end_time - start_time
        logger.info(f'**************<任务执行完成, 所用时间：{run_time:.6f} 秒>****')
        return result
    return wrapper
