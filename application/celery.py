import functools
import os

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')

from django.conf import settings
from celery import platforms

from celery.schedules import crontab


# 租户模式
if "django_tenants" in settings.INSTALLED_APPS:
    from tenant_schemas_celery.app import CeleryApp as TenantAwareCeleryApp

    celery = TenantAwareCeleryApp()
else:
    from celery import Celery

    celery = Celery(f"chaos")
celery.config_from_object('django.conf:settings')
# 动态添加任务模块
celery.conf.update(
    CELERY_IMPORTS=(
        'dvadmin.celery_workers.sync_openstack_resource_worker',
        'dvadmin.celery_workers.sync_notice_ticket_worker',
        'dvadmin.celery_workers.host_status_notice_worker',
        'dvadmin.celery_workers.sync_scheduletask_worker',
        'dvadmin.celery_workers.contract_payment_notice_worker'
    )
)

# 定时任务
celery.conf.beat_schedule = {
    'sync-openstack-image-info': {
        'task': 'dvadmin.celery_workers.sync_openstack_resource_worker.sync_tenant_openstack_image_worker',
        'schedule': crontab(minute='*/6',),
        'args': (['金华',],)
    },
    'sync-openstack-flavor-info': {
        'task': 'dvadmin.celery_workers.sync_openstack_resource_worker.sync_tenant_openstack_flavor_worker',
        'schedule': crontab(minute='*/30',),
        'args': (['金华',],)
    },
    'sync-openstack-ironic-hypervisor-info': {
        'task': 'dvadmin.celery_workers.sync_openstack_resource_worker.sync_tenant_openstack_ironic_hypervisor_worker',
        'schedule': crontab(minute='*/1',),
        'args': (['金华',],)
    },
    'sync-openstack-project-info': {
        'task': 'dvadmin.celery_workers.sync_openstack_resource_worker.sync_tenant_openstack_project_worker',
        'schedule': crontab(minute='*/12',),
        'args': (['金华',],)
    },
    'sync-openstack-network-info': {
        'task': 'dvadmin.celery_workers.sync_openstack_resource_worker.sync_tenant_openstack_network_worker',
        'schedule': crontab(minute='*/15',),
        'args': (['金华',],)
    },
    'sync-openstack-security-group-info': {
        'task': 'dvadmin.celery_workers.sync_openstack_resource_worker.sync_tenant_openstack_security_group_worker',
        'schedule': crontab(minute='*/18',),
        'args': (['金华',],)
    },
    'sync-openstack-server-info': {
        'task': 'dvadmin.celery_workers.sync_openstack_resource_worker.sync_tenant_openstack_server_worker',
        'schedule': crontab(minute='*/1',),
        'args': (['金华',],)
    },
    'check-host-quite-buffer-notice': {
        'task': 'dvadmin.celery_workers.host_status_notice_worker.check_operator_cmdb_host_quite_buffer_worker',
        'schedule': crontab(hour='9,14', minute='0',),
        'args': ()
    },
    'check-operator-cmdb-host-expire-at-notice': {
        'task': 'dvadmin.celery_workers.host_status_notice_worker.check_operator_cmdb_host_expire_at_worker',
        'schedule': crontab(hour='9,14', minute='0',),
        'args': ()
    },
    'check-tenant-openstack-server-expired-notice': {
        'task': 'dvadmin.celery_workers.host_status_notice_worker.check_tenant_openstack_server_expired_worker',
        'schedule': crontab(hour='9,14', minute='0',),
        'args': ()
    },
    'check-tenant-openstack-server-reachable-notice': {
        'task': 'dvadmin.celery_workers.host_status_notice_worker.check_tenant_openstack_server_reachable_worker',
        'schedule': crontab(minute='*/1',),
        'args': ()
    },
    'wait-create-openstack-server-software-install-worker': {
        'task': 'dvadmin.celery_workers.host_status_notice_worker.wait_create_openstack_server_software_install_worker',
        'schedule': crontab(minute='*/3',),
        'args': ()
    },
    # TODO 回款通知
    'sync-contract-expired-notice': {
        'task': 'dvadmin.celery_workers.contract_payment_notice_worker.check_contract_expired_worker',
        'schedule': crontab(minute=0,hour=9,day_of_week=1),
        'args': ()
    },
    'sync-contract-archive_task': {
        'task': 'dvadmin.celery_workers.contract_payment_notice_worker.check_contract_is_archive_worker',
        'schedule': crontab(hour='9,14', minute='0',),
        'args': ()
    },
    'sync-contract-sign-notice': {
        'task': 'dvadmin.celery_workers.contract_payment_notice_worker.check_contract_is_sign_worker',
        'schedule': crontab(minute=0,hour=9,day_of_week=1),
        'args': ()
    },
    'sync-payment-period-overdue-notice': {
        'task': 'dvadmin.celery_workers.contract_payment_notice_worker.check_payment_period_overdue_worker',
        'schedule': crontab(hour='9', minute='0',),
        'args': ()
    },
    'sync-payment-period-collection-current-notice': {
        'task': 'dvadmin.celery_workers.contract_payment_notice_worker.check_payment_period_collection_current_worker',
        'schedule': crontab(hour='9', minute='0',),
        'args': ()
    },
}
celery.autodiscover_tasks(lambda: settings.INSTALLED_APPS)
platforms.C_FORCE_ROOT = True


def retry_base_task_error():
    """
    celery 失败重试装饰器
    :return:
    """

    def wraps(func):
        @celery.task(bind=True, retry_delay=180, max_retries=3)
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as exc:
                raise self.retry(exc=exc)

        return wrapper

    return wraps
