"""
<AUTHOR>
@Date    ：2024/10/29
"""
from application.conf_urls.base import *


interval_admin_urls =(
    [
        # 添加资源管理模块API
        path("api/resource/", include("xresource.urls")),
        # 添加OPERATOR-CMDB管理模块API
        path("api/operatorcmdb/", include("operatorcmdb.urls")),
        # 添加OPERATOR-CMDB管理模块API
        path("api/customer/", include("customer.urls")),
        # 添加 tenants 管理模块API
        path("api/tenants/", include("tenant.urls")),
        # 添加任务执行模块
        path("api/scheduletask/", include("scheduletask.urls")),
        # 简易工单模块API
        path("api/ticket/", include("ticket.urls")),
        # 简易通知模块API
        path("api/notice/", include("notice.urls")),
        # nova urls TODO 将来替换掉
        path('', include('nova.urls')),
        # nova jobs TODO 将来替换掉
        path('', include('job.urls')),
        path('api/report_datav/', include('report_datav.urls')),
        # 添加合同管理模块
        path("api/contract/", include("contract.urls")),
    ]
)


urlpatterns += interval_admin_urls
