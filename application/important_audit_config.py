import xresource.dictionary_enum_keys as resource_dictionary_enum_config
import operatorcmdb.dictionary_enum_keys as operatorcmdb_dictionary_enum_config

AUDIT_LOG_CONFIG = {
    # 包间
    'xresource.PrivateRoom': {
        # 是否启用，主要针对于配置了需要关闭的
        'is_enabled': True,
        # 配置特殊字段获取可查看内容的数据如：枚举类key->提取value
        'using_fields': {
            'machine_room_id': {
                # 查询其他数据表的配置，不一定必须是foreign_key才可使用，有关联性就可，只要是在本系统内皆可配置使用。
                'type': 'foreign_key',
                # 关联的目标数据表名称
                'relation_model': 'xresource.MachineRoom',
                # 此关联的值在目标表中对应的 field 名称
                'key': 'id',
                # 此关联的值在目标表中对应想要展示值的 field 名称
                'value': 'name',
                },
            'private_room_type': {
                'type': 'dictionary',
                # 使用此系统的字典配置时，对应的key名称
                'dictionary_key': resource_dictionary_enum_config.PRIVATE_ROOM_TYPE_KEY,
                # 未获取到配置值，设置的默认值, 默认为原始值；修改为其他值时，则默认为该值。
                'default': 'self',
                },
            'status': {
                # 使用此系统的字典配置时，对应的key名称
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.PRIVATE_ROOM_STATUS_KEY,
                },
            # 'block_sn': {
            #     # 未使用此系统的字典配置时，无需使用字段配置时，使用 枚举类型
            #     'type': 'enum',
            #     # 枚举值的配置信息
            #     'enum_config': {
            #         '2': "测试enum类型为2",
            #         '1': "测试enum类型为1"
            #     },
            #     # 未获取到配置值，设置的默认值, 默认为原始值；修改为其他值时，则默认为该值。
            #     'default': 'self',
            # }
            },
        },
    # 机房
    'xresource.MachineRoom': {
        'is_enabled': True,
        'using_fields': {
            'province': {
                'type': 'foreign_key',
                'relation_model': 'dvadmin.system.Area',
                'key': 'code',
                'value': 'name',
                },
            'city': {
                'type': 'foreign_key',
                'relation_model': 'dvadmin.system.Area',
                'key': 'code',
                'value': 'name',
                },
            'district': {
                'type': 'foreign_key',
                'relation_model': 'dvadmin.system.Area',
                'key': 'code',
                'value': 'name',
                },
            },
        },
    # 机柜
    'xresource.IDCRackMachine': {
        'is_enabled': True,
        'using_fields': {
            'machine_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.MachineRoom',
                'key': 'id',
                'value': 'name',
                },
            'private_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.PrivateRoom',
                'key': 'id',
                'value': 'name',
                },
            'net_operator_business': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.IDC_RACK_MACHINE_NET_OPERATOR_BUSINESS_KEY,
                'default': 'self',
                },
            'rack_type': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.IDC_RACK_MACHINE_RACK_TYPE_KEY,
                'default': 'self',
                },
            'rack_physics_type': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.IDC_RACK_MACHINE_RACK_PHYSICS_TYPE_KEY,
                'default': 'self',
                },
            'network_type': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.IDC_RACK_MACHINE_NETWORK_TYPE_KEY,
                'default': 'self',
                },
            'security_domain': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.IDC_RACK_MACHINE_SECURITY_DOMAIN_KEY,
                'default': 'self',
                },
            'logical_zone': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.IDC_RACK_MACHINE_LOGICAL_ZONE_KEY,
                'default': 'self',
                },
            'rack_status': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.IDC_RACK_MACHINE_RACK_STATUS_KEY,
                'default': 'self',
                },
            'power_status': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.IDC_RACK_MACHINE_POWER_STATUS_KEY,
                'default': 'self',
                },
            'power_type': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.IDC_RACK_MACHINE_POWER_TYPE_KEY,
                'default': 'self',
                },
            },
        },
    # 资产管理 -> 物理服务器管理
    'xresource.PhysicalServerMachine': {
        'is_enabled': True,
        'using_fields': {
            'machine_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.MachineRoom',
                'key': 'id',
                'value': 'name',
                },
            'private_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.PrivateRoom',
                'key': 'id',
                'value': 'name',
                },
            'idc_rack_machine_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.IDCRackMachine',
                'key': 'id',
                'value': 'rack_sn',
                },
            'machine_type': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.PHYSICAL_ASSET_PHYSICAL_SERVER_MACHINE_TYPE_KEY,
                'default': 'self',
                },
            'belong': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.PHYSICAL_ASSET_BELONG_KEY,
                'default': 'self',
                },
            'gpu_model': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.PHYSICAL_ASSET_PHYSICAL_SERVER_MACHINE_GPU_MODEL_KEY,
                'default': 'self',
                },
            },
        },
    # 资产管理 -> 网络设备管理
    'xresource.NetworkHardware': {
        'is_enabled': True,
        'using_fields': {
            'machine_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.MachineRoom',
                'key': 'id',
                'value': 'name',
                },
            'private_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.PrivateRoom',
                'key': 'id',
                'value': 'name',
                },
            'idc_rack_machine_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.IDCRackMachine',
                'key': 'id',
                'value': 'rack_sn',
                },
            'machine_type': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.PHYSICAL_ASSET_NETWORK_HARDWARE_MACHINE_TYPE_KEY,
                'default': 'self',
                },
            'belong': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.PHYSICAL_ASSET_BELONG_KEY,
                'default': 'self',
                },
            },
        },
    # 资产管理 -> 配件管理
    'xresource.HolisticAccessory': {
        'is_enabled': True,
        'using_fields': {
            'machine_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.MachineRoom',
                'key': 'id',
                'value': 'name',
                },
            'private_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.PrivateRoom',
                'key': 'id',
                'value': 'name',
                },
            'machine_type': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.PHYSICAL_ASSET_HOLISTIC_ACCESSORY_MACHINE_TYPE_KEY,
                'default': 'self',
                },
            'belong': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.PHYSICAL_ASSET_BELONG_KEY,
                'default': 'self',
                },
            },
        },
    # 资产管理 -> 耗材管理
    'xresource.GeneralConsumable': {
        'is_enabled': True,
        'using_fields': {
            'machine_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.MachineRoom',
                'key': 'id',
                'value': 'name',
                },
            'private_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.PrivateRoom',
                'key': 'id',
                'value': 'name',
                },
            'machine_type': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.PHYSICAL_ASSET_GENERAL_CONSUMABLE_MACHINE_TYPE_KEY,
                'default': 'self',
                },
            'belong': {
                'type': 'dictionary',
                'dictionary_key': resource_dictionary_enum_config.PHYSICAL_ASSET_BELONG_KEY,
                'default': 'self',
                },
            },
        },
    # 资产管理 -> 耗材管理
    'operatorcmdb.Host': {
        'is_enabled': True,
        'using_fields': {
            'machine_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.MachineRoom',
                'key': 'id',
                'value': 'name',
                },
            'private_room_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.PrivateRoom',
                'key': 'id',
                'value': 'name',
                },
            'idc_rack_machine_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.IDCRackMachine',
                'key': 'id',
                'value': 'rack_sn',
                },
            'physical_server_machine_id': {
                'type': 'foreign_key',
                'relation_model': 'xresource.PhysicalServerMachine',
                'key': 'id',
                'value': 'physical_machine_sn',
                },
            'customer_id': {
                'type': 'foreign_key',
                'relation_model': 'customer.Customer',
                'key': 'id',
                'value': 'name',
                },
            'host_type': {
                'type': 'dictionary',
                'dictionary_key': operatorcmdb_dictionary_enum_config.OPERATOR_CMDB_HOST_HOST_TYPE,
                'default': 'self',
                },
            'host_status': {
                'type': 'dictionary',
                'dictionary_key': operatorcmdb_dictionary_enum_config.OPERATOR_CMDB_HOST_HOST_STATUS,
                'default': 'self',
                },
            'os': {
                'type': 'dictionary',
                'dictionary_key': operatorcmdb_dictionary_enum_config.OPERATOR_CMDB_HOST_OS,
                'default': 'self',
                },
            'gpu_model': {
                'type': 'dictionary',
                'dictionary_key': operatorcmdb_dictionary_enum_config.OPERATOR_CMDB_HOST_GPU_MODEL,
                'default': 'self',
                },
            },
        },
    #  合同管理 -> 产品管理
    'contract.Product': {
        'is_enabled': True,
        'using_fields': {
            },
        },
    #  合同管理 -> 合同管理
    'contract.Contract': {
        'is_enabled': True,
        'using_fields': {
            },
        'exclude': ['customer_id'],
        },
    #  合同管理 -> 付款管理
    'contract.Payment': {
        'is_enabled': True,
        'using_fields': {
            },
        },
    #  合同管理 -> 附件管理
    'contract.Accessory': {
        'is_enabled': True,
        'using_fields': {
            },
        },
    }
