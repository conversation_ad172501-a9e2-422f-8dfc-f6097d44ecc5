import React from 'react';
import { Card, Space, Tag, Divider, Typography } from 'antd';
import { 
  ClusterOutlined, 
  ApiOutlined, 
  HddOutlined,
  HomeOutlined 
} from '@ant-design/icons';

const { Text, Title } = Typography;

const NetworkTopologyLegend = ({ style }) => {
  const nodeTypes = [
    {
      type: 'switch',
      icon: <ClusterOutlined />,
      label: '交换机',
      color: '#1890ff',
      description: '网络交换设备'
    },
    {
      type: 'physical-port',
      icon: <ApiOutlined />,
      label: '物理端口',
      color: '#52c41a',
      description: '物理网络接口'
    },
    {
      type: 'trunk-port',
      icon: <HddOutlined />,
      label: '聚合端口',
      color: '#fa8c16',
      description: '链路聚合接口'
    },
    {
      type: 'machine-room',
      icon: <HomeOutlined />,
      label: '机房',
      color: '#722ed1',
      description: '数据中心机房'
    }
  ];

  const connectionTypes = [
    {
      type: 'physical-connection',
      label: '物理连接',
      color: '#52c41a',
      width: 2,
      description: '交换机到物理端口'
    },
    {
      type: 'trunk-connection',
      label: '聚合连接',
      color: '#fa8c16',
      width: 4,
      description: '交换机到聚合端口'
    },
    {
      type: 'trunk-member',
      label: '成员连接',
      color: '#1890ff',
      width: 2,
      description: '聚合端口到成员端口'
    },
    {
      type: 'room-connection',
      label: '机房连接',
      color: '#722ed1',
      width: 3,
      description: '机房到交换机'
    }
  ];

  const statusTypes = [
    {
      status: 'active',
      label: '活跃',
      color: '#52c41a',
      description: '设备正常运行'
    },
    {
      status: 'inactive',
      label: '非活跃',
      color: '#ff4d4f',
      description: '设备异常或关闭'
    },
    {
      status: 'unknown',
      label: '未知',
      color: '#d9d9d9',
      description: '状态未知'
    }
  ];

  return (
    <Card 
      title={<Title level={5} style={{ margin: 0 }}>图例说明</Title>}
      size="small"
      style={{ width: 280, ...style }}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {/* 节点类型 */}
        <div>
          <Text strong style={{ fontSize: 12 }}>节点类型</Text>
          <div style={{ marginTop: 8 }}>
            <Space direction="vertical" size={4} style={{ width: '100%' }}>
              {nodeTypes.map(item => (
                <div key={item.type} style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <div style={{
                    width: 20,
                    height: 20,
                    background: item.color,
                    borderRadius: 4,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#fff',
                    fontSize: 10
                  }}>
                    {item.icon}
                  </div>
                  <div style={{ flex: 1 }}>
                    <Text style={{ fontSize: 11, fontWeight: 500 }}>{item.label}</Text>
                    <br />
                    <Text style={{ fontSize: 10, color: '#666' }}>{item.description}</Text>
                  </div>
                </div>
              ))}
            </Space>
          </div>
        </div>

        <Divider style={{ margin: '8px 0' }} />

        {/* 连接类型 */}
        <div>
          <Text strong style={{ fontSize: 12 }}>连接类型</Text>
          <div style={{ marginTop: 8 }}>
            <Space direction="vertical" size={4} style={{ width: '100%' }}>
              {connectionTypes.map(item => (
                <div key={item.type} style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <div style={{
                    width: 30,
                    height: item.width,
                    background: item.color,
                    borderRadius: 1
                  }} />
                  <div style={{ flex: 1 }}>
                    <Text style={{ fontSize: 11, fontWeight: 500 }}>{item.label}</Text>
                    <br />
                    <Text style={{ fontSize: 10, color: '#666' }}>{item.description}</Text>
                  </div>
                </div>
              ))}
            </Space>
          </div>
        </div>

        <Divider style={{ margin: '8px 0' }} />

        {/* 状态类型 */}
        <div>
          <Text strong style={{ fontSize: 12 }}>状态标识</Text>
          <div style={{ marginTop: 8 }}>
            <Space direction="vertical" size={4} style={{ width: '100%' }}>
              {statusTypes.map(item => (
                <div key={item.status} style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Tag color={item.color} style={{ margin: 0, fontSize: 10 }}>
                    {item.label}
                  </Tag>
                  <Text style={{ fontSize: 10, color: '#666' }}>{item.description}</Text>
                </div>
              ))}
            </Space>
          </div>
        </div>
      </Space>
    </Card>
  );
};

export default NetworkTopologyLegend;
