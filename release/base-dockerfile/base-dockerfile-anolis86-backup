FROM registry.openanolis.cn/openanolis/anolisos:8.6
# 保留镜像的编译能力及排查问题能力
RUN set -x cd /tmp \
    && curl -O -# https://www.python.org/ftp/python/3.10.9/Python-3.10.9.tgz \
    && tar -xzvf Python-3.10.9.tgz \
    && cd Python-3.10.12 \
    && mkdir /usr/local/python3 \
    && dnf install -y make zlib zlib-devel openssl-devel sqlite-devel bzip2-devel libffi libffi-devel gcc gcc-c++ \
    && ./configure --prefix=/usr/local/python3 --enable-shared \
    && make && make install \
    && cp /usr/local/python3/lib/libpython3.10.so.1.0 /usr/local/lib \
    && cp /usr/local/python3/lib/libpython3.10.so.1.0 /usr/local/lib64 \
    && cp /usr/local/python3/lib/libpython3.10.so.1.0 /usr/lib \
    && cp /usr/local/python3/lib/libpython3.10.so.1.0 /usr/lib64 \
    && ln -s /usr/local/python3/bin/python3 /usr/bin/python \
    && ln -s /usr/local/python3/bin/python3 /usr/bin/python3 \
    && ln -s /usr/local/python3/bin/pip3 /usr/bin/pip3 \
    && ln -s /usr/local/python3/bin/pip3 /usr/bin/pip \
    && dnf clean all \
    && rm -rf /var/cache/dnf/* \
    && rm -rf /tmp/Python*
ENV PATH $PATH:/usr/local/python3/bin
