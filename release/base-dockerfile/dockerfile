FROM harbor.hzxingzai.cn/tools/python:3.10.14-alpine
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk update && apk add bash bash-doc bash-completion git freetds-dev jpeg-dev linux-headers mysql-client mariadb-dev build-base libffi-dev openssl-dev zlib-dev bzip2-dev pcre-dev ncurses-dev readline-dev tk-dev postgresql-dev openssh sshpass vim busybox-extras
WORKDIR /code
COPY base_requirements.txt .
RUN python3 -m pip install --upgrade pip && python3 -m pip install -i https://mirrors.aliyun.com/pypi/simple/ -r base_requirements.txt