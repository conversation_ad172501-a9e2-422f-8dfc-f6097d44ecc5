# Generated by Django 5.0.6 on 2025-04-09 09:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Report',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(blank=True, db_comment='报表名称', default='Unknown', help_text='报表名称', max_length=255, null=True, verbose_name='报表名称')),
                ('code', models.CharField(blank=True, db_comment='报表编码', help_text='报表编码', max_length=63, null=True, verbose_name='报表编码')),
                ('category', models.CharField(blank=True, db_comment='报表编码', help_text='报表编码', max_length=63, null=True, verbose_name='报表编码')),
                ('source_data', models.JSONField(blank=True, db_comment='元数据', default=dict, help_text='元数据', null=True, verbose_name='元数据')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '报表基础信息表',
                'verbose_name_plural': '报表基础信息表',
                'db_table': 'rdv_report',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='ReportCronJob',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(blank=True, db_comment='任务名称', default='Unknown', help_text='任务名称', max_length=255, null=True, verbose_name='任务名称')),
                ('cron_table', models.CharField(blank=True, db_comment='定时任务表达式', help_text='定时任务表达式', max_length=255, null=True, verbose_name='定时任务表达式')),
                ('enable', models.BooleanField(blank=True, db_comment='是否启用', default=False, help_text='是否启用', null=True, verbose_name='是否启用')),
                ('job_type', models.IntegerField(blank=True, db_comment='任务类型', default=0, help_text='任务类型', null=True, verbose_name='任务类型')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '报表定时任务表',
                'verbose_name_plural': '报表定时任务表',
                'db_table': 'rdv_report_cron_job',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='ReportTask',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(blank=True, db_comment='任务名称', default='Unknown', help_text='任务名称', max_length=255, null=True, verbose_name='任务名称')),
                ('params', models.JSONField(blank=True, db_comment='任务参数', default=dict, help_text='任务参数', null=True, verbose_name='任务参数')),
                ('task_type', models.CharField(blank=True, db_comment='任务类型', help_text='任务类型', max_length=63, null=True, verbose_name='任务类型')),
                ('path', models.CharField(blank=True, db_comment='任务路径', help_text='任务路径', max_length=255, null=True, verbose_name='任务路径')),
                ('output_data', models.JSONField(blank=True, db_comment='任务结果', default=dict, help_text='任务结果', null=True, verbose_name='任务结果')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '报表任务表',
                'verbose_name_plural': '报表任务表',
                'db_table': 'rdv_report_task',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
