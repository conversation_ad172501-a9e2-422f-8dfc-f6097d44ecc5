# import json
# from platform import machine
#
#
# def init_django():
#     import os
#     import django
#
#     os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
#     django.setup()
# init_django()
from datetime import timedelta
from collections import defaultdict
from django.utils import timezone
from django.db.models import Count, Sum
from django.db.models.functions  import ExtractYear, ExtractMonth

from dvadmin.system.models import Area
from customer.models import Customer
from operatorcmdb.models import Host
from dvadmin.utils.sdk.prometheus import PrometheusClient
from tenant.models import TenantOpenstackIronicHypervisor, TenantOpenstackServer
from application.dispatch import get_dictionary_values
from operatorcmdb.dictionary_enum_keys import OPERATOR_CMDB_HOST_AREA_NODE


class ChartDataManager(object):
    def __init__(self, env='product'):

        self.ENV = env
        self.exclude_projects = [
            '8f788892a6b444e69c618031b66a1240',  # 测试项目, 曹相鹏网络测试
        ]
        self.exclude_customer_ids = [
            'customer-282958905d924a1eaf75ee73c9c0cedc',  # 测试项目, 曹相鹏网络测试
        ]
        # 明确需要展示的网络型号
        self.ns_all_network_models = [
            '10G',
            '25G',
        ]
        # 明确需要展示的GPU型号
        self.ns_all_gpu_models = [
            '4090',
            'A800-PCIe',
        ]
    @staticmethod
    def get_node_value(area_node='全部'):
        if area_node == '全部':
            area_node_values = get_dictionary_values(OPERATOR_CMDB_HOST_AREA_NODE)
            area_nodes = [i['value'] for i in area_node_values.get('children', []) if i['value'] != '全部']
        else:
            area_nodes = [area_node]
        return area_nodes

    def get_machine_room_amount(self, area_node='全部'):
        """机房总量"""
        area_nodes = self.get_node_value(area_node)
        return len(area_nodes)

    def get_idc_rack_machine_amount(self, area_node='全部'):
        """
        机柜总量
        SELECT
            COUNT(DISTINCT idc_rack_machine_id) AS unique_count
        FROM
            operator_cmdb_hosts
        WHERE
            is_deleted = 0
            AND resource_category = '裸金属机'
            AND host_status != '下线'
            AND host_type = '裸金属';
        """
        area_node = self.get_node_value(area_node)
        idc_rack_machine_amount = Host.objects.filter(
            area__in=area_node,
            is_deleted=False,
            host_type='裸金属',
            # resource_category='裸金属机',
        ).exclude(host_status__in=['下线']).values('idc_rack_machine_id').distinct().count()
        return idc_rack_machine_amount

    def get_physical_server_machine_amount(self, area_node='全部'):
        """
        目前展示的机房数量仅展示正式使用的资源池。
        服务器总量
        SELECT
            COUNT(*) AS count
        FROM
            operator_cmdb_hosts
        WHERE
            is_deleted=0
            # AND resource_category='裸金属机'
            AND host_status != '下线'
            AND host_type="裸金属";
        """
        area_node = self.get_node_value(area_node)
        physical_server_machine_amount = Host.objects.filter(
            area__in=area_node,
            is_deleted=False,
            host_type='裸金属',
            # resource_category='裸金属机',
        ).exclude(host_status__in=['下线']).count()
        return physical_server_machine_amount

    def get_physical_server_machine_had_used_amount(self, area_node='全部'):
        """
        已使用服务器总量
        SELECT
            COUNT(*) AS count
        FROM
            operator_cmdb_hosts
        WHERE
            is_deleted=0
            AND resource_category='裸金属机'
            AND host_status = '启用'
            AND host_type="裸金属";
        """
        area_node = self.get_node_value(area_node)
        physical_server_machine_had_used_amount = Host.objects.filter(
            area__in=area_node,
            is_deleted=False,
            host_type='裸金属',
            # resource_category='裸金属机',
            host_status__in=['启用'],
        ).count()
        return physical_server_machine_had_used_amount

    def get_physical_server_machine_had_rental_amount(self, area_node='全部'):
        """
        已出租服务器总量
        """
        area_node = self.get_node_value(area_node)
        physical_server_machine_had_rental_amount = Host.objects.filter(
            area__in=area_node,
            is_deleted=False,
            host_type='裸金属',
            resource_category__in=['裸金属机', '客户存储机'],
            host_status__in=['启用'],
        ).count()
        return physical_server_machine_had_rental_amount

    def get_physical_server_machine_can_rental_amount(self, area_node='全部'):
        """获取可以出售的服务器总量"""
        area_node = self.get_node_value(area_node)
        physical_server_machine_can_rental_amount = Host.objects.filter(
            area__in=area_node,
            is_deleted=False,
            host_type='裸金属',
            resource_category__in=['裸金属机',  '客户存储机'],
            host_status__in=['启用', '空闲'],
        ).count()
        return physical_server_machine_can_rental_amount

    def get_physical_server_machine_had_auto_manager_amount(self, area_node='全部'):
        """
        已自动化管理的机器总数
        """
        area_node = self.get_node_value(area_node)
        physical_server_machine_had_auto_manager_amount = TenantOpenstackIronicHypervisor.objects.filter(
            is_deleted=False,
            node__in=area_node,
        ).count()
        return physical_server_machine_had_auto_manager_amount

    def get_buffer_machine_amount(self, area_node='全部'):
        """
        buffer机总量
        """
        area_node = self.get_node_value(area_node)
        buffer_machine_amount = Host.objects.filter(
            area__in=area_node,
            is_deleted=False,
            host_type='裸金属',
            resource_category__in=['裸金属机'],
            host_status__in=['空闲'],
        ).count()
        return buffer_machine_amount

    @staticmethod
    def get_op_ironic_hyper_tag_info(resource_class):
        # GPU型号映射表（优先级从高到低）
        GPU_MAPPING = {
            '4090': '4090',
            '3090': '3090',
            '2080': '2080',
            '800': 'A800-PCIe'
        }

        # 网络类型映射表（默认值设为10G）
        NETWORK_MAPPING = {
            'n10g': '10G',
            'n25g': '25G',
            'n40g': '40G',
            'n50g': '50G',
            'n100g': '100G'
        }

        # 提取GPU型号（不区分大小写）
        resource_lower = resource_class.lower()
        _gpu_tag = next(
            (value for key, value in GPU_MAPPING.items()
             if key in resource_lower),
            '未知'  # 默认值
        )

        # 提取网络类型（不区分大小写）
        _network_tag = next(
            (value for key, value in NETWORK_MAPPING.items()
             if key in resource_lower),
            '10G'  # 默认值
        )

        return {
            'gpu_model': _gpu_tag,
            'network_model': _network_tag
        }

    def get_buffer_machine_tags(self, area_node='全部', chart_type='bar'):
        """
        获取Buffer资源池显卡/网络日历图/柱状图
        返回优化后的数据结构，确保所有网络类型和GPU型号都显示（包括数量为0的情况）

        返回格式:
        - 柱状图: [{'network_class': '10G', '4090': 5, 'A800-PCIe': 3}, ...]
        - 日历图: [{'Name': '10G', 'Week': '4090', 'Value': 5}, ...]
        """
        area_node = self.get_node_value(area_node)

        # 1. 获取基础数据
        machines = Host.objects.filter(
            area__in=area_node,
            is_deleted=False,
            host_type='裸金属',
            resource_category='裸金属机',
            host_status='空闲'
        ).values_list('ip_bmc', flat=True)

        # 2. 批量查询hypervisor信息
        hypervisors = TenantOpenstackIronicHypervisor.objects.filter(
            is_deleted=False,
            name__in=list(machines)  # Convert to list for better performance
        ).only('name', 'resource_class')

        # 3. 构建统计字典
        stats = defaultdict(lambda: defaultdict(int))
        for hypervisor in hypervisors:
            tags = self.get_op_ironic_hyper_tag_info(hypervisor.resource_class)
            stats[tags['network_model']][tags['gpu_model']] += 1

            # 4. 根据图表类型返回不同格式的数据
        if chart_type == 'bar':
            # 柱状图格式
            bar_data = []
            for network_type in sorted(self.ns_all_network_models):
                entry = {'network_class': network_type}
                # 确保所有GPU型号都出现，默认值为0
                for gpu in sorted(self.ns_all_gpu_models):
                    entry[gpu] = stats[network_type].get(gpu, 0)
                bar_data.append(entry)
            return bar_data
        else:
            # 日历图格式
            calendar_data = []
            for network_type in sorted(self.ns_all_network_models):
                for gpu in sorted(self.ns_all_gpu_models):
                    calendar_data.append({
                        '网络带宽': network_type,
                        'GPU型号': gpu,
                        'Value': stats[network_type].get(gpu, 0)
                    })
            return calendar_data

    def get_month_commit_server_occurrence(self, area_node='全部', month_count=12):
        """
        params area_node: 区域简称
        params month_count: 月份数，默认12个月
        获取月交付频次
        """
        nowtime = timezone.now()
        start_time = (nowtime - timedelta(days=month_count * 30)).strftime('%Y-%m-01')
        end_time = nowtime.strftime('%Y-%m-%d')
        area_node = self.get_node_value(area_node)
        results = TenantOpenstackServer.objects.filter(
            node__in=area_node,
            create_datetime__range=(start_time, end_time),
        ).exclude(
            project_id__in=self.exclude_projects
        ).annotate(
            year=ExtractYear('create_datetime'),
            month=ExtractMonth('create_datetime')
        ).values(
            'year', 'month'
        ).annotate(
            count=Count('id')
        ).order_by(
            'year', 'month'
        )

        # 格式化为 YYYY-MM
        formatted_results = [
            {
                'month': f"{item['year']}-{item['month']:02d}",
                '频次': item['count']
            }
            for item in results
        ]
        return formatted_results

    def get_operator_server_feature_category(self, area_node='全部'):
        """
        获取按资源功能分类统计
        """
        area_node = self.get_node_value(area_node)
        operator_server_feature_category = Host.objects.filter(
            area__in=area_node,
            is_deleted=False,
            host_type='裸金属',
            host_status__in=['启用', '空闲'],
        ).values(
            'resource_category'
        ).annotate(
            count=Count('resource_category')
        ).all()  # 按GPU总数降序
        # 命名改为Charts数据格式
        operator_server_feature_category = [
            {'name': item['resource_category'], 'value': item['count']}
            for item in operator_server_feature_category
        ]
        operator_server_feature_category = sorted(
            operator_server_feature_category,
            key=lambda x: x['value'],
            reverse=True
        )
        return operator_server_feature_category

    def get_operator_server_gpu_category(self, area_node='全部'):
        """
        获取按资源GPU分类统计
        """
        area_node = self.get_node_value(area_node)
        operator_server_gpu_category = Host.objects.filter(
            area__in=area_node,
            is_deleted=False,
            host_type='裸金属',
            resource_category='裸金属机',
            host_status__in=['启用', '空闲']
        ).values(
            'gpu_model'
        ).annotate(
            count=Count('gpu_model')
        ).all()  # 按GPU总数降序
        # 命名改为Charts数据格式
        operator_server_gpu_category = [
            {'name': item['gpu_model'], 'value': item['count']}
            for item in operator_server_gpu_category
        ]
        operator_server_gpu_category = sorted(
            operator_server_gpu_category,
            key=lambda x: x['value'],
            reverse=True
        )
        return operator_server_gpu_category

    def get_server_power_watts_top10(self, area_node='全部'):
        # PrometheusClient(env='product', node='金华') node 为监控数据节点
        p_client = PrometheusClient(env='product', node='金华')
        # 监控数据的区域 tag region 信息
        server_power_watts_top10 = p_client.query_top10_power_watts(area_node=area_node)
        return server_power_watts_top10

    def get_customer_resource_count_top(self, area_node='全部', top=5):
        area_node = self.get_node_value(area_node)

        # 0. 定义一个主机为几张GPU卡数, 仅在显示时显示*8
        GPU_COUNT_PER_HOST = 1
        # TODO 0. 定义所有可能的GPU类型（按需补充）
        ALL_GPU_TYPES = ['4090', 'A800-PCle']

        # 1. 单次查询获取数据（使用select_related优化）
        host_queryset = (
            Host.objects.filter(
                area__in=area_node,
                is_deleted=False,
                host_status='启用',
                host_type='裸金属',
                resource_category='裸金属机',
            )
            .exclude(customer_id__in=self.exclude_customer_ids)
            .exclude(customer_id__isnull=True)
            .select_related('customer')
            .values('customer_id', 'customer__name', 'gpu_model')
        )

        # 2. 内存聚合统计
        company_stats = {}
        for host in host_queryset:
            customer_id = host['customer_id']
            company_name = host['customer__name']
            gpu_model = host['gpu_model'] or '未知GPU'  # 处理空值

            if customer_id not in company_stats:
                company_stats[customer_id] = {
                    'company': company_name,
                    '裸金属': 0,
                    'gpu_counts': defaultdict(int)
                }

            company_stats[customer_id]['裸金属'] += 1
            company_stats[customer_id]['gpu_counts'][gpu_model] += GPU_COUNT_PER_HOST  # 每台机器8块GPU

        # 4. 补全所有GPU类型（不存在则设为0）
        result = []
        for stats in company_stats.values():
            entry = {
                'company': stats['company'],
                '裸金属': stats['裸金属'],
                **{f'{gpu}显卡(*8张)': stats['gpu_counts'].get(gpu, 0)
                   for gpu in ALL_GPU_TYPES}  # 确保所有GPU类型都出现
            }
            result.append(entry)

        # 5. 按裸金属数量排序取TOPN

        result.sort(key=lambda x: x['裸金属'], reverse=True)
        # 仅取所需 Top 数据排序展示, 前端展示时需要由高到底
        data = sorted(result[:top], key=lambda x: x['裸金属'], reverse=False)
        return data

    @staticmethod
    def get_china_province_customer_had_resource_map():
        # Prefetch province names in a single query
        province_codes = Customer.objects.filter(
            is_deleted=False,
            province__isnull=False
        ).values_list('province', flat=True).distinct()

        # Create a mapping of province code to name
        province_names = dict(Area.objects.filter(
            code__in=province_codes,
            enable=True,
        ).values_list('code', 'name'))

        # Get customer counts and join with province names
        customer_counts = Customer.objects.filter(
            is_deleted=False,
            province__isnull=False
        ).values('province').annotate(
            value=Count('id')
        ).order_by('province')

        # Build the result with fallback to code if name not found
        data = [{
            'name': province_names.get(item['province'], item['province']),
            'value': item['value']
        } for item in customer_counts]
        return data

    def get_resource_position_charts_data(self, params):
        area_node = params.get('area_node')
        machine_room_amount = self.get_machine_room_amount(area_node=area_node)
        idc_rack_machine_amount = self.get_idc_rack_machine_amount(area_node=area_node)
        physical_server_machine_amount = self.get_physical_server_machine_amount(area_node=area_node)
        had_used_amount = self.get_physical_server_machine_had_used_amount(area_node=area_node)
        had_rental_amount = self.get_physical_server_machine_had_rental_amount(area_node=area_node)
        can_rental_amount = self.get_physical_server_machine_can_rental_amount(area_node=area_node)
        physical_server_machine_had_auto_manager_amount = self.get_physical_server_machine_had_auto_manager_amount(area_node=area_node)
        buffer_machine_amount = self.get_buffer_machine_amount(area_node=area_node)
        buffer_machine_tags = self.get_buffer_machine_tags(area_node=area_node)
        month_commit_server_occurrence = self.get_month_commit_server_occurrence(area_node=area_node)
        operator_server_feature_category = self.get_operator_server_feature_category(area_node=area_node)
        operator_server_gpu_category = self.get_operator_server_gpu_category(area_node=area_node)
        server_power_watts_top10 = self.get_server_power_watts_top10(area_node=area_node)
        customer_resource_count_top5 = self.get_customer_resource_count_top(
            area_node=area_node, top=5)
        china_province_customer_had_resource_map = self.get_china_province_customer_had_resource_map()
        data =  {
            'machine_room_amount': machine_room_amount,
            'idc_rack_machine_amount': idc_rack_machine_amount,
            'physical_server_machine_amount': physical_server_machine_amount,
            'had_used_amount': had_used_amount,
            'had_used_rate': round(had_used_amount / physical_server_machine_amount * 100, 2),
            'had_rental_amount': had_rental_amount,
            'had_rental_rate': round(had_rental_amount / can_rental_amount * 100, 2),
            'physical_server_machine_had_auto_manager_amount': physical_server_machine_had_auto_manager_amount,
            'buffer_machine_amount': buffer_machine_amount,
            'buffer_machine_tags': buffer_machine_tags,
            'operator_server_feature_category': operator_server_feature_category,
            'operator_server_gpu_category': operator_server_gpu_category,
            'server_power_watts_top10': server_power_watts_top10,
            'customer_resource_count_top5': customer_resource_count_top5,
            'month_commit_server_occurrence': month_commit_server_occurrence,
            'china_province_customer_had_resource_map': china_province_customer_had_resource_map,
        }
        return data


if __name__ == '__main__':
    obj = ChartDataManager()
    _node = '上海'
    print(f'当前机房：{_node}')
    # machine_room_amount = obj.get_machine_room_amount(area_node=_node)
    # print(f'机房总量：{machine_room_amount}')
    # idc_rack_machine_amount = obj.get_idc_rack_machine_amount(area_node=_node)
    # print(f'机柜总量：{idc_rack_machine_amount}')
    # physical_server_machine_amount = obj.get_physical_server_machine_amount(area_node=_node)
    # print(f'服务器总量：{physical_server_machine_amount}')
    # had_used_amount = obj.get_physical_server_machine_had_used_amount(area_node=_node)
    # print(f'已使用服务器总量：{had_used_amount}')
    # had_rental_amount = obj.get_physical_server_machine_had_rental_amount(area_node=_node)
    # print(f'已出租服务器总量：{had_rental_amount}')
    # physical_server_machine_had_auto_manager_amount = obj.get_physical_server_machine_had_auto_manager_amount(area_node=_node)
    # print(f'已自动化管理的机器总数：{physical_server_machine_had_auto_manager_amount}')
    # buffer_pool_amount = obj.get_buffer_machine_amount(area_node=_node)
    # print(f'buffer机总量：{buffer_pool_amount}')
    buffer_machine_tags = obj.get_buffer_machine_tags(area_node=_node, chart_type='bar')
    print(f'buffer机TAG信息：{buffer_machine_tags}')

    # month_commit_server_occurrence = obj.get_month_commit_server_occurrence(area_node=_node)
    # print(f'近一个月提交服务器的 occurrence：{month_commit_server_occurrence}')

    # operator_server_gpu_category = obj.get_operator_server_gpu_category(area_node=_node)
    # print(f'按资源GPU分类统计：{operator_server_gpu_category}')
    # operator_server_feature_category = obj.get_operator_server_feature_category(area_node=_node)
    # print(f'按资源功能分类统计：{operator_server_feature_category}')

    # server_power_watts_top10 = obj.get_server_power_watts_top10(area_node=_node)
    # print(f'服务器功率Top10统计：{server_power_watts_top10}')

    # customer_resource_count_top10 = obj.get_customer_resource_count_top10(
    #     area_node=_node)
    # print(f'客户资源统计Top10：{customer_resource_count_top10}')

    # china_province_customer_had_resource_map = obj.get_china_province_customer_had_resource_map()
    # print(f'中国省份客户资源统计：{china_province_customer_had_resource_map}')

    # resource_position_charts_data = obj.get_resource_position_charts_data(params={'area_node': _node})
    # print(json.dumps(resource_position_charts_data))
    # print(f'资源统计数据：{resource_position_charts_data}')





