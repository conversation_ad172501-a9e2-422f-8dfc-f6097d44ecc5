from datetime import timedelta
from django.utils import timezone

from application.logger import logger

from tenant.models import (
    TenantAccount,
    TenantOpenstackProject,
    TenantOpenstackServer,
)
from tenant.utils.sdk.openstack_client import OpenstackAdminClient


class CustomerResourceChartManager(object):
    def __init__(self, account_id=None):
        self.account_id = account_id

    def check_account_id(self):
        account = TenantAccount.objects.filter(
            id=self.account_id,
            is_deleted=False,
        ).values('id', 'company').first()
        if account:
            logger.error(f'get_tenant_portal_dashboard:<ErrorDetail: AccountIdNotProvider:{str(self.account_id)}>')
            return account
        else:
            return False

    def get_interval_report_customer_resource_data(self, user_type=1):
        """
        获取租户概况数据
        :param user_type: 账号类型
        :return:
        """
        # 近N天过期
        expire_time_interval = 15
        # 近N天已过期
        had_expired_time_interval = 15
        # 近N天创建
        create_time_interval = 7

        server_dashboard_data = {
            'total': 0,
            'baremetal_total': 0,
            'virtual_host_total': 0,
            'running': 0,
            'coming_expire': 0,
            'had_expired': 0,
            'coming_created': 0,
            'gpu_counts': 0,
        }
        project_quota_set_and_usage = {
            'quota_set': {
                'cores': 0,
                'instances': 0,
                'ram': 0,
            },
            'usage': {
                'cores': 0,
                'instances': 0,
                'ram': 0,
            }
        }
        account = self.check_account_id()
        if not account:
            return {
                'server_statical': server_dashboard_data,
                'project_quota_set_and_usage': project_quota_set_and_usage,
            }

        try:
            if user_type == 1:
                projects = TenantOpenstackProject.objects.filter(
                    account_id=self.account_id,
                    is_deleted=False,
                ).all()
                project_ids = [i.project_id for i in projects]
            else:
                project_ids = 'all'

            # 获取裸金属统计
            if project_ids == 'all':
                servers = TenantOpenstackServer.objects.filter(
                    is_deleted=False,
                ).all()
            else:
                servers = TenantOpenstackServer.objects.filter(
                    project_id__in=project_ids,
                    is_deleted=False,
                ).all()
            # 整理成dashboard 数据

            now_time = timezone.now()
            utc_now_time = timezone.datetime.now()
            coming_expire_end_time = now_time + timedelta(days=expire_time_interval)
            had_expire_start_time = now_time - timedelta(days=had_expired_time_interval)
            create_time = utc_now_time - timedelta(days=create_time_interval)
            for server in servers:
                server_dashboard_data['total'] += 1
                if server.instance_type == '云主机':
                    server_dashboard_data['virtual_host_total'] += 1
                # elif server.instance_type == '裸金属':
                #     server_dashboard_data['baremetal_total'] += 1
                else:
                    server_dashboard_data['baremetal_total'] += 1
                if server.status == 'ACTIVE':
                    server_dashboard_data['running'] += 1
                if server.expire_at and now_time < server.expire_at <= coming_expire_end_time:
                    server_dashboard_data['coming_expire'] += 1
                if server.expire_at and had_expire_start_time <= server.expire_at <= now_time:
                    server_dashboard_data['had_expired'] += 1
                elif server.create_datetime and create_time < server.create_datetime:
                    server_dashboard_data['coming_created'] += 1
                # 获取总的 GPU 数量
                try:
                    flavor_info = server.flavor_info
                    if flavor_info:
                        server_dashboard_data['gpu_counts'] += int(
                            flavor_info.get('extra_specs', {}).get('gpu_count', 0))
                except Exception as e:
                    logger.error(f'get_tenant_portal_dashboard_gpu_count_error:<ErrorDetail: {str(e)}>')
                    server_dashboard_data['gpu_counts'] += 0
            if project_ids != 'all':
                # 创建 Openstack 管理 SDK 实例
                admin_client = OpenstackAdminClient(node='金华')
                # 调用 SDK 接口获取��户概况数据
                project_quota_set_and_usage = admin_client.get_project_quota_set_and_usage(project_id=project_ids[0])
        except Exception as e:
            logger.error(f'get_tenant_portal_dashboard:<ErrorDetail: {str(e)}>')

        tenant_dashboard = {
            'server_statical': server_dashboard_data,
            'project_quota_set_and_usage': project_quota_set_and_usage,  # 租户裸金属配额和使用情况
        }
        return tenant_dashboard

    def get_customer_resource_charts(self):
        customer_resource_statical_charts = self.get_interval_report_customer_resource_data()
        return customer_resource_statical_charts


if __name__ == '__main__':
    obj = CustomerResourceChartManager(account_id='account-82a2f14315d342a5924c86fe101380e9')
    customer_resource_statical_charts_data = obj.get_customer_resource_charts()
    print(customer_resource_statical_charts_data)



