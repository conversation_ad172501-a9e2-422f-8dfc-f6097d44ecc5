from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.db import transaction

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import ErrorResponse, SuccessResponse

from report_datav.models import Report
from report_datav.serializers.report import (
    ReportSerializer,
    ReportImportSerializer
)
from report_datav.views.resource_statisical_charts import ChartDataManager
from report_datav.views.customer_resource_charts import CustomerResourceChartManager


class ReportViewSet(ChaosCustomModelViewSet):
    """
    报表接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Report.objects.order_by('-create_datetime')
    serializer_class = ReportSerializer
    filter_fields = '__all__'

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = ReportImportSerializer
    import_field_dict = {}
    permission_classes = []

    area_node = openapi.Parameter('area_node', openapi.IN_QUERY, description='机房区域简称', type=openapi.TYPE_STRING)
    @swagger_auto_schema(
        method='GET', manual_parameters=[area_node, ],
        operation_summary='查询资源统计图表数据')
    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def get_resource_statisical_charts_data(self, request):
        """
        查询资源统计图表数据
        """
        query_params = request.query_params
        data = ChartDataManager().get_resource_position_charts_data(query_params)
        return SuccessResponse(data=data)

    account_id = openapi.Parameter('account_id', openapi.IN_QUERY, description='客户账号ID', type=openapi.TYPE_STRING)

    @swagger_auto_schema(
        method='GET', manual_parameters=[area_node, ],
        operation_summary='查询客户资源统计图表数据')
    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def get_customer_resource_statical_charts_data(self, request):
        """
        查询客户资源统计图表数据
        """
        query_params = request.query_params
        account_id = query_params.get('account_id', 'account-82a2f14315d342a5924c86fe101380e9')
        data = CustomerResourceChartManager(account_id=account_id).get_customer_resource_charts()
        return SuccessResponse(data=data)
