from email.policy import default

from django.db import models
from dvadmin.utils.chaos_models import ChaosCoreModel

# report_datav 缩写
TABLE_PREFIX = 'rdv_'


class Report(ChaosCoreModel):
    name = models.CharField(
        max_length=255, default="Unknown", null=True, blank=True,
        verbose_name="报表名称", db_comment='报表名称', help_text="报表名称")
    code = models.CharField(
        max_length=63, null=True, blank=True,
        verbose_name="报表编码", db_comment='报表编码', help_text="报表编码")
    category = models.CharField(
        max_length=63, null=True, blank=True,
        verbose_name="报表编码", db_comment='报表编码', help_text="报表编码")
    source_data = models.JSONField(
        default=dict, null=True, blank=True,
        verbose_name="元数据", db_comment='元数据', help_text="元数据"
    )
    path_name = models.CharField(
        max_length=1023, null=True, blank=True,
        verbose_name="报表前端路径", db_comment='报表前端路径', help_text="报表前端路径")
    selected_data = models.JSONField(
        null=True, blank=True, default=dict,
        verbose_name="搜索值", db_comment='搜索值', help_text="搜索值"
    )
    class Meta:
        db_table = TABLE_PREFIX + "report"
        verbose_name = "报表基础信息表"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)


class ReportTask(ChaosCoreModel):
    name = models.CharField(
        max_length=255, default="Unknown", null=True, blank=True,
        verbose_name="任务名称", db_comment='任务名称', help_text="任务名称")
    params = models.JSONField(
        default=dict, null=True, blank=True,
        verbose_name="任务参数", db_comment='任务参数', help_text="任务参数")
    task_type = models.CharField(
        max_length=63, null=True, blank=True,
        verbose_name="任务类型", db_comment='任务类型', help_text="任务类型")
    path = models.CharField(
        max_length=255, null=True, blank=True,
        verbose_name="任务路径", db_comment='任务路径', help_text="任务路径")
    output_data = models.JSONField(
        default=dict, null=True, blank=True,
        verbose_name="任务结果", db_comment='任务结果', help_text="任务结果")

    class Meta:
        db_table = TABLE_PREFIX + "report_task"
        verbose_name ="报表任务表"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)


class ReportCronJob(ChaosCoreModel):
    name = models.CharField(
        max_length=255, default="Unknown", null=True, blank=True,
        verbose_name="任务名称", db_comment='任务名称', help_text="任务名称")
    cron_table = models.CharField(
        max_length=255, null=True, blank=True,
        verbose_name="定时任务表达式", db_comment='定时任务表达式', help_text="定时任务表达式")
    enable = models.BooleanField(
        default=False, null=True, blank=True,
        verbose_name="是否启用", db_comment='是否启用', help_text="是否启用")
    # 0:定时任务 1:手动执行任务 2:一次性任务
    job_type = models.IntegerField(
        default=0, null=True, blank=True,
        verbose_name="任务类型", db_comment='任务类型', help_text="任务类型")

    class Meta:
        db_table = TABLE_PREFIX + "report_cron_job"
        verbose_name ="报表定时任务表"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)

