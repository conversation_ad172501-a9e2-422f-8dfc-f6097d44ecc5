from django.utils import timezone
from django.db.models import Q

from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from report_datav.models import Report


class ReportImportSerializer(ChaosCustomModelSerializer):
    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = Report
        exclude = ()


class ReportSerializer(ChaosCustomModelSerializer):
    """
    主机-序列化器
    """

    class Meta:
        model = Report
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


class ReportUpdateSerializer(ChaosCustomModelSerializer):
    """
    主机-序列化器
    """

    class Meta:
        model = Report
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]
