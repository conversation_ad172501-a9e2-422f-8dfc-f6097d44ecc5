<html>
<head>
  <script type="text/javascript" charset="utf-8">
    // Map of projects that support handling 404s.
    var supportedProjects = {
      'ReDoc': 'https://redocly.github.io/redoc'
    };

    var project = window.location.pathname.split('/')[1];

    // Always fallback to rebilly.com redirect.
    var loc = 'https://www.rebilly.com/';

    if (supportedProjects.hasOwnProperty(project)) {
      if (typeof supportedProjects[project] === 'string') {
        loc = supportedProjects[project];
      }
    }
    window.location.href = loc;
  </script>
</head>
<body></body>
</html>
