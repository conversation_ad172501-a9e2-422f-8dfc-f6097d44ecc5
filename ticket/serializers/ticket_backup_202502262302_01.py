"""
<AUTHOR>
@Date    ：2024/11/26
"""
import json
import time

from application.logger import logger
from django.db import transaction
from rest_framework import serializers

from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from dvadmin.utils.toolkits import get_shanghai_str_time
from scheduletask.urls import scheduletask_url

from ticket.serializers.task import TaskSerializer
from ticket.models import (
    Ticket, Task, TenantOPServerStatusMachine
)

from tenant.models import (
    TenantOpenstackProject,
    TenantOpenstackNetwork,
    TenantOpenstackSecurityGroup,
    TenantAccount,
    TenantOpenstackFlavor,
    TenantOpenstackServer,
)
from tenant.utils.tenant_resource_relation import (
    get_operatorcmdb_host_info,
    from_ticket_get_operatorcmdb_instance_ids,
)
from tenant.serializers.tenant_openstack_server import CreateBaremetalServerView

from scheduletask.views.work import WorkModelViewSet
from scheduletask.models import Work


class TicketImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        instance = super().save(**kwargs)
        return instance

    class Meta:
        model = Ticket
        exclude = ()


class TicketSerializer(ChaosCustomModelSerializer):
    """
    工单-序列化器
    """

    class Meta:
        model = Ticket
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


class TicketCreateSerializer(ChaosCustomModelSerializer):
    """
    工单-序列化器
    """

    def get_account_op_info(self, account_id):
        # 获取账号关联的openstack信息
        error_info = ''
        data = {}
        project = TenantOpenstackProject.objects.filter(
            account_id=account_id,
            is_deleted=False,
        ).first()
        if not project:
            error_info += '账号不存在: {0}\n'.format(account_id)
        else:
            data.update({'project_id': project.project_id})
        network = TenantOpenstackNetwork.objects.filter(
            project_id=project.project_id,
            is_deleted=False,
        ).first()
        if not network:
            error_info += '账号所属的openstack 网络不存在: {0}\n'.format(project.project_id)
        else:
            data.update({'network_id': network.network_id})

        security_group_name = TenantOpenstackSecurityGroup.objects.filter(
            project_id=project.project_id,
            is_deleted=False,
        ).first()
        if not security_group_name:
            # 暂不做安全组预检测
            pass
            # error_info += '账号所属的openstack 安全组不存在: {0}\n'.format(project.project_id)
        else:
            data.update({'security_group_names': security_group_name.name})
        data.update({
            'error_info': error_info,
        })
        return data

    @staticmethod
    def get_account_name(account_id):
        obj = TenantAccount.objects.filter(
            id=account_id,
            is_deleted=False,
        ).first()
        return obj.company if obj else 'unknown-company'

    @staticmethod
    def get_flavor_name(flavor_id):
        obj = TenantOpenstackFlavor.objects.filter(
            flavor_id=flavor_id,
            is_deleted=False,
        ).first()
        return obj.name if obj else 'unknown-flavor'

    def save(self, **kwargs):
        with transaction.atomic():
            source_form_data = self.validated_data.get('source_form_data', {})
            self.validated_data['name'] = (
                f'关于-{self.get_account_name(source_form_data["account_id"])}-'
                f'创建{self.get_flavor_name(source_form_data["flavor_id"])}-订单'
            )
            self.validated_data['current_status'] = '审批中'
            data = super().save(**kwargs)
            # 获取账号关联的openstack信息
            op_info = self.get_account_op_info(source_form_data.get('account_id', ''))
            form_data = op_info
            form_data.update(data.source_form_data)
            # 使用 TaskSerializer 创建任务
            task_data = {
                'ticket': data.id,
                'creator_id': data.creator_id,
                'modifier': data.modifier,
                'dept_belong_id': data.dept_belong_id,
                'create_datetime': data.create_datetime,
                'update_datetime': data.update_datetime,
                'step_index': 1,
                'name': '确认账号和主机信息',
                'form_data': form_data,
                'status': '审批中',
                'assign_to': '2',
            }
            task_serializer = TaskSerializer(data=task_data)
            if task_serializer.is_valid():
                task_serializer.save()
            else:
                raise serializers.ValidationError(task_serializer.errors)
        return data

    class Meta:
        model = Ticket
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


def ticket_create_baremetal_server_task(task_id, form_data):
    response = CreateBaremetalServerView.create_baremetal_server(form_data)
    res_data = response.data
    results = res_data['data']
    error_server_data = []
    success_server_data = []
    error_msg = []
    for result in results:
        if result.get('error_code'):
            error_server_data.append(result.get('error_info'))
            error_msg.append({
                'timestamp': get_shanghai_str_time(),
                'level': 'ERROR',
                'msg': f"调度失败(创建任务未调用成功)，错误原因：{result.get('error_info')}"
            })
        elif result.get('id'):
            success_server_data.append({
                'id': result.get('id'),
                'name': result.get('name')
            })
        else:
            t_msg = '【创建任务失败<未知原因：%s>】' % str(result)
            error_msg.append({
                'timestamp': get_shanghai_str_time(),
                'level': 'ERROR',
                'msg': t_msg
            })
            logger.error(t_msg)
    try:
        with transaction.atomic():
            task = Task.objects.select_for_update().get(
                id=task_id,
                is_deleted=False,
            )
            t_msg = 'TaskId: %s, TicketId : %s' % (task.id, task.ticket_id)
            logger.info(t_msg)
            if len(error_server_data) == int(task.form_data['count']):
                ticket = Ticket.objects.filter(
                    id=task.ticket_id,
                    is_deleted=False,
                ).first()
                ticket.current_status = '失败'
                ticket.is_finished = 1
                ticket.current_step_index = 2
                ticket.save()
                t_msg = '所有创建主机在调度创建【失败】'
                error_msg.append({
                    'timestamp': get_shanghai_str_time(),
                    'level': 'ERROR',
                    'msg': t_msg,
                })
                logger.error(t_msg)
            # 更新 async_worker_result 字段
            t_form_data = task.form_data
            t_form_data.update({
                'error_info': error_msg
            })
            task.form_data = t_form_data
            task.async_worker_result = {
                'success_server_data': success_server_data,
                'error_server_data': error_server_data,
                'total_server_count': len(results),
            }
            task.save()
    except Exception as e:
        logger.error('【创建调度任务失败<任务id：%s>】【详细信息 %s】' % (str(task_id), str(e)))
        # 处理任务不存在的情况
    from dvadmin.celery_workers.sync_openstack_resource_worker import wait_for_ticket_finished_worker
    wait_for_ticket_finished_worker.apply_async(args=(task_id,))


def ticket_task_create_openstack_server_software_install(task_id):
    task_info = Task.objects.filter(
        id=task_id,
        is_deleted=False,
    ).first()
    if not task_info:
        logger.error(f'【创建调度任务失败<任务id：{task_id}>】【任务记录不存在】')
        return
    operatorcmdb_host_ids = from_ticket_get_operatorcmdb_instance_ids(task_info.ticket_id)
    # 获取创建主机信息
    softwares = task_info.form_data.get('softwares', [])
    for software in softwares:
        software_options = json.loads(software['options'])
        # 整合任务调用参数
        task_params = {
            software_options['key']: software['current_version'],
        }
        template_id = software['ansible_template']
        code, data, msg = WorkModelViewSet.create_exec_work(
            project_vars=task_params,
            template_id=template_id,
            host_ids=operatorcmdb_host_ids,
            description=task_info.ticket.ticket_id,
        )
        logger.info(f'【软件安装任务调度结果】【code:{str(code)},data: {str(data)}, msg: {msg}】')
        Work.objects.filter(
            id=data['id'],
            is_deleted=False,
        ).update(
            ticket_id=task_info.ticket_id
        )


def create_openstack_server_software_install_task(op_instance_id):
    server_status_machine = TenantOPServerStatusMachine.objects.filter(
        op_instance_id=op_instance_id,
        is_deleted=False,
    ).first()
    if not server_status_machine:
        logger.error(f'【创建调度任务失败<Openstack实例id：{op_instance_id}>】【主机状态机记录不存在】')
        return
    try:
        # 获取 operatorcmdb 主机信息
        operatorcmdb_host_info = get_operatorcmdb_host_info(op_instance_id=op_instance_id)
        if not operatorcmdb_host_info:
            logger.error(f'【创建调度任务失败<Openstack实例id：{op_instance_id}>】【获取 operatorcmdb 主机信息失败】')
            server_status_machine.fsm_status = '软件安装调度失败'
        else:
            # 获取创建主机信息
            softwares = server_status_machine.form_data.get('softwares', [])
            scheduletask_work_ids = []
            for software in softwares:
                software_options = json.loads(software['options'])
                # 整合任务调用参数
                task_params = {
                    software_options['key']: software['current_version'],
                }
                template_id = software['ansible_template']
                code, data, msg = WorkModelViewSet.create_exec_work(
                    project_vars=task_params,
                    template_id=template_id,
                    host_ids=[operatorcmdb_host_info.id],
                    description=server_status_machine.ticket.ticket_id,
                )
                logger.info(f'【软件安装任务调度结果】【code:{str(code)},data: {str(data)}, msg: {msg}】')
                scheduletask_work_ids.append(data['id'])
            server_status_machine.scheduletask_work_ids = scheduletask_work_ids
            server_status_machine.fsm_status = '软件安装中'
    except Exception as e:
        logger.error(f'【create_openstack_server_software_install_task】【ErrorDetail: {str(e)}】')
        server_status_machine.fsm_status = '软件安装调度失败'
    server_status_machine.retry_count += 1
    server_status_machine.save()


def wait_ticket_software_install_finished_task():
    # 先启动任务再检测同步任务状态
    sync_start_software_install_task()
    sync_schedule_worker_status_to_software_install_task()


def sync_start_software_install_task():
    logger.info('----------------------<开始【启动软件安装任务】>-----------------------')
    # 获取所有未结束的订单
    retry_amount = 3
    all_running_task = TenantOPServerStatusMachine.objects.filter(
        is_finished=False,
        fsm_status__in=['主机创建完成', '软件安装调度失败', '软件安装失败'],
        is_deleted=False,
    ).all()
    # 启动安装软件任务
    for running_task in all_running_task:
        # 启动软件安装任务
        if running_task.fsm_status == '主机创建完成':
            # 查询ping状态
            instance_server = TenantOpenstackServer.objects.filter(
                id=running_task.op_instance_id,
                is_deleted=False,
            ).first()
            if not instance_server:
                logger.error(f'OpenstackServer中主机已被删除或不存在<op_instance_id:{running_task.op_instance_id}>')
                running_task.description = f'OpenstackServer中主机已被删除或不存在<op_instance_id:{running_task.op_instance_id}>'
                running_task.is_finished = True
                running_task.save()
            else:
                if instance_server.is_reachable:
                    # 创建软件安装任务
                    create_openstack_server_software_install_task(op_instance_id=running_task.op_instance_id)
                    # TODO 通知到钉钉群
                else:
                    logger.warning(
                        f'【工单ID:{running_task.ticket.ticket_id}】OpenstackServer中主机【Ping】状态尚不可达<op_instance_id:{running_task.op_instance_id}>')
        elif running_task.fsm_status in ['软件安装调度失败', '软件安装失败']:
            if running_task.retry_count < retry_amount:
                create_openstack_server_software_install_task(op_instance_id=running_task.op_instance_id)
    logger.info('----------------------<结束【启动软件安装任务】>-----------------------')


def sync_schedule_worker_status_to_software_install_task():
    logger.info('----------------------<开始【同步软件安装状态】>-----------------------')
    all_running_task = TenantOPServerStatusMachine.objects.filter(
        is_finished=False,
        fsm_status__in=['主机创建完成', '软件安装中'],
        is_deleted=False,
    ).all()
    for running_task in all_running_task:
        status = '软件安装中'
        if running_task.scheduletask_work_ids:

            for scheduletask_work_id in running_task.scheduletask_work_ids:
                # 查询软件安装任务状态
                work = Work.objects.filter(
                    id=scheduletask_work_id,
                    is_deleted=False,
                ).first()

                if work:
                    if work.status == 2:
                        status = '软件安装完成'
                    elif work.status == 3:
                        status = '软件安装失败'
                    else:
                        status = '软件安装中'
        if status == '软件安装中':
            continue
        running_task.fsm_status = status
        running_task.save()
    logger.info('----------------------<结束【同步软件安装状态】>-----------------------')

# 创建任务完全结束，安装任务完全结束
ticket.models.py
class TenantOPServerStatusMachine(ChaosCoreModel):
    """无需记录创建等信息，仅用于后端记录数据使用 openstack 主机状态机管理"""
    FSM_STATUS = (
        ('主机创建中', '主机创建中'),
        ('主机创建完成', '主机创建完成'),
        ('主机创建失败', '主机创建失败'),
        ('软件安装中', '软件安装中'),
        ('软件安装调度失败', '软件安装调度失败'),
        ('软件安装完成', '软件安装完成'),
        ('软件安装失败', '软件安装失败'),
    )
    op_instance_id = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='OpenStack实例ID', help_text='OpenStack实例ID', db_comment='OpenStack实例ID'
    )
    ticket = models.ForeignKey(
        Ticket,
        to_field='id',
        null=True,
        db_constraint=False,
        on_delete=models.SET_NULL,
        verbose_name="关联工单",
        help_text="关联工单",
    )
    fsm_status = models.CharField(
        max_length=63, blank=True, null=True, default='主机创建中',
        verbose_name='状态机状态', help_text='状态机状态', db_comment='状态机状态',
    )
    scheduletask_work_ids = models.JSONField(
        blank=True, null=True, default=dict,
        verbose_name='worker任务IDS', help_text='worker任务IDS', db_comment='worker任务IDS'
    )
    form_data = models.JSONField(
        blank=True, null=True, default=dict,
        verbose_name='任务数据', help_text='任务数据', db_comment='任务数据'
    )
    is_finished = models.BooleanField(
        default=False,
        verbose_name='是否结束', help_text='是否结束', db_comment='是否完成'
    )
    retry_count = models.IntegerField(
        default=0,
        verbose_name='重试次数', help_text='重试次数', db_comment='重试次数'
    )
    merged_log = models.TextField(
        blank=True, null=True,
        verbose_name='合并日志', help_text='合并日志', db_comment='合并日志'
    )

    def host_created_finished_failed(self):
        # 使用事务确保原子性
        self.fsm_status = '主机创建失败'
        self.save()

    def host_created_finished_success(self):
        """快速安装软件"""
        logger.error('in fast_install_host ------------->')
        # 先同步到 operatorcmdb
        host_info = get_tenant_op_server_info(self.op_instance_id)
        tenant_openstack_server_to_operator_cmdb_hosts(server_id=host_info.id)
        self.fsm_status = '主机创建完成'
        self.save()

    class Meta:
        db_table = TABLE_PREFIX + "opt_server_status_machines"
        verbose_name = "工单关联服务器状态机表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)
