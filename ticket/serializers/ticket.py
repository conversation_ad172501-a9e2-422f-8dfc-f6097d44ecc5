"""
<AUTHOR>
@Date    ：2024/11/26
"""
import json
import time
from datetime import timedelta
from application.logger import logger
from django.db import transaction
from django.utils import timezone
from rest_framework import serializers

from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from dvadmin.utils.toolkits import get_shanghai_str_time
from scheduletask.urls import scheduletask_url

from ticket.serializers.task import TaskSerializer
from ticket.models import (
    Ticket, Task,
)

from tenant.models import (
    TenantOpenstackProject,
    TenantOpenstackNetwork,
    TenantOpenstackSecurityGroup,
    TenantAccount,
    TenantOpenstackFlavor,
    TenantOpenstackServer,
    TenantOPServerSoftware,
)

from tenant.serializers.tenant_openstack_server import CreateBaremetalServerView, check_tenant_openstack_server_expired

from scheduletask.models import Work


class TicketImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        instance = super().save(**kwargs)
        return instance

    class Meta:
        model = Ticket
        exclude = ()


class TicketSerializer(ChaosCustomModelSerializer):
    """
    工单-序列化器
    """

    class Meta:
        model = Ticket
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


class TicketCreateSerializer(ChaosCustomModelSerializer):
    """
    工单-序列化器
    """

    def get_account_op_info(self, account_id):
        # 获取账号关联的openstack信息
        error_info = ''
        data = {}
        project = TenantOpenstackProject.objects.filter(
            account_id=account_id,
            is_deleted=False,
        ).first()
        if not project:
            error_info += '账号不存在: {0}\n'.format(account_id)
        else:
            data.update({'project_id': project.project_id})
        network = TenantOpenstackNetwork.objects.filter(
            project_id=project.project_id,
            is_deleted=False,
        ).first()
        if not network:
            error_info += '账号所属的openstack 网络不存在: {0}\n'.format(project.project_id)
        else:
            data.update({'network_id': network.network_id})

        security_group_name = TenantOpenstackSecurityGroup.objects.filter(
            project_id=project.project_id,
            is_deleted=False,
        ).first()
        if not security_group_name:
            # 暂不做安全组预检测
            pass
            # error_info += '账号所属的openstack 安全组不存在: {0}\n'.format(project.project_id)
        else:
            data.update({'security_group_names': security_group_name.name})
        data.update({
            'error_info': error_info,
        })
        return data

    @staticmethod
    def get_account_name(account_id):
        obj = TenantAccount.objects.filter(
            id=account_id,
            is_deleted=False,
        ).first()
        return obj.company if obj else 'unknown-company'

    @staticmethod
    def get_flavor_name(flavor_id):
        obj = TenantOpenstackFlavor.objects.filter(
            flavor_id=flavor_id,
            is_deleted=False,
        ).first()
        return obj.name if obj else 'unknown-flavor'

    def save(self, **kwargs):
        with transaction.atomic():
            source_form_data = self.validated_data.get('source_form_data', {})
            self.validated_data['name'] = (
                f'关于-{self.get_account_name(source_form_data["account_id"])}-'
                f'创建{self.get_flavor_name(source_form_data["flavor_id"])}-订单'
            )
            self.validated_data['current_status'] = '审批中'
            data = super().save(**kwargs)
            # 获取账号关联的openstack信息
            op_info = self.get_account_op_info(source_form_data.get('account_id', ''))
            form_data = op_info
            form_data.update(data.source_form_data)
            # 使用 TaskSerializer 创建任务
            task_data = {
                'ticket': data.id,
                'creator_id': data.creator_id,
                'modifier': data.modifier,
                'dept_belong_id': data.dept_belong_id,
                'create_datetime': data.create_datetime,
                'update_datetime': data.update_datetime,
                'step_index': 1,
                'name': '确认账号和主机信息',
                'form_data': form_data,
                'status': '审批中',
                'assign_to': '2',
            }
            task_serializer = TaskSerializer(data=task_data)
            if task_serializer.is_valid():
                task_serializer.save()
            else:
                raise serializers.ValidationError(task_serializer.errors)
        return data

    class Meta:
        model = Ticket
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


def ticket_create_baremetal_server_task(task_id, form_data):
    response = CreateBaremetalServerView.create_baremetal_server(form_data)
    res_data = response.data
    results = res_data['data']
    error_server_data = []
    success_server_data = []
    error_msg = []
    for result in results:
        if result.get('error_code'):
            error_server_data.append(result.get('error_info'))
            error_msg.append({
                'timestamp': get_shanghai_str_time(),
                'level': 'ERROR',
                'msg': f"调度失败(创建任务未调用成功)，错误原因：{result.get('error_info')}"
            })
        elif result.get('id'):
            success_server_data.append({
                'id': result.get('id'),
                'name': result.get('name')
            })
        else:
            t_msg = '【创建任务失败<未知原因：%s>】' % str(result)
            error_msg.append({
                'timestamp': get_shanghai_str_time(),
                'level': 'ERROR',
                'msg': t_msg
            })
            logger.error(t_msg)
    try:
        with transaction.atomic():

            task = Task.objects.select_for_update().get(
                id=task_id,
                is_deleted=False,
            )
            ticket = Ticket.objects.filter(
                id=task.ticket_id,
                is_deleted=False,
            ).first()
            t_msg = 'TaskId: %s, TicketId : %s' % (task.id, task.ticket_id)
            logger.info(t_msg)
            if len(error_server_data) == int(task.form_data['count']):
                ticket.current_status = '失败'
                ticket.is_finished = 1
                ticket.current_step_index = 2
                ticket.save()
                t_msg = '所有创建主机在调度创建【失败】'
                error_msg.append({
                    'timestamp': get_shanghai_str_time(),
                    'level': 'ERROR',
                    'msg': t_msg,
                })
                logger.error(t_msg)
            # 更新 async_worker_result 字段
            t_form_data = task.form_data
            t_form_data.update({
                'error_info': error_msg
            })
            task.form_data = t_form_data
            task.async_worker_result = {
                'success_server_data': success_server_data,
                'error_server_data': error_server_data,
                'total_server_count': len(results),
            }
            softwares = t_form_data.get('softwares', [])
            if not softwares:
                logger.info(f'【软件列表为空,无需创建软件与主机关联数据<任务id：{task_id}><工单id: {task.ticket.ticket_id}>】')
                ticket.software_install_status = '无安装软件'
                ticket.save()
            else:
                for software in softwares:
                    # 创建安装软件与主机的关系关系
                    [TenantOPServerSoftware.objects.create(**{
                        "op_server_id": i['id'],
                        "software_options": software,
                        "ticket_id": task.ticket.ticket_id,
                        "node": t_form_data["node"]
                    }) for i in success_server_data]
            task.save()
    except Exception as e:
        logger.error('【创建调度任务失败<任务id：%s>】【详细信息 %s】' % (str(task_id), str(e)))
        # 处理任务不存在的情况
    from dvadmin.celery_workers.sync_openstack_resource_worker import wait_for_ticket_finished_worker
    wait_for_ticket_finished_worker.apply_async(args=(task_id,))


def wait_ticket_software_install_finished_task():
    logger.info('-------------【开始】检测工单关联软件安装状态---------------')
    # 获取所有安装未结束的工单
    all_running_task = Ticket.objects.filter(
        software_install_status__in=['软件安装中', '软件未安装'],
        is_deleted=False,
    ).all()

    for ticket in all_running_task:
        # 获取工单中的所有主机
        ticket_server_softwares = TenantOPServerSoftware.objects.filter(
            ticket_id=ticket.ticket_id,
            is_deleted=False,
        ).all()

        # 结束 与结束状态
        if not ticket_server_softwares:
            # 此处为防止为空的情况，all([]) ==> True,防止误判
            logger.warning(f'<id: {ticket.id}><ticket_id: {ticket.ticket_id}><暂无主机安装软件信息>')
            now = timezone.now()
            if now >= ticket.create_datetime + timedelta(hours=8):
                logger.warning(f'【当前工单信息:ticket_id:{ticket.id}】【长时间未启动任务，自动取消检查】')
                ticket.software_install_status = '无安装软件(长时间未检测到运行的主机)'
                ticket.save()
            continue

        had_start_status = False
        for ticket_server_software in ticket_server_softwares:
            if ticket_server_software.retry_count > 0:
                had_start_status = True
                break
        if had_start_status and ticket.software_install_status == '软件未安装':
            # 有一个工单开始后即更新为正在安装中的状态
            ticket.notice_software_install_to_dingtalk(task_type='start')
            ticket.software_install_status = '软件安装中'
            ticket.save()
            continue
        all_finished_status = all([ticket_server_software.is_finished for ticket_server_software in ticket_server_softwares])
        if all_finished_status:
            all_success_status = all([ticket_server_software.is_success for ticket_server_software in ticket_server_softwares])
            any_success_status = any([ticket_server_software.is_success for ticket_server_software in ticket_server_softwares])
            software_install_status = '软件安装失败'
            if all_success_status:
                software_install_status = '软件安装完成'
                ticket.notice_software_install_to_dingtalk(task_type='success_end')
            else:
                if any_success_status:
                    software_install_status = '软件安装部分成功'
                    ticket.notice_software_install_to_dingtalk(task_type='part_success_end')
                else:
                    ticket.notice_software_install_to_dingtalk(task_type='failed_end')
            ticket.software_install_status = software_install_status
            ticket.save()
    logger.info('-------------【结束】检测工单关联软件安装状态---------------')
