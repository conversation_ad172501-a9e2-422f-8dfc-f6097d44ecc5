"""
<AUTHOR>
@Date    ：2024/11/26
"""

import time
from django.utils import timezone
from datetime import datetime, timedelta
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from ticket.models import (
    Task, Ticket
)
from tenant.serializers.tenant_openstack_server import CreateBaremetalServerView
from tenant.models import TenantOpenstackServer
from tenant.models import TenantHisecEnginePublicIP
from tenant.views.tenant_hisec_engine_nat_server_policy import TenantHisecEngineNatServerPolicy, \
    TenantHisecEngineNatServerPolicyViewSet
from application.logger import logger


class TaskImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = Task
        exclude = ()


class TaskSerializer(ChaosCustomModelSerializer):
    """
    工单-序列化器
    """

    class Meta:
        model = Task
        fields = "__all__"
        read_only_fields = ["id", "is_deleted", "seq"]


def sync_server_to_openstack(form_data):
    response = CreateBaremetalServerView.create_baremetal_server(form_data)
    res_data = response.data
    if res_data['code'] == 2000:
        return True
    else:
        return False


def wait_for_ticket_finished(task_id):
    """
    等待工单执行完成
    :param task_id: 任务id
    :return:
    """
    interval_time = 30  # s/秒
    occurrences = 120
    # 共等待1小时
    task = Task.objects.filter(id=task_id, is_deleted=False).first()
    if task is None:
        return False
    count = task.form_data['count']
    for i in range(occurrences):
        time.sleep(interval_time)
        if i == 0:
            time.sleep(60)
        op_servers = TenantOpenstackServer.objects.filter(
            belong_ticket_id=task.ticket_id,
            is_deleted=False,
        )
        all_op_servers = op_servers.all()
        is_finished = True  # 假设所有服务器都处于预期状态
        current_step_index = 2
        for op_server in all_op_servers:
            # 无需判断打印日志
            logger.info(
                f'【当前工单:{task.ticket_id}】'
                f'【当前主机：{op_server.instance_id}】'
                f'【创建状态：{op_server.status}】'
            )

        for op_server in all_op_servers:
            if op_server.status not in ['ACTIVE', 'ERROR']:
                is_finished = False
                break  # 遇到非 'ACTIVE' 或 'ERROR' 状态的服务器，停止检查

        if not is_finished:
                logger.info(f'<工单:{task.ticket_id}>主机创建中......')
                continue

        all_active = all(op_server.status == 'ACTIVE' for op_server in all_op_servers)
        all_error = all(op_server.status == 'ERROR' for op_server in all_op_servers)
        if op_servers.count() != int(count):
            # 有一部分调度失败的情况， 打印调度情况
            logger.warning(
                f'<工单中所需与创建任务列表不一致>,'
                f'【工单所需数量：{count}】'
                f'【主机列表数量: {op_servers.count()}】'
            )
        if not all_op_servers:
            # 若所有主机均调度失败，
            logger.error(
                f'【当前工单:{task.ticket_id}】'
                f'【当前主机列表为空】'
            )
            all_active = False
            all_error = True
        if all_error:
            current_status = '失败'
        elif not all_active or not all_error:
            current_status = '部分失败'
        elif all_active:
            current_status = '分配公网'
            current_step_index = 3
        else:
            current_status = '失败'
        # 更新对应的工单状态
        ticket = Ticket.objects.filter(
            id=task.ticket_id,
            is_deleted=False,
        ).first()
        logger.info(f'【当前工单信息:task_id:{task_id},ticket_id:{task.ticket_id}, 工单当前状态:{current_status}】')
        if ticket:
            if ticket.current_status not in ['分配公网', '失败', '部分失败', '订单完成', '驳回', '已交付']:
                ticket.current_status = current_status
            ticket.is_finished = is_finished
            ticket.current_step_index = current_step_index
            ticket.save()
            # 触发分配公网任务
            from dvadmin.celery_workers.sync_openstack_resource_worker import ticket_assign_public_ip_worker
            ticket_assign_public_ip_worker.apply_async(args=(ticket.id,))
            return True
        else:
            logger.error(f'【当前工单信息:task_id:{task_id},ticket_id:{task.ticket_id}】【工单不存在】')
            return False
    logger.error(f'【当前工单信息:task_id:{task_id},ticket_id:{task.ticket_id}】【等待超时，重新创建等待任务】')
    # 最大等待时间为2小时
    now = timezone.now()
    if now <= task.create_datetime + timedelta(hours=2):
        logger.error(f'【当前工单信息:task_id:{task_id},ticket_id:{task.ticket_id}】【等待超时，重新创建等待任务】')
        # 处理任务不存在的情况
        from dvadmin.celery_workers.sync_openstack_resource_worker import wait_for_ticket_finished_worker
        wait_for_ticket_finished_worker.apply_async(args=(task_id,))
    return False


def ticket_assign_public_ip(ticket_id):
    logger.info('-----------------------【开始工单关联主机分配公网任务】--------------------------')
    ticket = Ticket.objects.filter(
        id=ticket_id,
        is_deleted=False,
    ).first()
    if not ticket:
        logger.error(f'【当前工单信息:ticket_id:{ticket_id}】【工单不存在】')
        return False
    # 获取审批任务后的工单信息
    task = Task.objects.filter(
        ticket_id=ticket_id,
        step_index=2,
        is_deleted=False,
    ).first()
    if not task:
        logger.error(f'【当前工单信息:ticket_id:{ticket_id}】【工单审批任务不存在】')
        return False
    is_need_extra_public_ip = task.form_data.get('is_need_extra_public_ip', False)
    if not is_need_extra_public_ip:
        logger.info(f'【当前工单信息:ticket_id:{ticket_id}】【无需分配公网】')
        # 分配公网任务正常结束后更新工单状态到 ·订单完成·
        if ticket.current_status in ['失败', '部分失败']:
            pass
        else:
            ticket.current_status = '订单完成'
        ticket.current_step_index = 4
        ticket.save()
        return False

    # 获取工单中已创建的实例节点信息

    node = task.form_data["node"]
    op_servers = TenantOpenstackServer.objects.filter(
        belong_ticket_id=ticket_id,
        node=node,
        status='ACTIVE',
        is_deleted=False,
    ).all()
    if not op_servers:
        logger.error(f'【当前工单信息:ticket_id:{ticket_id}】【工单审批任务中无主机】')
        return False

    # 获取可分配的公网信息
    available_assign_public_ips = TenantHisecEnginePublicIP.objects.filter(
        is_deleted=False,
        node=node,
        used=False,
    ).order_by('public_ip').all()
    if len(available_assign_public_ips) < len(op_servers):
        logger.error(f'【当前工单信息:ticket_id:{ticket_id}】【公网资源不足<工单所需公网IP数:{len(op_servers)}> 大于 <公网资源数:{len(available_assign_public_ips)}>】, 请人工指定分配。')
        ticket.notice_assign_public_ip_to_dingtalk(task_status='failed_end')
        return False

    # 分配后剩余的IP资源数量
    available_assign_public_ip_amount = len(available_assign_public_ips) - len(op_servers)
    if available_assign_public_ip_amount <= 5:
        # 公网资源不足，通知到管理员
        # TODO 通知到群内
        logger.warning(f'【公网IP资源未来可能不足】，当前剩余IP资源数量为：{available_assign_public_ip_amount}')
    # 执行绑定任务
    all_op_public_res_code = []

    for index, op_server in enumerate(op_servers):
        # 构建分配公网及策略请求
        public_ip = available_assign_public_ips[index]
        body_data = {
            'public_ip': public_ip.public_ip,
            'op_server': op_server.instance_id,
            'node': op_server.node,
        }
        res = TenantHisecEngineNatServerPolicyViewSet.static_create_hisec_nat_server_policy(
            body_data=body_data
        )
        res_data = res.data
        if res_data["code"] == 2000:
            is_success = True
            logger.info(f'【自动创建NatServerPolicy响应】<RequestData: {body_data}><Response: {res_data}>')
        else:
            is_success = False
            logger.error(f'【自动创建NatServerPolicy响应】<RequestData: {body_data}><Response: {res_data}>')
        all_op_public_res_code.append(is_success)
    if not all_op_public_res_code:
        logger.info(f'无需推送通知')
        return True
    elif all(all_op_public_res_code):
        ticket.notice_assign_public_ip_to_dingtalk(task_status='success_end')
        # 分配公网任务正常结束后更新工单状态到 ·订单完成·
        ticket.current_step_index = 4
        ticket.current_status = '订单完成'
        ticket.save()
    elif any(all_op_public_res_code):
        ticket.notice_assign_public_ip_to_dingtalk(task_status='part_success_end')
    else:
        ticket.notice_assign_public_ip_to_dingtalk(task_status='failed_end')
    logger.info('-----------------------【结束工单关联主机分配公网任务】--------------------------')
    return True
