import re
import json

from django.db import models, transaction
from django.utils import timezone

from dvadmin.utils.chaos_models import ChaosCoreModel
from dvadmin.celery_workers.sync_notice_ticket_worker import sync_send_ticket_notice_worker

# Create your models here.

TABLE_PREFIX = 'ticket_'


class Ticket(ChaosCoreModel):
    """
    工单表
    """
    name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='工单名称', help_text='工单名称', db_comment='工单名称'
    )
    current_step_index = models.IntegerField(
        default=1, blank=True, null=True,
        verbose_name='步骤序号', help_text='步骤序号', db_comment='步骤序号'
    )
    current_status = models.CharField(
        default='已创建', blank=True, null=True, max_length=63,
        verbose_name='当前状态', help_text='当前状态', db_comment='当前状态'
    )
    msg = models.CharField(
        default='', blank=True, null=True, max_length=255,
        verbose_name='备注', help_text='备注', db_comment='备注'
    )
    is_finished = models.BooleanField(
        default=False, blank=True, null=True,
        verbose_name='是否已结束', help_text='是否已结束', db_comment='是否已结束'
    )
    ticket_id = models.CharField(
        max_length=63, blank=True, null=True, unique=True,
        verbose_name='工单ID', help_text='工单ID', db_comment='工单ID'
    )
    assign_to = models.IntegerField(
        default=1, blank=True, null=True,
        verbose_name='指派人', help_text='指派人', db_comment='指派人'
    )
    source_form_data = models.JSONField(
        default=dict, null=True, blank=True,
        verbose_name='原始表单数据', help_text='原始表单数据', db_comment='原始表单数据'
    )
    account_id = models.CharField(
        max_length=63, default='', blank=True, null=True,
        verbose_name='账号ID', help_text='账号ID', db_comment='账号ID'
    )
    report_url = models.CharField(
        max_length=511, default='', blank=True, null=True,
        verbose_name='报告URL', help_text='报告URL', db_comment='报告URL'
    )
    software_install_status = models.CharField(
        max_length=63, default='软件未安装', blank=True, null=True,
        verbose_name='软件安装状态', help_text='软件安装状态', db_comment='软件安装状态'
    )

    @classmethod
    def get_ticket_tasks(cls, ticket_id, is_step_index=False):
        """
        获取工单的任务
        """
        ticket = cls.objects.filter(
            id=ticket_id,
            is_deleted=False,
        ).first()
        if not ticket:
            return {
                "ticket": {},
                "tasks": [],
            }
        is_step_index = bool(int(is_step_index))
        if is_step_index:
            tasks = Task.objects.filter(
                ticket_id=ticket_id,
                is_deleted=False,
                step_index=ticket.current_step_index,
            ).order_by('-step_index').all()
        else:
            tasks = Task.objects.filter(
                ticket_id=ticket_id,
                is_deleted=False,
            ).order_by('-step_index').all()
        tasks = [task.to_dict for task in tasks]
        return {
            "ticket": ticket.to_dict,
            "tasks": tasks,
        }

    def generate_ticket_id(self):
        today = timezone.now().date()
        prefix = today.strftime('T%Y%m%d')

        # 查询当天最新的工单 ID
        latest_ticket = Ticket.objects.filter(
            ticket_id__startswith=prefix
        ).order_by('-ticket_id').first()

        if latest_ticket:
            # 提取流水号并自增
            match = re.match(rf'^{prefix}(\d{{4}})$', latest_ticket.ticket_id)
            if match:
                last_number = int(match.group(1))
                new_number = f'{last_number + 1:04d}'
            else:
                raise ValueError("Invalid ticket_id format")
        else:
            # 如果当天没有工单，从 0001 开始
            new_number = '0001'

        # 生成新的工单 ID
        self.ticket_id = f"{prefix}{new_number}"

    def save(self, *args, **kwargs):
        # 审批人
        TICKET_STEP_APPROVAL_ASSIGN_TO = 2
        # 修复人
        TICKET_STEP_OPERATOR_ASSIGN_TO = 12
        # 分配公网人
        TICKET_STEP_NETWORK_ASSIGN_TO = 7
        if not self.ticket_id:
            with transaction.atomic():
                self.generate_ticket_id()
        notice_title = ''
        if self.current_status == '审批中':
            notice_title = '烦请及时查看并处理【审批】阶段订单'
            self.assign_to = TICKET_STEP_APPROVAL_ASSIGN_TO
        elif self.current_status == '执行中':
            notice_title = '订单任务正在【执行中】'
            self.assign_to = TICKET_STEP_OPERATOR_ASSIGN_TO
        elif self.current_status == '驳回':
            notice_title = '烦请及时查看并处理【驳回】阶段订单'
            self.assign_to = int(self.creator.id)
        elif self.current_status == '失败' or self.current_status == '部分失败':
            notice_title = '烦请及时查看并处理【自动创建失败】阶段订单'
            self.assign_to = TICKET_STEP_OPERATOR_ASSIGN_TO
        # elif self.current_status == '分配公网':
        #     notice_title = '烦请及时查看并处理【分配公网】阶段订单'
        #     self.assign_to = TICKET_STEP_NETWORK_ASSIGN_TO
        elif self.current_status == '订单完成':
            notice_title = '【订单完成】，请等待交付报告'
            self.assign_to = int(self.creator.id)
        # elif self.report_url:
        #     notice_title = '【交付报告】已生成，可登录平台下载'
        #     self.assign_to = int(self.creator.id)
        super().save(*args, **kwargs)
        try:
            if notice_title:
                sync_send_ticket_notice_worker.apply_async(args=(
                    notice_title,
                    self._to_dict(is_continue_public_fields=False)))
        except Exception as e:
            print(str(e))

    def notice_software_install_to_dingtalk(self, task_type='start', retry_count=0):
        if task_type == 'start':
            notice_title = '【软件安装】任务已开始，请耐心等待'
            extr_status = '软件安装开始'
        elif task_type == 'success_end':
            notice_title = '【软件安装】任务已成功，请查看任务详情'
            extr_status = '软件安装成功'
        elif task_type == 'failed_end':
            notice_title = '【软件安装】任务已失败，请查看任务详情'
            extr_status = '软件安装失败'
        else:
            notice_title = '【软件安装】任务已部分成功，请查看任务详情'
            extr_status = '软件安装部分成功'
        try:
            sync_send_ticket_notice_worker.apply_async(
                args=(
                    notice_title,
                    self._to_dict(is_continue_public_fields=False),
                    extr_status,
                )
            )
        except Exception as e:
            print(str(e))

    def notice_assign_public_ip_to_dingtalk(self, task_status='failed_end'):
        if task_status == 'part_success_end':
            notice_title = '【分配公网】任务部分失败'
            extr_status = '分配公网'
        elif task_status == 'success_end':
            notice_title = '【分配公网】任务已成功'
            extr_status = '分配公网'
        elif task_status == 'failed_end':
            notice_title = '【分配公网】任务已失败'
            extr_status = '分配公网'
        else:
            notice_title = '【分配公网】任务已失败'
            extr_status = '分配公网'
        try:
            sync_send_ticket_notice_worker.apply_async(
                args=(
                    notice_title,
                    self._to_dict(is_continue_public_fields=False),
                    extr_status,
                )
            )
        except Exception as e:
            print(str(e))

    class Meta:
        db_table = TABLE_PREFIX + "tickets"
        verbose_name = "工单表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class Task(ChaosCoreModel):
    """
    工单表
    """
    name = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='任务名称', help_text='任务名称', db_comment='任务名称'
    )
    ticket = models.ForeignKey(
        to="Ticket",
        db_constraint=False,
        on_delete=models.CASCADE,
        verbose_name="关联工单",
        help_text="关联工单",
        to_field='id',
    )
    form_data = models.JSONField(
        blank=True, null=True,
        verbose_name='任务数据', help_text='任务数据', db_comment='任务数据'
    )
    step_index = models.IntegerField(
        default=1, blank=True, null=True,
        verbose_name='步骤序号', help_text='步骤序号', db_comment='步骤序号'
    )
    sub_step_index = models.IntegerField(
        default=1, blank=True, null=True,
        verbose_name='子步骤序号', help_text='子步骤序号', db_comment='子步骤序号'
    )
    check_task_name = models.CharField(
        default='', blank=True, null=True, max_length=255,
        verbose_name='检查任务名称', help_text='检查任务名称', db_comment='检查任务名称'
    )
    async_worker = models.CharField(
        max_length=255, blank=True, null=True,
        verbose_name='异步任务', help_text='异步任务', db_comment='异步任务'
    )
    async_worker_result = models.TextField(
        blank=True, null=True,
        verbose_name='异步任务结果', help_text='异步任务结果', db_comment='异步任务结果'
    )
    status = models.CharField(
        max_length=63, blank=True, null=True,
        verbose_name='状态', help_text='状态', db_comment='状态'
    )
    comment = models.TextField(
        blank=True, null=True,
        verbose_name='备注', help_text='备注', db_comment='备注'
    )
    assign_to = models.TextField(
        blank=True, null=True,
        verbose_name='指派人', help_text='指派人', db_comment='指派人'
    )

    class Meta:
        db_table = TABLE_PREFIX + "tasks"
        verbose_name = "工单任务表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


