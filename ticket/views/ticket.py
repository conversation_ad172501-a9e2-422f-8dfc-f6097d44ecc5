"""
<AUTHOR>
@Date    ：2024/11/26
"""

from rest_framework.decorators import action
from dvadmin.utils.json_response import DetailResponse, ErrorResponse
from rest_framework.permissions import IsAuthenticated
from rest_framework import serializers

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import SuccessResponse, ErrorResponse, DetailResponse
from dvadmin.system.views.file_list import FileSerializer
from dvadmin.utils.chaos_file_toolkits import upload_to_file_system

from ticket.serializers.task import TaskSerializer
from ticket.models import Ticket, Task
from ticket.serializers.ticket import (
    TicketSerializer,
    TicketImportSerializer,
    TicketCreateSerializer,
)

from tenant.models import TenantOpenstackServer

from dvadmin.utils.my_py_docx.write_baremetal_server_eport import BaremetalServerReport


class TicketViewSet(ChaosCustomModelViewSet):
    """
    工单接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Ticket.objects.order_by('-create_datetime')
    serializer_class = TicketSerializer
    create_serializer_class = TicketCreateSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = TicketImportSerializer
    import_field_dict = {
        "name": "vlan名称(非必填项)",
        "description": "描述(非必填项)",
        "vlan_id": "VlanId(必填项)",
        "phy_vlan_type": "物理vlan类型(默认为规定的vlan)(必填项)",
        "subnet": "子网网段(必填项)",
        "available_ip_pool": "可用IP池(必填项)",
        "gateway_ip": "网关IP(必填项)",
        "node": "Openstack节点(默认暂定为金华)(非必填项)"
    }
    permission_classes = []

    @action(methods=['get'], detail=False, permission_classes=[IsAuthenticated])
    def get_current_ticket_tasks(self, request):
        """
        创建 openstack 中对应的物理网络
        :param request:
        :return:
        """
        request_data = request.query_params
        ticket_id = request_data.get('ticket_id')
        if not ticket_id:
            return ErrorResponse(data={}, msg='ticket_id is required.')
        is_step_index = request_data.get('is_step_index', False)
        data = Ticket.get_ticket_tasks(ticket_id=ticket_id, is_step_index=is_step_index)
        return DetailResponse(msg="异步任务调度成功", data=data)

    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def update_ticket_status(self, request):
        """
        更新 工单状态
        :param request:
        :return:
        """
        request_data = request.data
        ticket_id = request_data.get('ticket_id')
        if not ticket_id:
            return ErrorResponse(data={}, msg='ticket_id is required.')
        try:
            # 验证 current_step_index 是否为整数
            current_step_index = int(request_data.get('current_step_index', 0))
        except ValueError:
            return ErrorResponse(data={}, msg='current_step_index must be an integer.')

            # 验证 current_status 是否存在
        current_status = request_data.get('current_status')
        if not current_status:
            return ErrorResponse(data={}, msg='current_status is required.')

        ticket = Ticket.objects.filter(
            id=ticket_id,
            is_deleted=False,
        ).first()
        if not ticket:
            return ErrorResponse(data={}, msg='工单不存在.')
        # 更新工单状态
        ticket.current_step_index = current_step_index
        ticket.current_status = current_status
        ticket.save()
        return DetailResponse(msg="更新成功", data=ticket.to_dict)

    @staticmethod
    def create_ticket_and_task(data):
        """
        新建工单和任务
        :param data: 请求dict参数
        :return:
        """
        ticket_id = data.get('ticket_id')
        if not ticket_id:
            return ErrorResponse(data={}, msg='ticket_id is required.')
        current_status = data.get('current_status')
        msg = data.get('msg')
        form_data = data.get('form_data')
        form_data.update({
            'belong_ticket_id': ticket_id,
        })
        is_finished = False
        if current_status == '审批通过':
            current_status = '执行中'
        else:
            current_status = '驳回'
            is_finished = True
        ticket = Ticket.objects.select_for_update().get(id=ticket_id)
        ticket.current_status = current_status
        ticket.msg = msg
        ticket.is_finished = is_finished
        ticket.current_step_index = 2
        ticket.save()
        if not is_finished:
            # 使用 TaskSerializer 创建任务
            task_data = {
                'ticket': ticket.id,
                'step_index': 2,
            }
            task_data.update({
                'creator_id': ticket.creator_id,
                'modifier': ticket.modifier,
                'dept_belong_id': ticket.dept_belong_id,
                'create_datetime': ticket.create_datetime,
                'update_datetime': ticket.update_datetime,
                'name': '执行创建任务',
                'form_data': form_data,
                'status': current_status,
                'assign_to': '3',
            })
            task_serializer = TaskSerializer(data=task_data)
            if task_serializer.is_valid():
                task_serializer.save()
                from dvadmin.celery_workers.sync_openstack_resource_worker import (
                    ticket_create_baremetal_server_task_worker
                )
                ticket_create_baremetal_server_task_worker.apply_async(args=(task_serializer.data['id'], form_data))
            else:
                raise serializers.ValidationError(task_serializer.errors)

    @action(methods=['post'], detail=False, permission_classes=[IsAuthenticated])
    def approval_ticket_task(self, request):
        """
        创建 openstack 中对应的物理网络
        :param request:
        :return:
        """
        request_data = request.data
        ticket_id = request_data.get('ticket_id')
        self.create_ticket_and_task(request_data)
        data = Ticket.get_ticket_tasks(ticket_id=ticket_id)
        return DetailResponse(msg="审批成功", data=data)

    @action(methods=['post'], detail=True, permission_classes=[IsAuthenticated])
    def create_ticket_report(self, request, id):
        """
        创建工单报告
        :param request:
        :param id: 工单原始ID, 如 ticket-xxxxxxxxxx(非显示的T2024122300001此类ID)
        :return:
        """

        ticket = Ticket.objects.filter(
            id=id,
            is_deleted=False,
        ).first()
        if not ticket:
            return ErrorResponse(data={}, msg='工单不存在.')
        servers = TenantOpenstackServer.objects.filter(
            belong_ticket_id=ticket.id,
            # TODO 已删除的主机也会被添加到报告中
            # is_deleted=False,
        ).all()
        server_info = [server._to_dict() for server in servers]

        obj = BaremetalServerReport(server_info, source_id=ticket.id, ticket_id=ticket.ticket_id)
        file_path = obj.write()
        res = upload_to_file_system(file_path, request)
        # 整合成前端可直接访问的地址 /api 为前端使用的base_url
        ticket.report_url = f"/api/{res.get('file_url', '') or res.get('url', '')}"
        ticket.save()
        msg = '生成报告成功'
        if not server_info:
            msg = '生成报告成功，但尚未未发现裸金属主机!!!'
        return DetailResponse(msg=msg, data={'ticket_id': ticket.ticket_id})







