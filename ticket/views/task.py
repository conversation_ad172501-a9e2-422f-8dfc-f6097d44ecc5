"""
<AUTHOR>
@Date    ：2024/11/26
"""

from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.json_response import SuccessResponse, ErrorResponse, DetailResponse


from ticket.models import Task
from ticket.serializers.task import (
    TaskSerializer,
    TaskImportSerializer
)


class TaskViewSet(ChaosCustomModelViewSet):
    """
    任务接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Task.objects.order_by('-create_datetime')
    serializer_class = TaskSerializer

    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'account_nick_name',
    }
    # 导入
    import_serializer_class = TaskImportSerializer
    import_field_dict = {
        "name": "vlan名称(非必填项)",
        "description": "描述(非必填项)",
        "vlan_id": "VlanId(必填项)",
        "phy_vlan_type": "物理vlan类型(默认为规定的vlan)(必填项)",
        "subnet": "子网网段(必填项)",
        "available_ip_pool": "可用IP池(必填项)",
        "gateway_ip": "网关IP(必填项)",
        "node": "Openstack节点(默认暂定为金华)(非必填项)"
    }
    permission_classes = []







