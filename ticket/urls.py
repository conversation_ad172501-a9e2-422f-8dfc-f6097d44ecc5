"""
<AUTHOR>
@Date    ：2024/11/26
简易工单路由
"""

from django.urls import path
from rest_framework import routers


from ticket.views.ticket import TicketViewSet
from ticket.views.task import TaskViewSet


# 特殊自定义 API

resource_url = routers.SimpleRouter()
resource_url.register(r'ticket', TicketViewSet)
resource_url.register(r'task', TaskViewSet)


app_name = 'ticket'


urlpatterns = [
    # path('create_baremetal_server/', CreateBaremetalServerView.as_view(), name='create_baremetal_server'),
    # path('resource/machine_room/', MachineRoomViewSet.as_view('get', 'list')),
    # path('resource/machine_room/<id>/', MachineRoomViewSet.as_view()),
]
urlpatterns += resource_url.urls
