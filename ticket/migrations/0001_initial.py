# Generated by Django 5.0.6 on 2024-12-06 14:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Ticket',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(blank=True, db_comment='工单名称', help_text='工单名称', max_length=255, null=True, verbose_name='工单名称')),
                ('current_step_index', models.IntegerField(blank=True, db_comment='步骤序号', default=1, help_text='步骤序号', null=True, verbose_name='步骤序号')),
                ('current_status', models.CharField(blank=True, db_comment='当前状态', default='已创建', help_text='当前状态', max_length=63, null=True, verbose_name='当前状态')),
                ('msg', models.CharField(blank=True, db_comment='备注', default='', help_text='备注', max_length=255, null=True, verbose_name='备注')),
                ('is_finished', models.BooleanField(blank=True, db_comment='是否已结束', default=False, help_text='是否已结束', null=True, verbose_name='是否已结束')),
                ('ticket_id', models.CharField(blank=True, db_comment='工单ID', help_text='工单ID', max_length=63, null=True, unique=True, verbose_name='工单ID')),
                ('source_form_data', models.JSONField(blank=True, db_comment='原始表单数据', default=dict, help_text='原始表单数据', null=True, verbose_name='原始表单数据')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '工单表',
                'verbose_name_plural': '工单表',
                'db_table': 'ticket_tickets',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(blank=True, db_comment='任务名称', help_text='任务名称', max_length=255, null=True, verbose_name='任务名称')),
                ('form_data', models.JSONField(blank=True, db_comment='任务数据', help_text='任务数据', null=True, verbose_name='任务数据')),
                ('step_index', models.IntegerField(blank=True, db_comment='步骤序号', default=1, help_text='步骤序号', null=True, verbose_name='步骤序号')),
                ('sub_step_index', models.IntegerField(blank=True, db_comment='子步骤序号', default=1, help_text='子步骤序号', null=True, verbose_name='子步骤序号')),
                ('check_task_name', models.CharField(blank=True, db_comment='检查任务名称', default='', help_text='检查任务名称', max_length=255, null=True, verbose_name='检查任务名称')),
                ('async_worker', models.CharField(blank=True, db_comment='异步任务', help_text='异步任务', max_length=255, null=True, verbose_name='异步任务')),
                ('async_worker_result', models.TextField(blank=True, db_comment='异步任务结果', help_text='异步任务结果', null=True, verbose_name='异步任务结果')),
                ('status', models.CharField(blank=True, db_comment='状态', help_text='状态', max_length=63, null=True, verbose_name='状态')),
                ('comment', models.TextField(blank=True, db_comment='备注', help_text='备注', null=True, verbose_name='备注')),
                ('assign_to', models.TextField(blank=True, db_comment='指派人', help_text='指派人', null=True, verbose_name='指派人')),
                ('creator', models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('ticket', models.ForeignKey(db_constraint=False, help_text='关联工单', on_delete=django.db.models.deletion.CASCADE, to='ticket.ticket', to_field='id', verbose_name='关联工单')),
            ],
            options={
                'verbose_name': '工单任务表',
                'verbose_name_plural': '工单任务表',
                'db_table': 'ticket_tasks',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
