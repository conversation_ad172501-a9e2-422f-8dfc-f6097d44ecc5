import React, { useEffect, useRef, useState } from 'react';
import { Graph, register, ExtensionCategory } from '@antv/g6';
import { ReactNode } from '@antv/g6-extension-react';
import { 
  Flex, 
  Typography, 
  Card, 
  Tag, 
  Space, 
  Tooltip,
  Spin,
  Alert,
  Button
} from 'antd';
import { 
  HddOutlined, 
  ApiOutlined, 
  ClusterOutlined,
  HomeOutlined,
  ReloadOutlined,
  FullscreenOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;

// 颜色配置
const COLOR_MAP = {
  'switch': '#1890ff',
  'physical-port': '#52c41a', 
  'trunk-port': '#fa8c16',
  'machine-room': '#722ed1'
};

const STATUS_COLOR_MAP = {
  'active': '#52c41a',
  'inactive': '#ff4d4f',
  'unknown': '#d9d9d9'
};

// 自定义节点组件
const NetworkNode = ({ data }) => {
  const { text, type, status, speed, state, ip, vendor, member_count } = data.data;
  
  const isActive = status === 'active';
  const color = COLOR_MAP[type] || '#d9d9d9';
  const statusColor = STATUS_COLOR_MAP[status] || '#d9d9d9';
  
  const getIcon = () => {
    switch (type) {
      case 'switch':
        return <ClusterOutlined style={{ fontSize: 20, color: '#fff' }} />;
      case 'physical-port':
        return <ApiOutlined style={{ fontSize: 16, color: '#fff' }} />;
      case 'trunk-port':
        return <HddOutlined style={{ fontSize: 16, color: '#fff' }} />;
      case 'machine-room':
        return <HomeOutlined style={{ fontSize: 20, color: '#fff' }} />;
      default:
        return null;
    }
  };
  
  const getSize = () => {
    switch (type) {
      case 'switch':
        return { width: 180, height: 80 };
      case 'machine-room':
        return { width: 200, height: 90 };
      case 'trunk-port':
        return { width: 140, height: 60 };
      default:
        return { width: 120, height: 50 };
    }
  };
  
  const size = getSize();
  
  const containerStyle = {
    width: size.width,
    height: size.height,
    background: `linear-gradient(135deg, ${color}, ${color}dd)`,
    border: `2px solid ${statusColor}`,
    borderRadius: type === 'switch' ? 12 : 8,
    cursor: 'pointer',
    boxShadow: isActive ? `0 4px 12px ${color}40` : '0 2px 8px rgba(0,0,0,0.1)',
    transition: 'all 0.3s ease'
  };
  
  return (
    <Flex style={containerStyle} align="center" justify="center">
      <Flex vertical align="center" justify="center" style={{ padding: '8px 12px' }}>
        {getIcon()}
        <Text style={{ 
          color: '#fff', 
          fontWeight: 600, 
          fontSize: type === 'switch' ? 14 : 12,
          textAlign: 'center',
          marginTop: 4,
          lineHeight: 1.2
        }}>
          {text}
        </Text>
        {type === 'switch' && ip && (
          <Text style={{ color: '#fff', fontSize: 10, opacity: 0.8 }}>
            {ip}
          </Text>
        )}
        {type === 'physical-port' && speed && (
          <Text style={{ color: '#fff', fontSize: 10, opacity: 0.8 }}>
            {speed}
          </Text>
        )}
        {type === 'trunk-port' && member_count && (
          <Text style={{ color: '#fff', fontSize: 10, opacity: 0.8 }}>
            {member_count} members
          </Text>
        )}
      </Flex>
    </Flex>
  );
};

// 注册自定义节点
register(ExtensionCategory.NODE, 'network-node', ReactNode);

const SwitchNetworkTopology = ({ switchId, machineRoomId, onNodeClick }) => {
  const containerRef = useRef();
  const graphRef = useRef();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);
  
  // 初始化图形
  const initGraph = () => {
    if (!containerRef.current) return;
    
    const graph = new Graph({
      container: containerRef.current,
      autoFit: 'view',
      padding: [20, 20, 20, 20],
      node: {
        type: 'network-node',
        style: (d) => ({
          component: <NetworkNode data={d} />,
          size: getNodeSize(d.data.type),
          ports: [
            { placement: 'top' }, 
            { placement: 'bottom' }, 
            { placement: 'left' }, 
            { placement: 'right' }
          ]
        }),
        state: {
          hover: {
            halo: true,
            haloStroke: '#1890ff',
            haloStrokeWidth: 3
          },
          selected: {
            halo: true,
            haloStroke: '#fa8c16',
            haloStrokeWidth: 4
          }
        }
      },
      edge: {
        type: 'polyline',
        style: (d) => ({
          lineWidth: getEdgeWidth(d.data.type),
          stroke: getEdgeColor(d.data.type, d.data.status),
          endArrow: true,
          endArrowSize: 8,
          labelText: d.data.text,
          labelFill: '#666',
          labelFontSize: 10,
          labelFontWeight: 500,
          labelBackground: true,
          labelBackgroundFill: '#fff',
          labelBackgroundOpacity: 0.9,
          labelBackgroundStroke: '#d9d9d9',
          labelBackgroundStrokeWidth: 1,
          labelPadding: [2, 6],
          labelBackgroundRadius: 4,
          router: {
            type: 'orth',
            offset: 20
          }
        }),
        state: {
          hover: {
            lineWidth: 4,
            halo: true,
            haloStroke: '#1890ff',
            haloStrokeWidth: 2
          }
        }
      },
      layout: {
        type: 'force',
        preventOverlap: true,
        nodeSize: 100,
        nodeSpacing: 150,
        linkDistance: 200,
        nodeStrength: -300,
        edgeStrength: 0.2,
        collideStrength: 0.8
      },
      behaviors: [
        'zoom-canvas', 
        'drag-canvas', 
        'drag-node',
        'hover-activate',
        'click-select'
      ]
    });
    
    // 节点点击事件
    graph.on('node:click', (event) => {
      const { data } = event.target;
      if (onNodeClick) {
        onNodeClick(data);
      }
    });
    
    graphRef.current = graph;
    return graph;
  };
  
  // 获取节点大小
  const getNodeSize = (type) => {
    switch (type) {
      case 'switch':
        return [180, 80];
      case 'machine-room':
        return [200, 90];
      case 'trunk-port':
        return [140, 60];
      default:
        return [120, 50];
    }
  };
  
  // 获取边的宽度
  const getEdgeWidth = (type) => {
    switch (type) {
      case 'trunk-connection':
        return 4;
      case 'trunk-member':
        return 2;
      case 'room-connection':
        return 3;
      default:
        return 2;
    }
  };
  
  // 获取边的颜色
  const getEdgeColor = (type, status) => {
    const baseColors = {
      'physical-connection': '#52c41a',
      'trunk-connection': '#fa8c16', 
      'trunk-member': '#1890ff',
      'room-connection': '#722ed1'
    };
    
    const color = baseColors[type] || '#d9d9d9';
    return status === 'active' ? color : '#ff4d4f';
  };
  
  // 加载数据
  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      let url;
      if (switchId) {
        url = `/api/virtual-switches/${switchId}/get_g6_data/`;
      } else if (machineRoomId) {
        url = `/api/virtual-switches/get_room_g6_data/?machine_room_id=${machineRoomId}`;
      } else {
        throw new Error('需要提供 switchId 或 machineRoomId');
      }
      
      const response = await fetch(url);
      const result = await response.json();
      
      if (result.code === 2000) {
        setData(result.data);
        
        if (graphRef.current) {
          graphRef.current.data(result.data);
          graphRef.current.render();
          graphRef.current.fitView();
        }
      } else {
        throw new Error(result.msg || '获取数据失败');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  // 重新布局
  const relayout = () => {
    if (graphRef.current) {
      graphRef.current.layout();
    }
  };
  
  // 适应视图
  const fitView = () => {
    if (graphRef.current) {
      graphRef.current.fitView();
    }
  };
  
  useEffect(() => {
    const graph = initGraph();
    loadData();
    
    return () => {
      if (graph) {
        graph.destroy();
      }
    };
  }, [switchId, machineRoomId]);
  
  return (
    <Card 
      title={
        <Flex justify="space-between" align="center">
          <Title level={4} style={{ margin: 0 }}>
            网络拓扑图
          </Title>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadData}
              loading={loading}
              size="small"
            >
              刷新
            </Button>
            <Button 
              icon={<FullscreenOutlined />} 
              onClick={fitView}
              size="small"
            >
              适应视图
            </Button>
          </Space>
        </Flex>
      }
      style={{ height: '100%' }}
      bodyStyle={{ height: 'calc(100% - 57px)', padding: 0 }}
    >
      {error && (
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          style={{ margin: 16 }}
          action={
            <Button size="small" onClick={loadData}>
              重试
            </Button>
          }
        />
      )}
      
      <Spin spinning={loading} tip="加载拓扑数据...">
        <div 
          ref={containerRef} 
          style={{ 
            width: '100%', 
            height: '100%',
            minHeight: 600,
            background: '#fafafa'
          }} 
        />
      </Spin>
      
      {data && (
        <div style={{ 
          position: 'absolute', 
          top: 70, 
          right: 20, 
          background: 'rgba(255,255,255,0.9)',
          padding: '8px 12px',
          borderRadius: 6,
          fontSize: 12
        }}>
          <Space direction="vertical" size={4}>
            <Text strong>统计信息</Text>
            <Text>节点: {data.nodes?.length || 0}</Text>
            <Text>连接: {data.edges?.length || 0}</Text>
          </Space>
        </div>
      )}
    </Card>
  );
};

export default SwitchNetworkTopology;
