#!/usr/bin/env python
"""
测试新的G6网络拓扑数据格式
"""

import os
import sys
import django
import json
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from operatorcmdb.models import VirtualSwitch, VirtualSwitchPhysicalInterface, VirtualSwitchTrunkInterface
from operatorcmdb.services.g6_data_service import g6_data_service


def test_switch_network_topology():
    """测试单个交换机的网络拓扑数据格式"""
    print("🔍 测试交换机网络拓扑数据格式...")
    
    # 获取第一个交换机
    switch = VirtualSwitch.objects.filter(is_deleted=False).first()
    
    if not switch:
        print("❌ 未找到可用的交换机数据")
        return
    
    print(f"📊 测试交换机: {switch.name} (ID: {switch.id})")
    
    try:
        g6_data = g6_data_service.get_switch_g6_data(switch.id)
        
        if 'error' in g6_data:
            print(f"❌ 获取G6数据失败: {g6_data['error']}")
            return
        
        print("✅ G6网络拓扑数据获取成功")
        
        # 验证数据结构
        required_fields = ['nodes', 'edges']
        
        for field in required_fields:
            if field not in g6_data:
                print(f"❌ 缺少必需字段: {field}")
                return
        
        print(f"📈 数据统计:")
        print(f"   - 节点数量: {len(g6_data['nodes'])}")
        print(f"   - 边数量: {len(g6_data['edges'])}")
        
        # 分析节点类型
        node_types = {}
        for node in g6_data['nodes']:
            node_type = node['data']['type']
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        print(f"📋 节点类型分布:")
        for node_type, count in node_types.items():
            print(f"   - {node_type}: {count}")
        
        # 分析边类型
        edge_types = {}
        for edge in g6_data['edges']:
            edge_type = edge['data']['type']
            edge_types[edge_type] = edge_types.get(edge_type, 0) + 1
        
        print(f"🔗 边类型分布:")
        for edge_type, count in edge_types.items():
            print(f"   - {edge_type}: {count}")
        
        # 显示示例数据
        print(f"\n📄 示例节点数据:")
        if g6_data['nodes']:
            example_node = g6_data['nodes'][0]
            print(json.dumps(example_node, indent=2, ensure_ascii=False))
        
        print(f"\n📄 示例边数据:")
        if g6_data['edges']:
            example_edge = g6_data['edges'][0]
            print(json.dumps(example_edge, indent=2, ensure_ascii=False))
        
        return g6_data
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_room_network_topology():
    """测试机房网络拓扑数据格式"""
    print("\n🏢 测试机房网络拓扑数据格式...")
    
    # 获取有交换机的机房
    switch = VirtualSwitch.objects.filter(
        is_deleted=False, 
        machine_room__isnull=False
    ).first()
    
    if not switch or not switch.machine_room:
        print("❌ 未找到有机房信息的交换机")
        return
    
    machine_room = switch.machine_room
    print(f"🏢 测试机房: {machine_room.name} (ID: {machine_room.id})")
    
    try:
        g6_data = g6_data_service.get_room_switches_g6_data(machine_room.id)
        
        if 'error' in g6_data:
            print(f"❌ 获取机房G6数据失败: {g6_data['error']}")
            return
        
        print("✅ 机房网络拓扑数据获取成功")
        
        print(f"📈 数据统计:")
        print(f"   - 节点数量: {len(g6_data['nodes'])}")
        print(f"   - 边数量: {len(g6_data['edges'])}")
        
        # 分析节点类型
        node_types = {}
        for node in g6_data['nodes']:
            node_type = node['data']['type']
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        print(f"📋 节点类型分布:")
        for node_type, count in node_types.items():
            print(f"   - {node_type}: {count}")
        
        return g6_data
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def show_data_format_guide():
    """显示数据格式指南"""
    print("\n📚 G6网络拓扑数据格式指南:")
    print("""
数据结构:
{
  "nodes": [
    {
      "id": "switch-123",
      "data": {
        "type": "switch",
        "text": "Core-Switch-01",
        "ip": "***********",
        "vendor": "Huawei",
        "status": "active",
        "details": { ... }
      }
    },
    {
      "id": "physical-456",
      "data": {
        "type": "physical-port",
        "text": "GigabitEthernet0/0/1",
        "speed": "1000Mbps",
        "state": "up",
        "status": "active",
        "details": { ... }
      }
    },
    {
      "id": "trunk-789",
      "data": {
        "type": "trunk-port",
        "text": "Eth-Trunk1",
        "state": "up",
        "status": "active",
        "member_count": 2,
        "details": { ... }
      }
    }
  ],
  "edges": [
    {
      "source": "switch-123",
      "target": "physical-456",
      "data": {
        "text": "up",
        "type": "physical-connection",
        "status": "active"
      }
    },
    {
      "source": "trunk-789",
      "target": "physical-456",
      "data": {
        "text": "member",
        "type": "trunk-member",
        "status": "active"
      }
    }
  ]
}

节点类型:
- switch: 交换机
- physical-port: 物理端口
- trunk-port: 聚合端口
- machine-room: 机房

边类型:
- physical-connection: 交换机到物理端口
- trunk-connection: 交换机到聚合端口
- trunk-member: 聚合端口到成员物理端口
- room-connection: 机房到交换机

状态值:
- active: 活跃/正常
- inactive: 非活跃/异常
- unknown: 未知状态
""")


def main():
    """主函数"""
    print("🚀 G6网络拓扑数据格式测试")
    print("=" * 50)
    
    # 测试单个交换机
    switch_data = test_switch_network_topology()
    
    # 测试机房级别
    room_data = test_room_network_topology()
    
    # 显示格式指南
    show_data_format_guide()
    
    print("\n✅ 测试完成!")
    
    # 保存示例数据到文件
    if switch_data:
        with open('switch_topology_example.json', 'w', encoding='utf-8') as f:
            json.dump(switch_data, f, indent=2, ensure_ascii=False)
        print("💾 交换机拓扑示例数据已保存到 switch_topology_example.json")
    
    if room_data:
        with open('room_topology_example.json', 'w', encoding='utf-8') as f:
            json.dump(room_data, f, indent=2, ensure_ascii=False)
        print("💾 机房拓扑示例数据已保存到 room_topology_example.json")


if __name__ == "__main__":
    main()
