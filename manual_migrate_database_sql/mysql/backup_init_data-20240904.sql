# chaos_system_dept 部门表
INSERT INTO `chaos_system_dept` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `key`, `sort`, `owner`, `phone`, `email`, `status`, `creator_id`, `parent_id`) VALUES (1, NULL, '1', NULL, '2024-08-30 10:38:56.773327', '2024-08-01 14:34:32.374411', '星在科技', 'xingzai-technology', 1, '', '', '', 1, 1, NULL);
INSERT INTO `chaos_system_dept` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `key`, `sort`, `owner`, `phone`, `email`, `status`, `creator_id`, `parent_id`) VALUES (4, '', '1', 4, '2024-08-01 14:44:54.160393', '2024-08-01 14:44:54.155388', '项目部', 'xiangmubu', 3, '', NULL, NULL, 1, 1, 1);
INSERT INTO `chaos_system_dept` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `key`, `sort`, `owner`, `phone`, `email`, `status`, `creator_id`, `parent_id`) VALUES (5, '', '1', 5, '2024-08-01 14:46:10.217200', '2024-08-01 14:46:10.212128', '运维部', 'yunweibu', 4, '', NULL, NULL, 1, 1, 1);
INSERT INTO `chaos_system_dept` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `key`, `sort`, `owner`, `phone`, `email`, `status`, `creator_id`, `parent_id`) VALUES (6, '', '1', 6, '2024-08-01 14:46:28.704576', '2024-08-01 14:46:28.699575', '销售部', 'xiaoshoubu', 5, '', NULL, NULL, 1, 1, 1);
INSERT INTO `chaos_system_dept` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `key`, `sort`, `owner`, `phone`, `email`, `status`, `creator_id`, `parent_id`) VALUES (7, '', '1', 7, '2024-08-01 14:47:25.760526', '2024-08-01 14:47:25.755369', '网络运维组', 'wangluoyunweizu', 6, '', NULL, NULL, 1, 1, 5);
INSERT INTO `chaos_system_dept` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `key`, `sort`, `owner`, `phone`, `email`, `status`, `creator_id`, `parent_id`) VALUES (8, '', '1', 8, '2024-08-01 14:47:55.251149', '2024-08-01 14:47:55.246495', '系统运维组', 'xitongyunweizu', 6, '', NULL, NULL, 1, 1, 5);


# chaos_system_post 岗位表
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (1, NULL, '1', 1, '2024-08-01 15:00:36.338618', '2024-08-01 14:49:18.897320', '运维总监', 'yunweizongjian', 2, 1);
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (2, NULL, '1', 1, '2024-08-01 14:49:38.752592', '2024-08-01 14:49:38.752643', 'CEO', 'ceo', 1, 1);
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (3, NULL, '1', 1, '2024-08-01 14:50:36.109434', '2024-08-01 14:50:36.109500', 'CMO', 'cmo', 3, 1);
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (4, NULL, '1', 1, '2024-08-01 14:51:01.253823', '2024-08-01 14:51:01.253872', '渠道经理', 'qudaojingli', 4, 1);
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (5, NULL, '1', 1, '2024-08-01 14:51:46.171082', '2024-08-01 14:51:46.171130', '销售经理', 'xiaoshoujingli', 5, 1);
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (6, NULL, '1', 1, '2024-08-01 14:52:34.776280', '2024-08-01 14:52:34.776381', 'CEO助理', 'ceozhuli', 6, 1);
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (7, NULL, '1', 1, '2024-08-01 14:53:00.951876', '2024-08-01 14:53:00.951930', 'IDC运维工程师', 'idcyunweigonghengshi', 7, 1);
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (8, '', '2', 1, '2024-08-01 15:33:47.935986', '2024-08-01 14:53:20.351245', '运维助理', 'yunweizhuli', 8, 1);
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (9, NULL, '1', 1, '2024-08-01 14:54:25.620089', '2024-08-01 14:54:25.620146', '系统运维工程师', 'xitongyunweigongchengshi', 9, 1);
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (10, NULL, '1', 1, '2024-08-01 14:54:59.587448', '2024-08-01 14:54:59.587505', '主机运维工程师', 'zhujiyunweigongchengshi', 10, 1);
INSERT INTO `chaos_system_post` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `code`, `sort`, `creator_id`) VALUES (11, NULL, '1', 1, '2024-08-01 14:55:23.713357', '2024-08-01 14:55:23.713417', '运维开发工程师', 'yunweikaifagongchengshi', 11, 1);


# chaos_system_api API白名单
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (1, NULL, '1', NULL, '2024-08-30 10:39:01.245298', '2024-08-01 14:34:36.445847', '/api/system/dept_lazy_tree/', 0, 1, NULL);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (2, NULL, '1', 1, '2024-08-07 14:03:24.087729', '2024-08-07 14:03:24.087790', '/api/resource/machine_room/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (3, NULL, '1', 1, '2024-08-07 14:03:36.742257', '2024-08-07 14:03:36.742312', '/api/resource/idc_rack_machine/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (4, NULL, '1', 1, '2024-08-07 14:04:03.061607', '2024-08-07 14:04:03.061668', '/api/resource/private_room/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (5, NULL, '1', 1, '2024-08-07 14:04:41.014183', '2024-08-07 14:04:41.014241', '/api/resource/customer/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (6, NULL, '1', 1, '2024-08-07 14:04:53.297613', '2024-08-07 14:04:53.297673', '/api/resource/physical_asset/general_consumable/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (7, NULL, '1', 1, '2024-08-07 14:05:07.872829', '2024-08-07 14:05:07.872875', '/api/resource/physical_asset/holistic_accessory/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (8, NULL, '1', 1, '2024-08-07 14:05:25.532140', '2024-08-07 14:05:25.532173', '/api/resource/physical_asset/network_hardware/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (9, NULL, '1', 1, '2024-08-07 14:05:38.218771', '2024-08-07 14:05:38.218825', '/api/resource/physical_asset/physical_server_machine/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (11, NULL, '1', 1, '2024-08-30 10:39:01.254182', '2024-08-09 11:12:59.930940', '/api/system/message_center/get_self_receive/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (12, NULL, '1', 1, '2024-08-30 10:39:01.267440', '2024-08-09 11:13:23.765293', '/api/system/message_center/delete_self_message_notice/', 3, 0, 1);
INSERT INTO `chaos_api_white_list` (`id`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (13, NULL, '1', 1, '2024-08-30 10:39:01.260977', '2024-08-09 11:14:13.953132', '/api/system/message_center/{id}/', 0, 0, 1);


