UPDATE operator_cmdb_hosts SET resource_category="裸金属机" WHERE host_type="裸金属";
UPDATE operator_cmdb_hosts SET resource_category="虚拟机" WHERE host_type="虚拟机";
UPDATE operator_cmdb_hosts SET resource_category="内部存储机" WHERE description="存储ceph";
UPDATE operator_cmdb_hosts SET resource_category="工具机" WHERE DESCRIPTION LIKE "%云平台%";
UPDATE operator_cmdb_hosts SET resource_category="工具机" WHERE DESCRIPTION LIKE "%工具机%";
UPDATE operator_cmdb_hosts SET resource_category="网络机" WHERE DESCRIPTION LIKE "%sdn%";

SELECT ip_bmc, COUNT(*) AS duplicate_count FROM operator_cmdb_hosts where is_deleted=0 GROUP BY ip_bmc HAVING COUNT(*) > 1;

SELECT ip_bmc, COUNT(*) AS duplicate_count FROM operator_cmdb_hosts where is_deleted=0 AND host_type='裸金属' GROUP BY ip_bmc HAVING COUNT(*) > 1;




SHOW VARIABLES LIKE 'sql_mode';  -- 若包含ONLY_FULL_GROUP_BY则需严格遵循规则


# GROUP BY 1 按照月份分组
SELECT DATE_FORMAT(create_datetime, '%Y-%m'), COUNT(*)
FROM tenant_op_servers
WHERE create_datetime BETWEEN '2024-01-01' AND '2025-04-09' AND project_id != '8f788892a6b444e69c618031b66a1240'
GROUP BY 1;

# 输出结果
[
		{
			"year_month": "2024-11",
			"交付频次": 29
		},
		{
			"year_month": "2024-12",
			"交付频次": 49
		},
		{
			"year_month": "2025-01",
			"交付频次": 47
		},
		{
			"year_month": "2025-02",
			"交付频次": 155
		},
		{
			"year_month": "2025-03",
			"交付频次": 34
		},
		{
			"year_month": "2025-04",
			"交付频次": 4
		}
]

# 资源GPU分布图
SELECT
    resource_category,
    SUM(gpu_num) AS total_gpu
FROM
    operator_cmdb_hosts
GROUP BY
    resource_category
ORDER BY
    total_gpu DESC;


# 资源用途分类
SELECT
    resource_category,
    COUNT(*) AS count
FROM
    operator_cmdb_hosts
GROUP BY
    resource_category
ORDER BY
    count DESC;

[
		{
			"name": "裸金属机",
			"value": 124
		},
		{
			"name": "工具机",
			"value": 20
		},
		{
			"name": "内部存储机",
			"value": 12
		},
		{
			"name": "网络机",
			"value": 6
		},
		{
			"name": "客户存储机",
			"value": 1
		}
]


# 主机GPU型号分布图
SELECT
    gpu_model,
    COUNT(*) AS count
FROM
    operator_cmdb_hosts WHERE is_deleted=0 AND resource_category='裸金属机' AND host_status != '下线' AND host_type="裸金属"
GROUP BY
    gpu_model
ORDER BY
    count DESC;


[
		{
			"name": "4090",
			"value": 58
		},
		{
			"name": "A800-PCIe",
			"value": 6
		}
]


# 统计各省份的客户数量

SELECT province, COUNT(*) AS customer_count
FROM customer_customers
WHERE province IS NOT NULL
GROUP BY province;



