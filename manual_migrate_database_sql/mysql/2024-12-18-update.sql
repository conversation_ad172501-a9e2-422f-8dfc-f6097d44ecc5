INSERT INTO `chaos_api_white_list` (`description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (NULL, '1', 1, '2024-12-17 17:33:55.376743', '2024-12-17 17:33:55.376743', '/api/tenants/tenant-account/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (NULL, '1', 1, '2024-12-17 17:33:33.505445', '2024-12-17 17:33:33.505445', '/api/tenants/tenant-op-ironic-hypervisor/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (NULL, '1', 1, '2024-12-17 17:33:00.507659', '2024-12-17 17:33:00.507659', '/api/tenants/tenant-project/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (NULL, '1', 1, '2024-12-17 17:34:14.814411', '2024-12-17 17:31:37.153793', '/api/tenants/tenant-op-network/get_list_by_ids/', 0, 0, 1);
INSERT INTO `chaos_api_white_list` (`description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `url`, `method`, `enable_datasource`, `creator_id`) VALUES (NULL, '1', 1, '2024-12-17 17:31:42.092176', '2024-12-17 17:31:17.617973', '/api/tenants/tenant-op-image/get_list_by_ids/', 0, 0, 1);


INSERT INTO `notice_notifications` (`seq`, `id`, `description`, `modifier`, `update_datetime`, `create_datetime`, `is_deleted`, `dept_belong_id`, `name`, `key`, `type`, `webhook_url`, `access_id`, `access_secret`, `is_enabled`, `creator_id`) VALUES (1, 'asdasdasdasd123312', NULL, '1', '2024-12-16 13:51:08.000000', '2024-12-16 13:51:09.000000', 0, '1', '订单通知机器人', 'interval_ticket_notice', '钉钉', 'https://oapi.dingtalk.com/robot/send', '13d7fc411f21fb80849d4e50fc29d6c39387ad4f9c5b770b2fc730afe4b47022', 'SECfad4240b24e369ba52ff51353eb9843e4600fa548bc15afcfba57b24004dcdf9', 1, 1);


