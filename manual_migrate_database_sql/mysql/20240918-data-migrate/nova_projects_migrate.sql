INSERT INTO `nova_projects_backup` (`id`, `is_deleted`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `sign`, `creator_id`) VALUES (CONCAT('nova_project-', REPLACE(UUID(), '-', '')), 0, NULL, '1', 1, '2024-08-14 10:31:31.818744', '2024-08-13 13:52:05.985881', '上海-云平台NDC', 'sh1', 1);
INSERT INTO `nova_projects_backup` (`id`, `is_deleted`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `sign`, `creator_id`) VALUES (CONCAT('nova_project-', REPLACE(UUID(), '-', '')), 0, NULL, '1', 1, '2024-08-13 14:13:18.634983', '2024-08-13 14:13:18.635013', '测试', 'test', 1);
INSERT INTO `nova_projects_backup` (`id`, `is_deleted`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `sign`, `creator_id`) VALUES (CONCAT('nova_project-', REPLACE(UUID(), '-', '')), 0, NULL, '1', 1, NULL, NULL, '金华-运维OPS', 'jh-ops', 1);
INSERT INTO `nova_projects_backup` (`id`, `is_deleted`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `sign`, `creator_id`) VALUES (CONCAT('nova_project-', REPLACE(UUID(), '-', '')), 0, NULL, '1', 1, NULL, NULL, '上海-存储集群CEPH', 'sh-ceph', 1);
INSERT INTO `nova_projects_backup` (`id`, `is_deleted`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `sign`, `creator_id`) VALUES (CONCAT('nova_project-', REPLACE(UUID(), '-', '')), 0, NULL, '1', 1, NULL, NULL, '上海-运维平台opa', 'sh-ops-opa', 1);
INSERT INTO `nova_projects_backup` (`id`, `is_deleted`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `sign`, `creator_id`) VALUES (CONCAT('nova_project-', REPLACE(UUID(), '-', '')), 0, NULL, '1', 1, NULL, NULL, '上海-运维系统OPS', 'sh-ops', 1);
INSERT INTO `nova_projects_backup` (`id`, `is_deleted`, `description`, `modifier`, `dept_belong_id`, `update_datetime`, `create_datetime`, `name`, `sign`, `creator_id`) VALUES (CONCAT('nova_project-', REPLACE(UUID(), '-', '')), 0, NULL, '1', 1, NULL, NULL, '资产管理Chaos', 'chaos', 1);
