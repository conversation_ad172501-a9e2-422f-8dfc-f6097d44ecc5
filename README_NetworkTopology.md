# 网络拓扑图组件使用指南

## 概述

这套组件用于展示交换机网络拓扑关系，包括交换机、物理端口、聚合端口之间的连接关系。基于 AntV G6 图可视化引擎开发，支持交互式操作和详细信息展示。

## 组件结构

```
├── SwitchNetworkTopology.jsx     # 主要拓扑图组件
├── NetworkTopologyLegend.jsx     # 图例组件
├── SwitchTopologyPage.jsx        # 完整页面示例
├── SwitchNetworkTopology.css     # 样式文件
└── README_NetworkTopology.md     # 使用文档
```

## 数据格式

### 后端API返回格式

```json
{
  "code": 2000,
  "msg": "获取G6数据成功",
  "data": {
    "nodes": [
      {
        "id": "switch-123",
        "data": {
          "type": "switch",
          "text": "Core-Switch-01",
          "ip": "***********",
          "vendor": "Huawei",
          "status": "active",
          "details": {
            "sn": "SN123456",
            "firmware_version": "V200R019C00",
            "last_sync_at": "2024-01-01T12:00:00Z"
          }
        }
      },
      {
        "id": "physical-456",
        "data": {
          "type": "physical-port",
          "text": "GigabitEthernet0/0/1",
          "speed": "1000Mbps",
          "state": "up",
          "status": "active",
          "details": {
            "mac_address": "00:11:22:33:44:55",
            "last_sync_at": "2024-01-01T12:00:00Z"
          }
        }
      }
    ],
    "edges": [
      {
        "source": "switch-123",
        "target": "physical-456",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      }
    ]
  }
}
```

### 节点类型

- **switch**: 交换机节点
- **physical-port**: 物理端口节点
- **trunk-port**: 聚合端口节点
- **machine-room**: 机房节点

### 边类型

- **physical-connection**: 交换机到物理端口的连接
- **trunk-connection**: 交换机到聚合端口的连接
- **trunk-member**: 聚合端口到成员物理端口的连接
- **room-connection**: 机房到交换机的连接

## 组件使用

### 1. 基础使用

```jsx
import SwitchNetworkTopology from './SwitchNetworkTopology';

// 单个交换机拓扑
<SwitchNetworkTopology 
  switchId="123"
  onNodeClick={(nodeData) => console.log(nodeData)}
/>

// 机房级别拓扑
<SwitchNetworkTopology 
  machineRoomId="456"
  onNodeClick={(nodeData) => console.log(nodeData)}
/>
```

### 2. 完整页面使用

```jsx
import SwitchTopologyPage from './SwitchTopologyPage';

function App() {
  return <SwitchTopologyPage />;
}
```

### 3. 自定义图例

```jsx
import NetworkTopologyLegend from './NetworkTopologyLegend';

<NetworkTopologyLegend 
  style={{ position: 'absolute', top: 20, right: 20 }}
/>
```

## 组件属性

### SwitchNetworkTopology

| 属性 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| switchId | string | 否 | - | 交换机ID，用于获取单个交换机拓扑 |
| machineRoomId | string | 否 | - | 机房ID，用于获取机房级别拓扑 |
| onNodeClick | function | 否 | - | 节点点击回调函数 |

### NetworkTopologyLegend

| 属性 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| style | object | 否 | {} | 自定义样式 |

## 功能特性

### 交互功能

- **节点拖拽**: 可以拖拽节点调整位置
- **画布缩放**: 鼠标滚轮缩放视图
- **画布平移**: 右键拖拽平移画布
- **节点选择**: 点击节点查看详细信息
- **悬停高亮**: 鼠标悬停时高亮节点和相关连接

### 视觉效果

- **渐变背景**: 美观的渐变色背景
- **阴影效果**: 节点和边的阴影效果
- **状态指示**: 不同颜色表示不同状态
- **动画过渡**: 平滑的动画过渡效果

### 响应式设计

- 支持不同屏幕尺寸
- 移动端适配
- 暗色主题支持
- 打印样式优化

## API接口

### 获取单个交换机拓扑数据

```
GET /api/virtual-switches/{switchId}/get_g6_data/
```

### 获取机房拓扑数据

```
GET /api/virtual-switches/get_room_g6_data/?machine_room_id={roomId}
```

## 样式定制

### 颜色配置

```css
:root {
  --switch-color: #1890ff;
  --physical-port-color: #52c41a;
  --trunk-port-color: #fa8c16;
  --machine-room-color: #722ed1;
  --active-color: #52c41a;
  --inactive-color: #ff4d4f;
  --unknown-color: #d9d9d9;
}
```

### 节点大小配置

```javascript
const getNodeSize = (type) => {
  switch (type) {
    case 'switch':
      return [180, 80];
    case 'machine-room':
      return [200, 90];
    case 'trunk-port':
      return [140, 60];
    default:
      return [120, 50];
  }
};
```

## 依赖项

```json
{
  "dependencies": {
    "@antv/g6": "^5.0.0",
    "@antv/g6-extension-react": "^1.0.0",
    "antd": "^5.0.0",
    "react": "^18.0.0"
  }
}
```

## 安装和配置

1. 安装依赖：
```bash
npm install @antv/g6 @antv/g6-extension-react antd
```

2. 引入样式文件：
```jsx
import './SwitchNetworkTopology.css';
```

3. 使用组件：
```jsx
import SwitchNetworkTopology from './SwitchNetworkTopology';
```

## 最佳实践

1. **性能优化**
   - 对于大型网络，考虑分页加载
   - 使用虚拟化技术处理大量节点
   - 合理设置布局算法参数

2. **用户体验**
   - 提供加载状态提示
   - 添加错误处理机制
   - 支持键盘快捷键操作

3. **数据更新**
   - 实现实时数据更新
   - 支持增量更新机制
   - 添加数据缓存策略

## 故障排除

### 常见问题

1. **图形不显示**
   - 检查容器元素是否有正确的宽高
   - 确认数据格式是否正确
   - 检查控制台是否有错误信息

2. **节点位置异常**
   - 检查布局算法配置
   - 确认节点数据是否完整
   - 尝试重新初始化图形

3. **交互功能失效**
   - 检查事件监听器是否正确绑定
   - 确认组件是否正确挂载
   - 检查浏览器兼容性

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础拓扑图展示
- 实现节点和边的交互功能
- 添加图例和详情面板
