from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from xresource.models import IDCRackMachine, MachineRoom, PrivateRoom
from dvadmin.system.models import Dictionary
from rest_framework import serializers

import xresource.dictionary_enum_keys as ENUM_KEYS


class CustomerImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = IDCRackMachine
        exclude = ()


class IDCRackMachineSerializer(ChaosCustomModelSerializer):
    """
    包间信息-序列化器
    """
    machine_room_name = serializers.Char<PERSON>ield(read_only=True, source='machine_room.name')
    state = serializers.CharField(read_only=True, source='machine_room.state')
    province = serializers.CharField(read_only=True, source='machine_room.province')
    city = serializers.CharField(read_only=True, source='machine_room.city')

    class Meta:
        model = IDCRackMachine
        fields = "__all__"
        read_only_fields = ["id", "seq", "is_deleted"]


class IDCRackMachineViewSet(ChaosCustomModelViewSet):
    """
    包间管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = IDCRackMachine.objects.order_by('-create_datetime')
    serializer_class = IDCRackMachineSerializer
    # permission_classes = []
    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'rack_sn',
    }

    # 导入
    import_serializer_class = CustomerImportSerializer
    import_field_dict = {
        "machine_room": {
            "title": "机房",
            "choices": {"queryset": MachineRoom.objects.filter(
                is_deleted=False
            ), "values_name": "name", "key_name": "id"}
        },
        "private_room": {
            "title": "包间",
            "choices": {"queryset": PrivateRoom.objects.filter(
                is_deleted=False
            ), "values_name": "name", "key_name": "id"}
        },
        "machine_room_alias": "机房缩写(必填项)",
        "private_room_alias": "包间缩写(必填项)",
        "rack_sn": "机柜编号(必填项、不可重复)",
        "description": "机柜描述",
        "net_operator_business": {
            "title": "网络运营商(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.IDC_RACK_MACHINE_NET_OPERATOR_BUSINESS_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "rack_type": {
            "title": "机柜类型(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.IDC_RACK_MACHINE_RACK_TYPE_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "rack_physics_type": {
            "title": "机柜物理形态(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.IDC_RACK_MACHINE_RACK_PHYSICS_TYPE_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "network_type": {
            "title": "网络类型",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.IDC_RACK_MACHINE_NETWORK_TYPE_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "network_cluster": "网络集群",
        "bmc_pod_sn": "BMC_POD编号",
        "asw_pod_sn": "ASW_POD编号",
        "network_arhitecture": "网络架构",
        "security_domain": {
            "title": "安全域",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.IDC_RACK_MACHINE_SECURITY_DOMAIN_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "logical_zone": {
            "title": "逻辑区域",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.IDC_RACK_MACHINE_LOGICAL_ZONE_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "construction_status": {
            "title": "模建状态",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.IDC_RACK_MACHINE_CONSTRUCTION_STATUS_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "accept_delivery_time": {
            "title": "验收交付时间(如:1990-07-01)",
            "type": "date"
        },
        "construction_finished_time": {
            "title": "模建完成时间(如:1990-07-01)",
            "type": "date"
        },
        "rack_status": {
            "title": "柜位状态(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.IDC_RACK_MACHINE_RACK_STATUS_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "power_status": {
            "title": "上电状态",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.IDC_RACK_MACHINE_POWER_STATUS_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "billing_unit": "计费单元(必填项)",
        "construction_density": "建设密度(整型数)",
        "to_shelve_density": "上架密度(整型数)",
        "rack_height": "高度(mm)(整型数)(必填项)",
        "rack_u_height": "高度U(mm)(整型数)(必填项)",
        "rack_width": "宽度(mm)(整型数)(必填项)",
        "rack_deep": "深度(mm)(整型数)(必填项)",
        "load_bearing": "承重(KG)(整型数)(必填项)",
        "power_type": {
            "title": "电源类型(整型数)(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=ENUM_KEYS.IDC_RACK_MACHINE_POWER_TYPE_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "power_count": "电源路数(整型数)(必填项)",
        "normal_power": "额定功率(整型数)(必填项)",
        "power_max": "功率上线(整型数)(必填项)"
    }
