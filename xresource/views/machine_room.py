from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from dvadmin.system.models import Area
from xresource.models import MachineRoom


class CustomerImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = MachineRoom
        exclude = ()


class MachineRoomSerializer(ChaosCustomModelSerializer):
    """
    日志-序列化器
    """

    class Meta:
        model = MachineRoom
        fields = "__all__"
        read_only_fields = ["id"]


class MachineRoomViewSet(ChaosCustomModelViewSet):
    """
    机房信息接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = MachineRoom.objects.order_by('-create_datetime')
    serializer_class = MachineRoomSerializer
    # permission_classes = []
    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = CustomerImportSerializer
    import_field_dict = {
        "name": "机房名称(必填项)",
        "description": "机房描述",
        "province": {
            "title": "省份(必填项)",
            "choices": {"queryset": Area.objects.filter(
                level=1
            ), "values_name": "name", "key_name": "code"}
        },
        "city": {
            "title": "城市(必填项)",
            "choices": {"queryset": Area.objects.filter(
                level=2
            ), "values_name": "name", "key_name": "code"}
        },
        "district": {
            "title": "区县(必填项)",
            "choices": {"queryset": Area.objects.filter(
                level=3
            ), "values_name": "name", "key_name": "code"}
        },
        "address": "地址(必填项)",
        "manager_name": "责任人名称(必填项)",
        "manager_phone": "责任人电话(必填项)",
        "manager_email": "责任人邮箱"
    }
    # permission_classes = []
