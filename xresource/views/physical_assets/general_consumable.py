from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from dvadmin.system.models import Dictionary

from xresource.models import PrivateRoom, MachineRoom, GeneralConsumable
from xresource.dictionary_enum_keys import PHYSICAL_ASSET_BELONG_KEY, PHYSICAL_ASSET_GENERAL_CONSUMABLE_MACHINE_TYPE_KEY


class CustomerImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = GeneralConsumable
        exclude = ()


class GeneralConsumableSerializer(ChaosCustomModelSerializer):
    """
    包间信息-序列化器
    """

    class Meta:
        model = GeneralConsumable
        fields = "__all__"
        read_only_fields = ["id", "seq", "is_deleted"]


class GeneralConsumableViewSet(ChaosCustomModelViewSet):
    """
    物理设备资产管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = GeneralConsumable.objects.order_by('-create_datetime')
    serializer_class = GeneralConsumableSerializer
    # permission_classes = []

    # 导入
    import_serializer_class = CustomerImportSerializer
    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    import_field_dict = {
        "name": "设备名称",
        "description": "设备描述",
        "machine_type": {
            "title": "设备类型(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=PHYSICAL_ASSET_GENERAL_CONSUMABLE_MACHINE_TYPE_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "machine_room": {
            "title": "机房",
            "choices": {"queryset": MachineRoom.objects.filter(
                is_deleted=False
            ), "values_name": "name", "key_name": "id"}
        },
        "private_room": {
            "title": "包间",
            "choices": {"queryset": PrivateRoom.objects.filter(
                is_deleted=False
            ), "values_name": "name", "key_name": "id"}
        },
        "manufacturer": "厂商(必填项)",
        "belong": {
            "title": "资产归属(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=PHYSICAL_ASSET_BELONG_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "specs": "规格(必填项)",
        "count": "数量(整型数)(必填项)",
    }
