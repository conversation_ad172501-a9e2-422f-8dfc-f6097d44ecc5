from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from dvadmin.system.models import Dictionary

from xresource.models import PrivateRoom, MachineRoom, PhysicalServerMachine, IDCRackMachine
from xresource.dictionary_enum_keys import (
    PHYSICAL_ASSET_PHYSICAL_SERVER_MACHINE_TYPE_KEY,
    PHYSICAL_ASSET_BELONG_KEY,
    PHYSICAL_ASSET_PHYSICAL_SERVER_MACHINE_GPU_MODEL_KEY
)


class CustomerImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = PhysicalServerMachine
        exclude = ()


class PhysicalServerMachineSerializer(ChaosCustomModelSerializer):
    """
    包间信息-序列化器
    """

    class Meta:
        model = PhysicalServerMachine
        fields = "__all__"
        read_only_fields = ["id", "seq", "is_deleted"]


class PhysicalServerMachineViewSet(ChaosCustomModelViewSet):
    """
    物理设备资产管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = PhysicalServerMachine.objects.order_by('-create_datetime')
    serializer_class = PhysicalServerMachineSerializer
    # permission_classes = []
    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'physical_machine_sn',
    }

    # 导入
    import_serializer_class = CustomerImportSerializer
    import_field_dict = {
        "physical_machine_sn": "设备SN(必填项、不可重复)",
        "description": "设备描述",
        # TODO 暂取消添加此字段
        "machine_type": {
            "title": "设备类型(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=PHYSICAL_ASSET_PHYSICAL_SERVER_MACHINE_TYPE_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "gpu_model": {
            "title": "GPU类型(非必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=PHYSICAL_ASSET_PHYSICAL_SERVER_MACHINE_GPU_MODEL_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "machine_room": {
            "title": "机房",
            "choices": {"queryset": MachineRoom.objects.filter(
                is_deleted=False
            ), "values_name": "name", "key_name": "id"}
        },
        "private_room": {
            "title": "包间",
            "choices": {"queryset": PrivateRoom.objects.filter(
                is_deleted=False
            ), "values_name": "name", "key_name": "id"}
        },
        "idc_rack_machine": {
            "title": "机柜",
            "choices": {"queryset": IDCRackMachine.objects.filter(
                is_deleted=False
            ), "values_name": "rack_sn", "key_name": "id"}
        },
        "rack_unit": "U 位(整型数)",
        "manufacturer": "厂商(必填项)",
        "machine_specs": "型号规格(必填项)",
        "belong": {
            "title": "资产归属(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=PHYSICAL_ASSET_BELONG_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "specs": "规格(必填项)",
        "normal_power": "额定功率(整型数)(必填项)",
    }
