from dvadmin.utils.chaos_viewset import ChaosCustomModelViewSet
from dvadmin.utils.chaos_serializers import ChaosCustomModelSerializer
from dvadmin.system.models import Dictionary

from rest_framework import serializers

from xresource.models import PrivateRoom, MachineRoom
from xresource.dictionary_enum_keys import PRIVATE_ROOM_TYPE_KEY, PRIVATE_ROOM_STATUS_KEY


class CustomerImportSerializer(ChaosCustomModelSerializer):

    def save(self, **kwargs):
        data = super().save(**kwargs)
        data.save()
        return data

    class Meta:
        model = PrivateRoom
        exclude = ()


class PrivateRoomSerializer(ChaosCustomModelSerializer):
    """
    包间信息-序列化器
    """
    machine_room_name = serializers.CharField(read_only=True, source='machine_room.name')

    class Meta:
        model = PrivateRoom
        fields = "__all__"
        read_only_fields = ["id", "seq", "is_deleted"]


class PrivateRoomViewSet(ChaosCustomModelViewSet):
    """
    包间管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = PrivateRoom.objects.order_by('-create_datetime')
    serializer_class = PrivateRoomSerializer
    # permission_classes = []
    # _get_list_by_ids configuration
    api_list_by_ids_dict = {
        'is_opened': True,
        'id_field': 'id',
        'name_field': 'name',
    }
    # 导入
    import_serializer_class = CustomerImportSerializer
    import_field_dict = {
        "machine_room": {
            "title": "机房名称(必填项)",
            "choices": {"queryset": MachineRoom.objects.filter(
                is_deleted=False
            ), "values_name": "name", "key_name": "id"}
        },
        "name": "包间名称(必填项、不可重复)",
        "description": "包间描述",
        "private_room_type": {
            "title": "包间类型",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=PRIVATE_ROOM_TYPE_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "block_sn": "楼号",
        "floor_sn": "楼层",
        "in_service_date": {
            "title": "正式投入使用时间(必填项、如:1990-07-01)",
            "type": "date"
        },
        "out_of_service_date": {
            "title": "解约时间(如:1990-07-01)",
            "type": "date"
        },
        "planed_service_date": {
            "title": "规划开始时间(如:1990-07-01)",
            "type": "date"
        },
        "build_service_date": {
            "title": "建设开始时间(如:1990-07-01)",
            "type": "date"
        },
        "last_inspection_date": {
            "title": "最近一次检修时间(如:1990-07-01 10:00:00)",
            "type": "datetime"
        },
        "status": {
            "title": "状态(必填项)",
            "choices": {"queryset": Dictionary.objects.filter(
                parent__value=PRIVATE_ROOM_STATUS_KEY
            ), "values_name": "label", "key_name": "value"}
        },
        "manager_name": "责任人名称(必填项)",
        "manager_phone": "责任人电话(必填项)",
        "manager_email": "责任人邮箱"
    }
