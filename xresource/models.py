from django.db import models

from dvadmin.utils.chaos_models import ChaosCoreModel

TABLE_PREFIX = 'resource_'  # 数据库表名前缀


class MachineRoom(ChaosCoreModel):
    name = models.CharField(
        max_length=255, default=None, unique=True, blank=False, verbose_name='机房名称', help_text='机房名称',
        db_comment='机房名称')
    description = models.TextField(blank=True, verbose_name='机房描述', help_text='机房描述', db_comment='机房描述')
    province = models.CharField(max_length=255, blank=True, verbose_name='省份', help_text='省份', db_comment='省份')
    city = models.CharField(max_length=255, blank=True, verbose_name='城市', help_text='城市', db_comment='城市')
    district = models.CharField(max_length=255, blank=True, verbose_name='区县', help_text='区县', db_comment='区县')
    address = models.CharField(
        max_length=15, blank=True, verbose_name='机房地址', help_text='机房地址', db_comment='机房地址')
    manager_name = models.Char<PERSON>ield(
        max_length=255, blank=True, verbose_name='责任人名称', help_text='责任人名称', db_comment='责任人名称')
    manager_phone = models.CharField(
        max_length=15, blank=True, verbose_name='责任人电话', help_text='责任人电话', db_comment='责任人电话')
    manager_email = models.CharField(
        max_length=255, blank=True, verbose_name='责任人邮箱', help_text='责任人邮箱', db_comment='责任人邮箱')

    class Meta:
        db_table = TABLE_PREFIX + "machine_rooms"
        verbose_name = "机房信息表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class PrivateRoom(ChaosCoreModel):
    machine_room = models.ForeignKey(
        MachineRoom, max_length=63, on_delete=models.SET_NULL, to_field='id', related_name='private_room_machine_room',  blank=False, null=True, db_index=True,
        verbose_name='机房', help_text='机房ID', db_comment='机房ID')
    name = models.CharField(
        max_length=255, default=None,  unique=True, blank=False, verbose_name='包间名称', help_text='包间名称',
        db_comment='包间名称')
    private_room_type = models.CharField(
        max_length=255, default=None, blank=False, verbose_name='包间类型', help_text='包间描述', db_comment='包间描述')
    description = models.TextField(blank=True, verbose_name='包间描述', help_text='包间描述', db_comment='包间描述')
    block_sn = models.CharField(
        max_length=255, default=None, blank=False, verbose_name='楼号', help_text='楼号', db_comment='楼号')
    floor_sn = models.CharField(
        max_length=255, default=None, blank=False, verbose_name='楼层', help_text='楼层', db_comment='楼层')
    in_service_date = models.DateField(
        default=None, null=True, blank=True, verbose_name='正式投入使用时间', help_text='正式投入使用时间', db_comment='正式投入使用时间')
    out_of_service_date = models.DateField(
        default=None, null=True, blank=True, verbose_name='解约时间', help_text='解约时间', db_comment='解约时间')
    planed_service_date = models.DateField(
        default=None, null=True, blank=True, verbose_name='规划开始时间', help_text='规划开始时间', db_comment='规划开始时间')
    build_service_date = models.DateField(
        default=None, null=True, blank=True, verbose_name='建设开始时间', help_text='建设开始时间', db_comment='建设开始时间')
    last_inspection_date = models.DateTimeField(
        default=None, null=True, blank=True, verbose_name='最近一次检修时间', help_text='最近一次检修时间', db_comment='最近一次检修时间')
    # [Important] 此为【在线配置字段】，参见: key-> private_room:status
    status = models.CharField(
        max_length=63, blank=True, verbose_name='状态', help_text='状态', db_comment='状态')
    manager_name = models.CharField(
        max_length=63, blank=True, verbose_name='责任人名称', help_text='责任人名称', db_comment='责任人名称')
    manager_phone = models.CharField(
        max_length=15, blank=True, verbose_name='责任人电话', help_text='责任人电话', db_comment='责任人电话')
    manager_email = models.CharField(
        max_length=255, blank=True, verbose_name='责任人邮箱', help_text='责任人邮箱', db_comment='责任人邮箱')

    class Meta:
        db_table = TABLE_PREFIX + "private_rooms"
        verbose_name = "包间信息表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class IDCRackMachine(ChaosCoreModel):
    machine_room = models.ForeignKey(
        MachineRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='idc_rack_machine_machine_room', blank=True, null=True, db_index=True,
        verbose_name='机房', help_text='机房ID', db_comment='机房ID')
    private_room = models.ForeignKey(
        PrivateRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='idc_rack_machine_private_room', blank=True, null=True, db_index=True,
        verbose_name='包间', help_text='包间ID', db_comment='包间ID')
    machine_room_alias = models.CharField(
        max_length=255, default=None, unique=False, blank=False, verbose_name='机房缩写', help_text='机房缩写',
        db_comment='机房缩写')
    private_room_alias = models.CharField(
        max_length=255, default=None, unique=False, blank=False, verbose_name='包间缩写', help_text='包间缩写',
        db_comment='包间缩写')
    rack_sn = models.CharField(
        max_length=255, default=None,  unique=False, blank=False, verbose_name='机柜编号', help_text='机柜编号',
        db_comment='机柜编号')
    net_operator_business = models.CharField(
        max_length=63, default=None, blank=False, verbose_name='网络运营商', help_text='网络运营商',
        db_comment='网络运营商')
    rack_type = models.CharField(
        max_length=63, default=None, blank=False, verbose_name='机柜类型', help_text='机柜类型',
        db_comment='机柜类型')
    rack_physics_type = models.CharField(
        max_length=63, default=None, blank=False, verbose_name='机柜物理形态', help_text='机柜物理形态',
        db_comment='机柜物理形态')
    network_type = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='网络类型', help_text='网络类型',
        db_comment='网络类型')
    network_cluster = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='网络集群', help_text='网络集群',
        db_comment='网络集群')
    bmc_pod_sn = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='BMC_POD编号', help_text='BMC_POD编号',
        db_comment='BMC_POD编号')
    asw_pod_sn = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='ASW_POD编号', help_text='ASW_POD编号',
        db_comment='ASW_POD编号')
    network_arhitecture = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='网络架构', help_text='网络架构',
        db_comment='网络架构')
    security_domain = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='安全域', help_text='安全域',
        db_comment='安全域')
    logical_zone = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='逻辑区域', help_text='逻辑区域',
        db_comment='逻辑区域')
    accept_delivery_time = models.DateTimeField(
        default=None, null=True, blank=True, verbose_name='验收交付时间',
        help_text='验收交付时间', db_comment='验收交付时间')
    construction_status = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='模建状态', help_text='模建状态',
        db_comment='模建状态')
    construction_finished_time = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='模建完成时间', help_text='模建完成时间',
        db_comment='模建完成时间')
    rack_status = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='柜位状态', help_text='柜位状态',
        db_comment='柜位状态')
    power_status = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='上电状态', help_text='上电状态',
        db_comment='上电状态')
    billing_unit = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='计费单元', help_text='计费单元',
        db_comment='计费单元')
    construction_density = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='建设密度', help_text='建设密度', db_comment='建设密度')
    to_shelve_density = models.FloatField(
        default=None, null=True, blank=True, verbose_name='上架密度', help_text='上架密度', db_comment='上架密度')
    # TODO 建设密度要大于上架密度 密度为整数
    rack_height = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='高度(mm)', help_text='高度(mm)', db_comment='高度(mm)')
    rack_u_height = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='高度U/机柜U位', help_text='高度U/机柜U位', db_comment='高度U/机柜U位')
    rack_width = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='宽度(mm)', help_text='宽度(mm)', db_comment='宽度(mm)')
    rack_deep = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='深度/长度(mm)', help_text='深度/长度(mm)', db_comment='深度/长度(mm)')
    load_bearing = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='承重(KG)', help_text='承重(KG)', db_comment='承重(KG)')
    power_type = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='电源类型', help_text='电源类型',
        db_comment='电源类型')
    power_count = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='电源路数', help_text='电源路数', db_comment='电源路数')
    normal_power = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='额定功率', help_text='额定功率', db_comment='额定功率')
    power_max = models.IntegerField(
        default=None, null=True, blank=True, verbose_name='功率上限', help_text='功率上限', db_comment='功率上限')

    class Meta:
        db_table = TABLE_PREFIX + "idc_rack_machines"
        verbose_name = "IDC资源机柜表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class PhysicalServerMachine(ChaosCoreModel):
    physical_machine_sn = models.CharField(
        max_length=255, default=None, unique=True, blank=False, verbose_name='设备SN', help_text='设备SN',
        db_comment='设备SN')
    machine_type = models.CharField(
        max_length=63, default=None, blank=False, verbose_name='设备类型', help_text='设备类型', db_comment='设备类型')
    machine_room = models.ForeignKey(
        MachineRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='physical_server_machine_machine_room', blank=True, null=True, db_index=True,
        verbose_name='机房', help_text='机房ID', db_comment='机房ID')
    private_room = models.ForeignKey(
        PrivateRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='physical_server_machine_private_room', blank=True, null=True, db_index=True,
        verbose_name='包间', help_text='包间ID', db_comment='包间ID')
    idc_rack_machine = models.ForeignKey(
        IDCRackMachine, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='physical_server_machine_idc_rack_machine', blank=True, null=True,
        db_index=True, verbose_name='机柜', help_text='机柜ID', db_comment='机柜ID')
    rack_unit = models.IntegerField(
        default=-1, null=True, blank=True, verbose_name='U位', help_text='U位', db_comment='U位')
    manufacturer = models.CharField(
        max_length=255, default=None, null=True, blank=True, verbose_name='厂商', help_text='厂商', db_comment='厂商')
    machine_specs = models.CharField(
        max_length=255, default=None, null=True, blank=True, verbose_name='型号规格',
        help_text='型号规格', db_comment='型号规格')
    belong = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='资产归属',
        help_text='资产归属', db_comment='资产归属')
    specs = models.CharField(
        max_length=255, default=None, null=True, blank=True, verbose_name='规格',
        help_text='规格', db_comment='规格')
    normal_power = models.IntegerField(
        default=None, null=True, blank=True,
        verbose_name='额定功率', help_text='额定功率', db_comment='额定功率')
    gpu_model = models.CharField(
        max_length=63, null=True, blank=True,
        verbose_name='GPU类型', help_text='GPU类型', db_comment='GPU类型')
    resource_category = models.CharField(
        max_length=63, blank=False, null=False, default='裸金属机',
        verbose_name='资源分类', help_text='资源分类', db_comment='资源分类')

    class Meta:
        db_table = TABLE_PREFIX + "physical_server_machines"
        verbose_name = "资产-服务器设备表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class NetworkHardware(ChaosCoreModel):
    physical_machine_sn = models.CharField(
        max_length=255, default=None, unique=True, blank=False, verbose_name='设备SN', help_text='设备SN',
        db_comment='设备SN')
    machine_type = models.CharField(
        max_length=63, default=None, blank=False, verbose_name='设备类型', help_text='设备类型', db_comment='设备类型')
    description = models.TextField(blank=True, verbose_name='设备描述', help_text='设备描述', db_comment='设备描述')
    machine_room = models.ForeignKey(
        MachineRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='network_hardware_machine_room', blank=False, null=True, db_index=True,
        verbose_name='机房', help_text='机房ID', db_comment='机房ID')
    private_room = models.ForeignKey(
        PrivateRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='network_hardware_private_room', blank=False, null=True, db_index=True,
        verbose_name='包间', help_text='包间ID', db_comment='包间ID')
    idc_rack_machine = models.ForeignKey(
        IDCRackMachine, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='network_hardware_idc_rack_machine', blank=False, null=True,
        db_index=True, verbose_name='机柜', help_text='机柜ID', db_comment='机柜ID')
    rack_unit = models.IntegerField(
        default=-1, blank=False, verbose_name='U位', help_text='U位', db_comment='U位')
    manufacturer = models.CharField(
        max_length=255, default=None, blank=False, verbose_name='厂商', help_text='厂商', db_comment='厂商')
    machine_specs = models.CharField(
        max_length=255, default=None, null=True, blank=False, verbose_name='型号规格',
        help_text='型号规格', db_comment='型号规格')
    belong = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='资产归属',
        help_text='资产归属', db_comment='资产归属')
    specs = models.CharField(
        max_length=255, default=None, null=True, blank=False, verbose_name='规格',
        help_text='规格', db_comment='规格')
    normal_power = models.IntegerField(
        null=True, blank=False, verbose_name='额定功率', help_text='额定功率', db_comment='额定功率')

    class Meta:
        db_table = TABLE_PREFIX + "network_hardwares"
        verbose_name = "资产-网络设备表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class HolisticAccessory(ChaosCoreModel):
    physical_machine_sn = models.CharField(
        max_length=255, default=None, unique=True, blank=False, verbose_name='设备SN', help_text='设备SN',
        db_comment='设备SN')
    machine_type = models.CharField(
        max_length=63, default=None, blank=False, verbose_name='设备类型', help_text='设备类型', db_comment='设备类型')
    machine_room = models.ForeignKey(
        MachineRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='holistic_accessory_machine_room', blank=False, null=True, db_index=True,
        verbose_name='机房', help_text='机房ID', db_comment='机房ID')
    private_room = models.ForeignKey(
        PrivateRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='holistic_accessory_private_room', blank=False, null=True, db_index=True,
        verbose_name='包间', help_text='包间ID', db_comment='包间ID')
    manufacturer = models.CharField(
        max_length=255, default=None, blank=False, verbose_name='厂商', help_text='厂商', db_comment='厂商')
    belong = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='资产归属',
        help_text='资产归属', db_comment='资产归属')
    specs = models.CharField(
        max_length=255, default=None, null=True, blank=False, verbose_name='规格',
        help_text='规格', db_comment='规格')
    count = models.IntegerField(
        null=True, blank=False, default=1, verbose_name='数量', help_text='数量', db_comment='数量')

    class Meta:
        db_table = TABLE_PREFIX + "holistic_accessories"
        verbose_name = "资产-综合配件表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)


class GeneralConsumable(ChaosCoreModel):
    name = models.CharField(
        max_length=255, default=None, null=True, blank=True,
        verbose_name='设备名称', help_text='设备名称', db_comment='设备名称')
    machine_type = models.CharField(
        max_length=63, default=None, blank=False, verbose_name='设备类型', help_text='设备类型', db_comment='设备类型')
    machine_room = models.ForeignKey(
        MachineRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='general_consumable_machine_room', blank=False, null=True, db_index=True,
        verbose_name='机房', help_text='机房ID', db_comment='机房ID')
    private_room = models.ForeignKey(
        PrivateRoom, max_length=63, on_delete=models.SET_NULL, to_field='id',
        related_name='general_consumable_private_room', blank=False, null=True, db_index=True,
        verbose_name='包间', help_text='包间ID', db_comment='包间ID')
    manufacturer = models.CharField(
        max_length=255, default=None, blank=False, verbose_name='厂商', help_text='厂商', db_comment='厂商')
    belong = models.CharField(
        max_length=63, default=None, null=True, blank=True, verbose_name='资产归属',
        help_text='资产归属', db_comment='资产归属')
    specs = models.CharField(
        max_length=255, default=None, null=True, blank=False, verbose_name='规格',
        help_text='规格', db_comment='规格')
    count = models.IntegerField(
        null=True, blank=False, verbose_name='数量', help_text='数量', db_comment='数量')

    class Meta:
        db_table = TABLE_PREFIX + "general_consumables"
        verbose_name = "资产-综合耗材表"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)
