
# ########### 资源管理 枚举KEY ####################
# 资源管理->包间管理->包间类型
PRIVATE_ROOM_TYPE_KEY = 'private_room:private_room_type'
# 资源管理->包间管理->状态
PRIVATE_ROOM_STATUS_KEY = 'private_room:status'


# ########### IDC机柜管理 枚举KEY ####################
# IDC机柜管理-网络运营商
IDC_RACK_MACHINE_NET_OPERATOR_BUSINESS_KEY = 'idc_rack_machine:net_operator_business'
# IDC机柜管理-机柜类型
IDC_RACK_MACHINE_RACK_TYPE_KEY = 'idc_rack_machine:rack_type'
# IDC机柜管理-机柜物理形态
IDC_RACK_MACHINE_RACK_PHYSICS_TYPE_KEY = 'idc_rack_machine:rack_physics_type'
# IDC机柜管理-网络类型
IDC_RACK_MACHINE_NETWORK_TYPE_KEY = 'idc_rack_machine:network_type'
# IDC机柜管理-模建状态
IDC_RACK_MACHINE_CONSTRUCTION_STATUS_KEY = 'idc_rack_machine:construction_status'
# IDC机柜管理-上电状态
IDC_RACK_MACHINE_POWER_STATUS_KEY = 'idc_rack_machine:power_status'
# IDC机柜管理-电源类型
IDC_RACK_MACHINE_POWER_TYPE_KEY = 'idc_rack_machine:power_type'
# IDC机柜管理-柜位状态
IDC_RACK_MACHINE_RACK_STATUS_KEY = 'idc_rack_machine:rack_status'
# IDC机柜管理-安全域
IDC_RACK_MACHINE_SECURITY_DOMAIN_KEY = 'idc_rack_machine:security_domain'
# IDC机柜管理-逻辑区域
IDC_RACK_MACHINE_LOGICAL_ZONE_KEY = 'idc_rack_machine:logical_zone'


# ########### 物理资产管理 枚举KEY ####################

# 物理资产管理-(通用)资产归属
PHYSICAL_ASSET_BELONG_KEY = 'physical_asset:belong'

# 资源管理->物理资产管理-物理服务器-设备类型
PHYSICAL_ASSET_PHYSICAL_SERVER_MACHINE_TYPE_KEY = 'physical_asset:physical_server_machine:machine_type'
# 资源管理->物理资产管理-物理服务器-GPU类型
PHYSICAL_ASSET_PHYSICAL_SERVER_MACHINE_GPU_MODEL_KEY = 'physical_asset:physical_server_machine:gpu_model'

# 资源管理->物理资产管理-网络设备-设备类型
PHYSICAL_ASSET_NETWORK_HARDWARE_MACHINE_TYPE_KEY = 'physical_asset:network_hardware:machine_type'
# 资源管理->物理资产管理-综合耗材-设备类型
PHYSICAL_ASSET_GENERAL_CONSUMABLE_MACHINE_TYPE_KEY = 'physical_asset:general_consumable:machine_type'
# 资源管理->物理资产管理-配件-设备类型
PHYSICAL_ASSET_HOLISTIC_ACCESSORY_MACHINE_TYPE_KEY = 'physical_asset:holistic_accessory:machine_type'





