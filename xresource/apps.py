from django.apps import AppConfig
# from django.dispatch import receiver
# from resource.signals import resource_changed_log


class XResourceConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'xresource'

    # def ready(self):
    #     @receiver(resource_changed_log)
    #     def log_data_change(sender, instance, **kwargs):
    #         """资源重要信息更改记录日志"""
    #         print(f'sender: {sender}')
    #         print(f'kwargs: {kwargs}')
    #         print(f"Data has been changed for {instance}")
