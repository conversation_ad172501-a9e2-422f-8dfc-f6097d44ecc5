"""
资源模块路由
"""
from django.urls import path
from rest_framework import routers

from xresource.views.machine_room import MachineRoomViewSet
from xresource.views.private_room import PrivateRoomViewSet
from xresource.views.idc_rock_machine import IDCRackMachineViewSet
from xresource.views.physical_assets.physical_server_machine import PhysicalServerMachineViewSet
from xresource.views.physical_assets.network_hardware import NetworkHardwareViewSet
from xresource.views.physical_assets.general_consumable import GeneralConsumableViewSet
from xresource.views.physical_assets.holistic_accessory import HolisticAccessoryViewSet


resource_url = routers.SimpleRouter()
resource_url.register(r'machine_room', MachineRoomViewSet)
resource_url.register(r'private_room', PrivateRoomViewSet)
resource_url.register(r'idc_rack_machine', IDCRackMachineViewSet)

# 资产管理
resource_url.register(r'physical_asset/physical_server_machine', PhysicalServerMachineViewSet)
resource_url.register(r'physical_asset/network_hardware', NetworkHardwareViewSet)
resource_url.register(r'physical_asset/general_consumable', GeneralConsumableViewSet)
resource_url.register(r'physical_asset/holistic_accessory', HolisticAccessoryViewSet)


app_name = 'xresource'


urlpatterns = [
    # path('user/export/', UserViewSet.as_view({'post': 'export_data', }))
    # path('resource/machine_room/', MachineRoomViewSet.as_view('get', 'list')),
    # path('resource/machine_room/<id>/', MachineRoomViewSet.as_view()),
]
urlpatterns += resource_url.urls
