# Generated by Django 5.0.6 on 2024-10-10 10:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('xresource', '0005_physicalservermachine_gpu_model_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='generalconsumable',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='general_consumable_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='generalconsumable',
            name='private_room',
            field=models.ForeignKey(db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='general_consumable_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
        migrations.AlterField(
            model_name='holisticaccessory',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='holistic_accessory_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='holisticaccessory',
            name='private_room',
            field=models.ForeignKey(db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='holistic_accessory_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
        migrations.AlterField(
            model_name='idcrackmachine',
            name='machine_room',
            field=models.ForeignKey(blank=True, db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='idc_rack_machine_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='idcrackmachine',
            name='private_room',
            field=models.ForeignKey(blank=True, db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='idc_rack_machine_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
        migrations.AlterField(
            model_name='idcrackmachine',
            name='rack_deep',
            field=models.IntegerField(blank=True, db_comment='深度/长度(mm)', default=None, help_text='深度/长度(mm)', null=True, verbose_name='深度/长度(mm)'),
        ),
        migrations.AlterField(
            model_name='idcrackmachine',
            name='rack_u_height',
            field=models.IntegerField(blank=True, db_comment='高度U', default=None, help_text='高度U', null=True, verbose_name='高度U'),
        ),
        migrations.AlterField(
            model_name='networkhardware',
            name='idc_rack_machine',
            field=models.ForeignKey(db_comment='机柜ID', help_text='机柜ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='network_hardware_idc_rack_machine', to='xresource.idcrackmachine', to_field='id', verbose_name='机柜'),
        ),
        migrations.AlterField(
            model_name='networkhardware',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='network_hardware_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='networkhardware',
            name='private_room',
            field=models.ForeignKey(db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='network_hardware_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='idc_rack_machine',
            field=models.ForeignKey(blank=True, db_comment='机柜ID', help_text='机柜ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_idc_rack_machine', to='xresource.idcrackmachine', to_field='id', verbose_name='机柜'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='machine_room',
            field=models.ForeignKey(blank=True, db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='private_room',
            field=models.ForeignKey(blank=True, db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
        migrations.AlterField(
            model_name='privateroom',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='private_room_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
    ]
