# Generated by Django 5.0.6 on 2024-08-01 16:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('xresource', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='creator',
            field=models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='generalconsumable',
            name='creator',
            field=models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='holisticaccessory',
            name='creator',
            field=models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='idcrackmachine',
            name='creator',
            field=models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='machineroom',
            name='creator',
            field=models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='idcrackmachine',
            name='machine_room',
            field=models.ForeignKey(blank=True, db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='idc_rack_machine_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房ID'),
        ),
        migrations.AddField(
            model_name='holisticaccessory',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='holistic_accessory_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房ID'),
        ),
        migrations.AddField(
            model_name='generalconsumable',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='general_consumable_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房ID'),
        ),
        migrations.AddField(
            model_name='networkhardware',
            name='creator',
            field=models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='networkhardware',
            name='idc_rack_machine',
            field=models.ForeignKey(db_comment='机柜ID', help_text='机柜ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='network_hardware_idc_rack_machine', to='xresource.idcrackmachine', to_field='id', verbose_name='机柜ID'),
        ),
        migrations.AddField(
            model_name='networkhardware',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='network_hardware_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房ID'),
        ),
        migrations.AddField(
            model_name='physicalservermachine',
            name='creator',
            field=models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='physicalservermachine',
            name='idc_rack_machine',
            field=models.ForeignKey(db_comment='机柜ID', help_text='机柜ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_idc_rack_machine', to='xresource.idcrackmachine', to_field='id', verbose_name='机柜ID'),
        ),
        migrations.AddField(
            model_name='physicalservermachine',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房ID'),
        ),
        migrations.AddField(
            model_name='privateroom',
            name='creator',
            field=models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='privateroom',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='private_room_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房ID'),
        ),
        migrations.AddField(
            model_name='physicalservermachine',
            name='private_room',
            field=models.ForeignKey(db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间ID'),
        ),
        migrations.AddField(
            model_name='networkhardware',
            name='private_room',
            field=models.ForeignKey(db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='network_hardware_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间ID'),
        ),
        migrations.AddField(
            model_name='idcrackmachine',
            name='private_room',
            field=models.ForeignKey(blank=True, db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='idc_rack_machine_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间ID'),
        ),
        migrations.AddField(
            model_name='holisticaccessory',
            name='private_room',
            field=models.ForeignKey(db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='holistic_accessory_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间ID'),
        ),
        migrations.AddField(
            model_name='generalconsumable',
            name='private_room',
            field=models.ForeignKey(db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='general_consumable_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间ID'),
        ),
        migrations.AddField(
            model_name='resourcechangelog',
            name='creator',
            field=models.ForeignKey(db_comment='创建人', db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
    ]
