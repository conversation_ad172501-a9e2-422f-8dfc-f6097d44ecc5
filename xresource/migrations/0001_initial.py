# Generated by Django 5.0.6 on 2024-08-01 16:14

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('modifier', models.Char<PERSON>ield(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('name', models.CharField(db_comment='客户名称', help_text='客户名称', max_length=255, verbose_name='客户名称')),
                ('description', models.TextField(blank=True, db_comment='客户描述', help_text='客户描述', verbose_name='客户描述')),
                ('locale_state', models.CharField(blank=True, db_comment='国家', default='China', help_text='国家', max_length=255, verbose_name='国家')),
                ('locale_province', models.CharField(blank=True, db_comment='省份', help_text='省份', max_length=63, verbose_name='省份')),
                ('locale_city', models.CharField(blank=True, db_comment='城市', help_text='城市', max_length=63, verbose_name='城市')),
                ('phone', models.CharField(blank=True, db_comment='客户联系人手机号', help_text='客户联系人手机号', max_length=15, verbose_name='客户联系人手机号')),
                ('email', models.CharField(blank=True, db_comment='客户联系人邮箱', help_text='客户联系人邮箱', max_length=255, verbose_name='客户联系人邮箱')),
                ('usage_type', models.CharField(blank=True, db_comment='使用类型', help_text='使用类型', max_length=63, verbose_name='使用类型')),
                ('customer_manager_name', models.CharField(blank=True, db_comment='客户经理名称', help_text='客户经理名称', max_length=63, verbose_name='客户经理名称')),
                ('customer_manager_phone', models.CharField(blank=True, db_comment='客户经理电话', help_text='客户经理电话', max_length=15, verbose_name='客户经理电话')),
                ('customer_manager_email', models.CharField(blank=True, db_comment='客户经理邮箱', help_text='客户经理邮箱', max_length=255, verbose_name='客户经理邮箱')),
            ],
            options={
                'verbose_name': '客户信息表',
                'verbose_name_plural': '客户信息表',
                'db_table': 'resource_customers',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='GeneralConsumable',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('name', models.CharField(blank=True, db_comment='设备名称', default=None, help_text='设备名称', max_length=255, null=True, verbose_name='设备名称')),
                ('machine_type', models.CharField(db_comment='设备类型', default=None, help_text='设备类型', max_length=63, verbose_name='设备类型')),
                ('manufacturer', models.CharField(db_comment='厂商', default=None, help_text='厂商', max_length=255, verbose_name='厂商')),
                ('belong', models.CharField(blank=True, db_comment='资产归属', default=None, help_text='资产归属', max_length=63, null=True, verbose_name='资产归属')),
                ('specs', models.CharField(db_comment='规格', default=None, help_text='规格', max_length=255, null=True, verbose_name='规格')),
                ('count', models.IntegerField(db_comment='数量', help_text='数量', null=True, verbose_name='数量')),
            ],
            options={
                'verbose_name': '资产-综合耗材表',
                'verbose_name_plural': '资产-综合耗材表',
                'db_table': 'resource_general_consumables',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='HolisticAccessory',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('physical_machine_sn', models.CharField(db_comment='设备SN', default=None, help_text='设备SN', max_length=255, unique=True, verbose_name='设备SN')),
                ('machine_type', models.CharField(db_comment='设备类型', default=None, help_text='设备类型', max_length=63, verbose_name='设备类型')),
                ('manufacturer', models.CharField(db_comment='厂商', default=None, help_text='厂商', max_length=255, verbose_name='厂商')),
                ('belong', models.CharField(blank=True, db_comment='资产归属', default=None, help_text='资产归属', max_length=63, null=True, verbose_name='资产归属')),
                ('specs', models.CharField(db_comment='规格', default=None, help_text='规格', max_length=255, null=True, verbose_name='规格')),
                ('count', models.IntegerField(db_comment='数量', default=1, help_text='数量', null=True, verbose_name='数量')),
            ],
            options={
                'verbose_name': '资产-综合配件表',
                'verbose_name_plural': '资产-综合配件表',
                'db_table': 'resource_holistic_accessories',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='IDCRackMachine',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('machine_room_alias', models.CharField(db_comment='机房缩写', default=None, help_text='机房缩写', max_length=255, verbose_name='机房缩写')),
                ('private_room_alias', models.CharField(db_comment='包间缩写', default=None, help_text='包间缩写', max_length=255, verbose_name='包间缩写')),
                ('rack_sn', models.CharField(db_comment='机柜编号', default=None, help_text='机柜编号', max_length=255, unique=True, verbose_name='机柜编号')),
                ('net_operator_business', models.CharField(db_comment='网络运营商', default=None, help_text='网络运营商', max_length=63, verbose_name='网络运营商')),
                ('rack_type', models.CharField(db_comment='机柜类型', default=None, help_text='机柜类型', max_length=63, verbose_name='机柜类型')),
                ('rack_physics_type', models.CharField(db_comment='机柜物理形态', default=None, help_text='机柜物理形态', max_length=63, verbose_name='机柜物理形态')),
                ('network_type', models.CharField(blank=True, db_comment='网络类型', default=None, help_text='网络类型', max_length=63, null=True, verbose_name='网络类型')),
                ('network_cluster', models.CharField(blank=True, db_comment='网络集群', default=None, help_text='网络集群', max_length=63, null=True, verbose_name='网络集群')),
                ('bmc_pod_sn', models.CharField(blank=True, db_comment='BMC_POD编号', default=None, help_text='BMC_POD编号', max_length=63, null=True, verbose_name='BMC_POD编号')),
                ('asw_pod_sn', models.CharField(blank=True, db_comment='ASW_POD编号', default=None, help_text='ASW_POD编号', max_length=63, null=True, verbose_name='ASW_POD编号')),
                ('network_arhitecture', models.CharField(blank=True, db_comment='网络架构', default=None, help_text='网络架构', max_length=63, null=True, verbose_name='网络架构')),
                ('security_domain', models.CharField(blank=True, db_comment='安全域', default=None, help_text='安全域', max_length=63, null=True, verbose_name='安全域')),
                ('logical_zone', models.CharField(blank=True, db_comment='逻辑区域', default=None, help_text='逻辑区域', max_length=63, null=True, verbose_name='逻辑区域')),
                ('accept_delivery_time', models.DateTimeField(blank=True, db_comment='验收交付时间', default=None, help_text='验收交付时间', null=True, verbose_name='验收交付时间')),
                ('construction_status', models.CharField(blank=True, db_comment='模建状态', default=None, help_text='模建状态', max_length=63, null=True, verbose_name='模建状态')),
                ('construction_finished_time', models.CharField(blank=True, db_comment='模建完成时间', default=None, help_text='模建完成时间', max_length=63, null=True, verbose_name='模建完成时间')),
                ('rack_status', models.CharField(blank=True, db_comment='柜位状态', default=None, help_text='柜位状态', max_length=63, null=True, verbose_name='柜位状态')),
                ('power_status', models.CharField(blank=True, db_comment='上电状态', default=None, help_text='上电状态', max_length=63, null=True, verbose_name='上电状态')),
                ('billing_unit', models.CharField(blank=True, db_comment='计费单元', default=None, help_text='计费单元', max_length=63, null=True, verbose_name='计费单元')),
                ('construction_density', models.IntegerField(blank=True, db_comment='建设密度', default=None, help_text='建设密度', null=True, verbose_name='建设密度')),
                ('to_shelve_density', models.FloatField(blank=True, db_comment='上架密度', default=None, help_text='上架密度', null=True, verbose_name='上架密度')),
                ('rack_height', models.IntegerField(blank=True, db_comment='高度(mm)', default=None, help_text='高度(mm)', null=True, verbose_name='高度(mm)')),
                ('rack_u_height', models.IntegerField(blank=True, db_comment='高度U(mm)', default=None, help_text='高度U(mm)', null=True, verbose_name='高度U(mm)')),
                ('rack_width', models.IntegerField(blank=True, db_comment='宽度(mm)', default=None, help_text='宽度(mm)', null=True, verbose_name='宽度(mm)')),
                ('rack_deep', models.IntegerField(blank=True, db_comment='深度(mm)', default=None, help_text='深度(mm)', null=True, verbose_name='深度(mm)')),
                ('load_bearing', models.IntegerField(blank=True, db_comment='承重(KG)', default=None, help_text='承重(KG)', null=True, verbose_name='承重(KG)')),
                ('power_type', models.CharField(blank=True, db_comment='电源类型', default=None, help_text='电源类型', max_length=63, null=True, verbose_name='电源类型')),
                ('power_count', models.IntegerField(blank=True, db_comment='电源路数', default=None, help_text='电源路数', null=True, verbose_name='电源路数')),
                ('normal_power', models.IntegerField(blank=True, db_comment='额定功率', default=None, help_text='额定功率', null=True, verbose_name='额定功率')),
                ('power_max', models.IntegerField(blank=True, db_comment='功率上限', default=None, help_text='功率上限', null=True, verbose_name='功率上限')),
            ],
            options={
                'verbose_name': 'IDC资源机柜表',
                'verbose_name_plural': 'IDC资源机柜表',
                'db_table': 'resource_idc_rack_machines',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='MachineRoom',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('name', models.CharField(db_comment='机房名称', default=None, help_text='机房名称', max_length=255, unique=True, verbose_name='机房名称')),
                ('description', models.TextField(blank=True, db_comment='机房描述', help_text='机房描述', verbose_name='机房描述')),
                ('province', models.CharField(blank=True, db_comment='省份', help_text='省份', max_length=255, verbose_name='省份')),
                ('city', models.CharField(blank=True, db_comment='城市', help_text='城市', max_length=255, verbose_name='城市')),
                ('district', models.CharField(blank=True, db_comment='区县', help_text='区县', max_length=255, verbose_name='区县')),
                ('address', models.CharField(blank=True, db_comment='机房地址', help_text='机房地址', max_length=15, verbose_name='机房地址')),
                ('manager_name', models.CharField(blank=True, db_comment='责任人名称', help_text='责任人名称', max_length=255, verbose_name='责任人名称')),
                ('manager_phone', models.CharField(blank=True, db_comment='责任人电话', help_text='责任人电话', max_length=15, verbose_name='责任人电话')),
                ('manager_email', models.CharField(blank=True, db_comment='责任人邮箱', help_text='责任人邮箱', max_length=255, verbose_name='责任人邮箱')),
            ],
            options={
                'verbose_name': '机房信息表',
                'verbose_name_plural': '机房信息表',
                'db_table': 'resource_machine_rooms',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='NetworkHardware',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('physical_machine_sn', models.CharField(db_comment='设备SN', default=None, help_text='设备SN', max_length=255, unique=True, verbose_name='设备SN')),
                ('machine_type', models.CharField(db_comment='设备类型', default=None, help_text='设备类型', max_length=63, verbose_name='设备类型')),
                ('description', models.TextField(blank=True, db_comment='设备描述', help_text='设备描述', verbose_name='设备描述')),
                ('rack_unit', models.IntegerField(db_comment='U位', default=-1, help_text='U位', verbose_name='U位')),
                ('manufacturer', models.CharField(db_comment='厂商', default=None, help_text='厂商', max_length=255, verbose_name='厂商')),
                ('machine_specs', models.CharField(db_comment='型号规格', default=None, help_text='型号规格', max_length=255, null=True, verbose_name='型号规格')),
                ('belong', models.CharField(blank=True, db_comment='资产归属', default=None, help_text='资产归属', max_length=63, null=True, verbose_name='资产归属')),
                ('specs', models.CharField(db_comment='规格', default=None, help_text='规格', max_length=255, null=True, verbose_name='规格')),
                ('normal_power', models.IntegerField(db_comment='额定功率', help_text='额定功率', null=True, verbose_name='额定功率')),
            ],
            options={
                'verbose_name': '资产-网络设备表',
                'verbose_name_plural': '资产-网络设备表',
                'db_table': 'resource_network_hardwares',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='PhysicalServerMachine',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('physical_machine_sn', models.CharField(db_comment='设备SN', default=None, help_text='设备SN', max_length=255, unique=True, verbose_name='设备SN')),
                ('machine_type', models.CharField(db_comment='设备类型', default=None, help_text='设备类型', max_length=63, verbose_name='设备类型')),
                ('description', models.TextField(blank=True, db_comment='设备描述', help_text='设备描述', verbose_name='设备描述')),
                ('rack_unit', models.IntegerField(db_comment='U位', default=-1, help_text='U位', verbose_name='U位')),
                ('manufacturer', models.CharField(db_comment='厂商', default=None, help_text='厂商', max_length=255, verbose_name='厂商')),
                ('machine_specs', models.CharField(db_comment='型号规格', default=None, help_text='型号规格', max_length=255, null=True, verbose_name='型号规格')),
                ('belong', models.CharField(blank=True, db_comment='资产归属', default=None, help_text='资产归属', max_length=63, null=True, verbose_name='资产归属')),
                ('specs', models.CharField(db_comment='规格', default=None, help_text='规格', max_length=255, null=True, verbose_name='规格')),
                ('normal_power', models.IntegerField(db_comment='额定功率', help_text='额定功率', null=True, verbose_name='额定功率')),
            ],
            options={
                'verbose_name': '资产-服务器设备表',
                'verbose_name_plural': '资产-服务器设备表',
                'db_table': 'resource_physical_server_machines',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='PrivateRoom',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('name', models.CharField(db_comment='包间名称', default=None, help_text='包间名称', max_length=255, unique=True, verbose_name='包间名称')),
                ('private_room_type', models.CharField(db_comment='包间描述', default=None, help_text='包间描述', max_length=255, verbose_name='包间类型')),
                ('description', models.TextField(blank=True, db_comment='包间描述', help_text='包间描述', verbose_name='包间描述')),
                ('block_sn', models.CharField(db_comment='楼号', default=None, help_text='楼号', max_length=255, verbose_name='楼号')),
                ('floor_sn', models.CharField(db_comment='楼层', default=None, help_text='楼层', max_length=255, verbose_name='楼层')),
                ('in_service_date', models.DateField(blank=True, db_comment='正式投入使用时间', default=None, help_text='正式投入使用时间', null=True, verbose_name='正式投入使用时间')),
                ('out_of_service_date', models.DateField(blank=True, db_comment='解约时间', default=None, help_text='解约时间', null=True, verbose_name='解约时间')),
                ('planed_service_date', models.DateField(blank=True, db_comment='规划开始时间', default=None, help_text='规划开始时间', null=True, verbose_name='规划开始时间')),
                ('build_service_date', models.DateField(blank=True, db_comment='建设开始时间', default=None, help_text='建设开始时间', null=True, verbose_name='建设开始时间')),
                ('last_inspection_date', models.DateTimeField(blank=True, db_comment='最近一次检修时间', default=None, help_text='最近一次检修时间', null=True, verbose_name='最近一次检修时间')),
                ('status', models.CharField(blank=True, db_comment='状态', help_text='状态', max_length=63, verbose_name='状态')),
                ('manager_name', models.CharField(blank=True, db_comment='责任人名称', help_text='责任人名称', max_length=63, verbose_name='责任人名称')),
                ('manager_phone', models.CharField(blank=True, db_comment='责任人电话', help_text='责任人电话', max_length=15, verbose_name='责任人电话')),
                ('manager_email', models.CharField(blank=True, db_comment='责任人邮箱', help_text='责任人邮箱', max_length=255, verbose_name='责任人邮箱')),
            ],
            options={
                'verbose_name': '包间信息表',
                'verbose_name_plural': '包间信息表',
                'db_table': 'resource_private_rooms',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='ResourceChangeLog',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('description', models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, db_comment='修改人', help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('resource_id', models.CharField(db_comment='资源ID', db_index=True, default=None, help_text='资源ID', max_length=63, verbose_name='资源ID')),
                ('resource_model', models.CharField(db_comment='资源类型', default=None, help_text='资源类型', max_length=255, verbose_name='资源类型')),
                ('old_value', models.TextField(blank=True, db_comment='变更信息旧值', default=None, help_text='变更信息旧值', null=True, verbose_name='变更信息旧值')),
                ('new_value', models.TextField(blank=True, db_comment='变更信息新值', default=None, help_text='变更信息新值', null=True, verbose_name='变更信息新值')),
                ('value_name', models.CharField(db_comment='变更信息KEY英文名称', default=None, help_text='变更信息KEY英文名称', max_length=255, verbose_name='变更信息KEY英文名称')),
                ('key_cn_name', models.CharField(db_comment='变更信息KEY中文名称', default=None, help_text='变更信息KEY中文名称', max_length=255, verbose_name='变更信息KEY中文名称')),
                ('value_type', models.IntegerField(choices=[(0, 'string'), (1, 'model_select'), (2, 'dictionary_select')], db_comment='值数据类型', default=0, help_text='值数据类型', verbose_name='值数据类型')),
                ('cn_content_log', models.TextField(blank=True, db_comment='变更中文日志', default=None, help_text='变更中文日志', null=True, verbose_name='变更中文日志')),
            ],
            options={
                'verbose_name': '核心模型',
                'verbose_name_plural': '核心模型',
                'abstract': False,
            },
        ),
    ]
