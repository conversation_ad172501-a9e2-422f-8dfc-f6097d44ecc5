# Generated by Django 5.0.6 on 2025-02-11 14:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('xresource', '0007_alter_idcrackmachine_rack_u_height_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='generalconsumable',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='general_consumable_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='generalconsumable',
            name='private_room',
            field=models.ForeignKey(db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='general_consumable_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
        migrations.AlterField(
            model_name='holisticaccessory',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='holistic_accessory_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='holisticaccessory',
            name='private_room',
            field=models.ForeignKey(db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='holistic_accessory_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
        migrations.AlterField(
            model_name='idcrackmachine',
            name='machine_room',
            field=models.ForeignKey(blank=True, db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='idc_rack_machine_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='idcrackmachine',
            name='private_room',
            field=models.ForeignKey(blank=True, db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='idc_rack_machine_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
        migrations.AlterField(
            model_name='networkhardware',
            name='idc_rack_machine',
            field=models.ForeignKey(db_comment='机柜ID', help_text='机柜ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='network_hardware_idc_rack_machine', to='xresource.idcrackmachine', to_field='id', verbose_name='机柜'),
        ),
        migrations.AlterField(
            model_name='networkhardware',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='network_hardware_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='networkhardware',
            name='private_room',
            field=models.ForeignKey(db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='network_hardware_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='idc_rack_machine',
            field=models.ForeignKey(blank=True, db_comment='机柜ID', help_text='机柜ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_idc_rack_machine', to='xresource.idcrackmachine', to_field='id', verbose_name='机柜'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='machine_room',
            field=models.ForeignKey(blank=True, db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='private_room',
            field=models.ForeignKey(blank=True, db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间'),
        ),
        migrations.AlterField(
            model_name='privateroom',
            name='machine_room',
            field=models.ForeignKey(db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='private_room_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房'),
        ),
    ]
