# Generated by Django 5.0.6 on 2024-09-12 15:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('xresource', '0004_alter_idcrackmachine_rack_sn'),
    ]

    operations = [
        migrations.AddField(
            model_name='physicalservermachine',
            name='gpu_model',
            field=models.CharField(blank=True, db_comment='GPU类型', help_text='GPU类型', max_length=63, null=True, verbose_name='GPU类型'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='description',
            field=models.CharField(blank=True, db_comment='描述', help_text='描述', max_length=255, null=True, verbose_name='描述'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='idc_rack_machine',
            field=models.ForeignKey(blank=True, db_comment='机柜ID', help_text='机柜ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_idc_rack_machine', to='xresource.idcrackmachine', to_field='id', verbose_name='机柜ID'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='machine_room',
            field=models.ForeignKey(blank=True, db_comment='机房ID', help_text='机房ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_machine_room', to='xresource.machineroom', to_field='id', verbose_name='机房ID'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='machine_specs',
            field=models.CharField(blank=True, db_comment='型号规格', default=None, help_text='型号规格', max_length=255, null=True, verbose_name='型号规格'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='manufacturer',
            field=models.CharField(blank=True, db_comment='厂商', default=None, help_text='厂商', max_length=255, null=True, verbose_name='厂商'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='normal_power',
            field=models.IntegerField(blank=True, db_comment='额定功率', default=None, help_text='额定功率', null=True, verbose_name='额定功率'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='private_room',
            field=models.ForeignKey(blank=True, db_comment='包间ID', help_text='包间ID', max_length=63, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='physical_server_machine_private_room', to='xresource.privateroom', to_field='id', verbose_name='包间ID'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='rack_unit',
            field=models.IntegerField(blank=True, db_comment='U位', default=-1, help_text='U位', null=True, verbose_name='U位'),
        ),
        migrations.AlterField(
            model_name='physicalservermachine',
            name='specs',
            field=models.CharField(blank=True, db_comment='规格', default=None, help_text='规格', max_length=255, null=True, verbose_name='规格'),
        ),
    ]
